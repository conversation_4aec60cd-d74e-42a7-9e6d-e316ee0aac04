using System.Windows.Input;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.ViewModels
{
    public class ConfirmationViewModel : WindowViewModel
    {
        private readonly Action<bool> _callback;

        public ConfirmationViewModel(Action<bool> callback)
        {
            _callback = callback;
            YesCommand = new RelayCommand(_ => _callback(true));
            NoCommand = new RelayCommand(_ => _callback(false));
        }

        public ICommand YesCommand { get; }
        public ICommand NoCommand { get; }

        public string Message => "Вы уверены, что хотите отменить операцию?";
        public string Title => "Подтверждение отмены";
    }
}
