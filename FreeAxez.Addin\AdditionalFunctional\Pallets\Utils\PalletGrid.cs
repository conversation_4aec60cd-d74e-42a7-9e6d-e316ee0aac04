﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.Pallets.Models;
using FreeAxez.Addin.Infrastructure.UI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.Pallets.Utils
{
    public class PalletGrid
    {
        public const double UnitStep = 1.64061245058534;

        private double _palletLengthX;
        private double _palletLengthY;
        private double _requiredAreaToCreateStagingPlane = 400d;
        private double _offsetFromUndersheet = 0.5;
        private XYZ _startPoint;
        private XYZ _placementDirectionVector;
        private PalletConfigurator _palletConfigurator;
        private PlacementOption _placementOption;


        public PalletGrid(PalletConfigurator palletConfigurator, PlacementOption placementOption)
        {
            _palletConfigurator = palletConfigurator;
            _placementOption = placementOption;

            if (_placementOption.VerticalDirection)
            {
                _palletLengthX = PalletConfigurator.PalletWidth;
                _palletLengthY = PalletConfigurator.PalletLength;
            }
            else
            {
                _palletLengthX = PalletConfigurator.PalletLength;
                _palletLengthY = PalletConfigurator.PalletWidth;
            }
        }


        public bool IsStagingPlaneRequired()
        {
            var installationAreaForOneBaseUnit = UnitStep * UnitStep;
            var countOfAllBaseUnits = _palletConfigurator.GetUnitsAssociatedWithBaseUnits().Count;
            var installationArea = countOfAllBaseUnits * installationAreaForOneBaseUnit;

            return installationArea >= _requiredAreaToCreateStagingPlane;
        }
      
        public List<PalletLocation> GetPallets()
        {
            List<PalletLocation> palletLocations = new List<PalletLocation>();

            _placementDirectionVector = GetPlacementDirectionVector();
            _startPoint = GetStartPoint();

            // The points are sorted in such a way that, first of all, evenly spread the pallets with the base blocks.
            var grid = GetAvailablePointsForPlacement();

            // The pallet placement for base units is different from the placement of all others.
            // There are few basic units on the pallet and there will be more of these pallets on the plan and
            // it is necessary that these pallets are evenly distributed over the floor.
            // Secondary pallets are placed at points that are equidistant from the units that will be in this pallet.
            palletLocations.AddRange(GetPalletLocationsForBaseUnitPalletType(ref grid));
            palletLocations.AddRange(GetPalletLocationsForSecondaryPalletType(ref grid));

            return palletLocations;
        }

        private XYZ GetPlacementDirectionVector()
        {
            var allBaseUnitPoints = _palletConfigurator.GetUnitsAssociatedWithBaseUnits()
                .Select(u => (u.Location as LocationPoint).Point)
                .Select(p => new XYZ(p.X, p.Y, 0))
                .ToList();

            var closestUnitPoints = allBaseUnitPoints
                .OrderBy(p => p.DistanceTo(_placementOption.StartRegion.Center))
                .Take(2 * _palletConfigurator.GetNumberOfUnitsPerPallet(PalletType.BaseUnit))
                .ToList();

            var closestUnitsCenterPoint = GetCenterPoint(closestUnitPoints);

            return closestUnitsCenterPoint.Subtract(_placementOption.StartRegion.Center);
        }

        private XYZ GetStartPoint()
        {
            if (_placementOption.VerticalDirection)
            {
                var offset = _placementOption.StartRegion.Length / 2 + _palletLengthX / 2 + _offsetFromUndersheet;
                var direction = _placementDirectionVector.X >= 0 ? 1 : -1;
                return _placementOption.StartRegion.Center.Add(new XYZ(offset * direction, 0, 0));
            }
            else
            {
                var offset = _placementOption.StartRegion.Width / 2 + _palletLengthY / 2 + _offsetFromUndersheet;
                var direction = _placementDirectionVector.Y >= 0 ? 1 : -1;
                return _placementOption.StartRegion.Center.Add(new XYZ(0, offset * direction, 0));
            }
        }

        /// <summary>
        /// 
        /// Returns sorted points where pallets can be placed. 
        /// 
        /// The points are sorted as follows:
        /// 1. Main grid points created based on the horizontal/vertical spacing of the pallets and the starting point. 
        ///    When placing a pallet at this point, it is completely inside the room.
        /// 2. Points that had to be slightly shifted so that the dimensions of the pallet 
        ///    did not protrude beyond the contour of the room when placed at such a point.
        /// 3. Around each point of the main grid, additional points are created, one for each direction up, right, down, left.
        ///    They are needed to place pallets if the main points are over.
        ///     
        /// </summary>
        private List<XYZ> GetAvailablePointsForPlacement()
        {
            // Base unit points are used to indirectly determine the dimensions of a room.
            var baseUnits = _palletConfigurator.GetUnitsAssociatedWithBaseUnits();
            var baseUnitPoints = baseUnits.Select(u => (u.Location as LocationPoint).Point).Select(p => new XYZ(p.X, p.Y, 0)).ToList();

            var originalGrid = GetOriginalGrid(baseUnitPoints);

            // If do not delete the row above, then the pallets will be placed directly on the starting region.
            originalGrid = RemoveRowAboveStartPoint(originalGrid);
            var correctedGrid = CorrectOriginalGrid(originalGrid, baseUnitPoints);
            var additionalPoints = GetAdditionalPoints(correctedGrid, baseUnitPoints);

            // If there is a need to take additional points for base pallets, we need to take from the end
            additionalPoints.Reverse();

            correctedGrid.AddRange(additionalPoints);

            if (_placementOption.SelectProtectedRegion)
            {
                correctedGrid = ExcludePointsInProtectedRegion(correctedGrid);
            }

            // TODO: If there is a request to add points, you can add them here.
            // Now one point is added for each pallet, thus creating a double stock.
            // But due to the protected region, this may not be enough.
            var requiredNumberOfPallets = RequiredNumberOfPallets();
            if (correctedGrid.Count < requiredNumberOfPallets)
            {
                InfoDialog.ShowDialog("Warning", "The plan configuration is too complex and\n" +
                                                 "there is not enough space for pallets.\n" +
                                                 $"{correctedGrid.Count} pallets out of {requiredNumberOfPallets} required will be placed.");
            }

            return correctedGrid;
        }

        private List<XYZ> GetOriginalGrid(List<XYZ> baseUnitPoints)
        {
            // The offset of the maximum coordinates is necessary to increase the placement area
            // and take into account possible points that are in close proximity to the border
            var minX = baseUnitPoints.Min(p => p.X) - _placementOption.StartRegion.Length / 2;
            var maxX = baseUnitPoints.Max(p => p.X) + _placementOption.StartRegion.Length / 2;
            var minY = baseUnitPoints.Min(p => p.Y) - _placementOption.StartRegion.Width / 2;
            var maxY = baseUnitPoints.Max(p => p.Y) + _placementOption.StartRegion.Width / 2;

            double verticalPalletStep = _placementOption.StartRegion.Width;
            double horizontalPalletStep = _placementOption.StartRegion.Length;

            var x = _startPoint.X;
            while (x > minX)
            {
                x -= horizontalPalletStep;
            }
            var y = _startPoint.Y;
            while (y > minY)
            {
                y -= verticalPalletStep;
            }

            var startY = y;

            var originalGrid = new List<XYZ>();
            while (x < maxX)
            {
                while (y < maxY)
                {
                    originalGrid.Add(new XYZ(x, y, 0));
                    y += verticalPalletStep;
                }
                x += horizontalPalletStep;
                y = startY;
            }

            // Order points depends on placement direction from option and direction vector
            var axisSignX = _placementDirectionVector.X >= 0 ? 1 : -1;
            var axisSignY = _placementDirectionVector.Y >= 0 ? 1 : -1;
            if (_placementOption.VerticalDirection)
            {
                originalGrid = originalGrid.OrderBy(p => p.X * axisSignX).ThenBy(p => p.Y * axisSignY).ToList();
            }
            else
            {
                originalGrid = originalGrid.OrderBy(p => p.Y * axisSignY).ThenBy(p => p.X * axisSignX).ToList();
            }

            return originalGrid;
        }

        private List<XYZ> RemoveRowAboveStartPoint(List<XYZ> originalGrid)
        {
            if (_placementOption.VerticalDirection)
            {
                var axisSignX = _placementDirectionVector.X >= 0 ? 1 : -1;
                var x = _startPoint.X - _placementOption.StartRegion.Length * axisSignX;
                originalGrid.RemoveAll(p => Math.Abs(p.X - x) < 0.001);
            }
            else
            {
                var axisSignY = _placementDirectionVector.Y >= 0 ? 1 : -1;
                var y = _startPoint.Y - _placementOption.StartRegion.Width * axisSignY;
                originalGrid.RemoveAll(p => Math.Abs(p.Y - y) < 0.001);
            }

            return originalGrid;
        }

        private List<XYZ> CorrectOriginalGrid(List<XYZ> originalGrid, List<XYZ> baseUnitPoints)
        {
            var correctedPoints = new List<XYZ>();
            var pointsForCorrecting = new List<XYZ>();

            foreach (var gridPoint in originalGrid)
            {
                if (PointFitForPlacingPallet(gridPoint, baseUnitPoints))
                {
                    correctedPoints.Add(gridPoint);
                }
                else
                {
                    pointsForCorrecting.Add(gridPoint);
                }
            }

            // The corrected points will go after the initially correct ones, which will make the mesh more uniform.
            var movedPoints = new List<XYZ>();

            // One step is equal 1 feet
            // Subtract pallet length because the start region length does not include half the pallet length
            var availableStepsAlongX = Math.Floor((_placementOption.StartRegion.Length - _palletLengthX) / 2);
            var availableStepsAlongY = Math.Floor((_placementOption.StartRegion.Width - _palletLengthY) / 2);

            // Move no more than one flight length
            availableStepsAlongX = availableStepsAlongX <= 4 ? availableStepsAlongX : 4;
            availableStepsAlongY = availableStepsAlongY <= 4 ? availableStepsAlongY : 4;

            foreach (var gridPoint in pointsForCorrecting)
            {
                var possiblePoints = new List<XYZ>();

                var x = gridPoint.X - availableStepsAlongX;
                while (x <= gridPoint.X + availableStepsAlongX)
                {
                    possiblePoints.Add(new XYZ(x, gridPoint.Y, 0));
                    x++;
                }

                var y = gridPoint.Y - availableStepsAlongY;
                while (y <= gridPoint.Y + availableStepsAlongY)
                {
                    possiblePoints.Add(new XYZ(gridPoint.X, y, 0));
                    y++;
                }

                possiblePoints = possiblePoints.OrderBy(p => p.DistanceTo(gridPoint)).ToList();

                foreach (var point in possiblePoints)
                {
                    if (PointFitForPlacingPallet(point, baseUnitPoints))
                    {
                        movedPoints.Add(point);
                        break;
                    }
                }
            }

            correctedPoints.AddRange(movedPoints);

            return correctedPoints;
        }

        /// <summary>
        /// The method adds one additional points depending on the placement direction. 
        /// One additional point should be enough to arrange all the necessary pallets.
        /// </summary>
        private List<XYZ> GetAdditionalPoints(List<XYZ> correctedGrid, List<XYZ> baseUnitPoints)
        {
            var additionalPoints = new List<XYZ>();

            if (_placementOption.VerticalDirection)
            {
                foreach (var point in correctedGrid)
                {
                    var up = new XYZ(point.X, point.Y + _palletLengthY, 0);
                    if (PointFitForPlacingPallet(up, baseUnitPoints))
                    {
                        additionalPoints.Add(up);
                    }
                }
            }
            else
            {
                foreach (var point in correctedGrid)
                {
                    var right = new XYZ(point.X + _palletLengthX, point.Y, 0);
                    if (PointFitForPlacingPallet(right, baseUnitPoints))
                    {
                        additionalPoints.Add(right);
                    }
                }
            }

            return additionalPoints;
        }

        private List<XYZ> ExcludePointsInProtectedRegion(List<XYZ> grid)
        {
            var validPoints = new List<XYZ>();

            foreach (var point in grid)
            {
                if (_placementOption.ProtectedRegion.IsInside(point))
                {
                    continue;
                }

                var palletCorners = new List<XYZ>()
                {
                    new XYZ(point.X - _palletLengthX / 2, point.Y - _palletLengthY / 2, 0),
                    new XYZ(point.X - _palletLengthX / 2, point.Y + _palletLengthY / 2, 0),
                    new XYZ(point.X + _palletLengthX / 2, point.Y - _palletLengthY / 2, 0),
                    new XYZ(point.X + _palletLengthX / 2, point.Y + _palletLengthY / 2, 0)
                };

                var cornerInProtectedRegion = false;
                foreach (var corner in palletCorners)
                {
                    if (_placementOption.ProtectedRegion.IsInside(corner))
                    {
                        cornerInProtectedRegion = true;
                        break;
                    }
                }

                if (cornerInProtectedRegion)
                {
                    continue;
                }

                validPoints.Add(point);
            }

            return validPoints;
        }

        private List<XYZ> GetPalletAdditionalPoints(XYZ testPoint, List<XYZ> baseUnitPoints)
        {
            var output = new List<XYZ>();

            var closestUnitPoints = baseUnitPoints.OrderBy(p => p.DistanceTo(testPoint)).Take(16);
            var unitBoundingBoxes = closestUnitPoints.Select(p => new BoundingBox2(p, UnitStep / 2)).ToList();
            var palletBoundingBox = new BoundingBox2(testPoint, _palletLengthX / 2, _palletLengthY / 2);

            foreach (var palletCorner in palletBoundingBox.Corners)
            {
                foreach (var unitBoundingBox in unitBoundingBoxes)
                {
                    if (unitBoundingBox.IsInside(palletCorner))
                    {
                        output.Add(palletCorner);
                    }
                }
            }

            return output;
        }

        /// <summary>
        /// 
        /// For the tested point, 16 nearest units are taken. 
        /// 16 because that's the maximum number of units that can hold one pallet at any position. 
        /// For each unit, an bounding box is built with a side equal to the step of the units. 
        /// Based on the tested point, the corner points of the pallet are taken. 
        /// As a result, the tested point is suitable for placing a pallet if all the corners of the pallet are in one of the base unit's bounding boxes.
        /// 
        /// TODO: The algorithm is long and not exact. 
        /// For example, it will give an incorrect result if a wall passes through the pallet and there are no units in the center, 
        /// but due to the fact that the corners are in the bounding boxes, the point will be designated as suitable.
        /// 
        /// </summary>
        private bool PointFitForPlacingPallet(XYZ testPoint, List<XYZ> baseUnitPoints)
        {
            var closestUnitPoints = baseUnitPoints.OrderBy(p => p.DistanceTo(testPoint)).Take(16);
            var unitBoundingBoxes = closestUnitPoints.Select(p => new BoundingBox2(p, UnitStep / 2)).ToList();
            var palletBoundingBox = new BoundingBox2(testPoint, _palletLengthX / 2, _palletLengthY / 2);

            foreach (var palletCorner in palletBoundingBox.Corners)
            {
                var cornerInsideUnitBoundingBox = false;

                foreach (var unitBoundingBox in unitBoundingBoxes)
                {
                    if (unitBoundingBox.IsInside(palletCorner))
                    {
                        cornerInsideUnitBoundingBox = true;
                        break;
                    }
                }

                if (!cornerInsideUnitBoundingBox)
                {
                    return false;
                }
            }

            return true;
        }
        
        private List<PalletLocation> GetPalletLocationsForBaseUnitPalletType(ref List<XYZ> grid)
        {
            if (grid.Count == 0)
            {
                return new List<PalletLocation>();
            }

            var baseUnitsCount = _palletConfigurator.GetUnits(PalletType.BaseUnit).Count;
            var baseUnitsPerPallet = _palletConfigurator.GetNumberOfUnitsPerPallet(PalletType.BaseUnit);
            var palletsCount = (int)Math.Floor((double)baseUnitsCount / baseUnitsPerPallet);

            if (grid.Count < palletsCount)
            {
                palletsCount = grid.Count;
            }

            var palletLocations = grid.Take(palletsCount).Select(p => new PalletLocation() { Location = p, PalletType = PalletType.BaseUnit }).ToList();
            grid.RemoveRange(0, palletsCount);

            return palletLocations;
        }

        private List<PalletLocation> GetPalletLocationsForSecondaryPalletType(ref List<XYZ> grid)
        {
            var palletLocations = new List<PalletLocation>();

            if (grid.Count == 0)
            {
                return palletLocations;
            }

            foreach (var palletType in _palletConfigurator.GetPalletTypes())
            {
                // Base pallets are placed separately
                if (palletType == PalletType.BaseUnit)
                {
                    continue;
                }

                var unitPoints = _palletConfigurator.GetUnits(palletType).Select(f => (f.Location as LocationPoint).Point).ToList();
                var palletLocationsForPalletType = GetPalletLocations(new List<PalletLocation>(), palletType, unitPoints, ref grid);
                palletLocations.AddRange(palletLocationsForPalletType);
            }

            return palletLocations;
        }

        private int RequiredNumberOfPallets()
        {
            var numberOfPallets = 0;

            foreach (var palletType in _palletConfigurator.GetPalletTypes())
            {
                var unitsCount = _palletConfigurator.GetUnits(palletType).Count;
                var numberOfUnitsPerPallet = _palletConfigurator.GetNumberOfUnitsPerPallet(palletType);
                numberOfPallets += (int)Math.Floor((double)unitsCount / numberOfUnitsPerPallet);
            }

            return numberOfPallets;
        }


        private List<PalletLocation> GetPalletLocations(
            List<PalletLocation> palletLocations, 
            PalletType palletType, 
            List<XYZ> unitPoints, 
            ref List<XYZ> grid)
        {
            if (grid.Count == 0)
            {
                return palletLocations;
            }

            var numberOfUnitsPerPallet = _palletConfigurator.GetNumberOfUnitsPerPallet(palletType);

            // We do not place incomplete pallets
            if (unitPoints.Count < numberOfUnitsPerPallet)
            {
                return palletLocations;
            }

            var orderedPoints = unitPoints.OrderBy(p => p.DistanceTo(_startPoint)).ToList();
            var pointsForCurrentPallet = orderedPoints.Take(numberOfUnitsPerPallet).ToList();
            var centerPoint = GetCenterPoint(pointsForCurrentPallet);
            var palletLocationPoint = grid.OrderBy(p => p.DistanceTo(centerPoint)).First();

            orderedPoints.RemoveRange(0, numberOfUnitsPerPallet);
            grid.RemoveAt(grid.IndexOf(palletLocationPoint));

            palletLocations.Add(new PalletLocation()
            {
                PalletType = palletType,
                Location = palletLocationPoint
            });

            return GetPalletLocations(palletLocations, palletType, orderedPoints, ref grid);
        }

        private XYZ GetCenterPoint(List<XYZ> points)
        {
            var x = points.Select(p => p.X).Sum() / points.Count;
            var y = points.Select(p => p.Y).Sum() / points.Count;
            return new XYZ(x, y, 0);
        }
    }
}
