using System;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Core.Elements
{
    public abstract class ElementTypeConfiguration
    {
        public abstract double PrimaryLength { get; }
        public abstract double LengthTolerance { get; }
        public abstract string FamilyName { get; }
        public abstract int MinimumRequiredLines { get; }

        public virtual string GetFamilyType(int baseUnitHeight)
        {
            return $"Gridd-{baseUnitHeight}";
        }

        public virtual bool IsValidLength(double length)
        {
            return Math.Abs(length - PrimaryLength) <= LengthTolerance;
        }
    }

    public class BaseUnitConfiguration : ElementTypeConfiguration
    {
        public override double PrimaryLength => 11.84375; // 11 27/32 inches
        public override double LengthTolerance => 0.1;
        public override string FamilyName => "FreeAxez-Base_Unit";
        public override int MinimumRequiredLines => 3;
    }

    public class BaseUnitHalfConfiguration : ElementTypeConfiguration
    {
        public override double PrimaryLength => 11.84375; // 11 27/32 inches
        public override double LengthTolerance => 0.1;
        public override string FamilyName => "FreeAxez-Base_Unit_Half";
        public override int MinimumRequiredLines => 3;

        public double HalfSideMinRatio => 0.25;
        public double HalfOffset => 7.84375; // 7 11/32 inches
    }

    public class BaseUnitQuarterConfiguration : ElementTypeConfiguration
    {
        public override double PrimaryLength => 5.90625; // 5 29/32 inches
        public override double LengthTolerance => 0.1;
        public override string FamilyName => "FreeAxez-Base_Unit_Quarter";
        public override int MinimumRequiredLines => 2;
    }

    public class PlateCornerConfiguration : ElementTypeConfiguration
    {
        public override double PrimaryLength => 5.375; // 5 3/8 inches
        public override double LengthTolerance => 0.1;
        public override string FamilyName => "FreeAxez-Plate_Corner";
        public override int MinimumRequiredLines => 4;
    }

    public class PlateChannelConfiguration : ElementTypeConfiguration
    {
        public override double PrimaryLength => 5.0; // 5 inches (short side)
        public override double LengthTolerance => 0.1;
        public override string FamilyName => "FreeAxez-Plate_Channel";
        public override int MinimumRequiredLines => 4;

        public double LongLength => 11.84375; // 11 27/32 inches

        public bool IsValidShortLength(double length)
        {
            return Math.Abs(length - PrimaryLength) <= LengthTolerance;
        }

        public bool IsValidLongLength(double length)
        {
            return Math.Abs(length - LongLength) <= LengthTolerance;
        }
    }

    public class BorderConfiguration : ElementTypeConfiguration
    {
        public override double PrimaryLength => 0.0; // Variable length
        public override double LengthTolerance => 0.0; // Not used for borders
        public override string FamilyName => "FreeAxez-Border-Full";
        public override int MinimumRequiredLines => 4; // Rectangle = 4 lines

        public override string GetFamilyType(int baseUnitHeight)
        {
            return $"Gridd-{baseUnitHeight} (Straight Gap) - Short End Cover";
        }
    }
}
