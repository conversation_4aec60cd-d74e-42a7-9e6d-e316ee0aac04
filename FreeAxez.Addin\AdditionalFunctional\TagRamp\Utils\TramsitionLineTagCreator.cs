﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TagRamp.Models;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagRamp.Utils
{
    public class TramsitionLineTagCreator
    {
        private readonly double _lengthToCenterOfTag;

        public TramsitionLineTagCreator(double lengthToCenterOfTag)
        {
            _lengthToCenterOfTag = lengthToCenterOfTag;
        }

        public void Create(TramsitionLine tramsitionLine)
        {
            var rumpAnnotationSymbol = new RampFamilies().AnnotationSymbol;

            var rampAnnotationSymbol = RevitManager.Document
                .Create
                .NewFamilyInstance(tramsitionLine.CenterPoint,
                                   rumpAnnotationSymbol,
                                   RevitManager.Document.ActiveView) as AnnotationSymbol;

            RevitManager.Document.Regenerate();

            ResolveCorrectTagLocation(rampAnnotationSymbol, tramsitionLine);
        }

        private void ResolveCorrectTagLocation(AnnotationSymbol annotationSymbol, TramsitionLine tramsitionLine)
        {
            double rampAnnotationSymbolWidth = GetAnnotationSymbolWidth(
                annotationSymbol, RevitManager.Document.ActiveView);

            if (Math.Abs(tramsitionLine.Direction.X) >= 0.5
                && Math.Abs(tramsitionLine.Direction.Y) <= 0.5)
            {
                annotationSymbol.Location.Move(
                    tramsitionLine.Direction *
                        (rampAnnotationSymbolWidth / 2
                            + _lengthToCenterOfTag * RevitManager.Document.ActiveView.Scale)
                        + tramsitionLine.RampDirection * _lengthToCenterOfTag
                            * RevitManager.Document.ActiveView.Scale);

                annotationSymbol.addLeader();

                var leader = annotationSymbol.GetLeaders().FirstOrDefault();

                leader.End = tramsitionLine.CenterPoint;
                leader.Elbow = tramsitionLine.CenterPoint
                    + (tramsitionLine.RampDirection * _lengthToCenterOfTag
                        * RevitManager.Document.ActiveView.Scale)
                    + (tramsitionLine.Direction * (_lengthToCenterOfTag / 1.5)
                        * RevitManager.Document.ActiveView.Scale);
            }
            else
            {
                annotationSymbol.Location.Move(
                    tramsitionLine.RampDirection *
                        (rampAnnotationSymbolWidth / 2 + _lengthToCenterOfTag
                        * RevitManager.Document.ActiveView.Scale)
                    + tramsitionLine.Direction * _lengthToCenterOfTag
                        * RevitManager.Document.ActiveView.Scale);

                annotationSymbol.addLeader();

                var leader = annotationSymbol.GetLeaders().FirstOrDefault();

                leader.End = tramsitionLine.CenterPoint;
                leader.Elbow = tramsitionLine.CenterPoint
                    + (tramsitionLine.Direction * _lengthToCenterOfTag
                        * RevitManager.Document.ActiveView.Scale)
                    + (tramsitionLine.RampDirection * (_lengthToCenterOfTag / 1.5)
                        * RevitManager.Document.ActiveView.Scale);
            }
        }

        private double GetAnnotationSymbolWidth(AnnotationSymbol annotationSymbol, View activeView)
        {
            var boundingBoxXYZ = annotationSymbol.get_BoundingBox(activeView);

            double width = boundingBoxXYZ.Max.X - boundingBoxXYZ.Min.X;

            return width;
        }
    }
}