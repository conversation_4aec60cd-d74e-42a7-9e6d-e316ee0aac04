﻿using System.Windows.Input;
using System.Windows;

namespace FreeAxez.Addin.Infrastructure.UI.ViewModels
{
    public class WindowViewModel : ViewModelBase
    {

        public WindowViewModel()
        {
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
        }

        public ICommand CancelCommand { get; }

        private string _error;
        public string Error
        {
            get => _error;
            set
            {
                if (_error != value)
                {
                    _error = value;
                    OnPropertyChanged();
                }
            }
        }

        protected virtual void OnCancelCommandExecute(object sender)
        {
            if (sender is Window window)
            {
                window.Close();
            }
        }

    }

}
