﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using NetTopologySuite.Geometries;
using Point = NetTopologySuite.Geometries.Point;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Models
{
    public class CircuitWhip : CircuitElement
    {
        private readonly FlexPipe _flexPipe;
        private readonly Point[] _points;
        private List<Geometry> _geometries;

        public CircuitWhip(FlexPipe flexPipe)
        {
            _flexPipe = flexPipe;
            _points = GetPoints();
        }

        public override ElementId LevelId => _flexPipe.ReferenceLevel.Id;
        public ElementId ElementId => _flexPipe.Id;
        public FlexPipe FlexPipe => _flexPipe;
        public Point[] Points => _points;
        public override List<Geometry> Geometries
        {
            get
            {
                if (_geometries == null)
                {
                    _geometries = _points.Select(p => p.<PERSON>uffer(1.0)).ToList();
                }

                return _geometries;
            }
        } 

        private Point[] GetPoints() 
        {
            var output = new Point[2];

            var start = (_flexPipe.Location as LocationCurve).Curve.GetEndPoint(0);
            var end = (_flexPipe.Location as LocationCurve).Curve.GetEndPoint(1);
            output[0] = new Point(start.X, start.Y);
            output[1] = new Point(end.X, end.Y);

            return output;
        }
    }
}
