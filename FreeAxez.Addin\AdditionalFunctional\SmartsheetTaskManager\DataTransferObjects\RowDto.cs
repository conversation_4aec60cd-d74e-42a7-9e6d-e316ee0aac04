﻿using System.Collections.Generic;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.DataTransferObjects;

public class RowDto
{
    public long Id { get; set; }
    public string? ProjectName { get; set; }
    public string? AssignedToEmail { get; set; }
    public int RowNumber { get; set; }
    public IEnumerable<ColumnDto> ColumnDtos { get; set; } = null!;
    public IEnumerable<CommentDto> CommentDtos { get; set; } = null!;
    public IEnumerable<AttachmentDto> AttachmentsDtos { get; set; } = null!;
    public RowDto? ParentRowDto { get; set; }
}
