﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Views;
using System.Windows.Interop;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]

    class ElectricalCircuitCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var electricalCircuitOption = new ElectricalCircuitOptionsWindow();
            var handler = new WindowInteropHelper(electricalCircuitOption);
            handler.Owner = RevitManager.UIApplication.MainWindowHandle;
            electricalCircuitOption.ShowDialog();

            return Result.Succeeded;
        }
    }
}
