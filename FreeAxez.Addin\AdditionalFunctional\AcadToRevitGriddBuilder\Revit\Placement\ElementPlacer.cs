﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Windows.Forms;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Structure;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Core.Elements;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Interfaces;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.Extraction;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Workflow;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Revit.Placement
{
    /// <summary>
    /// Unified service for placing all element types in Revit with separate transactions
    /// </summary>
    public class ElementPlacer
    {
        public BuildResult PlaceAllElements(
            (List<BaseUnit> baseUnits, List<BaseUnitHalf> baseUnitHalves, List<BaseUnitQuarter> baseUnitQuarters,
            List<PlateCorner> plateCorners, List<PlateChannel> plateChannels, List<BorderElement> borderElements) elements,
            Document document, Level level, int baseUnitHeight, Transform dwgTransform, string dwgLinkName,
            IProgressReporter progressReporter = null, CancellationToken cancellationToken = default)
        {
            var result = new BuildResult { DwgLinkName = dwgLinkName };
            var familySymbolCache = new Dictionary<string, FamilySymbol>();

            // Calculate total elements for progress
            var totalElements = elements.baseUnits.Count + elements.baseUnitHalves.Count + elements.baseUnitQuarters.Count +
                               elements.plateCorners.Count + elements.plateChannels.Count + elements.borderElements.Count;

            if (totalElements == 0)
            {
                progressReporter?.ReportStatus("No elements to place");
                return result;
            }

            var placedElements = 0;
            var baseProgress = 60; // Starting from 60% (after extraction)
            var progressRange = 20; // 20% range for element placement (60-80%)

            // Place each element type with detailed progress
            var placedBaseUnits = PlaceElementsWithProgress(elements.baseUnits, "Base Units", document, level, baseUnitHeight,
                dwgTransform, familySymbolCache, result, progressReporter, ref placedElements, totalElements, baseProgress, progressRange, cancellationToken);

            var placedBaseUnitHalves = PlaceElementsWithProgress(elements.baseUnitHalves, "Base Unit Halves", document, level, baseUnitHeight,
                dwgTransform, familySymbolCache, result, progressReporter, ref placedElements, totalElements, baseProgress, progressRange, cancellationToken);

            var placedBaseUnitQuarters = PlaceElementsWithProgress(elements.baseUnitQuarters, "Base Unit Quarters", document, level, baseUnitHeight,
                dwgTransform, familySymbolCache, result, progressReporter, ref placedElements, totalElements, baseProgress, progressRange, cancellationToken);

            var placedPlateCorners = PlaceElementsWithProgress(elements.plateCorners, "Plate Corners", document, level, baseUnitHeight,
                dwgTransform, familySymbolCache, result, progressReporter, ref placedElements, totalElements, baseProgress, progressRange, cancellationToken);

            var placedPlateChannels = PlaceElementsWithProgress(elements.plateChannels, "Plate Channels", document, level, baseUnitHeight,
                dwgTransform, familySymbolCache, result, progressReporter, ref placedElements, totalElements, baseProgress, progressRange, cancellationToken);

            var placedBorderElements = PlaceElementsWithProgress(elements.borderElements, "Border Elements", document, level, baseUnitHeight,
                dwgTransform, familySymbolCache, result, progressReporter, ref placedElements, totalElements, baseProgress, progressRange, cancellationToken);

            result.UpdateElementPlacement(placedBaseUnits, placedBaseUnitHalves, placedBaseUnitQuarters,
                placedPlateCorners, placedPlateChannels, placedBorderElements);

            return result;
        }



        private int PlaceElementsWithProgress<T>(List<T> elements, string typeName, Document document, Level level,
            int baseUnitHeight, Transform dwgTransform, Dictionary<string, FamilySymbol> familySymbolCache, BuildResult result,
            IProgressReporter progressReporter, ref int placedElements, int totalElements, int baseProgress, int progressRange, CancellationToken cancellationToken)
            where T : BaseFaElement
        {
            if (!elements.Any()) return 0;

            progressReporter?.ReportStatus($"Placing {elements.Count} {typeName}...");
            Application.DoEvents();
            cancellationToken.ThrowIfCancellationRequested();

            try
            {
                using (var transaction = new Transaction(document, $"Place {typeName}"))
                {
                    transaction.Start();

                    int placedCount = 0;
                    for (int i = 0; i < elements.Count; i++)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        var element = elements[i];
                        try
                        {
                            var instance = PlaceElement(element.Configuration, element.Center, element.RotationAngle, element.IsRotated,
                                document, level, baseUnitHeight, dwgTransform, familySymbolCache);
                            element.SetElementSpecificParameters(instance);
                            placedCount++;
                            placedElements++;

                            // Update progress every 5 elements or on last element
                            if (i % 5 == 0 || i == elements.Count - 1)
                            {
                                var progress = baseProgress + (double)placedElements / totalElements * progressRange;
                                progressReporter?.ReportProgress(progress);
                                progressReporter?.ReportStatus($"Placed {placedElements}/{totalElements} elements ({typeName}: {placedCount}/{elements.Count})");
                                Application.DoEvents();
                            }
                        }
                        catch (Exception ex)
                        {
                            result.AddWarning($"Failed to place {typeName} element {element.Id}: {ex.Message}");
                        }
                    }

                    // Configure failure handling with CommonFailuresPreprocessor
                    var options = transaction.GetFailureHandlingOptions();
                    options.SetFailuresPreprocessor(new CommonFailuresPreprocessor());
                    transaction.Commit(options);

                    progressReporter?.ReportStatus($"Completed placing {placedCount} {typeName}");
                    return placedCount;
                }
            }
            catch (Exception ex)
            {
                result.AddError($"Failed to place {typeName}: {ex.Message}");
                return 0;
            }
        }

        private int PlaceElementsInTransaction<T>(List<T> elements, string typeName, Document document, Level level,
            int baseUnitHeight, Transform dwgTransform, Dictionary<string, FamilySymbol> familySymbolCache, BuildResult result)
            where T : BaseFaElement
        {
            if (!elements.Any()) return 0;

            try
            {
                using (var transaction = new Transaction(document, $"Place {typeName}"))
                {
                    transaction.Start();

                    int placedCount = 0;
                    foreach (var element in elements)
                    {
                        try
                        {
                            var instance = PlaceElement(element.Configuration, element.Center, element.RotationAngle, element.IsRotated,
                                document, level, baseUnitHeight, dwgTransform, familySymbolCache);
                            element.SetElementSpecificParameters(instance);
                            placedCount++;
                        }
                        catch (Exception ex)
                        {
                            result.AddWarning($"Failed to place {typeName} element {element.Id}: {ex.Message}");
                        }
                    }

                    // Configure failure handling with CommonFailuresPreprocessor
                    var options = transaction.GetFailureHandlingOptions();
                    options.SetFailuresPreprocessor(new CommonFailuresPreprocessor());
                    transaction.Commit(options);

                    return placedCount;
                }
            }
            catch (Exception ex)
            {
                result.AddError($"Failed to place {typeName}: {ex.Message}");
                return 0;
            }
        }

        public FamilyInstance PlaceElement(
            ElementTypeConfiguration configuration,
            Data.Point center,
            double rotationAngle,
            bool isRotated,
            Document document,
            Level level,
            int baseUnitHeight,
            Transform dwgTransform,
            Dictionary<string, FamilySymbol> familySymbolCache)
        {
            var familySymbol = FindOrLoadFamilySymbol(configuration, document, baseUnitHeight, familySymbolCache);
            var instance = CreateInstanceWithRotation(familySymbol, center, rotationAngle, isRotated, document, level);

            return instance;
        }



        private FamilySymbol FindOrLoadFamilySymbol(ElementTypeConfiguration configuration, Document document,
            int baseUnitHeight, Dictionary<string, FamilySymbol> familySymbolCache)
        {
            var familyName = configuration.FamilyName;
            var familyType = configuration.GetFamilyType(baseUnitHeight);
            var cacheKey = $"{familyName}_{familyType}";

            if (familySymbolCache.TryGetValue(cacheKey, out var cachedSymbol))
                return cachedSymbol;

            var symbol = new FilteredElementCollector(document)
                .OfClass(typeof(FamilySymbol))
                .Cast<FamilySymbol>()
                .FirstOrDefault(fs => fs.Family.Name.Equals(familyName, StringComparison.OrdinalIgnoreCase)
                                      && fs.Name.Equals(familyType, StringComparison.OrdinalIgnoreCase));

            if (symbol == null)
                throw new InvalidOperationException($"Family '{familyName}' with type '{familyType}' not found in document.");

            if (!symbol.IsActive)
                symbol.Activate();

            familySymbolCache[cacheKey] = symbol;
            return symbol;
        }

        private FamilyInstance CreateInstanceWithRotation(
            FamilySymbol familySymbol,
            Data.Point center,
            double rotationAngle,
            bool isRotated,
            Document document,
            Level level)
        {
            var location = new XYZ(center.X / 12.0, center.Y / 12.0, level.Elevation);

            FamilyInstance instance;

            if (isRotated)
            {
                var normalizedAngle = NormalizeAngle(rotationAngle);
                var radians = normalizedAngle * Math.PI / 180.0;
                var direction = new XYZ(Math.Cos(radians), Math.Sin(radians), 0);

                instance = document.Create.NewFamilyInstance(
                    location,
                    familySymbol,
                    direction,
                    level,
                    StructuralType.NonStructural);
            }
            else
            {
                instance = document.Create.NewFamilyInstance(
                    location,
                    familySymbol,
                    level,
                    StructuralType.NonStructural);
            }

            return instance;
        }

        private static double NormalizeAngle(double angle)
        {
            while (angle < 0) angle += 360;
            while (angle >= 360) angle -= 360;
            return angle;
        }
    }
}