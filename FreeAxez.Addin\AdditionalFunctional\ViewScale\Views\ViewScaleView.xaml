<Window x:Class="FreeAxez.Addin.AdditionalFunctional.ViewScale.Views.ViewScaleView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.ViewScale.Views"
        xmlns:vm="clr-namespace:FreeAxez.Addin.AdditionalFunctional.ViewScale.ViewModels"
        xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen"
        Title="View Scale"
        Height="800"
        MinHeight="300"
        Width="800"
        MinWidth="300">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Window.Style>
        <Style TargetType="{x:Type Window}"
               BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>

    <Window.DataContext>
        <vm:ViewScaleViewModel />
    </Window.DataContext>

    <Grid Margin="0,10,0,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Search panel -->
        <DockPanel Grid.Row="0"
                   Margin="0,0,0,10">
            <Button DockPanel.Dock="Right"
                    Content="X Clear"
                    Command="{Binding ClearFiltersCommand}"
                    Width="90"
                    Height="25"
                    Style="{StaticResource ButtonOutlinedRed}" />
            <TextBlock Text="Search:"
                       Style="{StaticResource TextH5}"
                       Width="60"
                       Margin="0,0,10,0" />
            <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                     Style="{StaticResource UiTextBox}"
                     Margin="0,0,10,0"
                     Height="25" />
        </DockPanel>

        <!-- Filter checkboxes -->
        <StackPanel Grid.Row="1"
                    Orientation="Horizontal"
                    Margin="0,0,0,10">
            <CheckBox Content="Views"
                      IsChecked="{Binding ViewFilter}"
                      Margin="0,0,15,0" />
            <CheckBox Content="View Templates"
                      IsChecked="{Binding ViewTemplateFilter}"
                      Margin="0,0,15,0" />
        </StackPanel>

        <!-- Header with counts -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    Margin="0,0,0,10">
            <TextBlock Text="Filtered: "
                       Style="{StaticResource TextH5}" />
            <TextBlock Text="{Binding FilteredCount}"
                       Style="{StaticResource TextH5}"
                       Margin="5,0,15,0" />
            <TextBlock Text="Selected: "
                       Style="{StaticResource TextH5}" />
            <TextBlock Text="{Binding SelectedCount}"
                       Style="{StaticResource TextH5}"
                       Margin="5,0,0,0" />
        </StackPanel>

        <!-- List of views -->
        <ListView Grid.Row="3"
                  ItemsSource="{Binding FilteredItems}"
                  SelectionMode="Extended"
                  HorizontalAlignment="Stretch"
                  HorizontalContentAlignment="Stretch"
                  Style="{StaticResource ListViewBase}">
            <i:Interaction.Triggers>
                <i:EventTrigger EventName="SelectionChanged">
                    <i:InvokeCommandAction Command="{Binding SelectionChangedCommand}"
                                           PassEventArgsToCommand="True" />
                </i:EventTrigger>
            </i:Interaction.Triggers>
            <ListView.ItemTemplate>
                <DataTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="75" />
                            <ColumnDefinition Width="120" />
                        </Grid.ColumnDefinitions>

                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Column="0"
                                    Grid.Row="0"
                                    Grid.RowSpan="2">
                            <TextBlock Text="{Binding Title}" />
                            <TextBlock Text="{Binding ViewTemplate}"
                                       FontStyle="Italic"
                                       Foreground="{StaticResource Gray300}"
                                       FontSize="13" />
                        </StackPanel>

                        <TextBlock Grid.Row="0"
                                   Grid.Column="1"
                                   Margin="0,0,0,5"
                                   Text="View Scale"
                                   VerticalAlignment="Center"
                                   FontSize="14" />
                        <ComboBox Grid.Row="0"
                                  Grid.Column="2"
                                  Margin="0,0,0,5"
                                  Style="{StaticResource Combobox}"
                                  Height="25"
                                  IsEnabled="{Binding IsScaleNotDependOnViewTemplate}"
                                  ItemsSource="{Binding ScaleStringList}"
                                  SelectedItem="{Binding ScaleString}" />

                        <TextBlock Grid.Row="1"
                                   Grid.Column="1"
                                   Text="Scale 1:"
                                   VerticalAlignment="Center"
                                   FontSize="14" />
                        <TextBox  Grid.Row="1"
                                  Grid.Column="2"
                                  Text="{Binding Scale, UpdateSourceTrigger=PropertyChanged}"
                                  Style="{StaticResource UiTextBoxWhite}"
                                  IsEnabled="{Binding IsScaleNotDependOnViewTemplate}"
                                  Height="25">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="TextChanged">
                                    <i:InvokeCommandAction Command="{Binding DataContext.ScaleUpdatedCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                           CommandParameter="{Binding DataContext, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type ListViewItem}}}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </TextBox>

                        <Border Grid.Row="3"
                                BorderBrush="{StaticResource Gray100}"
                                BorderThickness="0,1,0,0"
                                Grid.ColumnSpan="3"
                                Margin="0,4,0,0" />

                    </Grid>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>

        <!-- Button panel -->
        <UniformGrid HorizontalAlignment="Right"
                     Grid.Row="4"
                     Rows="1"
                     Columns="2"
                     Margin="0,10,0,0">
            <Button Content="Apply"
                    Width="150"
                    Command="{Binding ApplyCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"
                    Style="{StaticResource ButtonSimpleGreen}"
                    Margin="5,0"
                    Padding="10,5" />

            <Button Content="Cancel"
                    Command="{Binding CancelCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"
                    Style="{StaticResource ButtonSimpleRed}"
                    Margin="5,0,0,0"
                    Padding="10,5" />
        </UniformGrid>
    </Grid>
</Window>
