using System.Windows.Media.Imaging;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals
{
    public class DetailPreviewDialogVm : ModalDialogVm
    {
        private BitmapSource _previewImage;

        public DetailPreviewDialogVm(BitmapSource previewImage, string title)
        {
            _previewImage = previewImage;
            Title = title ?? "Detail View Preview";
        }

        public BitmapSource PreviewImage
        {
            get => _previewImage;
            set
            {
                if (_previewImage == value) return;
                _previewImage = value;
                OnPropertyChanged();
            }
        }
    }
}
