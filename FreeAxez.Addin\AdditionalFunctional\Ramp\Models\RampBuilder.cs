﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.Ramp.Enums;
using FreeAxez.Addin.Enums;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.Ramp.Models
{
    public class RampBuilder
    {
        private readonly List<CurveElement> _selectedLines;
        private readonly GriddType _griddType;
        private readonly RampSlope _rampSlope;
        private readonly bool _isLeftSideSlopeChecked;
        private readonly bool _isRightSideSlopeChecked;

        public RampBuilder(List<CurveElement> selectedLines,
                           GriddType griddType,
                           RampSlope rampSlope,
                           bool isLeftSideSlopeChecked,
                           bool isRightSideSlopeChecked)
        {
            _selectedLines = selectedLines;
            _griddType = griddType;
            _rampSlope = rampSlope;
            _isLeftSideSlopeChecked = isLeftSideSlopeChecked;
            _isRightSideSlopeChecked = isRightSideSlopeChecked;
        }

        public void Build()
        {
            var rampDirectionProvider = new RampDirectionProvider();
            var rampComponentProvider = new RampComponentProvider(_griddType, _rampSlope);
            var rampComponentLocationProvider = new RampComponentLocationProvider(rampComponentProvider);
            var rampComponentCreator = new RampComponentCreator(rampComponentProvider);

            List<List<CurveElement>> groupedCurveElements = RampLine.Group(_selectedLines);

            using (var transaction = new Transaction(RevitManager.Document, "Create Ramp"))
            {
                transaction.Start();

                foreach (var group in groupedCurveElements)
                {
                    List<Curve> selectedCurves = group.Select(l => l.GeometryCurve).ToList();
                    List<Curve> orderedCurves = RampLine.CorrectCurveOrder(selectedCurves);
                    List<Curve> correctedCurves = RampLine.CorrectCurveDirections(orderedCurves);
                    List<Curve> correctedCurvesWithoutZAxis = correctedCurves
                        .Select(c => RemoveZAxis(c))
                        .ToList();
                    XYZ rampComponentDirection = rampDirectionProvider.GetRampComponentsDirection(group.First());
                    List<RampLine> rampLines = rampComponentLocationProvider.Calculate(rampComponentDirection,
                                                                                        correctedCurvesWithoutZAxis,
                                                                                        _isLeftSideSlopeChecked,
                                                                                        _isRightSideSlopeChecked);

                    rampComponentCreator.Create(rampLines);
                }

                transaction.Commit();
            }
        }

        private Curve RemoveZAxis(Curve curve)
        {
            var p0 = curve.GetEndPoint(0);
            var p1 = curve.GetEndPoint(1);

            return Line.CreateBound(
                new XYZ(p0.X, p0.Y, 0), 
                new XYZ(p1.X, p1.Y, 0));
        }
    }
}