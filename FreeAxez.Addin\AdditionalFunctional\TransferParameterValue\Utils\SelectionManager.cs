﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.Utils
{
    public class SelectionManager
    {
        private Element _sourceElement;
        private List<Element> _targetElements;


        public Element SourceElement => _sourceElement;
        public List<Element> TargetElements => _targetElements;


        public bool TryPickSourceElement()
        {
            try
            {
                _sourceElement = RevitManager.Document.GetElement(
                    RevitManager.UIDocument.Selection.PickObject(Autodesk.Revit.UI.Selection.ObjectType.Element, "Select source element to copy parameter values.").ElementId);
                
                return true;
            }
            catch (Autodesk.Revit.Exceptions.OperationCanceledException)
            {
                return false;
            }
            catch
            {
                TaskDialog.Show("Warning", $"Can't select this element type.");
                return false;
            }
        }

        public bool TryPickTargetElements()
        {
            try
            {
                _targetElements = RevitManager.UIDocument.Selection.PickObjects(Autodesk.Revit.UI.Selection.ObjectType.Element, 
                    "Select target elements to copy parameter values.").Select(r => RevitManager.Document.GetElement(r)).ToList();

                if (_targetElements.Count > 0)
                {
                    return true;
                }

                TaskDialog.Show("Warning", $"No elements selected.");
                return false;
            }
            catch (Autodesk.Revit.Exceptions.OperationCanceledException)
            {
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
