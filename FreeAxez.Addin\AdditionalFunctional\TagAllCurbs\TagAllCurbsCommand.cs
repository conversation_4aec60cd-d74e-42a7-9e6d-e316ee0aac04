﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure.UI;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Views;
using FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Models;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllCurbs
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    internal class TagAllCurbsCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            if (RevitManager.UIDocument.ActiveView.ViewType == ViewType.ThreeD)
            {
                InfoDialog.ShowDialog("Error", "Unable to place tags in 3D view.\nPlease select another view.");
                return Result.Cancelled;
            }

            var curbAnnotationFamily = new CurbAnnatationFamily();
            if (curbAnnotationFamily.IsFamilyNotExist(out var errorMessage))
            {
                InfoDialog.ShowDialog("Error", errorMessage);
                return Result.Cancelled;
            }

            var curbFamily = new CurbFamily();
            if (curbFamily.IsFamilyNotExist(out errorMessage))
            {
                InfoDialog.ShowDialog("Error", errorMessage);
                return Result.Cancelled;
            }
            else if (curbFamily.IsNoPlacedInstancesOnView(out errorMessage))
            {
                InfoDialog.ShowDialog("Warning", errorMessage);
                return Result.Cancelled;
            }

            var tagAllCurbsView = new TagAllCurbsView();
            tagAllCurbsView.ShowDialog();

            return Result.Succeeded;
        }
    }
}
