﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Views.TransferProjectContentView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:viewModels="clr-namespace:FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.ViewModels"
        xmlns:converters1="clr-namespace:FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Converters"
        mc:Ignorable="d"
        Title="Transfer Content Between Projects"              
        Width="1400"
        SizeToContent="Height"
        WindowStartupLocation="CenterScreen">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
            <converters1:EnumToBooleanConverter x:Key="EnumToBooleanConverter"/>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Window.DataContext>
        <viewModels:TransferProjectContentViewModel/>
    </Window.DataContext>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height ="*"/>
            <RowDefinition Height ="Auto"/>
        </Grid.RowDefinitions>
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="10">
                <TextBlock Margin="0 0 0 3"
                           Style="{StaticResource TextH5}"
                           Text="Select Documents to Transfer Content"/>
                <Grid Margin="0,0,0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="110"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="200"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="Source Document" 
                               Style="{StaticResource TextBase}"/>
                    <ComboBox Grid.Column="1" ItemsSource="{Binding OpenedDocuments}" 
                              SelectedItem="{Binding SourceDocument}"
                              Style="{StaticResource Combobox}"/>
                    <TextBlock Grid.Column="2" Text="Target Document" 
                               Style="{StaticResource TextBase}"
                               Margin="50 0 0 0"/>
                    <ComboBox Grid.Column="3" ItemsSource="{Binding OpenedDocuments}" 
                              SelectedItem="{Binding TargetDocument}"
                              Style="{StaticResource Combobox}"/>
                </Grid>

                <StackPanel Margin="0,5,0,5" Orientation="Vertical">
                    <TextBlock Margin="0 0 0 5" 
                               Style="{StaticResource TextH5}"
                               Text="Choose how to handle Legends and Drafting Views:"/>
                    <RadioButton GroupName="ViewPlanOptions" 
                                 Margin="0 0 0 5"
                                 Content="Use legends and drafting views from the target project (recommended if up-to-date; some legends and drafting views may be missing)" 
                                 IsChecked="{Binding Path=SelectedViewHandlingOption, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=UseTarget, Mode=TwoWay}" />
                    <RadioButton GroupName="ViewPlanOptions" 
                                 Margin="0 0 0 5"
                                 Content="Use legends and drafting views from the source project (recommended for consistency with source)" 
                                 IsChecked="{Binding Path=SelectedViewHandlingOption, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=UseSource, Mode=TwoWay}" />
                    <RadioButton GroupName="ViewPlanOptions" 
                                 Content="Combine: Use legends and drafting views from the target project, and add missing ones from the source project" 
                                 IsChecked="{Binding Path=SelectedViewHandlingOption, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=Combine, Mode=TwoWay}" />
                </StackPanel>

                <StackPanel Margin="0,5,0,5" Orientation="Vertical">
                    <TextBlock Margin="0 0 0 5" 
                               Style="{StaticResource TextH5}"
                               Text="Choose how to handle Schedules:"/>
                    <RadioButton GroupName="ScheduleOptions" 
                                 Margin="0 0 0 5"
                                 Content="Use schedules from the target project (recommended if up-to-date; some schedules, not presented in target project, may be missing)" 
                                 IsChecked="{Binding Path=SelectedScheduleHandlingOption, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=UseTarget, Mode=TwoWay}" />
                    <RadioButton GroupName="ScheduleOptions" 
                                 Margin="0 0 0 5"
                                 Content="Use schedules from the source project (recommended for consistency with source)" 
                                 IsChecked="{Binding Path=SelectedScheduleHandlingOption, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=UseSource, Mode=TwoWay}" />
                    <RadioButton GroupName="ScheduleOptions" 
                                 Content="Combine: Use schedules from the target project, and add missing ones from the source project" 
                                 IsChecked="{Binding Path=SelectedScheduleHandlingOption, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=Combine, Mode=TwoWay}" />
                </StackPanel>

                <StackPanel Margin="0,5,0,5" Orientation="Vertical">
                    <TextBlock Margin="0 0 0 5" 
                               Style="{StaticResource TextH5}" 
                               Text="Select content to copy:"/>
                    <CheckBox Content="Copy Revisions" 
                              IsChecked="{Binding CopyRevisions}" 
                              Margin="0,5,0,0" />
                    <CheckBox Content="Copy Dimensions" 
                              IsChecked="{Binding CopyDimensions}" 
                              Margin="0,5,0,0" />
                    <CheckBox Content="Copy Tags and Text Notes" 
                              IsChecked="{Binding CopyTagsAndTextNotes}" 
                              Margin="0,5,0,0" />
                    <CheckBox Content="Copy Detail Lines and Regions"
                              IsChecked="{Binding CopyDetailLinesAndRegions}" 
                              Margin="0,5,0,0" />
                </StackPanel>

                <TextBlock Margin="0,5,0,3"
                           Style="{StaticResource TextH5}"
                           Text="Match view templates between source and target documents"/>
                <DataGrid ItemsSource="{Binding ViewTemplateMappings}" Height="350"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          Style="{StaticResource DataGridWithoutBorders}"
                          CanUserDeleteRows="False"
                          ColumnHeaderStyle="{DynamicResource DataGridColumnHeader}"
                          RowHeaderWidth="0"
                          VirtualizingStackPanel.IsVirtualizing="False">
                    <DataGrid.Columns>
                        <DataGridTemplateColumn Header="Source View Template" Width="400">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Source.Name}" 
                                               TextWrapping="Wrap"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Target View Template" Width="400">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ComboBox ItemsSource="{Binding AllTargets}"
                                              Style="{StaticResource Combobox}"
                                              SelectedItem="{Binding Target, Mode=TwoWay}"
                                              DisplayMemberPath="Name"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="" Width="50">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ContentControl Visibility="{Binding IsKeyMatching, Converter={StaticResource BoolToVisibilityConverter}}"
                                                    Template="{StaticResource WarningIcon}" 
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="" Width="50">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ContentControl Visibility="{Binding IsTargetDuplicated, Converter={StaticResource BoolToVisibilityConverter}}"
                                                    Template="{StaticResource NotifyIcon}" 
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0">
                    <StackPanel Orientation="Horizontal" Margin="5">
                        <ContentControl Template="{StaticResource WarningIcon}" 
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                        <TextBlock Text="Source and target view template names match less than 90%" 
                                   Margin="3 0 0 0"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="5">
                        <ContentControl Template="{StaticResource NotifyIcon}" 
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                        <TextBlock Text="One target view template corresponds to multiple source view templates" 
                                   Margin="3 0 0 0"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>

                <Grid Margin="0,0,0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Button Content="Transfer Content" 
                            Style="{StaticResource ButtonSimpleBlue}"
                            HorizontalAlignment="Right"
                            Command="{Binding CopyElementsCommand}" 
                            CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}" 
                            Grid.Column="1"
                            Margin="10,0,0,0"/>
                </Grid>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
