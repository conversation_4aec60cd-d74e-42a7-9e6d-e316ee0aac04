﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Constants;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Contracts.Requests;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.DataTransferObjects;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Abstractions
{
    public abstract class TaskManagerHttpClientBase
    {
        private readonly Uri _httpBaseAddress;

        protected TaskManagerHttpClientBase()
        {
            _httpBaseAddress = new Uri($@"{HttpClientConstants.HttpBaseAddress}");
        }

        protected Uri HttpBaseAddress => _httpBaseAddress;

        public abstract Task<RowDto> GetRecentRowDtoAsync(long sheetId, CancellationToken cancellationToken);
        public abstract Task<IEnumerable<long>> GetSubRowDtoIdsFromParent(long sheetId,
                                                                          long rowId,
                                                                          CancellationToken cancellationToken);
        public abstract Task<HttpResponseMessage> SendUpdateRequestEmail(long sheetId,
                                                                         UpdateEmailRequest emailRequest,
                                                                         CancellationToken cancellationToken);
        public abstract Task<HttpResponseMessage> SetCompleteStatusAsync(long sheetId,
                                                                         long recentStepId,
                                                                         IEnumerable<string>? filePaths,
                                                                         string? commentText, CancellationToken cancellationToken);
        public abstract Task<ScopeInstruction?> GetScopeInstructionByName(string scopName, CancellationToken cancellationToken);
        public abstract Task<ScopeInstruction?> GetScopeInstructionByNumber(double scopeNumber, CancellationToken cancellationToken);

        protected virtual async Task ThrowProblemDetailsExceptionAsync(HttpResponseMessage httpResponseMessage)
        {
            string exceptionMessage = await httpResponseMessage.Content.ReadAsStringAsync();

            throw new Exception(exceptionMessage);
        }
    }
}
