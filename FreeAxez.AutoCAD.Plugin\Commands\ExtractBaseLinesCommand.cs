using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.ApplicationServices.Core;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;
using FreeAxez.AutoCAD.Plugin.Models;
using Newtonsoft.Json;
using Formatting = Newtonsoft.Json.Formatting;

namespace FreeAxez.AutoCAD.Plugin.Commands
{
    public class ExtractBaseLinesCommand
    {
        [CommandMethod("EXTRACT_BASE_LINES")]
        public void ExtractBaseLines()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var db = doc.Database;
            var ed = doc.Editor;

            try
            {
                var outputPath = GetOutputPath();

                if (string.IsNullOrEmpty(outputPath))
                {
                    var errorMsg = "Error: No output path specified.";
                    ed.WriteMessage($"\n{errorMsg}");
                    return;
                }

                ed.WriteMessage($"\nExtracting lines on layers BASE, EZ_CORNER, CHANNEL, A-AREA-PATT, and OPT* (including polylines) to:\n  {outputPath}\n");

                using (var trans = db.TransactionManager.StartTransaction())
                {
                    var blockTable = (BlockTable)trans.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var modelSpace = (BlockTableRecord)trans.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    using var fileStream = new FileStream(outputPath, FileMode.Create, FileAccess.Write, FileShare.None, 64 * 1024);
                    using var streamWriter = new StreamWriter(fileStream, System.Text.Encoding.UTF8, 64 * 1024);
                    using var jsonWriter = new JsonTextWriter(streamWriter)
                    {
                        Formatting = Formatting.None
                    };

                    jsonWriter.WriteStartObject();
                    jsonWriter.WritePropertyName("layer");
                    jsonWriter.WriteValue("BASE");
                    jsonWriter.WritePropertyName("lines");
                    jsonWriter.WriteStartArray();

                    var allLines = new List<LineData>();

                    foreach (ObjectId objId in modelSpace)
                    {
                        var entity = trans.GetObject(objId, OpenMode.ForRead) as Entity;
                        if (entity == null) continue;

                        if (!IsValidLayer(entity.Layer))
                            continue;

                        if (entity is Line line)
                        {
                            var lineData = CreateLineDataFromLine(line);
                            allLines.Add(lineData);
                        }
                        else if (entity is Polyline polyline && (entity.Layer == "A-AREA-PATT" || entity.Layer.Contains("OPT")))
                        {
                            var polylineLines = CreateLineDataFromPolyline(polyline);
                            allLines.AddRange(polylineLines);
                        }
                    }

                    var uniqueLines = RemoveDuplicatesPerLayer(allLines);

                    int processedCount = 0;
                    foreach (var line in uniqueLines)
                    {
                        jsonWriter.WriteStartObject();

                        jsonWriter.WritePropertyName("layer");
                        jsonWriter.WriteValue(line.layer);

                        jsonWriter.WritePropertyName("startPoint");
                        jsonWriter.WriteStartObject();
                        jsonWriter.WritePropertyName("x");
                        jsonWriter.WriteValue(line.startPoint.x);
                        jsonWriter.WritePropertyName("y");
                        jsonWriter.WriteValue(line.startPoint.y);
                        jsonWriter.WriteEndObject();

                        jsonWriter.WritePropertyName("endPoint");
                        jsonWriter.WriteStartObject();
                        jsonWriter.WritePropertyName("x");
                        jsonWriter.WriteValue(line.endPoint.x);
                        jsonWriter.WritePropertyName("y");
                        jsonWriter.WriteValue(line.endPoint.y);
                        jsonWriter.WriteEndObject();

                        jsonWriter.WritePropertyName("length");
                        jsonWriter.WriteValue(line.length);

                        jsonWriter.WritePropertyName("angle");
                        jsonWriter.WriteValue(line.angle);

                        jsonWriter.WriteEndObject();

                        processedCount++;

                        if (processedCount % 10000 == 0)
                        {
                            streamWriter.Flush();
                            fileStream.Flush();
                        }
                    }

                    jsonWriter.WriteEndArray();
                    jsonWriter.WritePropertyName("count");
                    jsonWriter.WriteValue(processedCount);
                    jsonWriter.WriteEndObject();

                    streamWriter.Flush();
                    fileStream.Flush();

                    trans.Commit();
                }

                ed.WriteMessage($"\nDone. Total line segments: , output file at:\n  {outputPath}\n");
            }
            catch (System.Exception ex)
            {
                var errorMsg = $"Error: {ex.Message}";
                ed.WriteMessage($"\n{errorMsg}");
            }
        }

        private List<LineData> RemoveDuplicatesPerLayer(List<LineData> lines)
        {
            var result = new List<LineData>();
            var linesByLayer = lines.GroupBy(l => l.layer).ToList();

            foreach (var layerGroup in linesByLayer)
            {
                var uniqueInLayer = RemoveDuplicateLines(layerGroup.ToList());
                result.AddRange(uniqueInLayer);
            }

            return result;
        }

        private List<LineData> RemoveDuplicateLines(List<LineData> lines)
        {
            const double COORDINATE_TOLERANCE = 0.001;

            string NormalizeCoordinate(double coord)
            {
                double rounded = Math.Round(coord / COORDINATE_TOLERANCE) * COORDINATE_TOLERANCE;
                return rounded.ToString("F3");
            }

            string GetLineKey(LineData line)
            {
                var p1x = NormalizeCoordinate(line.startPoint.x);
                var p1y = NormalizeCoordinate(line.startPoint.y);
                var p2x = NormalizeCoordinate(line.endPoint.x);
                var p2y = NormalizeCoordinate(line.endPoint.y);

                var keyPoints = new List<string> { p1x, p1y, p2x, p2y };
                var reversedKeyPoints = new List<string> { p2x, p2y, p1x, p1y };

                return string.Compare(string.Join("_", keyPoints), string.Join("_", reversedKeyPoints)) <= 0
                    ? string.Join("_", keyPoints)
                    : string.Join("_", reversedKeyPoints);
            }

            var uniqueLinesDict = new Dictionary<string, LineData>();

            foreach (var line in lines)
            {
                var key = GetLineKey(line);
                if (!uniqueLinesDict.ContainsKey(key))
                {
                    uniqueLinesDict[key] = line;
                }
            }

            return uniqueLinesDict.Values.ToList();
        }

        private LineData CreateLineDataFromLine(Line line)
        {
            var deltaX = line.EndPoint.X - line.StartPoint.X;
            var deltaY = line.EndPoint.Y - line.StartPoint.Y;
            var angleRadians = Math.Atan2(deltaY, deltaX);
            var angleDegrees = angleRadians * (180.0 / Math.PI);
            if (angleDegrees < 0) angleDegrees += 360.0;

            return new LineData
            {
                layer = line.Layer,
                startPoint = new Point3D
                {
                    x = line.StartPoint.X,
                    y = line.StartPoint.Y
                },
                endPoint = new Point3D
                {
                    x = line.EndPoint.X,
                    y = line.EndPoint.Y
                },
                length = line.Length,
                angle = angleDegrees
            };
        }

        private List<LineData> CreateLineDataFromPolyline(Polyline polyline)
        {
            var lines = new List<LineData>();

            for (int i = 0; i < polyline.NumberOfVertices; i++)
            {
                var startPoint = polyline.GetPoint3dAt(i);
                int nextIndex = (i + 1) % polyline.NumberOfVertices;

                if (!polyline.Closed && nextIndex == 0)
                    break;

                var endPoint = polyline.GetPoint3dAt(nextIndex);

                var segmentLength = startPoint.DistanceTo(endPoint);
                if (segmentLength < 0.001)
                    continue;

                var deltaX = endPoint.X - startPoint.X;
                var deltaY = endPoint.Y - startPoint.Y;
                var angleRadians = Math.Atan2(deltaY, deltaX);
                var angleDegrees = angleRadians * (180.0 / Math.PI);
                if (angleDegrees < 0) angleDegrees += 360.0;

                var lineData = new LineData
                {
                    layer = polyline.Layer,
                    startPoint = new Point3D
                    {
                        x = startPoint.X,
                        y = startPoint.Y
                    },
                    endPoint = new Point3D
                    {
                        x = endPoint.X,
                        y = endPoint.Y
                    },
                    length = segmentLength,
                    angle = angleDegrees
                };

                lines.Add(lineData);
            }

            return lines;
        }

        private bool IsValidLayer(string layerName)
        {
            return layerName == "BASE" ||
                   layerName == "EZ_CORNER" ||
                   layerName == "CHANNEL" ||
                   layerName == "A-AREA-PATT" ||
                   layerName.Contains("OPT");
        }

        private string GetOutputPath()
        {
            var path = Environment.GetEnvironmentVariable("FREEAXEZ_OUTPUT_PATH");

            if (string.IsNullOrEmpty(path))
            {
                path = Path.Combine(Path.GetTempPath(), "base_lines.json");
            }
            return path;
        }
    }
}
