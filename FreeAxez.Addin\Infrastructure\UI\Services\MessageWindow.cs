﻿using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using FreeAxez.Addin.Infrastructure.UI.Views;
using System;
using System.Windows.Interop;

namespace FreeAxez.Addin.Infrastructure.UI.Services
{
    public static class MessageWindow
    {
        public static IntPtr MainWindowHandler;


        public static bool? ShowDialog(string title, string message, MessageType messageType)
        {
            var viewModel = new MessageViewModel(title, message, messageType);
            var window = new MessageView();
            window.DataContext = viewModel;

            if (MainWindowHandler != null)
            {
                var handler = new WindowInteropHelper(window);
                handler.Owner = MainWindowHandler;
            }

            return window.ShowDialog();
        }

        public static bool? ShowDialog(string message, MessageType messageType)
        {
            var viewModel = new MessageViewModel(message, messageType);
            var window = new MessageView();
            window.DataContext = viewModel;

            if (MainWindowHandler != null)
            {
                var handler = new WindowInteropHelper(window);
                handler.Owner = MainWindowHandler;
            }

            return window.ShowDialog();
        }

        public static void Notification(string title, string message, MessageType messageType)
        {
            var viewModel = new MessageViewModel(title, message, messageType);
            var window = new NotificationView();
            window.DataContext = viewModel;

            if (MainWindowHandler != null)
            {
                var handler = new WindowInteropHelper(window);
                handler.Owner = MainWindowHandler;
            }

            window.Show();
        }

        public static void Notification(string message, MessageType messageType)
        {
            var viewModel = new MessageViewModel(message, messageType);
            var window = new NotificationView();
            window.DataContext = viewModel;

            if (MainWindowHandler != null)
            {
                var handler = new WindowInteropHelper(window);
                handler.Owner = MainWindowHandler;
            }

            window.Show();
        }
    }
}
