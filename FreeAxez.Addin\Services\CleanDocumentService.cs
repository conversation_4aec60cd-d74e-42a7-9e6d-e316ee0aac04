﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace FreeAxez.Addin.Services
{
    internal class CleanDocumentService
    {
        /// <summary>
        /// Creates a copy of the document in a temporary folder and cleans it of unnecessary elements.
        /// </summary>
        /// <returns>The path to the copied document.</returns>
        public string CleanCopyOfDocument(Document sourceDocument)
        {
            try
            {
                var tempDocPath = CopyDocument(sourceDocument.PathName);
                var tempDoc = sourceDocument.Application.OpenDocumentFile(tempDocPath);
                DeleteReferences(tempDoc);
                tempDoc.Close();
                return tempDocPath;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Failed to clear temporary project due to {ex.Message}");
                return null;
            }
        }

        public void DeleteDocument(string path)
        {
            try
            {
                File.Delete(path);
            }
            catch (Exception ex)
            {
                LogHelper.Warning($"Unable to delete temporary project {path} due to {ex.Message}");
            }
        }

        private string CopyDocument(string path)
        {
            var tempDocPath = Path.GetTempPath() + Path.GetFileName(path);
            File.Copy(path, tempDocPath, true);
            return tempDocPath;
        }

        private void DeleteReferences(Document doc)
        {
            var multicategoryFilter = new ElementMulticategoryFilter(new List<BuiltInCategory>() {
                BuiltInCategory.OST_PointClouds,
                BuiltInCategory.OST_RvtLinks
             });

            var revLinks = new FilteredElementCollector(doc)
                .WherePasses(multicategoryFilter)
                .WhereElementIsElementType()
                .ToElementIds();

            var cadLinks = new FilteredElementCollector(doc)
                .OfClass(typeof(CADLinkType))
                .ToElementIds();

            using (var t = new Transaction(doc, "Delete References"))
            {
                t.Start();

                foreach (var link in revLinks.Union(cadLinks))
                {
                    try
                    {
                        doc.Delete(link);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"Unable to delete {link} via {ex.Message}");
                    }
                }

                t.Commit();
            }
        }
    }
}
