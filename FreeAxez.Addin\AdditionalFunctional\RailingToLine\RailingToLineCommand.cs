﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.RailingToLine.Views;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.RailingToLine
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    public class RailingToLineCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            if (!(RevitManager.UIDocument.ActiveView is ViewPlan))
            {
                TaskDialog.Show("Warning", "The plugin only works with plans.");
                return Result.Cancelled;
            }

            var railingToLineView = new RailingToLineView();
            railingToLineView.ShowDialog();

            return Result.Succeeded;
        }
    }
}
