﻿namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Constants;

public static class HttpClientConstants
{
    private const string Base = "api/SmartsheetTaskManager";

    public const string HttpBaseAddress = "https://api-freeaxez.bimsmith.com";
    //public const string HttpBaseAddress = "https://api-freeaxez-uat.bimsmith.com";
    //public const string HttpBaseAddress = "https://localhost:44376";
    public const string HttpMediaType = "application/json";

    public static class Scopes
    {
        private const string ScopesBase = $"{HttpBaseAddress}/api/scopeInstructions";

        public static string GetByScopeName(string scopeName)
        {
            return $"{ScopesBase}/by-scope-name/{scopeName}";
        }

        public static string GetByScopeNumber(double scopeNumber)
        {
            return $"{ScopesBase}/by-scope-number/{scopeNumber}";
        }
    }

    public static class Rows
    {
        public const string GetRecentRow = $"{Base}/GetRecentRow";
        public const string SetCompleteStatus = $"{Base}/SetCompleteStatus";
        public const string SendUpdateRequestEmail = $"{Base}/SendUpdateRequestEmail";
        public const string GetSubRowDtoIdsFromParent = $"{Base}/GetSubRowDtoIdsFromParent";
    }

    public static class FormArguments
    {
        public const string FormFiles = "formFiles";
        public const string FilePaths = "filePaths";
        public const string CommentText = "commentText";
    }
}
