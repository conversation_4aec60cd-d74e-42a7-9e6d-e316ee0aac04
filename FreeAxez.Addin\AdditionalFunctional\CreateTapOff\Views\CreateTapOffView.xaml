﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.CreateTapOff.Views.CreateTapOffView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:FreeAxez.Addin.AdditionalFunctional.CreateTapOff.ViewModels"
        MinHeight="450" MinWidth="500"
        Height="450" Width="500"
        WindowStartupLocation="CenterScreen"
        Title="Create Tap Off">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Window.DataContext>
        <vm:CreateTapOffViewModel/>
    </Window.DataContext>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <DataGrid Margin="0" 
                  Grid.Row="0" 
                  x:Name="dataGrid" 
                  Style="{StaticResource DataGridWithBorders}"
                  ItemsSource="{Binding Levels}" 
                  AutoGenerateColumns="False" 
                  CanUserAddRows="False" 
                  HeadersVisibility="Column" 
                  SelectionUnit="FullRow" 
                  SelectionMode="Extended">
            <DataGrid.Columns>
                <DataGridTemplateColumn Header="Create" SortMemberPath="IsCheck">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <CheckBox HorizontalAlignment="Center" 
                                      IsChecked="{Binding IsCheck, UpdateSourceTrigger=PropertyChanged}" 
                                      Checked="CheckBox_Checked" 
                                      Unchecked="CheckBox_Checked"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Binding="{Binding Name}" Header="Level" MinWidth="200" IsReadOnly="True"/>
            </DataGrid.Columns>
        </DataGrid>
        <StackPanel Margin="0,10,0,0" Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="Create" 
                    Width="100" 
                    Style="{StaticResource ButtonOutlinedBlue}"
                    Command="{Binding CreateCommand}" 
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
            <Button Margin="10,0,0,0" 
                    Content="Cancel" 
                    Width="100" 
                    Style="{StaticResource ButtonOutlinedRed}"
                    Command="{Binding CancelCommand}" 
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
        </StackPanel>
    </Grid>
</Window>
