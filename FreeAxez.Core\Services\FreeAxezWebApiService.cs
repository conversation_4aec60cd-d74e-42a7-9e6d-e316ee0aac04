﻿using FreeAxez.Core.Dto;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Serilog;
using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;

namespace FreeAxez.Core.Services
{
    public class FreeAxezWebApiService
    {
        public const string WebApiUrl = "https://api-freeaxez.bimsmith.com/api/";       // PROD
        //public const string WebApiUrl = "https://api-freeaxez-uat.bimsmith.com/api/"; // UAT
        //public const string WebApiUrl = "https://localhost:44376/api/";               // DEBUG

        public const string InstructionLink = "https://freeaxez.bimsmith.com/instruction/"; // PROD

        public FreeAxezWebApiService()
        {
            Log.Information($"{nameof(FreeAxezWebApiService)} using {WebApiUrl} api link");

            //Client.Timeout = TimeSpan.FromMinutes(10);
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
        }


        public bool CheckCurrentVersion(string version)
        {
            //TODO: Add version check when creating test server

            return true;

            bool actual = false;
            var result = FreeAxezHttpClient.Instance.GetAsync(WebApiUrl + $"Installer/GetAddInVersion?Version={version}").Result;
            if (result.IsSuccessStatusCode)
            {
                actual = Boolean.Parse(result.Content.ReadAsStringAsync().Result);
            }
            return actual;
        }

        public Guid? GetProjectId(string revitId, string fileName)
        {
            Log.Information("FreeAxezWebApiService.GetProjectId revitId: {@revitId}, fileName: {@fileName}",
                revitId, fileName);
            var result = FreeAxezHttpClient.Instance.GetAsync(WebApiUrl + "project/getId/" + revitId + "/" + fileName).Result;
            if (result.IsSuccessStatusCode)
            {
                Guid id;
                var resultString = result.Content.ReadAsStringAsync().Result;
                resultString = resultString.Replace("\"", "");
                if(Guid.TryParse(resultString, out id))
                {
                    return id;
                }
            }

            return null;
        }

        public bool IsProjectExists(string projectUid, string fileName)
        {
            bool exists = false;
            Log.Information("FreeAxezWebApiService.IsProjectExists projectUid: {@prUid}, fileName: {@fileName}",
                projectUid, fileName);
            var result = FreeAxezHttpClient.Instance.GetAsync(WebApiUrl + "project/exist/" + projectUid + "/" + fileName).Result;
            if (result.IsSuccessStatusCode)
            {
                exists = Boolean.Parse(result.Content.ReadAsStringAsync().Result);
            }

            Log.Information("FreeAxezWebApiService.IsProjectExists projectUid: {@prUid}, fileName: {@fileName}, exists: {@exists}",
                projectUid, fileName, exists);
            return exists;
        }

        public bool IsProjectExists(Guid projectId)
        {
            bool exists = false;
            Log.Information("FreeAxezWebApiService.IsProjectExists projectId: {@projectId}",
                projectId);
            var result = FreeAxezHttpClient.Instance.GetAsync(WebApiUrl + "project/exist/" + projectId).Result;
            if (result.IsSuccessStatusCode)
            {
                exists = Boolean.Parse(result.Content.ReadAsStringAsync().Result);
            }

            Log.Information("FreeAxezWebApiService.IsProjectExists projectId: {@projectId}, exists: {@exists}",
                projectId, exists);
            return exists;
        }

        public Guid? AddProject(ProjectDto project, string filePath)
        {
            Log.Information("FreeAxezWebApiService.AddProject project: {@prId}, filePath: {@filePath}",
                project.RevitUniqueId, filePath);
            var projectId = PostCall(project, "project/create");
            var added = AddFile(projectId.Value, filePath);
            if (added)
            {
                return projectId.Value;
            }
            else
            {
                return null;
            }
        }

        public void UpdateProjectRegion(ProjectDto project, string filePath)
        {
            Log.Information($"FreeAxezWebApiService.AddProject project: {project.RevitUniqueId}, filePath: {filePath}");
            var projectId = PostCall(project, "region/update");
            AddFile(projectId.Value, filePath);
        }

        public bool AddRegionsToProject(ProjectDto project, string filePath)
        {
            Log.Information("FreeAxezWebApiService.AddRegionsToProject project: {@prId}, filePath: {@filePath}",
                project.RevitUniqueId, filePath);
            var projectId = PostCall(project, "region/add");
           return AddFile(projectId.Value, filePath);
        }

        public OptionDto GetOption(Guid optionId)
        {
            Log.Information("FreeAxezWebApiService.GetOption projectId: {@optionId}", optionId);
            OptionDto option = null;
            var result = FreeAxezHttpClient.Instance.GetAsync(WebApiUrl + "option/getOption/" + optionId).Result;
            if (result.IsSuccessStatusCode)
            {
                var json = result.Content.ReadAsStringAsync().Result;
                option = JsonConvert.DeserializeObject<OptionDto>(json);
            }

            Log.Information("FreeAxezWebApiService.GetOption projectId: {@optionId}, result: {@result}",
                optionId, option?.OrderIndex);
            return option;
        }

        public string GetProjectName(Guid id)
        {
            Log.Information("FreeAxezWebApiService.GetProjectName id: {@id}", id);
            string projectName = string.Empty;
            var result = FreeAxezHttpClient.Instance.GetAsync(WebApiUrl + "project/name/" + id).Result;
            if (result.IsSuccessStatusCode)
            {
                try
                {
                    projectName = result.Content.ReadAsStringAsync().Result;
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "FreeAxezWebApiService.GetProjectName id: {@id}, prName: {@projectName}", id, projectName);
                }
            }

            Log.Information("FreeAxezWebApiService.GetProjectName id: {@id}, prName: {@projectName}", id, projectName);
            return projectName;
        }

        public bool ConstructionComplete(Guid optionId, string path)
        {
            Log.Information("FreeAxezWebApiService.ConstructionComplete optionId: {@opId}, path: {@path}",
                optionId, path);
            var result = FreeAxezHttpClient.Instance.GetAsync(WebApiUrl + "option/construction/" + optionId + "/" + path).Result;
            return result.IsSuccessStatusCode;
        }

        public bool ConstructionFailed(Guid optionId)
        {
            Log.Information("FreeAxezWebApiService.ConstructionFailed optionId: {@opId}", optionId);
            var result = FreeAxezHttpClient.Instance.GetAsync(WebApiUrl + "option/failed/" + optionId).Result;
            return result.IsSuccessStatusCode;
        }

        #region Private methods
        private bool AddFile(Guid projectId, string filePath)
        {
            var fileName = Path.GetFileName(filePath);
            var path = Path.GetDirectoryName(filePath);
            var resultFile = path + @"\" + projectId + "-" + fileName;
            File.Copy(filePath, resultFile, true);

            
            using (var form = new MultipartFormDataContent())
            {
                var fileContent = new ByteArrayContent(File.ReadAllBytes(resultFile));
                fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse("multipart/form-data");
                form.Add(fileContent, "file", fileName);
                var result = FreeAxezHttpClient.Instance.PostAsync(WebApiUrl + "project/load/" + projectId, form).GetAwaiter().GetResult();
                File.Delete(resultFile);
                if (result.IsSuccessStatusCode)
                {
                    return true;
                }
                else
                {
                    if(result.StatusCode == HttpStatusCode.ServiceUnavailable)
                    {
                        Log.Warning($"Something went wrong adding file {result.StatusCode}, {result.Content.ReadAsStringAsync().Result}");
                    }
                    else
                    {
                        Log.Error($"Something went completely wrong adding file {result.StatusCode}, {result.Content.ReadAsStringAsync().Result}");
                    }
                    return false;
                }
            }
        }

        private Guid? PostCall<T>(T project, string url)
        {
            DefaultContractResolver contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var settings = new JsonSerializerSettings()
            {
                ContractResolver = contractResolver,
                Formatting = Formatting.None
            };

            var result = FreeAxezHttpClient.Instance.PostAsync(WebApiUrl + url,
                    new StringContent(JsonConvert.SerializeObject(project, settings), Encoding.UTF8, "application/json"))
                .Result;

            if (result.IsSuccessStatusCode)
            {
                Guid projId;
                var resultString = result.Content.ReadAsStringAsync().Result;
                resultString = resultString.Replace("\"", "");
                Guid.TryParse(resultString, out projId);
                return projId;
            }
            return null;
        }
        #endregion
    }
}