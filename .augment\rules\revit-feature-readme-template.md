---
type: "agent_requested"
description: "Template for README files to describe the functionality of individual commands"
---
# [Feature Name] - README

## Description
Brief description of what this feature does (1-2 sentences).

## Limitations and Considerations

### Revit Specifics
- Works only in Model Editor (not Family Editor)
- Requires active 3D view for calculations
- Uses Units API for unit conversions

### Constraints
- Does not work with linked models
- Does not support undo for certain operations
- Requires document modification rights

## UI

### Settings
- Parameter 1: description and default value
- Parameter 2: description and default value

### Interface Logic
```csharp
var config = new FeatureConfig
{
    Parameter1 = "default_value",
    Parameter2 = 100
};
```

## Input Data Validation

### Pre-execution Checks
1. **"No active document"** → Ensure document is open
2. **"Transaction conflict"** → Check for active transactions
3. **"Element access denied"** → Close Revit dialogs

### Data Model Validation
- Model requirements and constraints
- Expected data formats and ranges

## Report
- Description of output format and content
- Report generation logic and export options

## Architecture

### Main Components
```
Commands/
├── MainCommand.cs          # Main command entry point
├── SubCommand1.cs          # Helper command 1
└── SubCommand2.cs          # Helper command 2

Models/
├── FeatureModel.cs         # Core data model
└── ConfigModel.cs          # Configuration model

Services/
├── CalculationService.cs   # Business logic
└── RevitDataService.cs     # Revit API interactions

Views/
├── MainWindow.xaml         # Main window
└── SettingsDialog.xaml     # Settings dialog

Utils/
└── FeatureHelpers.cs       # Helper utilities
```

## Key Classes

### MainCommand.cs
- **Purpose**: Feature entry point
- **Inherits**: `IExternalCommand`
- **Key Methods**: `Execute()`
- **Notes**: Validates active document before execution

### CalculationService.cs
- **Purpose**: Core business logic
- **Dependencies**: `RevitDataService`
- **Key Methods**: `Calculate()`, `Validate()`
- **Notes**: Handles main feature calculations

### RevitDataService.cs  
- **Purpose**: Revit API abstraction layer
- **Key Methods**: `GetElements()`, `CreateTransaction()`
- **Notes**: Manages Revit data access and transactions


## Other
- Additional sections can be added here as needed
