﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.Ramp.Enums;
using FreeAxez.Addin.Enums;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.Ramp.Utils
{
    public class RampFamilyProvider
    {
        private List<FamilySymbol> _familySymbols;

        public RampFamilyProvider(GriddType griddType, RampSlope rampSlope)
        {
            _familySymbols = GetSymbols(griddType, rampSlope);
        }

        public FamilySymbol GetSymbol(RampFamilyOptions options)
        {
            var symbol = _familySymbols
                .Where(s => FilterRampSide(s, options.RampSide))
                .Where(s => FilterRampAngleType(s, options.RampAngleType))
                .Where(s => FilterRampAngle(s, options.RampAngle))
                .FirstOrDefault();

            if (symbol != null && symbol.IsActive == false) symbol.Activate();

            return symbol;
        }

        private List<FamilySymbol> GetSymbols(GriddType griddType, RampSlope rampSlope)
        {
            var griddTypeString = griddType == GriddType.Gridd40 ? "40" : "70";
            var rampSlopeString = rampSlope.ToString().Replace("Slope", "");

            return new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsElementType()
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .Cast<FamilySymbol>()
                .Where(s => s.FamilyName.Contains("Ramp"))
                .Where(s => s.Name.Contains(griddTypeString) && s.FamilyName.EndsWith(rampSlopeString))
                .ToList();
        }

        private bool FilterRampSide(FamilySymbol familySymbol, RampSide rampSide)
        {
            switch (rampSide)
            {
                case RampSide.Left:
                    return familySymbol.FamilyName.Contains(RampSide.Left.ToString());
                case RampSide.Right:
                    return familySymbol.FamilyName.Contains(RampSide.Right.ToString());
                case RampSide.None:
                    return familySymbol.FamilyName.Contains(RampSide.Left.ToString()) == false
                        && familySymbol.FamilyName.Contains(RampSide.Right.ToString()) == false;
                default:
                    throw new NotImplementedException();
            }
        }

        private bool FilterRampAngleType(FamilySymbol familySymbol, RampAngleType rampAngleType)
        {
            switch (rampAngleType)
            {
                case RampAngleType.Internal:
                    return familySymbol.FamilyName.Contains(RampAngleType.Internal.ToString());
                case RampAngleType.External:
                    return familySymbol.FamilyName.Contains(RampAngleType.External.ToString());
                case RampAngleType.None:
                    return familySymbol.FamilyName.Contains(RampAngleType.Internal.ToString()) == false
                        && familySymbol.FamilyName.Contains(RampAngleType.External.ToString()) == false;
                default:
                    throw new NotImplementedException();
            }
        }
        
        private bool FilterRampAngle(FamilySymbol familySymbol, RampAngle rampAngle)
        {
            // Example: Access_Flooring-FreeAxez-Gridd-Ramp_External_Angle_45_1_20
            switch (rampAngle)
            {
                case RampAngle.Straight:
                    return familySymbol.FamilyName.Contains("45") == false;
                case RampAngle.Diagonal:
                    return familySymbol.FamilyName.Contains("45");
                case RampAngle.None:
                    return familySymbol.FamilyName.Contains("45") == false;
                default:
                    throw new NotImplementedException();
            }
        }
    }
}
