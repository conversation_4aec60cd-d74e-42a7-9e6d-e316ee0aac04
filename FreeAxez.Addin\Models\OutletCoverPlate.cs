﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class OutletCoverPlate : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
                "Plate-Outlet_Cover_Plate_Nested",
                "Nested_Outlet_Cover_Plate"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public OutletCoverPlate(Element element) : base(element)
        {
        }

        public static List<OutletCoverPlate> Collect()
        {
            return FamilyCollector.Instances.Select(f => new OutletCoverPlate(f)).ToList();
        }
    }
}
