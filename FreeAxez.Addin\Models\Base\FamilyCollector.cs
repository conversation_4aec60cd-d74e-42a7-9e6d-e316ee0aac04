﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.Models.Base
{
    public class FamilyCollector
    {
        private readonly BuiltInCategory _category;
        private readonly List<string> _familyNamesContains;
        private readonly List<string> _familyNamesEndWith;
        private readonly List<string> _familyNamesNotContains;

        private List<Family> _families;
        private List<FamilySymbol> _familySymbols;
        private List<FamilyInstance> _familiesInstances;

        public FamilyCollector(FamilyDefinition familyDefinition)
        {
            _category = familyDefinition.BuiltInCategory;
            _familyNamesContains = familyDefinition.FamilyNamesContains?.Select(n => n.Replace("-", "_")).ToList() ?? new List<string>();
            _familyNamesEndWith = familyDefinition.FamilyNamesEndWith?.Select(n => n.Replace("-", "_")).ToList() ?? new List<string>();
            _familyNamesNotContains = familyDefinition.FamilyNamesNotContains?.Select(n => n.Replace("-", "_")).ToList() ?? new List<string>();
        }

        public List<Family> Families
        {
            get
            {
                if (_families == null)
                {
                    var uniqueFamilyNames = Symbols.Select(s => s.FamilyName).Distinct().ToList();

                    _families = new List<Family>();
                    foreach (var familyName in uniqueFamilyNames)
                    {
                        var family = Symbols.Where(s => s.FamilyName == familyName).Select(s => s.Family).First();
                        _families.Add(family);
                    }
                }

                return _families;
            }
        }

        public List<FamilySymbol> Symbols
        {
            get
            {
                if (_familySymbols == null)
                {
                    _familySymbols = new FilteredElementCollector(RevitManager.Document)
                       .OfCategory(_category)
                       .WhereElementIsElementType()
                       .Cast<FamilySymbol>()
                       .Where(s => CorrespondDefinition(s))
                       .ToList();
                }

                return _familySymbols;
            }
        }

        public List<FamilyInstance> Instances
        {
            get
            {
                if (_familiesInstances == null)
                {
                    _familiesInstances = Symbols
                       .SelectMany(s => s
                          .GetDependentElements(new ElementClassFilter(typeof(FamilyInstance)))
                          .Select(RevitManager.Document.GetElement)
                          .Cast<FamilyInstance>()
                          .Where(i => s.Id.Equals(i.Symbol.Id))) // Exclude dependent nested families
                      .ToList();
                }

                return _familiesInstances;
            }
        }

        private bool CorrespondDefinition(FamilySymbol familySymbol)
        {
            if (_familyNamesNotContains.Any(n => familySymbol.FamilyName.Contains(n))) return false;

            var cleanedFamilyName = FamilyNameCleaner.GetCleanFamilyName(familySymbol.FamilyName);

            var contains = _familyNamesContains.Count == 0 ? true :
                 _familyNamesContains.Any(n => cleanedFamilyName.Contains(n));

            var endWith = _familyNamesEndWith.Count == 0 ? true :
                 _familyNamesEndWith.Any(n => cleanedFamilyName.EndsWith(n));

            return contains && endWith;
        }
    }
}
