﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Services;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagRepeatingSupercomponent
{
    [Transaction(TransactionMode.Manual)]
    public class TagRepeatingSupercomponentCommand : BaseExternalCommand
    {
        private const string BorderNameKey = "Border";

        public override Result Execute()
        {
            List<string> errors = new List<string>();

            if (RevitManager.UIDocument == null)
            {
                TaskDialog.Show("Error", "There must be an open project to run this command");
                return Result.Cancelled;
            }

            List<FamilyInstance> elements = new FilteredElementCollector(RevitManager.Document, RevitManager.Document.ActiveView.Id)
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .Cast<FamilyInstance>()
                .Where(q => q.GetSubComponentIds().Any())
                .ToList();

            if (RevitManager.UIDocument.Selection.GetElementIds().Any())
            {
                elements = RevitManager.UIDocument.Selection.GetElementIds()
                    .Select(q => RevitManager.Document.GetElement(q))
                    .Where(q => q is FamilyInstance)
                    .Cast<FamilyInstance>()
                    .ToList();
            }

            List<Family> subComponentFamilies = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Family))
                .Cast<Family>()
                .Where(q => q.FamilyCategory.Id.GetIntegerValue() == (int)BuiltInCategory.OST_SpecialityEquipment)
                .Where(q => IsBorderFamily(q.Name))
                .ToList();

            if (subComponentFamilies == null || !subComponentFamilies.Any())
            {
                MessageWindow.ShowDialog("There are no border families in the project.", Infrastructure.UI.Enums.MessageType.Notify);
                return Result.Cancelled;
            }

            FamilySymbol fs = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(FamilySymbol))
                .Cast<FamilySymbol>()
                .FirstOrDefault(q => q.Family.Name == "Border Gap Tag");

            double offset = 2.25;
            using (TransactionGroup tg = new TransactionGroup(RevitManager.Document, "Tag"))
            {
                tg.Start();
                List<Element> tags = new List<Element>();
                List<Line> lines = new List<Line>();
                using (FormProgress progressForm = new FormProgress("Tagging", "{0} of " + elements.Count() + " elements tagged...", elements.Count()))
                {
                    foreach (FamilyInstance e in elements)
                    {
                        if (progressForm.getAbortFlag())
                            break;

                        if (e.SuperComponent != null)
                            continue;

                        Parameter lengthParameter = e.LookupParameter("Length");
                        if (lengthParameter == null)
                        {
                            continue;
                        }
                        double length = lengthParameter.AsDouble();
                        if (length < 1.5)
                        {
                            continue;
                        }

                        Parameter paramActualGapDistance = e.LookupParameter("Actual Gap Distance");
                        if (paramActualGapDistance == null)
                        {
                            continue;
                        }
                        double actualGapDistance = paramActualGapDistance.AsDouble();
                        if (actualGapDistance < 1.75 / 12.0)
                        {
                            continue;
                        }

                        LocationPoint lp = e.Location as LocationPoint;
                        double rotation = lp.Rotation;
                        XYZ endpoint = lp.Point;
                        XYZ midpoint = Transform.CreateRotationAtPoint(XYZ.BasisZ, rotation, endpoint)
                            .OfPoint(endpoint.Add(XYZ.BasisX.Multiply(length / 2.0)));

                        //If you are able to see Reference Planes that are named we think we have a solution.
                        //Reference Plane: L - Type : is the upper most reference plane at all times.
                        //Reference Plane: End Cover : is the lower most reference plane at all time.
                        //Parameter: Actual Gap Distance: is the width of the gap where we want the tag placed

                        //place the tag
                        //½ the parameter: Actual Gap Distance
                        //from
                        //Reference plane: L - Type
                        //Towards
                        //Reference Plane: End Cover
                        Reference lTypeRef = e.GetReferenceByName("L-Type");
                        if (lTypeRef == null)
                            continue;

                        using (Transaction t = new Transaction(RevitManager.Document, "tag"))
                        {
                            t.Start();
                            Plane planeLType = SketchPlane.Create(RevitManager.Document, lTypeRef).GetPlane();
                            XYZ midpointOnPlane = CommonUtils.ProjectOnto(planeLType, midpoint);
                            XYZ endpointOnPlane = CommonUtils.ProjectOnto(planeLType, endpoint);
                            XYZ dir = Line.CreateBound(midpointOnPlane, endpointOnPlane).Direction.Normalize();
                            XYZ ptEndCover = CommonUtils.ProjectOnto(SketchPlane.Create(RevitManager.Document, e.GetReferenceByName("End Cover")).GetPlane(), midpointOnPlane);
                            Line lineConnectingPlanes = Line.CreateBound(midpointOnPlane, ptEndCover);
                            double gap = actualGapDistance / 2.0;
                            XYZ tagPt = lineConnectingPlanes.Evaluate(gap / lineConnectingPlanes.Length, true);

                            TagOrientation orient = TagOrientation.Horizontal;
                            //if (dir.IsAlmostEqualTo(XYZ.BasisX) || dir.IsAlmostEqualTo(XYZ.BasisX.Negate()))
                            //{
                            //	orient = TagOrientation.Vertical;
                            //}

                            IndependentTag tag;
                            try
                            {
#if revit2018
                                tag = RevitManager.Document.Create.NewTag(RevitManager.Document.ActiveView, e, true, TagMode.TM_ADDBY_CATEGORY, orient, tagPt);
#else
                                tag = IndependentTag.Create(RevitManager.Document, RevitManager.Document.ActiveView.Id, new Reference(e), true, TagMode.TM_ADDBY_CATEGORY, orient, tagPt);
#endif
                            }
                            catch (Exception ex)
                            {
                                TaskDialog td = new TaskDialog("Error")
                                {
                                    MainInstruction = "Tag creation failed. Check that there is a Speciality Equipment tag loaded before running this command."
                                };
                                td.Show();
                                return Result.Cancelled;
                            }

                            if (fs != null)
                            {
                                if (!fs.IsActive)
                                {
                                    fs.Activate();
                                }
                                tag.ChangeTypeId(fs.Id);
                            }

                            tag.LeaderEndCondition = LeaderEndCondition.Free;
                            XYZ elbow = tagPt.Add(dir.Multiply(.3333));

#if revit2018 || revit2019 || revit2020 || revit2021
                            tag.LeaderEnd = tagPt;
                            tag.LeaderElbow = elbow;
#else
                            tag.SetLeaderEnd(lTypeRef, tagPt);
                            tag.SetLeaderElbow(lTypeRef, elbow);
#endif

                            XYZ headPosition = elbow.Add(lineConnectingPlanes.Direction.Normalize().Multiply(offset));
                            bool notInGrid = !IsInGrid(RevitManager.Document, RevitManager.Document.ActiveView, headPosition);
                            bool overlappingBorder = IsOverlappingBorder(RevitManager.Document, headPosition);
                            bool onWrongSide = OnWrongSide(tag);
                            bool overlapsExist = TagAll.TagUtils.DoOverlapsExist(tag, null);
                            if (notInGrid | overlappingBorder | onWrongSide | overlapsExist)
                            {
                                // flip the tag
                                headPosition = elbow.Subtract(lineConnectingPlanes.Direction.Normalize().Multiply(offset));
                                RevitManager.Document.Regenerate();
                            }

                            if (IsOverlappingBorder(RevitManager.Document, headPosition))
                            {
                                RevitManager.Document.Delete(tag.Id);
                            }
                            else
                            {
                                tag.TagHeadPosition = headPosition;

                                tags.Add(tag);

                                if (midpoint.DistanceTo(tag.TagHeadPosition) > 0.001)
                                {
                                    Line fullLine = Line.CreateBound(midpoint, tag.TagHeadPosition);
                                    lines.Add(Line.CreateBound(fullLine.Evaluate(0.1, true), fullLine.Evaluate(0.9, true)));
                                }

                                // make the leader perpendicular by accounting for the difference between TagHeadPosition vs Start of Leader
                                RevitManager.Document.Regenerate();
                                BoundingBoxXYZ box = tag.get_BoundingBox(RevitManager.Document.GetElement(tag.OwnerViewId) as View);
                                if (box == null)
                                {

                                }
                                else
                                {
                                    XYZ boxMid = Line.CreateBound(box.Min, box.Max).Evaluate(0.5, true);
                                    XYZ vec = boxMid.Subtract(tag.TagHeadPosition);
                                    tag.TagHeadPosition = tag.TagHeadPosition.Subtract(XYZ.BasisX.Multiply(vec.X));
                                }

                                if (TagAll.TagUtils.DoOverlapsExist(tag, lines))
                                {
                                    RevitManager.Document.Delete(tag.Id);
                                }

                            }
                            t.Commit();
                        }
                        progressForm.Increment();
                    }
                }
                tg.Assimilate();
            }

            if (errors.Any())
            {
                TaskDialog.Show("Errors", string.Join(Environment.NewLine, errors));
            }
            return Result.Succeeded;
        }

        private bool OnWrongSide(IndependentTag tag)
        {
            Document doc = tag.Document;
            doc.Regenerate();
            View view = doc.GetElement(tag.OwnerViewId) as View;
            BoundingBoxXYZ box = tag.get_BoundingBox(view);
            if (box == null)
            {
                return false;
            }
            Line line = Line.CreateBound(box.Min, box.Max);
            bool foundGap = false;
            bool hitSomething = false;
            int numberOfPoints = 20;
            for (int i = 0; i <= numberOfPoints; i++)
            {
                double d = (double)i / (double)numberOfPoints;
                XYZ pt = line.Evaluate(d, true);

                //Solid sphere = Anguleris.Utils.makeSphere(pt, 0.05); 
                Solid cylinder = CommonUtils.makeCylinder(pt, 0.05);

                List<FamilyInstance> instances = new FilteredElementCollector(doc, view.Id)
                    .OfClass(typeof(FamilyInstance))
                    .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                    .WherePasses(new BoundingBoxContainsPointFilter(pt, 0.5))
                    .WherePasses(new ElementIntersectsSolidFilter(cylinder))
                    .Cast<FamilyInstance>()
                    .ToList();

                //Anguleris.Utils.makePoint(doc, pt, 0.05, d.ToString());

                if (!hitSomething && instances.Any())
                {
                    //Anguleris.Utils.makePoint(doc, pt, 0.1, d.ToString() + " hitSomething = true");
                    hitSomething = true;
                    continue;
                }
                if (hitSomething && !foundGap && !instances.Any())
                {
                    foundGap = true;
                    //Anguleris.Utils.makePoint(doc, pt, 0.1, d.ToString() + " foundGap = true");
                    continue;
                }
                if (foundGap && instances.Any())
                {
                    //Anguleris.Utils.makePoint(doc, pt, 0.1, d.ToString() + " returning true");
                    return true;
                }
            }
            return false;
        }





        private bool IsInGrid(Document doc, View view, XYZ headPosition)
        {
            Solid cylinder = CommonUtils.makeCylinder(headPosition, 0.05);

            List<FamilyInstance> instances = new FilteredElementCollector(doc, view.Id)
                .OfClass(typeof(FamilyInstance))
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .WherePasses(new BoundingBoxContainsPointFilter(headPosition, 1.0))
                .WherePasses(new ElementIntersectsSolidFilter(cylinder))
                .Cast<FamilyInstance>()
                .ToList();

            //Anguleris.Utils.makePoint(doc, headPosition, 0.1, "Is In Grid "  + instances.Count.ToString());

            //MakeDs(doc, cylinder, "is in grid cylinder " + instances.Count.ToString());

            if (instances.Any())
                return true;
            else
                return false;
        }

        private static DirectShape MakeDs(Document doc, Solid s, string comment)
        {
            if (s == null)
                return null;

            DirectShape ds = DirectShape.CreateElement(doc, new ElementId((int)BuiltInCategory.OST_GenericModel));
            try
            {
                ds.SetShape(new List<GeometryObject> { s });
                ds.get_Parameter(BuiltInParameter.ALL_MODEL_INSTANCE_COMMENTS).Set(comment);
            }
            catch
            {

            }

            return ds;
        }

        private bool IsOverlappingBorder(Document doc, XYZ headPosition)
        {
            Solid cyl = CommonUtils.makeCylinder(headPosition, 0.05);

            List<FamilyInstance> instances = new FilteredElementCollector(doc, doc.ActiveView.Id)
                .OfClass(typeof(FamilyInstance))
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .WherePasses(new BoundingBoxContainsPointFilter(headPosition, 1.0))
                .WherePasses(new ElementIntersectsSolidFilter(cyl))
                .Cast<FamilyInstance>()
                .Where(q => IsBorderFamily(q.Symbol.FamilyName))
                .ToList();

            if (instances.Any())
                return true;
            else
                return false;
        }

        private bool IsBorderFamily(string familyName)
        {
            return familyName.Contains(BorderNameKey);
        }
    }
}
