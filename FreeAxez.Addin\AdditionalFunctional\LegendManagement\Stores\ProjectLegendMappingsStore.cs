﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.LegendManagement.Models;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.LegendManagement.Stores
{
    public class ProjectLegendMappingsStore
    {
        private readonly BrowserOrganization _browserOrganization;
        private readonly Dictionary<CompositeSheetKey, List<Legend>> _dictionary;

        public ProjectLegendMappingsStore(BrowserOrganization browserOrganization)
        {
            _browserOrganization = browserOrganization;
            _dictionary = Initialize();
        }

        public Dictionary<CompositeSheetKey, List<Legend>> Dictionary => _dictionary;

        private Dictionary<CompositeSheetKey, List<Legend>> Initialize()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(ViewSheet))
                .OfType<ViewSheet>()
                .ToDictionary(
                    viewSheetKey =>
                    {
                        IList<FolderItemInfo> folderItemInfos = _browserOrganization.GetFolderItems(viewSheetKey.Id);
                        var sheetSizeFolderItemInfo = FilterByParameterName(folderItemInfos, "Sheet Size");
                        var sheetSortingFolderItemInfo = FilterByParameterName(folderItemInfos, "Sheet Sorting");

                        var sheetSize = new SheetSize(
                            sheetSizeFolderItemInfo?.ElementId?.GetIntegerValue() ?? ElementId.InvalidElementId.GetIntegerValue(),
                            sheetSizeFolderItemInfo?.Name ?? string.Empty);

                        var sheetSorting = new SheetSorting(
                            sheetSortingFolderItemInfo?.ElementId?.GetIntegerValue() ?? ElementId.InvalidElementId.GetIntegerValue(),
                            sheetSortingFolderItemInfo?.Name ?? string.Empty);

                        var sheet = new Sheet(viewSheetKey.Id.GetIntegerValue(), viewSheetKey.SheetNumber);

                        return new CompositeSheetKey(sheetSize, sheetSorting, sheet);
                    },
                    viewSheetValue =>
                    {
                        List<Viewport> viewports = viewSheetValue
                                .GetAllViewports()
                                .Select(viewportId => RevitManager.Document.GetElement(viewportId))
                                .OfType<Viewport>()
                                .ToList();

                        return viewports
                            .Where(viewport =>
                            {
                                var view = (View)RevitManager.Document.GetElement(viewport.ViewId);
                                return view.ViewType == ViewType.Legend || view.ViewType == ViewType.DraftingView;
                            })
                            .Select(viewport =>
                            {
                                var view = (View)RevitManager.Document.GetElement(viewport.ViewId);

                                return new Legend(view.Id.GetIntegerValue(), view.Name);
                            })
                            .ToList();
                    }
                );
        }

        private FolderItemInfo? FilterByParameterName(IList<FolderItemInfo> folderItemInfos, string parameterName)
        {
            return folderItemInfos.FirstOrDefault(folderItemInfo =>
            {
                Element element = RevitManager.Document.GetElement(folderItemInfo.ElementId);

                if (element is ParameterElement parameterElement)
                {
                    return parameterElement
                        .GetDefinition()
                        .Name
                        .Equals(parameterName, StringComparison.OrdinalIgnoreCase);
                }

                return false;
            });
        }
    }
}
