﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using Autodesk.Revit.UI;
using Autodesk.Revit.DB.Structure;
using FreeAxez.Addin.AdditionalFunctional.Frame.Views;
using FreeAxez.Addin.AdditionalFunctional.Frame.Models;
using FreeAxez.Addin.AdditionalFunctional.Frame.Utils;
using FreeAxez.Addin.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.Frame
{
    [Transaction(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    public partial class FrameCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var window = new FrameView();
            if (window.ShowDialog() != true)
            {
                return Result.Cancelled;
            }

            var griddType = Properties.Settings.Default.FrameSelectedGridd40 ? GriddType.Gridd40 : GriddType.Gridd70;
            var longCorner = Properties.Settings.Default.FrameSelectedCornerLong;
            var familyProvider = new FrameFamilyProvider(griddType, longCorner);

            if (familyProvider.ValidateFamilies(out string message) == false)
            {
                MessageWindow.ShowDialog(message, MessageType.Notify);
                return Result.Cancelled;
            }

            // Select lines by UI
            var selectedLines = SelectLines();

            //Checking selection of lines
            if (selectedLines.Count == 0)
            {
                MessageWindow.ShowDialog("No lines were selected.", MessageType.Notify);
                return Result.Cancelled;
            }

            //Get points from cureve
            var points = GetCurvePoints(selectedLines);

            //Checking the number of contours and duplicate elements
            var checkIsOpenLoop = IsOpenLoop(points);
            if (checkIsOpenLoop == false)
            {
                MessageWindow.ShowDialog("Lines must be in one open loop.", MessageType.Notify);
                return Result.Cancelled;
            }

            if (ValidateLines(selectedLines, longCorner) == false)
            {
                var dialogResult = MessageWindow.ShowDialog(
                    "Not enough lenght to place the frame element.\nClick Agree to ignore and continue.", 
                    MessageType.Warning);

                if (dialogResult != true)
                {
                    return Result.Cancelled;
                }
            }

            //Reorganize the list of lines that the correct order.
            var correctLineOrder = CorrectLineOrder(selectedLines);                 

            //FixCurveDirection
            var correctLines = CorrectLineDirections(correctLineOrder);

            var cornerOffset = longCorner == true ?
                FrameConstants.LengthFamilyCornerLong - (FrameConstants.FamilyWidth / 2) :
                FrameConstants.LengthFamilyCornerShort - (FrameConstants.FamilyWidth / 2);

            var lineSeparator = new LineSeparator(correctLines, cornerOffset, FrameConstants.ModuleLength);
            lineSeparator.CalculateElements();

            using (Transaction trans = new Transaction(RevitManager.Document, "Place Family"))
            {
                trans.Start();

                if (familyProvider.Symbol.IsActive == false || familyProvider.CornerSymbol.IsActive == false)
                {
                    familyProvider.Symbol.Activate();
                    familyProvider.CornerSymbol.Activate();
                }

                //Create angle elements                  
                var lengthFamilyCorner = longCorner ? FrameConstants.LengthFamilyCornerLong : FrameConstants.MinLengthForCornerShort;
                foreach (var angleElement in lineSeparator.AngleElements)
                {
                    var instance = CreateAngleInstance(
                        familyProvider.CornerSymbol, angleElement.Origin, angleElement.GetMidVector(), angleElement, FrameConstants.FamilyWidth);
                    angleElement.SetOffset(cornerOffset, FrameConstants.FamilyWidth, lengthFamilyCorner);
                }
                var createdFamilys = new List<FamilyInstance>();

                //Create elements along line
                foreach (var straightElementPoints in lineSeparator.StraightElementPoints)
                {
                    //Create standart elements
                    foreach (var point in straightElementPoints.PointsList)
                    {
                        var familiyinstance = RevitManager.Document.Create.NewFamilyInstance(point, familyProvider.Symbol, StructuralType.NonStructural);
                        familiyinstance.LookupParameter("Desired Length").Set(FrameConstants.ModuleLength);
                        RotateElement(familiyinstance, GetAngle(familiyinstance, straightElementPoints.Direction));
                        createdFamilys.Add(familiyinstance);
                    }

                    //Create  elements with non uniform length
                    if (straightElementPoints.RebuildCurve.Length % straightElementPoints.PointsList.Count > 0 || 
                        ((straightElementPoints.RebuildCurve.Length < FrameConstants.ModuleLength) 
                            && straightElementPoints.Curve.Length - (cornerOffset * 2) > 0) || 
                        selectedLines.Count == 1)
                    {
                        var familiyinstance = CreateNonUniformStraightElement(
                            straightElementPoints.RebuildCurve, familyProvider.Symbol, straightElementPoints.Direction, FrameConstants.ModuleLength);
                        createdFamilys.Add(familiyinstance);
                    }
                }

                SetCap(createdFamilys);

                trans.Commit();
                
                MessageWindow.ShowDialog("Frames created sucsessfully.", MessageType.Success);
            }            

            return Result.Succeeded;
        }

        private bool ValidateLines(List<Curve> selectedLines, bool longCorner)
        {
            // Validate short Start/End lines for Corner
            var lastLine = selectedLines.Last();
            var firstLine = selectedLines.First();
            if (lastLine.Length < FrameConstants.MinLengthForCornerShort
                || firstLine.Length < FrameConstants.MinLengthForCornerShort) //0.453
            {
                return false;
            }

            if (lastLine.Length < FrameConstants.MinLength || firstLine.Length < FrameConstants.MinLength)
            {
                return false;
            }

            // Validate short middle Lines for Corner
            var middleLines = selectedLines.Skip(1).Take(selectedLines.Count - 2).ToList();
            foreach (var line in middleLines)
            {
                if (longCorner == false && line.Length < FrameConstants.MinLengthForCornerShort * 2) //0.453
                {
                    return false;
                }

                if (longCorner == true && line.Length < FrameConstants.MinLengthForCornerLong * 2) //0.9
                {
                    return false;
                }
            }

            return true;
        }

        private List<Curve> SelectLines()
        {
            var output = new List<Curve>();

            try
            {
                var references = RevitManager.UIDocument.Selection.PickObjects(Autodesk.Revit.UI.Selection.ObjectType.Element, new CurveElementSelectionFilter());

                foreach (var reference in references)
                {
                    var element = RevitManager.Document.GetElement(reference);
                    if (element is CurveElement curveElement)
                    {
                        output.Add(curveElement.GeometryCurve);
                    }
                }
            }
            catch { }

            return output;
        }

        private void SetCap(List<FamilyInstance> createdFamilys)
        {
            if (createdFamilys.Count == 1)
            {
                createdFamilys[0].LookupParameter("End Cap Back").Set(1);
                createdFamilys[0].LookupParameter("End Cap Front").Set(1);
            }
            for (int i = 0; i < createdFamilys.Count; i++)
            {
                if (i == 0)
                {
                    createdFamilys[i].LookupParameter("End Cap Front").Set(0);
                    createdFamilys[i].LookupParameter("End Cap Back").Set(1);
                }
                else if (i == createdFamilys.Count - 1)
                {
                    createdFamilys[i].LookupParameter("End Cap Front").Set(1);
                    createdFamilys[i].LookupParameter("End Cap Back").Set(0);
                }
                else
                {
                    createdFamilys[i].LookupParameter("End Cap Back").Set(0);
                    createdFamilys[i].LookupParameter("End Cap Front").Set(0);
                }    
            }
        }

        private FamilyInstance CreateNonUniformStraightElement(Curve curve, FamilySymbol symbol, XYZ lineDirection, double moduleLength)
        {
            var pt = curve.Evaluate(curve.Length, false);
            var familiyinstance = RevitManager.Document.Create.NewFamilyInstance(pt, symbol, StructuralType.NonStructural);
            var val = (curve.Length % moduleLength);
            familiyinstance.LookupParameter("Desired Length").Set(val);         
            RotateElement(familiyinstance, GetAngle(familiyinstance, lineDirection));
            return familiyinstance;
        }

        private double GetAngle(FamilyInstance familiyinstance, XYZ lineDirection)
        {
            var locationPoint = familiyinstance.Location as LocationPoint;
            var point = new XYZ(locationPoint.Point.X, locationPoint.Point.Y, 0);
            var axis = Line.CreateBound(point, new XYZ(point.X, point.Y, point.Z + 10)).Direction;
            var angle = (familiyinstance.FacingOrientation.AngleOnPlaneTo(lineDirection,axis) / (Math.PI / 180)) + 180;
            return angle;
        }

        private FamilyInstance CreateAngleInstance(FamilySymbol angleSymbol, XYZ point, XYZ vector, AngleElement element, double familyWidth)
        {
            var angleOffset = (familyWidth / 2) * -1;
            var movePoint = vector * angleOffset;
            var familyOrigin = new XYZ(point.X + movePoint.X, point.Y + movePoint.Y, point.Z + movePoint.Z);     
            var familiyinstance = RevitManager.Document.Create.NewFamilyInstance(familyOrigin, angleSymbol, StructuralType.NonStructural);
            
            var pt = new XYZ(familyOrigin.X, familyOrigin.Y, 0);
            var axis = Line.CreateBound(pt, new XYZ(pt.X, pt.Y, pt.Z + 10));
            var familyCustomVector = familiyinstance.FacingOrientation.Add(familiyinstance.HandOrientation);
            var angle = (familyCustomVector.AngleOnPlaneTo(vector, XYZ.BasisZ) / (Math.PI / 180)) - 90;      
            ElementTransformUtils.RotateElement(RevitManager.Document, familiyinstance.Id, axis, (Math.PI / 180) * Math.Round(angle, 0));
 
            element.Instance = familiyinstance;
            return familiyinstance;
        }

        private void RotateElement(FamilyInstance familiyinstance, double angle)
        {
            var lp = familiyinstance.Location as LocationPoint;
            var ppt = new XYZ(lp.Point.X, lp.Point.Y, 0);
            var axis = Line.CreateBound(ppt, new XYZ(ppt.X, ppt.Y, ppt.Z + 10));
            ElementTransformUtils.RotateElement(RevitManager.Document, familiyinstance.Id, axis, (Math.PI / 180) * (angle));
        }

        private List<XYZ> GetCurvePoints(List<Curve> curves)
        {
            var output = new List<XYZ>();
            foreach (var curve in curves)
            {
                var startPoint = curve.GetEndPoint(0);
                var endPoint = curve.GetEndPoint(1);
                output.Add(startPoint);
                output.Add(endPoint);
            }
            return output;
        }

        public static bool IsOpenLoop(List<XYZ> points)
        {
            var singlePoints = new List<XYZ>();
            var doublePoints = new List<XYZ>();

            for (int i = 0; i < points.Count; i++)
            {
                var countOfIntersection = 0;
                for (int j = 0; j < points.Count; j++)
                {
                    if (i == j)
                    {
                        continue;
                    }

                    if (points[i].IsAlmostEqualTo(points[j]))
                    {
                        countOfIntersection++;
                    }
                }

                if (countOfIntersection == 0)
                {
                    singlePoints.Add(points[i]);
                }
                else if (countOfIntersection == 1)
                {
                    doublePoints.Add(points[i]);
                }
            }

            return singlePoints.Count == 2 && singlePoints.Count + doublePoints.Count == points.Count;
        }

        private List<Curve> CorrectLineOrder(List<Curve> selectedCurves)
        {
            if (selectedCurves.Count == 1) return selectedCurves;

            var correctOrder = new List<Curve>();

            // Find first line
            foreach (var curve in selectedCurves)
            {
                var countOfIntersection = 0;
                foreach (var nextCurve in selectedCurves)
                {
                    if (curve == nextCurve)
                    {
                        continue;
                    }

                    var result = curve.Intersect(nextCurve);
                    if (result == SetComparisonResult.Subset || result == SetComparisonResult.Overlap)
                    {
                        countOfIntersection++;
                    }
                }
                if (countOfIntersection == 1)
                {
                    correctOrder.Add(curve);
                    break;
                }
            }

            // Sort lines
            for (int i = 0; i < selectedCurves.Count - 1; i++)
            {
                var nextLine = selectedCurves
                    .Where(c => correctOrder.Last().Intersect(c) == SetComparisonResult.Subset
                             || correctOrder.Last().Intersect(c) == SetComparisonResult.Overlap)
                    .Where(c => !correctOrder.Contains(c))
                    .First();

                correctOrder.Add(nextLine);
            }

            return correctOrder;
        }

        private List<Curve> CorrectLineDirections(List<Curve> lines)
        {
            if (lines.Count == 1) return lines;

            var output = new List<Curve>();

            // Add first line and fix direction if necessary
            var firstStart = lines[0].GetEndPoint(0);
            var nextStart = lines[1].GetEndPoint(0);
            var nextEnd = lines[1].GetEndPoint(1);
            if (firstStart.IsAlmostEqualTo(nextStart) || firstStart.IsAlmostEqualTo(nextEnd))
            {
                var revercedCurve = Line.CreateBound(lines[0].GetEndPoint(1), lines[0].GetEndPoint(0));
                output.Add(revercedCurve);
            }
            else
            {
                output.Add(lines[0]);
            }

            // Add next lines and fix directions if necessary
            for (int i = 1; i < lines.Count; i++)
            {
                var previousEnd = output.Last().GetEndPoint(1);
                var currentStart = lines[i].GetEndPoint(0);
                if (previousEnd.IsAlmostEqualTo(currentStart))
                {
                    output.Add(lines[i]);
                }
                else
                {
                    var revercedCurve = Line.CreateBound(lines[i].GetEndPoint(1), lines[i].GetEndPoint(0));
                    output.Add(revercedCurve);
                }
            }

            return output;
        }
    }
}
