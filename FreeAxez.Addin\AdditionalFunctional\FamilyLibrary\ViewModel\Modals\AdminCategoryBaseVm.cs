﻿using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.Infrastructure;
using NPOI.OpenXmlFormats.Dml.Diagram;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;

public abstract class AdminCategoryBaseVm : ModalDialogVm
{
    protected readonly LibraryCategoryDto SelectedCategory;
    private string _description;
    private string _name;
    private bool _isFreeAxezCategory;

    protected AdminCategoryBaseVm(LibraryCategoryDto selectedCategory)
    {
        SelectedCategory = selectedCategory;
        ApplyCommand = new AsyncRelayCommand(ApplyAsync, () => CanApply());

        if (SelectedCategory != null)
        {
            _name = SelectedCategory.CategoryName;
            _description = SelectedCategory.Description;
            _isFreeAxezCategory = SelectedCategory.IsFreeAxezCategory;
        }
    }

    public ICommand ApplyCommand { get; }

    public string Name
    {
        get => _name;
        set
        {
            if (_name != value)
            {
                _name = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CanApply));
                SelectedCategory.CategoryName = value;
            }
        }
    }

    public bool IsFreeAxezCategory
    {
        get => _isFreeAxezCategory;
        set
        {
            if (_isFreeAxezCategory != value)
            {
                _isFreeAxezCategory = value;
                OnPropertyChanged();
                SelectedCategory.IsFreeAxezCategory = value;
            }
        }
    }

    public string Description
    {
        get => _description;
        set
        {
            if (_description != value)
            {
                _description = value;
                OnPropertyChanged();
                SelectedCategory.Description = value;
            }
        }
    }

    protected abstract Task ApplyAsync();
    protected abstract bool CanApply();

    protected void HandleResponse(HttpResponseMessage response, Action<bool> onSuccess)
    {
        if (response.IsSuccessStatusCode)
            onSuccess(true);
        else
            HandleErrorResponse(response);
    }

    protected async Task HandleErrorResponse(HttpResponseMessage response)
    {
        var errorContent = await response.Content.ReadAsStringAsync();
        Error = errorContent;
    }
}