using System.Windows.Media;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Interfaces
{
    /// <summary>
    /// Common interface for layer information with basic properties
    /// </summary>
    public interface ILayerInfo
    {
        /// <summary>Layer name</summary>
        string Name { get; }
        
        /// <summary>Layer color</summary>
        Color Color { get; }
        
        /// <summary>Linetype name</summary>
        string LinetypeName { get; }
        
        /// <summary>Transparency percentage (0-100)</summary>
        int TransparencyPct { get; }
    }

    /// <summary>
    /// Interface for layers that can be displayed in UI with visual properties
    /// </summary>
    public interface IDisplayableLayer : ILayerInfo
    {
        /// <summary>Display name for UI (may include formatting)</summary>
        string DisplayName { get; }
        
        /// <summary>Color display string for UI</summary>
        string ColorDisplay { get; }
        
        /// <summary>Lineweight display string for UI</summary>
        string LineweightDisplay { get; }
        
        /// <summary>Transparency display string for UI</summary>
        string TransparencyDisplay { get; }
    }

    /// <summary>
    /// Interface for layers that can be mapped to other layers
    /// </summary>
    public interface IMappableLayer : ILayerInfo
    {
        /// <summary>Whether this layer can be mapped</summary>
        bool CanMap { get; }
        
        /// <summary>Whether this layer is currently mapped</summary>
        bool IsMapped { get; }
    }
}
