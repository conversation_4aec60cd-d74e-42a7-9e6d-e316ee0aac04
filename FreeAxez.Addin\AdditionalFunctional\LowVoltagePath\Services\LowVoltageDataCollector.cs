using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Constants;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;
using FreeAxez.Addin.Infrastructure;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services
{
    /// <summary>
    /// Service for collecting low voltage data from Revit model
    /// </summary>
    public class LowVoltageDataCollector
    {

        /// <summary>
        /// Collects all LV/MC lines from the active view
        /// </summary>
        public List<CurveElement> CollectLowVoltageLines()
        {
            var lowVoltageRegex = new Regex(LowVoltageConstants.LowVoltageLinePattern);

            return new FilteredElementCollector(RevitManager.Document, RevitManager.Document.ActiveView.Id)
                .OfClass(typeof(CurveElement))
                .WhereElementIsNotElementType()
                .Cast<CurveElement>()
                .Where(line => lowVoltageRegex.IsMatch(line.LineStyle.Name))
                .ToList();
        }

        /// <summary>
        /// Collects all electrical fixtures (outlets) with low voltage wire parameters
        /// </summary>
        public List<FamilyInstance> CollectLowVoltageOutlets()
        {
            return new FilteredElementCollector(RevitManager.Document, RevitManager.Document.ActiveView.Id)
                .OfCategory(BuiltInCategory.OST_ElectricalFixtures)
                .WhereElementIsNotElementType()
                .Cast<FamilyInstance>()
                .Where(HasLowVoltageWires)
                .ToList();
        }

        /// <summary>
        /// Collects selected elements if scope is set to selected elements
        /// </summary>
        public (List<CurveElement> lines, List<FamilyInstance> outlets) CollectSelectedElements()
        {
            var selectedIds = RevitManager.UIDocument.Selection.GetElementIds();
            var selectedElements = selectedIds.Select(id => RevitManager.Document.GetElement(id)).ToList();

            var lines = selectedElements
                .OfType<CurveElement>()
                .Where(line => new Regex(LowVoltageConstants.LowVoltageLinePattern).IsMatch(line.LineStyle.Name))
                .ToList();

            var outlets = selectedElements
                .OfType<FamilyInstance>()
                .Where(fi => fi.Category?.Id.IntegerValue == (int)BuiltInCategory.OST_ElectricalFixtures)
                .Where(HasLowVoltageWires)
                .ToList();

            return (lines, outlets);
        }

        /// <summary>
        /// Gets low voltage count parameters from a family instance
        /// </summary>
        public List<Parameter> GetLowVoltageCountParameters(FamilyInstance familyInstance)
        {
            var output = new List<Parameter>();
            var regex = new Regex(LowVoltageConstants.LowVoltageCountParameterPattern);

            foreach (Parameter param in familyInstance.ParametersMap)
            {
                if (regex.IsMatch(param.Definition.Name) && param.StorageType == StorageType.Integer)
                {
                    output.Add(param);
                }
            }
            return output;
        }

        /// <summary>
        /// Calculates total wire count for an outlet from all LV parameters
        /// </summary>
        public int CalculateOutletWireCount(FamilyInstance outlet)
        {
            var parameters = GetLowVoltageCountParameters(outlet);
            var totalCount = 0;

            foreach (var param in parameters)
            {
                var value = param.AsInteger();
                if (value > 0)
                {
                    totalCount += value;
                }
            }

            return totalCount > 0 ? totalCount : LowVoltageConstants.DefaultWireCount;
        }

        /// <summary>
        /// Converts outlets to OutletModel objects
        /// </summary>
        public List<OutletModel> ConvertOutletsToModels(List<FamilyInstance> outlets)
        {
            return outlets.Select(outlet => new OutletModel
            {
                Id = outlet.Id.IntegerValue,
                Location = NTSConverter.XYZToPoint((outlet.Location as LocationPoint).Point),
                WireCount = CalculateOutletWireCount(outlet)
            }).ToList();
        }

        /// <summary>
        /// Converts lines to geometry models
        /// </summary>
        public List<NetTopologySuite.Geometries.LineString> ConvertLinesToGeometry(List<CurveElement> lines)
        {
            return lines.Select(line => NTSConverter.CurveToLineString(line.GeometryCurve)).ToList();
        }

        /// <summary>
        /// Checks if a family instance has low voltage wires
        /// </summary>
        private bool HasLowVoltageWires(FamilyInstance familyInstance)
        {
            return GetLowVoltageCountParameters(familyInstance).Any(p => p.AsInteger() > 0);
        }
    }
}
