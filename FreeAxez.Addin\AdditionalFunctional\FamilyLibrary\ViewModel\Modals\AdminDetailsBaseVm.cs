﻿using System.Collections.ObjectModel;
using System.IO;
using System.Net.Http;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using Microsoft.Win32;
using Microsoft.WindowsAPICodePack.Shell;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;

public abstract class AdminDetailsBaseVm : ModalDialogVm
{
    protected readonly ObservableCollection<LibraryItemDetailsDto> DetailsToUpload = new();
    protected readonly List<string> LoadingErrors = new();
    protected bool _isUploading;
    protected ObservableCollection<DetailsFileInfoNew> _uploadFileInfo = new();

    protected AdminDetailsBaseVm()
    {
        RejectFileCommand = new RelayCommand(ExecuteRejectFile);
    }

    public ICommand RejectFileCommand { get; protected set; }

    protected ObservableCollection<MessageError> _errors { get; set; } = new();

    public ObservableCollection<DetailsFileInfoNew> UploadFileInfo
    {
        get => _uploadFileInfo;
        protected set
        {
            _uploadFileInfo = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(HasUploadedFiles));
        }
    }

    public ObservableCollection<MessageError> Errors
    {
        get => _errors;
        protected set
        {
            _errors = value;
            OnPropertyChanged();
        }
    }

    public bool HasUploadedFiles => UploadFileInfo.Count > 0;

    public virtual bool CanApply => HasUploadedFiles && !IsUploading;

    public bool IsUploading
    {
        get => _isUploading;
        set
        {
            _isUploading = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(CanApply));
            CommandManager.InvalidateRequerySuggested();
        }
    }

    protected abstract void ExecuteChooseFile(object parameter);

    protected void ExecuteRejectFile(object parameter)
    {
        if (parameter is DetailsFileInfoNewVm fileInfoVm)
        {
            var fileInfo = UploadFileInfo.Count > 0 ? UploadFileInfo[0] : null;
            if (fileInfo != null && fileInfo.DataContext == fileInfoVm)
            {
                UploadFileInfo.Clear();
                DetailsToUpload.Clear();
                OnPropertyChanged(nameof(HasUploadedFiles));
                OnPropertyChanged(nameof(CanApply));
            }
        }
    }

    protected static BitmapSource GetThumbnail(string filePath)
    {
        try
        {
            LogHelper.Information($"Attempting to get thumbnail for: {filePath}");

            if (!File.Exists(filePath))
            {
                LogHelper.Warning($"File does not exist: {filePath}");
                return null;
            }

            using (var shellFile = ShellFile.FromFilePath(filePath))
            {
                LogHelper.Information($"ShellFile created successfully for: {filePath}");

                var thumbnail = shellFile.Thumbnail;
                if (thumbnail == null)
                {
                    LogHelper.Warning($"Thumbnail is null for: {filePath}");
                    return null;
                }

                thumbnail.FormatOption = ShellThumbnailFormatOption.ThumbnailOnly;
                var bitmapSource = thumbnail.MediumBitmapSource;

                if (bitmapSource == null)
                {
                    LogHelper.Warning($"MediumBitmapSource is null for: {filePath}");
                    return null;
                }

                LogHelper.Information($"Thumbnail created successfully for: {filePath}");
                return bitmapSource;
            }
        }
        catch (Exception ex)
        {
            LogHelper.Error(
                $"Error occurred while trying to get thumbnail from details file '{filePath}': {ex.Message}");
            LogHelper.Error($"Exception type: {ex.GetType().Name}");
            LogHelper.Error($"Stack trace: {ex.StackTrace}");
            return null;
        }
    }


    protected void HandleResponse(HttpResponseMessage response, Action<bool> callback)
    {
        if (response.IsSuccessStatusCode)
        {
            callback(true);
        }
        else
        {
            Error = $"Error: {response.ReasonPhrase}";
            LogHelper.Error($"API error: {response.ReasonPhrase}");
            callback(false);
        }
    }

    protected void CloseDialog(bool result)
    {
        CloseModal(result);
    }

    protected virtual void ShowSuccessMessage()
    {
        var message = "All details were successfully uploaded to Library";
        FamilyLibraryCore.ShowMessage("Success", message, MessageType.Success);
    }

    protected void ShowLoadingErrorsMessage()
    {
        var message = "Errors occurred while loading some details:\n" +
                      string.Join("\n", LoadingErrors);
        FamilyLibraryCore.ShowMessage("Loading Errors", message, MessageType.Warning);
    }
}