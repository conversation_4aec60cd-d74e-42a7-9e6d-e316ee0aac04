﻿using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Utils
{
    public static class GriddBomExcelSchema
    {
        private const int LevelOffset = 5;

        public const string RevSheetNameTemplate = "Rev{0} {1}"; // Rev2 12-03-24
        public const int NumberOfPreparedLevelsOnRevSheet = 11;


        public static (int row, int column) GetLevelCell(int levelIndex)
        {
            var startRow = 9;
            var startColumn = 6;
            return (startRow, startColumn + GetOffset(levelIndex));
        }

        public static (int row, int column) GetAreaSFCell(int levelIndex)
        {
            var startRow = 13;
            var startColumn = 6;
            return (startRow, startColumn + GetOffset(levelIndex));
        }

        public static (int row, int column) GetGriddSFCell(int levelIndex)
        {
            var startRow = 14;
            var startColumn = 6;
            return (startRow, startColumn + GetOffset(levelIndex));
        }

        public static (int row, int column) GetReinforcedCell(int levelIndex)
        {
            var startRow = 15;
            var startColumn = 6;
            return (startRow, startColumn + GetOffset(levelIndex));
        }

        public static (int row, int column) GetScheduleStartCell(int levelIndex)
        {
            var startRow = 117;
            var startColumn = 5;
            return (startRow, startColumn + GetOffset(levelIndex));
        }

        public static (int rowStart, int rowEnd) GetScheduleRowRange()
        {
            return (117, 211);
        }

        public static int GetRevNumber(string sheetName)
        {
            if (!sheetName.StartsWith("Rev")) return -1;
            var match = Regex.Match(sheetName, @"\d+");
            if (match.Success)
            {
                return int.Parse(match.Value);
            }
            return -1;
        }

        private static int GetOffset(int levelIndex)
        {
            if (levelIndex >= 5) return levelIndex * LevelOffset + 1; // An error in the template that may be needed
            return levelIndex * LevelOffset;
        }
    }
}
