﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.TagRamp.Models;
using FreeAxez.Addin.AdditionalFunctional.TagRamp.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System;

namespace FreeAxez.Addin.AdditionalFunctional.TagRamp
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class TagRampCommand : BaseExternalCommand
    {
        private const string InfoDialogErrorTitle = "Error";

        public override Result Execute()
        {
            if (RevitManager.UIDocument.ActiveView.ViewType == ViewType.ThreeD)
            {
                InfoDialog.ShowDialog("Error", "Unable to place tags in 3D view.\nPlease select another view.");
                return Result.Cancelled;
            }
            
            var rampFamilies = new RampFamilies();
            rampFamilies.GetRampAnnotationFamily();
            if (rampFamilies.IsFamilyNotExist(out var errorAnnotationFamilyMessages))
            {
                InfoDialog.ShowDialog("Error", errorAnnotationFamilyMessages);
                return Result.Cancelled;
            }

            rampFamilies.GetMultiCategoryTagFamily();
            if (rampFamilies.IsFamilyNotExist(out var errorMultiCategoryTagFamilyMessages))
            {
                InfoDialog.ShowDialog("Error", errorMultiCategoryTagFamilyMessages);
                return Result.Cancelled;
            }

            rampFamilies.GetRampSlopeTagFamily();
            if (rampFamilies.IsFamilyNotExist(out var errorRampSlopeTagFamilyMessages))
            {
                InfoDialog.ShowDialog("Error", errorRampSlopeTagFamilyMessages);
                return Result.Cancelled;
            }

            try
            {
                var tagRampView = new TagRampView();
                tagRampView.ShowDialog();
            }
            catch (Autodesk.Revit.Exceptions.OperationCanceledException)
            {
                InfoDialog.ShowDialog(InfoDialogErrorTitle, "Select 2 or more ramp instances");
                return Result.Cancelled;
            }
            catch (NullReferenceException nullReferenceException)
            {
                InfoDialog.ShowDialog(InfoDialogErrorTitle, nullReferenceException.Message);
                return Result.Cancelled;
            }
            catch (Exception exception)
            {
                InfoDialog.ShowDialog(InfoDialogErrorTitle, exception.Message);
                return Result.Failed;
            }

            return Result.Succeeded;
        }
    }
}