﻿using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters
{
    public class UnknownToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue && stringValue == "Unknown")
            {
                return Brushes.Red;
            }

            return Brushes.Black;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
