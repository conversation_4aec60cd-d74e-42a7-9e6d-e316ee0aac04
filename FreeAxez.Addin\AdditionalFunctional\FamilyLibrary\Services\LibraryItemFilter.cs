﻿using System;
using System.Linq;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;

public class LibraryItemFilter
{
    public bool FilterLibraryItem(LibraryItemVm viewModel, string searchText, LibraryCategoryDto selectedCategory,
        string selectedRevitVersion)
    {
        return viewModel?.LibraryItem != null &&
               FilterBySearchText(viewModel.LibraryItem, searchText) &&
               FilterByCategory(viewModel.LibraryItem, selectedCategory) &&
               FilterByRevitVersion(viewModel.LibraryItem, selectedRevitVersion);
    }

    public bool FilterLibraryItem(LibraryItemDto item, string searchText, LibraryCategoryDto selectedCategory,
        string selectedRevitVersion)
    {
        return item != null &&
               FilterBySearchText(item, searchText) &&
               FilterByCategory(item, selectedCategory) &&
               FilterByRevitVersion(item, selectedRevitVersion);
    }

    public bool FilterBySearchText(LibraryItemDto item, string searchText)
    {
        if (string.IsNullOrEmpty(searchText)) return true;

        var searchTerms = new[]
        {
            item?.Name,
            item?.ProductName,
            item?.Version,
            item?.RevitVersion,
            item?.Category?.CategoryName,
            item?.CreatedBy,
            item?.ChangesDescription,
            item?.UpdatedBy
        };

        return searchTerms.Any(term => term?.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0);
    }

    public bool FilterByCategory(LibraryItemDto item, LibraryCategoryDto selectedCategory)
    {
        return item != null && (selectedCategory == null || item.CategoryId == selectedCategory.Id);
    }

    public bool FilterByRevitVersion(LibraryItemDto item, string selectedRevitVersion)
    {
        return item != null && (string.IsNullOrEmpty(selectedRevitVersion) || item.RevitVersion == selectedRevitVersion);
    }

    public bool FilterHistoryEntryBySearchText(LibraryItemHistoryEntryDto entry, string searchText)
    {
        if (string.IsNullOrEmpty(searchText)) return true;

        var searchTerms = new[]
        {
            entry?.Name,
            entry?.ProductName,
            entry?.Version,
            entry?.ChangesDescription,
            entry?.Action,
            entry?.CreatedBy
        };

        return searchTerms.Any(term => term?.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0);
    }
}