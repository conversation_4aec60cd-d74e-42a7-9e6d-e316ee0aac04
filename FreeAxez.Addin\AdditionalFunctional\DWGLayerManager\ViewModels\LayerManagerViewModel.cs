using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Data;
using System.Windows.Forms;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels;

public class LayerManagerViewModel : WindowViewModel
{
    private readonly FreeAxezLayerOperations _layerOperations;
    private readonly LayerPropertyService _layerPropertyService;
    private readonly AutoCADVersionService _autoCADVersionService;
    private bool _isLoading;
    private ObservableCollection<LayerModel> _layers;
    private ObservableCollection<LinetypeModel> _linetypes;
    private string _searchText;
    private LayerModel _selectedLayer;
    private ObservableCollection<int> _availableAutoCADVersions;
    private int _selectedAutoCADVersion;

    public LayerManagerViewModel(DwgLayerManagerApiService apiService)
    {
        _layerOperations = new FreeAxezLayerOperations(apiService);

        // Create LayerPropertyService with its dependencies
        var dialogService = new LayerDialogService(apiService);
        _layerPropertyService = new LayerPropertyService(dialogService);

        _autoCADVersionService = new AutoCADVersionService();

        Layers = new ObservableCollection<LayerModel>();
        Linetypes = new ObservableCollection<LinetypeModel>();

        // Initialize AutoCAD versions
        InitializeAutoCADVersions();

        // Initialize filtered collection
        FilteredLayers = _layerPropertyService.CreateFilteredView(Layers);

        AddLayerCommand = new RelayCommand(_ => AddNewLayerAsync());
        DeleteLayerCommand = new RelayCommand(_ => DeleteSelectedLayerAsync(), _ => SelectedLayer != null);
        SelectColorCommand = new RelayCommand(param => _layerPropertyService.UpdateLayerColor(param as LayerModel));
        SelectLinetypeCommand = new RelayCommand(param => _layerPropertyService.UpdateLayerLinetype(param as LayerModel));
        SelectLineweightCommand = new RelayCommand(param => _layerPropertyService.UpdateLayerLineweight(param as LayerModel));
        SelectTransparencyCommand = new RelayCommand(param => _layerPropertyService.UpdateLayerTransparency(param as LayerModel));
        LoadLinetypesCommand = new RelayCommand(_ => LoadLinetypesFromFile());

        LoadDataAsync();
    }

    public ObservableCollection<LayerModel> Layers
    {
        get => _layers;
        set => Set(ref _layers, value);
    }

    public ICollectionView FilteredLayers { get; }

    public ObservableCollection<LinetypeModel> Linetypes
    {
        get => _linetypes;
        set => Set(ref _linetypes, value);
    }

    public LayerModel SelectedLayer
    {
        get => _selectedLayer;
        set
        {
            if (Set(ref _selectedLayer, value)) ((RelayCommand)DeleteLayerCommand).RaiseCanExecuteChanged();
        }
    }

    public string SearchText
    {
        get => _searchText;
        set
        {
            if (Set(ref _searchText, value))
                _layerPropertyService.FilterLayers(Layers, value, FilteredLayers);
        }
    }

    public bool IsLoading
    {
        get => _isLoading;
        set => Set(ref _isLoading, value);
    }

    public ObservableCollection<int> AvailableAutoCADVersions
    {
        get => _availableAutoCADVersions;
        set => Set(ref _availableAutoCADVersions, value);
    }

    public int SelectedAutoCADVersion
    {
        get => _selectedAutoCADVersion;
        set
        {
            if (Set(ref _selectedAutoCADVersion, value))
            {
                // Save to settings
                Properties.Settings.Default.SelectedAutoCADVersion = value;
                Properties.Settings.Default.Save();

                // Notify that AutoCAD version changed
                OnAutoCADVersionChanged?.Invoke();
            }
        }
    }

    public AutoCADVersionService AutoCADVersionService => _autoCADVersionService;

    // Event to notify when AutoCAD version changes
    public event Action OnAutoCADVersionChanged;

    public ICommand AddLayerCommand { get; }
    public ICommand DeleteLayerCommand { get; }
    public ICommand SelectColorCommand { get; }
    public ICommand SelectLinetypeCommand { get; }
    public ICommand SelectLineweightCommand { get; }
    public ICommand SelectTransparencyCommand { get; }
    public ICommand LoadLinetypesCommand { get; }

    private async void LoadDataAsync()
    {
        IsLoading = true;
        try
        {
            await LoadLayersAsync();
            await LoadLinetypesAsync();
        }
        catch (Exception ex)
        {
            ShowErrorDialog("Failed to load data: " + ex.Message);
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadLayersAsync()
    {
        var layers = await _layerOperations.LoadLayersAsync();

        Layers.Clear();
        foreach (var layer in layers)
        {
            Layers.Add(layer);
        }
    }

    private async Task LoadLinetypesAsync()
    {
        var linetypes = await _layerOperations.LoadLinetypesAsync();

        Linetypes.Clear();
        foreach (var linetype in linetypes)
        {
            Linetypes.Add(linetype);
        }
    }

    private async void AddNewLayerAsync()
    {
        try
        {
            IsLoading = true;
            var newLayer = await _layerOperations.AddNewLayerAsync();
            if (newLayer != null)
            {
                Layers.Add(newLayer);
                SelectedLayer = newLayer;
            }
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async void DeleteSelectedLayerAsync()
    {
        if (SelectedLayer == null) return;

        try
        {
            IsLoading = true;
            var success = await _layerOperations.DeleteLayerAsync(SelectedLayer);
            if (success)
            {
                var layerToRemove = SelectedLayer;
                SelectedLayer = null;
                Layers.Remove(layerToRemove);
            }
        }
        finally
        {
            IsLoading = false;
        }
    }




    private void LoadLinetypesFromFile()
    {
        _layerPropertyService.LoadAndAddLinetypes(Linetypes);
    }

    private void InitializeAutoCADVersions()
    {
        try
        {
            if (!_autoCADVersionService.IsAutoCADAvailable())
            {
                MessageWindow.ShowDialog("AutoCAD Not Found",
                    "No supported AutoCAD versions (2022-2026) were found on this system. " +
                    "Please install AutoCAD to use DWG Layer Manager.",
                    MessageType.Warning);

                AvailableAutoCADVersions = new ObservableCollection<int>();
                return;
            }

            var availableVersions = _autoCADVersionService.GetAvailableVersionNumbers();
            AvailableAutoCADVersions = new ObservableCollection<int>(availableVersions);

            // Load saved version from settings
            var savedVersion = Properties.Settings.Default.SelectedAutoCADVersion;
            if (savedVersion > 0 && AvailableAutoCADVersions.Contains(savedVersion))
            {
                SelectedAutoCADVersion = savedVersion;
            }
            else if (AvailableAutoCADVersions.Count > 0)
            {
                // Select the highest available version by default
                SelectedAutoCADVersion = AvailableAutoCADVersions.First();
            }
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog("Error",
                $"Failed to initialize AutoCAD versions: {ex.Message}",
                MessageType.Error);

            AvailableAutoCADVersions = new ObservableCollection<int>();
        }
    }



    private void ShowErrorDialog(string message)
    {
        MessageWindow.ShowDialog("Error", message, MessageType.Error);
    }
}