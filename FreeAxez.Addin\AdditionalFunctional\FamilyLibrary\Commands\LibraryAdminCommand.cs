﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Commands
{
    [Transaction(TransactionMode.Manual)]
    internal class LibraryAdminCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            FamilyLibraryCore.Initialize();

            var window = new AdminView();
            window.Show();

            return Result.Succeeded;
        }
    }
}
