﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using NetTopologySuite.Geometries;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services
{
    public class WireCountingService
    {
        private readonly List<Railing> _railings;
        private readonly List<FamilyInstance> _annotations;

        public WireCountingService(List<Railing> railings, List<FamilyInstance> annotations)
        {
            _railings = railings;
            _annotations = annotations;
        }

        public void CountWires()
        {
            using (var t = new Transaction(_railings.First().Document, "Count Wires"))
            {
                t.Start();
                foreach (var annotation in _annotations)
                {
                    var location = NTSConverter.XYZToPoint((annotation.Location as LocationPoint).Point);
                    var count = 0.0;
                    foreach (var railing in _railings)
                    {
                        var path = NTSConverter.CurveLoopToLineString(railing.GetPath().ToList());

                        if (path.Distance(location) <= 0.0833334) // Tolerance of 1"
                        {
                            var wireCountParam = railing.LookupParameter("Quantity")?.AsDouble();
                            count += wireCountParam ?? 0.0;
                        }
                    }
                    annotation.LookupParameter("Quantity")?.Set((int)count);
                }
                t.Commit();
            }
        }
    }
}
