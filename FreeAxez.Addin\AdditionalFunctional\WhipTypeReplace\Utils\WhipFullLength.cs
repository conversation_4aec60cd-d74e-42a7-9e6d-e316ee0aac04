﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using System;

namespace FreeAxez.Addin.AdditionalFunctional.WhipTypeReplace.Utils
{
    public static class WhipFullLength
    {
        private const double PigtailLength = 1 + 4.0 / 12.0;

        public static double GetRiseLength(FlexPipe whip, bool roundUp = true)
        {
            var constraint = whip.FlexPipeType.LookupParameter("Constraint")?.AsInteger();
            if (constraint == null)
            {
                return 0;
            }

            var length = whip.get_Parameter(BuiltInParameter.CURVE_ELEM_LENGTH).AsDouble();
            var whipDrop = whip.LookupParameter("Whip Drop")?.AsDouble() ?? 0;
            var whipRise = whip.LookupParameter("Whip Rise")?.AsDouble() ?? 0;
            var whipOverhead = whip.LookupParameter("Whip Overhead")?.AsDouble() ?? 0;
            var splice = whip.LookupParameter("Splice")?.AsDouble() ?? 0;

            var pigtail1 = whip.LookupParameter("Pigtail 1")?.AsInteger() == 1 ? PigtailLength : 0;
            var pigtail2 = whip.LookupParameter("Pigtail 2")?.AsInteger() == 1 ? PigtailLength : 0;

            var dmRise1 = whip.LookupParameter("dm Rise1")?.AsDouble() ?? 0;
            var dmRise2 = whip.LookupParameter("dm Rise2")?.AsDouble() ?? 0;

            if (roundUp)
            {
                switch (constraint)
                {
                    case 1: // Access - Floor - FreeAxez - GriddPower - Whip - FeedModule
                        return Math.Ceiling(((length + whipDrop + whipRise + whipOverhead + (splice * 1)) / 5) / 1) * 5;
                    case 2: // Access - Floor - FreeAxez - GriddPower - Whip - Interlink
                        return Math.Ceiling(((length + whipDrop + whipRise + whipOverhead + (splice * 1)) / 1) / 1) * 1;
                    case 3: // Access - Floor - FreeAxez - GriddPower - SpinLock - BranchCircuit - Supply
                        return Math.Ceiling(((length + whipDrop + whipRise + whipOverhead - pigtail1 - pigtail2) / 5) / 1) * 5;
                    case 4: // Access - Floor - FreeAxez - GriddPower - SpinLock - BranchCircuit - Interlink
                        return Math.Ceiling(((length + whipDrop + whipRise + whipOverhead - pigtail1 - pigtail2) / 1) / 1) * 1;
                    case 5: // Access - Floor - FreeAxez - GriddPower - SpinLock - Whip - 50amp - Modular
                        return Math.Ceiling(((length + whipDrop + whipRise + whipOverhead + dmRise1 + dmRise2 - pigtail1 - pigtail2) / 5) / 1) * 5;
                    case 6: // Access - Floor - FreeAxez - GriddPower - SpinLock - BranchCircuit - Whip
                        return Math.Ceiling(((length + whipDrop + whipRise + whipOverhead + dmRise1 + dmRise2 - pigtail1 - pigtail2) / 1) / 1) * 1;
                    case var n when (n > 7): // Access - Floor - FreeAxez - GriddPower - Whip - 08', etc.
                        return Math.Ceiling(((length + whipDrop + whipRise + whipOverhead) / 1) / 1) * 1;
                    default:
                        return 0;
                }
            }
            else
            {
                switch (constraint)
                {
                    case 1: // Access - Floor - FreeAxez - GriddPower - Whip - FeedModule
                        return length + whipDrop + whipRise + whipOverhead + (splice * 1);
                    case 2: // Access - Floor - FreeAxez - GriddPower - Whip - Interlink
                        return length + whipDrop + whipRise + whipOverhead + (splice * 1);
                    case 3: // Access - Floor - FreeAxez - GriddPower - SpinLock - BranchCircuit - Supply
                        return length + whipDrop + whipRise + whipOverhead - pigtail1 - pigtail2;
                    case 4: // Access - Floor - FreeAxez - GriddPower - SpinLock - BranchCircuit - Interlink
                        return length + whipDrop + whipRise + whipOverhead - pigtail1 - pigtail2;
                    case 5: // Access - Floor - FreeAxez - GriddPower - SpinLock - Whip - 50amp - Modular
                        return length + whipDrop + whipRise + whipOverhead + dmRise1 + dmRise2 - pigtail1 - pigtail2;
                    case 6: // Access - Floor - FreeAxez - GriddPower - SpinLock - BranchCircuit - Whip
                        return length + whipDrop + whipRise + whipOverhead + dmRise1 + dmRise2 - pigtail1 - pigtail2;
                    case var n when (n > 7): // Access - Floor - FreeAxez - GriddPower - Whip - 08', etc.
                        return length + whipDrop + whipRise + whipOverhead;
                    default:
                        return 0;
                }

            }
        }
    }
}
