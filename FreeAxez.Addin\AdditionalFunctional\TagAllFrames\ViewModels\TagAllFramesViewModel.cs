﻿using FreeAxez.Addin.AdditionalFunctional.TagAllFrames.Models;
using FreeAxez.Addin.AdditionalFunctional.TagAllFrames.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllFrames.ViewModels
{
    public class TagAllFramesViewModel : BaseViewModel
    {
        public TagAllFramesViewModel()
        {
            _lengthToCenter = Properties.Settings.Default.TagAllFramesLengthToCenterOfTag;
            TagAll = Properties.Settings.Default.TagFramesVisibleInView;
            TagSelected = !TagAll;

            CreateCommand = new RelayCommand(OnCreateCommandExecute);
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
        }
        public double _lengthToCenter;
        public bool TagType { get; set; }
        public bool AnnotationType { get; set; }
        public bool TagAll { get; set; }
        public bool TagSelected { get; set; }
        public string LengthToCenter
        {
            get
            {
                return ProjectUnitsConverter.FormatLengthToFractionalInches(_lengthToCenter);
            }
            set
            {
                if (ProjectUnitsConverter.TryParseLengthFromFractionalInches(value, out _lengthToCenter))
                {
                    OnPropertyChanged();
                }
            }
        }

        public ICommand CreateCommand { get; set; }
        private void OnCreateCommandExecute(object p)
        {
            SaveSettings();
            (p as Window).Close();

            var tagCreator = new TagFramesCreator(new FrameTagOptions(_lengthToCenter, TagAll));

            var createdTags = tagCreator.CreateAnnatation();
            InfoDialog.ShowDialog("Report", $"Created {createdTags.Count} tags.");
        }

        public ICommand CancelCommand { get; set; }
        private void OnCancelCommandExecute(object p)
        {
            (p as Window).Close();
        }

        private void SaveSettings()
        {
            Properties.Settings.Default.TagAllFramesLengthToCenterOfTag = _lengthToCenter;
            Properties.Settings.Default.TagFramesVisibleInView = TagAll;
            Properties.Settings.Default.Save();
        }
    }
}