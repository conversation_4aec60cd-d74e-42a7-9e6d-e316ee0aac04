<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals.DetailPreviewDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:viewModel="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals"
             xmlns:panAndZoom="clr-namespace:Wpf.Controls.PanAndZoom;assembly=Wpf.Controls.PanAndZoom"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance Type={x:Type viewModel:DetailPreviewDialogVm}}"
             Width="1000" Height="700">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource Gray200}" Margin="0,0,0,15">
            <Grid Height="40">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Back button -->
                <Button Grid.Column="0"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource ButtonOutlinedBlue}"
                        Margin="15,0,0,0"
                        ToolTip="Close">
                    <Path Data="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"
                          Fill="{StaticResource Gray800}"
                          Width="16"
                          Height="16"
                          Stretch="Uniform"/>
                </Button>

                <!-- Title -->
                <TextBlock Grid.Column="1"
                           Text="{Binding Title}"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Foreground="{StaticResource Gray800}"
                           VerticalAlignment="Center"
                           HorizontalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- Main content with PanAndZoom -->
        <ScrollViewer Grid.Row="1" Margin="15"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Auto">
            <panAndZoom:ZoomBorder x:Name="ZoomAndPanControl"
                                   Background="White"
                                   HorizontalAlignment="Stretch"
                                   VerticalAlignment="Stretch"
                                   BorderThickness="0"
                                   BorderBrush="Transparent">
                <Image x:Name="PreviewImage"
                       Source="{Binding PreviewImage}"
                       Stretch="Uniform"
                       RenderOptions.BitmapScalingMode="HighQuality"/>
            </panAndZoom:ZoomBorder>
        </ScrollViewer>

    </Grid>
</UserControl>
