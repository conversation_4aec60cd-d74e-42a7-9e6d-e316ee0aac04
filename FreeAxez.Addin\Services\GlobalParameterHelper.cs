﻿using Autodesk.Revit.DB;
using System;

namespace FreeAxez.Addin.Services
{
    public static class GlobalParameterHelper
    {
        public static Guid? GetProjectId(Document document)
        {
            var paramId = GlobalParametersManager.FindByName(document, AppConstants.ProjectIdParameter);
            if (paramId == null || !GlobalParametersManager.IsValidGlobalParameter(document, paramId))
            {
                return null;
            }

            GlobalParameter gp = document.GetElement(paramId) as GlobalParameter;
            if (gp == null)
            {
                return null;
            }

            StringParameterValue gpvalue = gp.GetValue() as StringParameterValue;
            if (gpvalue == null)
            {
                return null;
            }

            Guid result;
            if (Guid.TryParse(gpvalue.Value, out result))
            {
                return result;
            }

            return null;
        }

        public static void SetProjectId(Guid id, Document document)
        {
            var paramId = GlobalParametersManager.FindByName(document, AppConstants.ProjectIdParameter);
            if (paramId == null || !GlobalParametersManager.IsValidGlobalParameter(document, paramId))
            {
#if  revit2018 || revit2019 || revit2020 || revit2021
                var glParam = GlobalParameter.Create(document, AppConstants.ProjectIdParameter, ParameterType.Text);
#else
                var glParam = GlobalParameter.Create(document, AppConstants.ProjectIdParameter, SpecTypeId.String.Text);
#endif
                var paramValue = glParam.GetValue();
                var textParamValue = paramValue as StringParameterValue;
                textParamValue.Value = id.ToString();
                glParam.SetValue(textParamValue);
            }
            else
            {
                GlobalParameter gp = document.GetElement(paramId) as GlobalParameter;
                StringParameterValue gpvalue = gp.GetValue() as StringParameterValue;
                gpvalue.Value = id.ToString();
                gp.SetValue(gpvalue);
            }
        }
    }
}
