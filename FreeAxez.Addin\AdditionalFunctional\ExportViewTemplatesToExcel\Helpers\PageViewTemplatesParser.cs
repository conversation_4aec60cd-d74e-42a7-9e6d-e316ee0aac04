﻿using System;
using System.Collections.Generic;
using System.Linq;
using OfficeOpenXml;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Models;
using FreeAxez.Addin.Infrastructure;
using JetBrains.Annotations;
using Serilog;

namespace FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Helpers
{
    public class PageViewTemplatesParser
    {
        private const string WorksheetName = "View Templates";
        public void ParseSheetAndUpdateViewTemplates(ExcelPackage package, Document doc, [CanBeNull] Dictionary<string, View> viewTemplates, LocalLogger localLogger)
        {
            var worksheet = package.Workbook.Worksheets[WorksheetName];
            if (worksheet == null)
                throw new InvalidOperationException($"Worksheet '{WorksheetName}' not found in the Excel file.");
            var rows = ParseViewTemplateRows(worksheet);
            if (!rows.Any())
                throw new InvalidOperationException("No valid data found in the View Templates worksheet.");

            // Statistics tracking
            int processedTemplates = 0;
            int updatedParameters = 0;
            var errors = new List<string>();

            foreach (var row in rows)
            {
                if (!viewTemplates.TryGetValue(row.ViewTemplateName, out var viewTemplate))
                    continue;
                try
                {
                    var paramCount = UpdateViewTemplate(doc, viewTemplate, row, errors);
                    processedTemplates++;
                    updatedParameters += paramCount;
                }
                catch (Exception ex)
                {
                    errors.Add($"Template '{row.ViewTemplateName}': {ex.Message}");
                }
            }

            // Log summary
            if (errors.Any())
            {
                localLogger?.Error($"View Templates processing completed with {errors.Count} errors:");
                foreach (var error in errors.Take(20))
                {
                    localLogger?.Error($"  {error}");
                }
                if (errors.Count > 20)
                    localLogger?.Error($"  ... and {errors.Count - 20} more errors");
            }

            localLogger?.Information($"View Templates processed: {processedTemplates} templates, {updatedParameters} parameters updated");
        }

        private List<PageViewTemplateRow> ParseViewTemplateRows(ExcelWorksheet worksheet)
        {
            var rows = new List<PageViewTemplateRow>();
            if (worksheet.Dimension == null) return rows;
            for (int rowIndex = 3; rowIndex <= worksheet.Dimension.End.Row; rowIndex++)
            {
                var templateName = worksheet.Cells[rowIndex, 1].Text.Trim();
                if (string.IsNullOrEmpty(templateName))
                    continue;
                var viewType = worksheet.Cells[rowIndex, 2].Text.Trim();
                var parameters = new Dictionary<string, ViewTemplateParameter>();
                int col = 3;
                while (col <= worksheet.Dimension.End.Column - 1)
                {
                    var paramName = worksheet.Cells[1, col].Text.Trim();
                    if (string.IsNullOrEmpty(paramName))
                        break;
                    var value = worksheet.Cells[rowIndex, col].Text.Trim();
                    if (string.IsNullOrEmpty(value))
                    {
                        col += 2;
                        continue;
                    }
                    var include = worksheet.Cells[rowIndex, col + 1].Text.Trim().Equals("Yes", StringComparison.OrdinalIgnoreCase);
                    parameters[paramName] = new ViewTemplateParameter(value, include);
                    col += 2;
                }
                rows.Add(new PageViewTemplateRow(templateName, viewType, parameters));
            }
            return rows;
        }

        private int UpdateViewTemplate(Document doc, View viewTemplate, PageViewTemplateRow row, List<string> errors)
        {
            int updatedCount = 0;
            foreach (var kv in row.Parameters)
            {
                var paramName = kv.Key;
                var paramData = kv.Value;
                try
                {
                    UpdateParameter(doc, viewTemplate, paramName, paramData, errors);
                    updatedCount++;
                }
                catch (Exception ex)
                {
                    errors.Add($"Template '{row.ViewTemplateName}', parameter '{paramName}': {ex.Message}");
                }
            }
            return updatedCount;
        }

        private void UpdateParameter(Document doc, View viewTemplate, string paramName, ViewTemplateParameter param, List<string> errors)
        {
            switch (paramName)
            {
                case "View Scale":
                    try
                    {
                        if (!string.IsNullOrEmpty(param.Value) && param.Value != "Custom")
                        {
                            var scale = Converters.ScaleConverter.ParseScale(param.Value);
                            if (scale != -1)
                                viewTemplate.Scale = scale;
                        }
                    }
                    catch (Exception ex) { errors.Add($"Template '{viewTemplate.Name}', View Scale: {ex.Message}"); }
                    break;
                case "Model Display":
                    try
                    {
                        var ds = Converters.DisplayStyleConverter.ParseDisplayStyle(param.Value);
                        if (ds != DisplayStyle.Undefined)
                            viewTemplate.DisplayStyle = ds;
                    }
                    catch (Exception ex) { errors.Add($"Template '{viewTemplate.Name}', Model Display: {ex.Message}"); }
                    break;
                case "Detail Level":
                    try
                    {
                        var dl = Converters.DetailLevelConverter.ParseDetailLevel(param.Value);
                        if (dl != ViewDetailLevel.Undefined)
                            viewTemplate.DetailLevel = dl;
                    }
                    catch (Exception ex) { errors.Add($"Template '{viewTemplate.Name}', Detail Level: {ex.Message}"); }
                    break;
                case "Parts Visibility":
                    try
                    {
                        var pv = Converters.PartsVisibilityConverter.ParsePartsVisibility(param.Value);
                        if (pv != PartsVisibility.Unset)
                            viewTemplate.PartsVisibility = pv;
                    }
                    catch (Exception ex) { errors.Add($"Template '{viewTemplate.Name}', Parts Visibility: {ex.Message}"); }
                    break;
                case "Discipline":
                    try
                    {
                        if (Enum.TryParse(param.Value, out ViewDiscipline discipline))
                            viewTemplate.Discipline = discipline;
                    }
                    catch (Exception ex) { errors.Add($"Template '{viewTemplate.Name}', Discipline: {ex.Message}"); }
                    break;
                case "Underlay Orientation":
                    try
                    {
                        var revitParam = viewTemplate.get_Parameter(BuiltInParameter.VIEW_UNDERLAY_ORIENTATION);
                        if (param.Value == "Look down") revitParam?.Set(0);
                        else if (param.Value == "Look up") revitParam?.Set(1);
                    }
                    catch (Exception ex) { errors.Add($"Template '{viewTemplate.Name}', Underlay Orientation: {ex.Message}"); }
                    break;
                case "Orientation":
                    try
                    {
                        var revitParam = viewTemplate.get_Parameter(BuiltInParameter.PLAN_VIEW_NORTH);
                        if (param.Value == "Project North") revitParam?.Set(0);
                        else if (param.Value == "True North") revitParam?.Set(1);
                    }
                    catch (Exception ex) { errors.Add($"Template '{viewTemplate.Name}', Orientation: {ex.Message}"); }
                    break;
                case "Phase Filter":
                    try
                    {
                        var revitParam = viewTemplate.get_Parameter(BuiltInParameter.VIEW_PHASE_FILTER);
                        var phaseFilter = new FilteredElementCollector(doc)
                            .OfClass(typeof(PhaseFilter))
                            .FirstOrDefault(x => x.Name == param.Value) as PhaseFilter;
                        if (phaseFilter != null)
                            revitParam?.Set(phaseFilter.Id);
                    }
                    catch (Exception ex) { errors.Add($"Template '{viewTemplate.Name}', Phase Filter: {ex.Message}"); }
                    break;
                case "Show Hidden Lines":
                    try
                    {
                        var revitParam = viewTemplate.get_Parameter(BuiltInParameter.VIEW_SHOW_HIDDEN_LINES);
                        int hiddenLinesValue = param.Value switch
                        {
                            "None" => 0,
                            "By Discipline" => 1,
                            "All" => 2,
                            _ => -1
                        };
                        if (hiddenLinesValue != -1)
                            revitParam?.Set(hiddenLinesValue);
                    }
                    catch (Exception ex) { errors.Add($"Template '{viewTemplate.Name}', Show Hidden Lines: {ex.Message}"); }
                    break;
                default:
                    try
                    {
                        var revitParam = viewTemplate.LookupParameter(paramName);
                        if (revitParam != null && revitParam.StorageType == StorageType.String)
                            revitParam.Set(param.Value);
                    }
                    catch (Exception ex) { errors.Add($"Template '{viewTemplate.Name}', parameter '{paramName}': {ex.Message}"); }
                    break;
            }
        }
    }
}
