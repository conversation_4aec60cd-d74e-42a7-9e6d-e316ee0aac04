<Window x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.UserView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:navigation="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Navigation"
        xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls"
        xmlns:navigation1="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Navigation"
        mc:Ignorable="d"
        Title="LibraryBrowserView"
        Height="900"
        Width="1600"
        MinWidth="1000"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize">
    <WindowChrome.WindowChrome>
        <WindowChrome GlassFrameThickness="0,0,0,0"
                      ResizeBorderThickness="5"
                      CornerRadius="0"/>
    </WindowChrome.WindowChrome>
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Grid>
        <Grid.Resources>
            <navigation:UserNavigationVm x:Key="SharedDataContext" />
        </Grid.Resources>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="100"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <controls:SideBar Grid.Column="0" SidebarTitle="Family Library">
            <controls:SideBar.InnerContent>
                <navigation1:UserNavigation DataContext="{StaticResource SharedDataContext}"/>
            </controls:SideBar.InnerContent>
        </controls:SideBar>
        <Grid Grid.Column="1">
            <Border Background="White"
                    BorderThickness="1"
                    BorderBrush="{StaticResource Gray200}"
                    CornerRadius="0 12 12 0" />
            <Grid>
                <navigation1:UserPagesPresenter DataContext="{StaticResource SharedDataContext}" Margin="1"/>
            </Grid>
        </Grid>
    </Grid>
</Window>
