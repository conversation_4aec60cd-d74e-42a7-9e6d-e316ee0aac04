﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.ExportCAD.Views.ExportCADView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.ExportCAD.Views"
        mc:Ignorable="d" 
        MinHeight="500" MinWidth="500"
        Height="500" Width="500"
        WindowStartupLocation="CenterScreen"
        Title="Export Sheets To DWG">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/TextBoxes.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    
    <Grid Margin="5">
        <Grid.RowDefinitions>
            <RowDefinition Height="30"/>
            <RowDefinition Height="33"/>
            <RowDefinition Height="35"/>
            <RowDefinition/>
            <RowDefinition Height="33"/>
            <RowDefinition Height="35"/>
        </Grid.RowDefinitions>
        <DockPanel Margin="0,5,0,0" Grid.Row="0" LastChildFill="True">
            <Label Width="100" HorizontalContentAlignment="Right"  DockPanel.Dock="Left" Content="Folder to save:" VerticalAlignment="Center"/>
            <Button Margin="5,0,0,0" DockPanel.Dock="Right" Content="Browse" Width="60" Command="{Binding BrowseCommand}"/>
            <TextBox VerticalContentAlignment="Center" Text="{Binding FolderPath}"/>
        </DockPanel>
        <DockPanel Margin="0,5,0,0" Grid.Row="1" LastChildFill="True">
            <Label Width="100" HorizontalContentAlignment="Right" DockPanel.Dock="Left" Content="File name/prefix:" VerticalAlignment="Center"/>
            <TextBox VerticalContentAlignment="Center" Text="{Binding Prefix}"/>
        </DockPanel>
        <TextBox Margin="0 5 0 0" Grid.Row="2"
                 Text="{Binding SheetNameFilter, UpdateSourceTrigger=PropertyChanged}"
                 Style="{StaticResource Search}"/>
        <StackPanel Orientation="Horizontal" Grid.Row="5">
            <Label Content="Export Mode:" VerticalAlignment="Center"/>
            <RadioButton x:Name="colorOption" IsChecked="{Binding ExportColorOptions}" Margin="5,0,10,0" Content="Color" GroupName="direction" VerticalAlignment="Center"/>
            <RadioButton x:Name="colorOptionGray" IsChecked="{Binding ExportGrayOptions}" Content="Monochrome" GroupName="direction" VerticalAlignment="Center"/>
        </StackPanel>
        <DataGrid Margin="0,10,0,0" x:Name="dataGrid" Grid.Row="3" ItemsSource="{Binding RevitViews}" AutoGenerateColumns="False" CanUserAddRows="False" HeadersVisibility="Column" SelectionUnit="FullRow" SelectionMode="Extended">
            <DataGrid.Columns>
                <DataGridTemplateColumn Header="Export" SortMemberPath="IsCheck">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <CheckBox HorizontalAlignment="Center" IsChecked="{Binding IsCheck, UpdateSourceTrigger=PropertyChanged}" Checked="CheckBox_Checked" Unchecked="CheckBox_Checked"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Binding="{Binding Name}" Header="Sheet" MinWidth="200" IsReadOnly="True"/>
            </DataGrid.Columns>
        </DataGrid>
        <CheckBox Grid.Row="4" Content="Include floating information"
                  IsChecked="{Binding IncludeFloatingInformation}"
                  VerticalAlignment="Center" HorizontalAlignment="Left"/>
        <StackPanel Margin="0,5,0,0" Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="Export" Width="100" Command="{Binding ExportCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
            <Button Margin="5,0,0,0" Content="Cancel" Width="100" Command="{Binding CancelCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
        </StackPanel>
    </Grid>
</Window>


