﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.ExportCAD.Views;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ExportCAD
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class ExportCADCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var window = new ExportCADView();
            window.ShowDialog();

            return Result.Succeeded;
        }
    }
}
