﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.Models;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System.IO;
using System.Text;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.ViewModels
{
    public class BaseUnitReplacementViewModel : WindowViewModel
    {
        public BaseUnitReplacementViewModel() // Empty ctor for xaml
        {
            
        }

        public BaseUnitReplacementViewModel(List<BoxUnitsIntersection> intersections)
        {
            Intersections = intersections
                .OrderBy(i => i.Correct)
                .ThenBy(c => c.Level)
                .ThenBy(c => c.Box?.Id.GetIntegerValue())
                .ToList();

            ExportCommand = new RelayCommand(OnExportCommandExecute);
            IdCommand = new RelayCommand(OnIdCommandExecute);
            CloseCommand = new RelayCommand(OnCloseCommandExecute);
        }

        public List<BoxUnitsIntersection> Intersections { get; private set; }

        public ICommand ExportCommand { get; set; }
        private void OnExportCommandExecute(object p)
        {
            (p as Window).Close();
            var report = CreateReportForExcel(Intersections);
            WriteToCSV(report);
        }

        public ICommand IdCommand { get; set; }
        private void OnIdCommandExecute(object p)
        {
            if (int.TryParse(p.ToString(), out int id))
            {
                if (id != 0 && RevitManager.Document.GetElement(new ElementId(id)) != null)
                {
                    var elementIds = new List<ElementId>() { new ElementId(id) };
                    RevitManager.UIDocument.Selection.SetElementIds(elementIds);
                    RevitManager.UIDocument.ShowElements(elementIds);
                }
            }
        }

        public ICommand CloseCommand { get; set; }
        private void OnCloseCommandExecute(object p)
        {
            (p as Window).Close();
        }

        private string CreateReportForExcel(List<BoxUnitsIntersection> intersections)
        {
            var report = new StringBuilder();
            report.AppendLine(string.Format("Correct,Box Id,Box Family,Box Type,Level,Unit Ids,Unit Families,Unit Types"));
            foreach (var intersection in intersections)
            {
                report.AppendLine(string.Format("{0},{1},{2},{3},{4},{5},{6},{7}",
                    intersection.Correct,
                    intersection.Box.Id.GetIntegerValue(),
                    intersection.Box.Symbol.FamilyName,
                    intersection.Box.Symbol.Name,
                    intersection.Box.get_Parameter(BuiltInParameter.FAMILY_LEVEL_PARAM)?.AsValueString(),
                    string.Join(" | ", intersection.Units.Select(u => u.Id.GetIntegerValue())),
                    string.Join(" | ", intersection.Units.Select(u => u.Symbol.FamilyName)),
                    string.Join(" | ", intersection.Units.Select(u => u.Symbol.Name))));
            }

            return report.ToString();
        }

        private void WriteToCSV(string report)
        {
            var dialog = new SaveFileDialog();
            dialog.Title = "Export Report";
            dialog.FileName = $"Base Unit Replacement Report_{RevitManager.Document.Title}_{DateTime.Now.ToString("yyMMdd_HHmm")}.csv";
            dialog.Filter = "CSV Files (*.csv)|*.csv|All Files (*.*)|*.*";

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                using (var writer = new StreamWriter(dialog.FileName))
                {
                    writer.WriteLine(report);
                }
            }
        }
    }
}
