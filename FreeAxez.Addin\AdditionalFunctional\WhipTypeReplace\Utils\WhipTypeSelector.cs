﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.WhipTypeReplace.Utils
{
    public class WhipTypeSelector
    {
        private Dictionary<int, ElementType> _whipTypeByLength;


        public WhipTypeSelector()
        {
            _whipTypeByLength = GetTypeByLengthDict();
        }


        public bool ProjectHasNotTypes => _whipTypeByLength.Count == 0;


        public ElementType GetWhipType(double length)
        {
            var roundedLength = (int)Math.Ceiling(length);
            if (_whipTypeByLength.ContainsKey(roundedLength))
            {
                return _whipTypeByLength[roundedLength];
            }

            return null;
        }

        private Dictionary<int, ElementType> GetTypeByLengthDict()
        {
            var flexPipeTypes = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(FlexPipeType))
                .ToElements()
                .Cast<ElementType>();

            var typeByLength = new Dictionary<int, ElementType>();
            foreach (var type in flexPipeTypes)
            {
                if (Regex.IsMatch(type.Name, "-\\d+'$"))
                {
                    var typeLengthString = type.Name.Split('-').Last().Replace("'", "");  // -08' 10' 12' 14' 16' 20'
                    int.TryParse(typeLengthString, out int typeLength);
                    typeByLength.Add(typeLength, type);
                }
            }

            return typeByLength;
        }
    }
}
