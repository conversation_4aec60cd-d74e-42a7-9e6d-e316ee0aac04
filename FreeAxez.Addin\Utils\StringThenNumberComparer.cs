﻿using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.Utils
{
    public class StringThenNumberComparer : IComparer<string>
    {
        public int Compare(string x, string y)
        {
            var xTextOnly = Regex.Replace(x, @"\d", "").Trim();
            var yTextOnly = Regex.Replace(y, @"\d", "").Trim();
            if (xTextOnly != yTextOnly) return xTextOnly.CompareTo(yTextOnly);

            var xNumbers = GetNumbersFromString(x);
            var yNumbers = GetNumbersFromString(y);
            for (int i = 0; i < Math.Min(xNumbers.Count, yNumbers.Count); i++)
            {
                if (xNumbers[i] != yNumbers[i])
                    return xNumbers[i].CompareTo(yNumbers[i]);
            }

            return xNumbers.Count.CompareTo(yNumbers.Count);


            List<int> GetNumbersFromString(string input)
            {
                List<int> numbers = new List<int>();
                foreach (Match match in Regex.Matches(input, @"\d+"))
                {
                    numbers.Add(int.Parse(match.Value));
                }
                return numbers;
            }
        }
    }
}
