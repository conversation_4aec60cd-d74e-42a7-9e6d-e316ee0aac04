﻿using System.Diagnostics;
using System.IO;
using System.Text;
using System.Windows.Forms;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Converters;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using Color = System.Drawing.Color;
using View = Autodesk.Revit.DB.View;

namespace FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel;

[Transaction(TransactionMode.Manual)]
public class ExportViewTemplatesToExcelCommand : IExternalCommand
{
    private readonly Dictionary<string, Color> _paramColors = new()
    {
        { "View Scale", Color.FromArgb(222, 224, 246) },
        { "Model Display", Color.FromArgb(204, 255, 204) },
        { "Detail Level", Color.FromArgb(204, 204, 255) },
        { "Parts Visibility", Color.FromArgb(255, 229, 204) },
        { "Underlay Orientation", Color.FromArgb(229, 204, 255) },
        { "Orientation", Color.FromArgb(204, 255, 255) },
        { "Phase Filter", Color.FromArgb(255, 255, 204) },
        { "Discipline", Color.FromArgb(255, 204, 229) },
        { "Show Hidden Lines", Color.FromArgb(224, 224, 224) },
        { "View Size", Color.FromArgb(255, 224, 192) },
        { "View Category", Color.FromArgb(224, 242, 241) },
        { "Category", Color.FromArgb(242, 221, 240) }
    };

    private readonly List<ViewParameterInfo> _parameterInfos = new()
    {
        new ViewParameterInfo("View Scale", BuiltInParameter.VIEW_SCALE),
        new ViewParameterInfo("Model Display", BuiltInParameter.GRAPHIC_DISPLAY_OPTIONS_MODEL),
        new ViewParameterInfo("Detail Level", BuiltInParameter.VIEW_DETAIL_LEVEL),
        new ViewParameterInfo("Parts Visibility", BuiltInParameter.VIEW_PARTS_VISIBILITY),
        new ViewParameterInfo("Underlay Orientation", BuiltInParameter.VIEW_UNDERLAY_ORIENTATION),
        new ViewParameterInfo("Orientation", BuiltInParameter.PLAN_VIEW_NORTH),
        new ViewParameterInfo("Phase Filter", BuiltInParameter.VIEW_PHASE_FILTER),
        new ViewParameterInfo("Discipline", BuiltInParameter.VIEW_DISCIPLINE),
        new ViewParameterInfo("Show Hidden Lines", BuiltInParameter.VIEW_SHOW_HIDDEN_LINES),
        new ViewParameterInfo("View Size", "View Size"),
        new ViewParameterInfo("View Category", "View Category"),
        new ViewParameterInfo("Category", "Category")
    };

    private readonly Dictionary<string, List<string>> _possibleValues = new()
    {
        { "Underlay Orientation", new List<string> { "Look down", "Look up" } },
        { "Orientation", new List<string> { "Project North", "True North" } },
        { "Discipline", new List<string> { "Architectural", "Structural", "Mechanical", "Electrical" } },
        { "Show Hidden Lines", new List<string> { "None", "By Discipline", "All" } }
    };

    public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
    {
        try
        {
            var folderPath = SelectFolder("Select folder to export view templates");
            if (string.IsNullOrEmpty(folderPath)) return Result.Cancelled;

            var timeStamp = DateTime.Now.ToString("yyyyMMdd_HHmm");
            var doc = commandData.Application.ActiveUIDocument.Document;

            var excelPath = Path.Combine(folderPath, $"{doc.Title}_ViewTemplates_{timeStamp}.xlsx");


            var viewTemplates = new FilteredElementCollector(doc)
                .OfClass(typeof(View))
                .Cast<View>()
                .Where(v => v.IsTemplate)
                .ToList();

            if (viewTemplates.Count == 0) return Result.Cancelled;

            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using (var package = new ExcelPackage())
            {
                var dataSheet = package.Workbook.Worksheets.Add("Data");
                BuildDataSheet(dataSheet, doc);

                var ws = package.Workbook.Worksheets.Add("View Templates");
                BuildMainSheetHeader(ws);
                FillViewTemplateData(ws, dataSheet, viewTemplates, doc);

                AddCategoriesSheet(package, viewTemplates, doc);

                AddAnnotationCategoriesSheet(package, viewTemplates, doc);

                AddFiltersSheet(package, viewTemplates, doc);

                AddFilterRulesSheet(package, viewTemplates, doc);

                AddRenameViewTemplatesSheet(package, viewTemplates);

                AddRenameFiltersSheet(package, doc);

                package.Workbook.Worksheets.MoveToEnd("Data");

                package.SaveAs(new FileInfo(excelPath));
            }

            try
            {
                var startInfo = new ProcessStartInfo()
                {
                    FileName = excelPath,
                    UseShellExecute = true
                };

                Process.Start(startInfo);
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog("Error Opening File", "Failed to open the exported file: " + ex.Message, MessageType.Error);
            }

            return Result.Succeeded;
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog($"{ex.Message}\n\nDetails: {ex}", Infrastructure.UI.Enums.MessageType.Error);
            return Result.Failed;
        }
    }

    private string SelectFolder(string description)
    {
        using var dlg = new FolderBrowserDialog { Description = description };
        return dlg.ShowDialog() == DialogResult.OK ? dlg.SelectedPath : string.Empty;
    }

    private void BuildDataSheet(ExcelWorksheet dataSheet, Document doc)
    {
        FillDataSheetColumn(dataSheet, 1, "Category", doc, "Category");
        FillDataSheetColumn(dataSheet, 2, "View Category", doc, "View Category");
        FillDataSheetColumn(dataSheet, 3, "View Size", doc, "View Size");

        dataSheet.Cells[dataSheet.Dimension.Address].AutoFitColumns();
    }

    private void FillDataSheetColumn(
        ExcelWorksheet sheet,
        int colIndex,
        string header,
        Document doc,
        string paramName)
    {
        sheet.Cells[1, colIndex].Value = header;
        sheet.Cells[1, colIndex].Style.Font.Bold = true;

        var values = new FilteredElementCollector(doc)
            .OfClass(typeof(View))
            .Cast<View>()
            .Where(v => v.IsTemplate)
            .Select(v => {
                var param = v.LookupParameter(paramName);
                var value = param?.AsString();
                return !string.IsNullOrEmpty(value) ? value : param?.AsValueString() ?? "Unknown";
            })
            .Distinct()
            .OrderBy(x => x)
            .ToList();

        var row = 2;
        foreach (var val in values) sheet.Cells[row++, colIndex].Value = val;
    }

    private void BuildMainSheetHeader(ExcelWorksheet ws)
    {
        ws.Cells[1, 1, 2, 1].Merge = true;
        ws.Cells[1, 1].Value = "View Template";
        ws.Cells[1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        ws.Cells[1, 1].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
        ws.Cells[1, 1].Style.Font.Bold = true;

        ws.Cells[1, 2, 2, 2].Merge = true;
        ws.Cells[1, 2].Value = "View Type";
        ws.Cells[1, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        ws.Cells[1, 2].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
        ws.Cells[1, 2].Style.Font.Bold = true;

        var startCol = 3;
        for (var i = 0; i < _parameterInfos.Count; i++)
        {
            var info = _parameterInfos[i];
            var colVal = startCol + i * 2;
            var colInc = colVal + 1;

            ws.Cells[1, colVal, 1, colInc].Merge = true;
            ws.Cells[1, colVal].Value = info.Name;
            ws.Cells[1, colVal].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            ws.Cells[1, colVal].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            ws.Cells[1, colVal].Style.Font.Bold = true;

            if (_paramColors.TryGetValue(info.Name, out var color))
            {
                ws.Cells[1, colVal, 1, colInc].Style.Fill.PatternType = ExcelFillStyle.Solid;
                ws.Cells[1, colVal, 1, colInc].Style.Fill.BackgroundColor.SetColor(color);
            }

            ws.Cells[2, colVal].Value = "Value";
            ws.Cells[2, colInc].Value = "Include";
            ws.Cells[2, colVal, 2, colInc].Style.Font.Bold = true;
            ws.Cells[2, colVal, 2, colInc].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            ws.Cells[2, colVal, 2, colInc].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
        }

        ws.View.FreezePanes(3, 3);
    }

    private void FillViewTemplateData(
        ExcelWorksheet ws,
        ExcelWorksheet dataSheet,
        List<View> templates,
        Document doc)
    {
        var row = 3;
        var startCol = 3;

        foreach (var t in templates)
        {
            var nonControlled = t.GetNonControlledTemplateParameterIds();
            ws.Cells[row, 1].Value = t.Name;
            ws.Cells[row, 2].Value = t.ViewType.ToString();

            for (var i = 0; i < _parameterInfos.Count; i++)
            {
                var info = _parameterInfos[i];
                var cVal = startCol + i * 2;
                var cInc = cVal + 1;
                ProcessParameter(ws, dataSheet, t, info, row, cVal, cInc, doc, nonControlled);
            }

            ws.Cells[row, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            ws.Cells[row, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            row++;
        }

        var lastRow = row - 1;
        var lastCol = startCol + _parameterInfos.Count * 2 - 1;
        ws.Cells[2, 1, lastRow, lastCol].AutoFilter = true;

        for (var r = 3; r <= lastRow; r++)
            if (r % 2 == 1)
            {
                ws.Cells[r, 1, r, lastCol].Style.Fill.PatternType = ExcelFillStyle.Solid;
                ws.Cells[r, 1, r, lastCol].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(242, 242, 242));
            }

        ws.Cells[ws.Dimension.Address].AutoFitColumns();

        for (var c = 5; c <= 20; c++)
        {
            var currentWidth = ws.Column(c).Width;
            ws.Column(c).Width = currentWidth + 2.0;
        }

        ws.Cells[row, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
    }

    private void ProcessParameter(
        ExcelWorksheet ws,
        ExcelWorksheet dataSheet,
        View template,
        ViewParameterInfo info,
        int row,
        int colVal,
        int colInc,
        Document doc,
        ICollection<ElementId> nonControlledIds)
    {
        var addrVal = ws.Cells[row, colVal].Address;
        Parameter param;
        if (info.BuiltInParameter != BuiltInParameter.INVALID)
            param = template.get_Parameter(info.BuiltInParameter);
        else
            param = template.LookupParameter(info.ParamName);

        var display = param?.AsValueString() ?? param?.AsString() ?? "-";
        var isControlled = true;

        if (info.BIPelementId != ElementId.InvalidElementId)
            isControlled = !nonControlledIds.Contains(info.BIPelementId);
        else if (param != null) isControlled = !nonControlledIds.Contains(param.Id);

        switch (info.Name)
        {
            case "Model Display":
                display = DisplayStyleConverter.ConvertDisplayStyle(template.DisplayStyle);
                DisplayStyleConverter.AddDisplayStyleValidation(ws, addrVal);
                break;

            case "Parts Visibility":
                display = PartsVisibilityConverter.ConvertPartsVisibility(template.PartsVisibility);
                PartsVisibilityConverter.AddPartsVisibilityValidation(ws, addrVal);
                break;

            case "Detail Level":
                display = DetailLevelConverter.ConvertDetailLevel(template.DetailLevel);
                DetailLevelConverter.AddDetailLevelValidation(ws, addrVal);
                break;

            case "View Scale":
                if (int.TryParse(param?.AsValueString(), out var scaleVal))
                    display = ScaleConverter.ConvertScale(scaleVal);
                else
                    display = "Custom";
                ScaleConverter.AddScaleValidation(ws, addrVal);
                break;

            case "Phase Filter":
                var phFilterParam = template.get_Parameter(BuiltInParameter.VIEW_PHASE_FILTER);
                display = phFilterParam?.AsValueString() ?? "Unknown";
                var nc = phFilterParam == null ? false : nonControlledIds.Contains(phFilterParam.Id);
                isControlled = !nc;

                var phaseFilters = new FilteredElementCollector(doc)
                    .OfClass(typeof(PhaseFilter))
                    .Cast<PhaseFilter>()
                    .Select(x => x.Name)
                    .OrderBy(x => x)
                    .ToList();

                AddListValidationFromList(ws, addrVal, phaseFilters, "Invalid phase filter", "Select valid filter");
                break;
        }

        if (_possibleValues.TryGetValue(info.Name, out var listVals))
            AddListValidationFromList(ws, addrVal, listVals, "Invalid value", "Select from list");

        if (info.Name == "Category") AddListValidationFromDataSheet(ws, addrVal, dataSheet, 1);
        if (info.Name == "View Category") AddListValidationFromDataSheet(ws, addrVal, dataSheet, 2);
        if (info.Name == "View Size") AddListValidationFromDataSheet(ws, addrVal, dataSheet, 3);

        ws.Cells[row, colVal].Value = display;
        ws.Cells[row, colInc].Value = isControlled ? "Yes" : "No";

        AddYesNoValidation(ws, ws.Cells[row, colInc].Address);

        ws.Cells[row, colVal].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        ws.Cells[row, colInc].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
    }

    private void AddListValidationFromList(
        ExcelWorksheet ws,
        string cellAddr,
        List<string> items,
        string errTitle,
        string errMsg)
    {
        var dv = ws.DataValidations.AddListValidation(cellAddr);
        foreach (var it in items)
            dv.Formula.Values.Add(it);
        dv.AllowBlank = true;
        dv.ShowErrorMessage = true;
        dv.ErrorTitle = errTitle;
        dv.Error = errMsg;
    }

    private void AddYesNoValidation(ExcelWorksheet ws, string cellAddr)
    {
        var dv = ws.DataValidations.AddListValidation(cellAddr);
        dv.Formula.Values.Add("Yes");
        dv.Formula.Values.Add("No");
        dv.AllowBlank = true;
        dv.ShowErrorMessage = true;
        dv.ErrorTitle = "Invalid Include";
        dv.Error = "Select Yes/No";
    }

    private void AddListValidationFromDataSheet(
        ExcelWorksheet ws,
        string cellAddr,
        ExcelWorksheet dataSheet,
        int columnIndex)
    {
        var totalRows = dataSheet.Cells[dataSheet.Dimension.Address]
            .Where(c => c.Start.Column == columnIndex && c.Value != null)
            .Count();

        var colName = GetExcelColumnName(columnIndex);
        var formula = $"'{dataSheet.Name}'!${colName}$2:${colName}${totalRows}";

        var dv = ws.DataValidations.AddListValidation(cellAddr);
        dv.Formula.ExcelFormula = formula;
        dv.AllowBlank = true;
        dv.ShowErrorMessage = true;
        dv.ErrorTitle = "Invalid value";
        dv.Error = "Select from Data sheet";
    }

    private string GetExcelColumnName(int colNumber)
    {
        var dividend = colNumber;
        var colName = string.Empty;

        while (dividend > 0)
        {
            var mod = (dividend - 1) % 26;
            colName = Convert.ToChar(65 + mod) + colName;
            dividend = (dividend - mod) / 26;
        }

        return colName;
    }

    private void AddFiltersSheet(
        ExcelPackage package,
        List<View> viewTemplates,
        Document doc)
    {
        var ws = package.Workbook.Worksheets.Add("Filters");

        string[] headers =
        {
            "View Template",
            "Filter Name",
            "Enable\nFilter",
            "Visibility",
            "Projection\nLine Color",
            "Projection\nLine Pattern",
            "Projection\nLine Weight",
            "Surface\nForeground Color",
            "Surface\nForeground Pattern",
            "Surface\nBackground Color",
            "Surface\nBackground Pattern",
            "Transparency",
            "Cut\nLine Color",
            "Cut\nLine Pattern",
            "Cut\nLine Weight",
            "Cut Foreground\nPattern Color",
            "Cut Foreground\nPattern",
            "Cut Background\nPattern Color",
            "Cut Background\nPattern",
            "Halftone"
        };

        for (var col = 1; col <= headers.Length; col++)
        {
            ws.Cells[1, col].Value = headers[col - 1];
            ws.Cells[1, col].Style.Font.Bold = true;
            ws.Cells[1, col].Style.WrapText = true;
            ws.Cells[1, col].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            ws.Cells[1, col].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
        }

        var currentRow = 2;
        foreach (var vt in viewTemplates)
        {
            var filterIds = vt.GetFilters();
            if (filterIds == null || filterIds.Count == 0)
                continue;

            foreach (var fid in filterIds)
            {
                var filterElem = doc.GetElement(fid) as ParameterFilterElement;
                var filterName = filterElem?.Name ?? "(Unknown Filter)";

                ws.Cells[currentRow, 1].Value = vt.Name; // View Template
                ws.Cells[currentRow, 2].Value = filterName; // Filter Name

                var enabled = IsFilterEnabled(vt, fid);
                ws.Cells[currentRow, 3].Value = enabled ? "Yes" : "No";

                var visible = vt.GetFilterVisibility(fid);
                ws.Cells[currentRow, 4].Value = visible ? "Yes" : "No";

                var ovs = vt.GetFilterOverrides(fid);

                SetColorCell(ws, currentRow, 5, ovs.ProjectionLineColor);
                ws.Cells[currentRow, 6].Value = GetLinePatternName(doc, ovs.ProjectionLinePatternId);
                ws.Cells[currentRow, 7].Value = ovs.ProjectionLineWeight < 0 ? "" : ovs.ProjectionLineWeight.ToString();

                SetColorCell(ws, currentRow, 8, ovs.SurfaceForegroundPatternColor);
                ws.Cells[currentRow, 9].Value = GetFillPatternName(doc, ovs.SurfaceForegroundPatternId);
                SetColorCell(ws, currentRow, 10, ovs.SurfaceBackgroundPatternColor);
                ws.Cells[currentRow, 11].Value = GetFillPatternName(doc, ovs.SurfaceBackgroundPatternId);

                ws.Cells[currentRow, 12].Value = ovs.Transparency < 0 ? "" : ovs.Transparency.ToString();

                SetColorCell(ws, currentRow, 13, ovs.CutLineColor);
                ws.Cells[currentRow, 14].Value = GetLinePatternName(doc, ovs.CutLinePatternId);
                ws.Cells[currentRow, 15].Value = ovs.CutLineWeight < 0 ? "" : ovs.CutLineWeight.ToString();

                SetColorCell(ws, currentRow, 16, ovs.CutForegroundPatternColor);
                ws.Cells[currentRow, 17].Value = GetFillPatternName(doc, ovs.CutForegroundPatternId);
                SetColorCell(ws, currentRow, 18, ovs.CutBackgroundPatternColor);
                ws.Cells[currentRow, 19].Value = GetFillPatternName(doc, ovs.CutBackgroundPatternId);

                ws.Cells[currentRow, 20].Value = ovs.Halftone ? "Yes" : "No";

                // Wrap text
                for (var c = 1; c <= 20; c++)
                    ws.Cells[currentRow, c].Style.WrapText = true;

                currentRow++;
            }
        }

        var lastRow = currentRow - 1;
        ws.Cells[1, 1, lastRow, 20].AutoFilter = true;

        ws.View.FreezePanes(2, 3);

        for (var r = 2; r <= lastRow; r++)
            if (r % 2 == 0)
            {
                ws.Cells[r, 1, r, 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                ws.Cells[r, 1, r, 2].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(242, 242, 242));
            }

        ws.Cells[ws.Dimension.Address].AutoFitColumns();

        ws.Column(1).Width = 40;
        ws.Column(2).Width = 40;

        for (var c = 3; c <= 20; c++)
        {
            ws.Column(c).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            ws.Column(c).Style.VerticalAlignment = ExcelVerticalAlignment.Center;
        }

        for (var c = 5; c <= 20; c++)
        {
            var currentWidth = ws.Column(c).Width;
            ws.Column(c).Width = currentWidth + 2.0;
        }
    }

    private bool IsFilterEnabled(View view, ElementId filterId)
    {
#if revit2020
        return view.GetFilters().Contains(filterId);
#else
            return view.GetIsFilterEnabled(filterId);
#endif
    }

    private void SetColorCell(ExcelWorksheet ws, int row, int col, Autodesk.Revit.DB.Color revitColor)
    {
        if (revitColor == null || !revitColor.IsValid) return;
        var color = Color.FromArgb(revitColor.Red, revitColor.Green, revitColor.Blue);
        ws.Cells[row, col].Style.Fill.PatternType = ExcelFillStyle.Solid;
        ws.Cells[row, col].Style.Fill.BackgroundColor.SetColor(color);
    }

    private string GetLinePatternName(Document doc, ElementId patternId)
    {
        if (patternId == ElementId.InvalidElementId || patternId.GetIntegerValue() < 1) return "";
        var lpe = doc.GetElement(patternId) as LinePatternElement;
        return lpe?.Name ?? "";
    }

    private string GetFillPatternName(Document doc, ElementId patternId)
    {
        if (patternId == ElementId.InvalidElementId) return "";
        var fpe = doc.GetElement(patternId) as FillPatternElement;
        return fpe?.Name ?? "";
    }

    private void AddCategoriesSheet(
        ExcelPackage package,
        List<View> viewTemplates,
        Document doc)
    {
        var ws = package.Workbook.Worksheets.Add("Model Categories");

        var modelCategories = new List<Category>();
        foreach (Category cat in doc.Settings.Categories)
            if (cat != null
                && cat.CategoryType == CategoryType.Model
                && cat.CanAddSubcategory)
                modelCategories.Add(cat);
        modelCategories = modelCategories.OrderBy(x => x.Name).ToList();

        string[] headers =
        {
            "View Template",
            "Category Name",
            "Visible",
            "Detail Level",
            "Projection Line Color",
            "Projection Line Pattern",
            "Projection Line Weight",
            "Surface Foreground Pattern Color",
            "Surface Foreground Pattern",
            "Surface Background Pattern Color",
            "Surface Background Pattern",
            "Transparency",
            "Cut Line Color",
            "Cut Line Pattern",
            "Cut Line Weight",
            "Cut Foreground Pattern Color",
            "Cut Foreground Pattern",
            "Cut Background Pattern Color",
            "Cut Background Pattern",
            "Halftone"
        };

        for (var col = 0; col < headers.Length; col++)
        {
            ws.Cells[1, col + 1].Value = headers[col];
            ws.Cells[1, col + 1].Style.Font.Bold = true;
            ws.Cells[1, col + 1].Style.WrapText = true;
            ws.Cells[1, col + 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            ws.Cells[1, col + 1].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
        }

        var currentRow = 2;
        foreach (var vt in viewTemplates)
        foreach (var cat in modelCategories)
        {
            var catId = cat.Id;
            var isHidden = vt.GetCategoryHidden(catId);
            var ogs = vt.GetCategoryOverrides(catId);

            ws.Cells[currentRow, 1].Value = vt.Name; // View Template
            ws.Cells[currentRow, 2].Value = cat.Name; // Category name
            ws.Cells[currentRow, 3].Value = isHidden ? "No" : "Yes";

            var detail = ogs.DetailLevel;
            ws.Cells[currentRow, 4].Value = detail == ViewDetailLevel.Undefined
                ? ""
                : detail.ToString();

            SetColorCell(ws, currentRow, 5, ogs.ProjectionLineColor);
            ws.Cells[currentRow, 6].Value = GetLinePatternName(doc, ogs.ProjectionLinePatternId);
            var plw = ogs.ProjectionLineWeight;
            ws.Cells[currentRow, 7].Value = plw < 0 ? "" : plw.ToString();

            SetColorCell(ws, currentRow, 8, ogs.SurfaceForegroundPatternColor);
            ws.Cells[currentRow, 9].Value = GetFillPatternName(doc, ogs.SurfaceForegroundPatternId);
            SetColorCell(ws, currentRow, 10, ogs.SurfaceBackgroundPatternColor);
            ws.Cells[currentRow, 11].Value = GetFillPatternName(doc, ogs.SurfaceBackgroundPatternId);

            var transp = ogs.Transparency;
            ws.Cells[currentRow, 12].Value = transp < 0 ? "" : transp.ToString();

            SetColorCell(ws, currentRow, 13, ogs.CutLineColor);
            ws.Cells[currentRow, 14].Value = GetLinePatternName(doc, ogs.CutLinePatternId);
            var clw = ogs.CutLineWeight;
            ws.Cells[currentRow, 15].Value = clw < 0 ? "" : clw.ToString();

            SetColorCell(ws, currentRow, 16, ogs.CutForegroundPatternColor);
            ws.Cells[currentRow, 17].Value = GetFillPatternName(doc, ogs.CutForegroundPatternId);
            SetColorCell(ws, currentRow, 18, ogs.CutBackgroundPatternColor);
            ws.Cells[currentRow, 19].Value = GetFillPatternName(doc, ogs.CutBackgroundPatternId);

            ws.Cells[currentRow, 20].Value = ogs.Halftone ? "Yes" : "No";

            // Wrap text
            for (var c = 1; c <= 20; c++)
                ws.Cells[currentRow, c].Style.WrapText = true;

            currentRow++;
        }

        var lastRow = currentRow - 1;
        ws.Cells[1, 1, lastRow, 20].AutoFilter = true;
        ws.View.FreezePanes(2, 3);

        for (var r = 2; r <= lastRow; r++)
            if (r % 2 == 0)
            {
                ws.Cells[r, 1, r, 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                ws.Cells[r, 1, r, 2].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(242, 242, 242));
            }

        ws.Cells[ws.Dimension.Address].AutoFitColumns();
        ws.Column(1).Width = 40;
        ws.Column(2).Width = 40;

        for (var c = 3; c <= 20; c++)
        {
            ws.Column(c).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            ws.Column(c).Style.VerticalAlignment = ExcelVerticalAlignment.Center;
        }

        for (var c = 4; c <= 20; c++)
        {
            var currentWidth = ws.Column(c).Width;
            ws.Column(c).Width = currentWidth + 2.0;
        }
    }

    private void AddAnnotationCategoriesSheet(
        ExcelPackage package,
        List<View> viewTemplates,
        Document doc)
    {
        var ws = package.Workbook.Worksheets.Add("Annotation Categories");

        var annotationCategories = new List<Category>();
        foreach (Category cat in doc.Settings.Categories)
            if (cat != null && cat.CategoryType == CategoryType.Annotation)
                annotationCategories.Add(cat);
        annotationCategories = annotationCategories.OrderBy(x => x.Name).ToList();

        string[] headers =
        {
            "View Template",
            "Category Name",
            "Visible",
            "Line Color",
            "Line Pattern",
            "Line Weight",
            "Halftone"
        };

        for (var i = 0; i < headers.Length; i++)
        {
            ws.Cells[1, i + 1].Value = headers[i];
            ws.Cells[1, i + 1].Style.Font.Bold = true;
            ws.Cells[1, i + 1].Style.WrapText = true;
            ws.Cells[1, i + 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            ws.Cells[1, i + 1].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
        }

        var currentRow = 2;

        foreach (var vt in viewTemplates)
        foreach (var cat in annotationCategories)
        {
            var hidden = vt.GetCategoryHidden(cat.Id);
            var ogs = vt.GetCategoryOverrides(cat.Id);

            ws.Cells[currentRow, 1].Value = vt.Name;
            ws.Cells[currentRow, 2].Value = cat.Name;
            ws.Cells[currentRow, 3].Value = hidden ? "No" : "Yes";

            SetColorCell(ws, currentRow, 4, ogs.ProjectionLineColor);
            ws.Cells[currentRow, 5].Value = GetLinePatternName(doc, ogs.ProjectionLinePatternId);

            var lw = ogs.ProjectionLineWeight;
            ws.Cells[currentRow, 6].Value = lw < 0 ? "" : lw.ToString();

            ws.Cells[currentRow, 7].Value = ogs.Halftone ? "Yes" : "No";

            for (var col = 1; col <= headers.Length; col++)
                ws.Cells[currentRow, col].Style.WrapText = true;

            currentRow++;
        }

        var lastRow = currentRow - 1;
        ws.Cells[1, 1, lastRow, headers.Length].AutoFilter = true;
        ws.View.FreezePanes(2, 3);

        for (var r = 2; r <= lastRow; r++)
            if (r % 2 == 0)
            {
                ws.Cells[r, 1, r, 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                ws.Cells[r, 1, r, 2].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(242, 242, 242));
            }

        ws.Cells[ws.Dimension.Address].AutoFitColumns();
        ws.Column(1).Width = 40;
        ws.Column(2).Width = 40;

        for (var c = 3; c <= 20; c++)
        {
            ws.Column(c).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            ws.Column(c).Style.VerticalAlignment = ExcelVerticalAlignment.Center;
        }

        for (var c = 4; c <= 20; c++)
        {
            var currentWidth = ws.Column(c).Width;
            ws.Column(c).Width = currentWidth + 2.0;
        }
    }

    private void AddFilterRulesSheet(
        ExcelPackage package,
        List<View> viewTemplates,
        Document doc)
    {
        var allViewFilterIds = viewTemplates
            .SelectMany(v => v.GetFilters())
            .Distinct()
            .ToList();

        var filterRows = new List<FilterExcelRow>();

        foreach (var filterId in allViewFilterIds)
        {
            var pfe = doc.GetElement(filterId) as ParameterFilterElement;
            if (pfe == null) continue;

            var filterName = pfe.Name;
            var categories = pfe.GetCategories();
            var categoriesString = GetCategoriesString(doc, categories);

            var rootFilter = pfe.GetElementFilter();
            if (rootFilter == null)
                continue;

            var logicType = GetTopLevelLogicType(rootFilter);

            var rowsForThisFilter = GetRowsFromElementFilter(doc, rootFilter, categoriesString);

            foreach (var r in rowsForThisFilter)
            {
                r.FilterName = filterName;
                r.LogicType = logicType;
            }

            filterRows.AddRange(rowsForThisFilter);
        }

        var ws = package.Workbook.Worksheets.Add("Filter Rules");

        ws.Cells[1, 1].Value = "Filter";
        ws.Cells[1, 2].Value = "Logic Type";
        ws.Cells[1, 3].Value = "Category";
        ws.Cells[1, 4].Value = "Parameter";
        ws.Cells[1, 5].Value = "Evaluator";
        ws.Cells[1, 6].Value = "Storage Type";
        ws.Cells[1, 7].Value = "Value";

        using (var headerRange = ws.Cells[1, 1, 1, 7])
        {
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
            headerRange.Style.Fill.BackgroundColor.SetColor(Color.ForestGreen);
            headerRange.Style.Font.Color.SetColor(Color.White);
            headerRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        }

        ws.View.FreezePanes(2, 1);

        var groups = filterRows
            .GroupBy(r => r.FilterName)
            .OrderBy(g => g.Key)
            .ToList();

        var currentRowExcel = 2;
        var colorIndex = 0;

        foreach (var group in groups)
        {
            var groupColor = colorIndex % 2 == 0 ? Color.White : Color.LightGray;
            colorIndex++;

            var groupStart = currentRowExcel;
            foreach (var row in group)
            {
                ws.Cells[currentRowExcel, 1].Value = row.FilterName;
                ws.Cells[currentRowExcel, 2].Value = row.LogicType;
                ws.Cells[currentRowExcel, 3].Value = row.CategoryName;
                ws.Cells[currentRowExcel, 4].Value = row.ParamName;
                ws.Cells[currentRowExcel, 5].Value = row.Evaluator;
                ws.Cells[currentRowExcel, 6].Value = row.StorageType;
                ws.Cells[currentRowExcel, 7].Value = row.Value;
                currentRowExcel++;
            }

            var groupEnd = currentRowExcel - 1;

            var groupRange = ws.Cells[groupStart, 1, groupEnd, 7];
            groupRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
            groupRange.Style.Fill.BackgroundColor.SetColor(groupColor);
        }

        var lastRow = currentRowExcel - 1;
        if (lastRow >= 1) ws.Cells[1, 1, lastRow, 7].AutoFilter = true;

        // 7) AutoFit
        ws.Cells[1, 1, lastRow, 7].AutoFitColumns();

        var tableRange = ws.Cells[1, 1, lastRow, 7];
        tableRange.Style.Border.Top.Style = ExcelBorderStyle.None;
        tableRange.Style.Border.Left.Style = ExcelBorderStyle.None;
        tableRange.Style.Border.Right.Style = ExcelBorderStyle.None;
        tableRange.Style.Border.Bottom.Style = ExcelBorderStyle.None;
    }

    private string GetTopLevelLogicType(ElementFilter rootFilter)
    {
        if (rootFilter is LogicalAndFilter)
            return "AND (All rules must be true)";
        if (rootFilter is LogicalOrFilter)
            return "OR (At least one rule must be true)";
        return "N/A";
    }

    private List<FilterExcelRow> GetRowsFromElementFilter(Document doc, ElementFilter filter, string categoriesString)
    {
        var result = new List<FilterExcelRow>();

        if (filter is LogicalAndFilter andFilter)
        {
            foreach (var sf in andFilter.GetFilters())
                result.AddRange(GetRowsFromElementFilter(doc, sf, categoriesString));
        }
        else if (filter is LogicalOrFilter orFilter)
        {
            foreach (var sf in orFilter.GetFilters())
                result.AddRange(GetRowsFromElementFilter(doc, sf, categoriesString));
        }
        else if (filter is ElementParameterFilter paramFilter)
        {
            var rules = paramFilter.GetRules();
            foreach (var rule in rules)
            {
                var rows = ParseFilterRule(doc, rule, false, categoriesString);
                result.AddRange(rows);
            }
        }

        return result;
    }

    private List<FilterExcelRow> ParseFilterRule(
        Document doc,
        FilterRule rule,
        bool isInverted,
        string categoriesString)
    {
        var rows = new List<FilterExcelRow>();

        if (rule is FilterInverseRule invRule)
        {
            var inner = invRule.GetInnerRule();
            var innerRows = ParseFilterRule(doc, inner, !isInverted, categoriesString);
            rows.AddRange(innerRows);
        }
        else if (rule is FilterCategoryRule)
        {
            rows.Add(new FilterExcelRow
            {
                CategoryName = categoriesString,
                Evaluator = isInverted ? "NOT (Category)" : "Category",
                Value = "",
                StorageType = GetStorageType(rule)
            });
        }
        else if (rule is FilterValueRule valRule)
        {
            rows.AddRange(ParseFilterValueRule(doc, valRule, isInverted, categoriesString));
        }
        else if (rule is ParameterValuePresenceRule presenceRule)
        {
            var hasValue = presenceRule is HasValueFilterRule;
            var paramId = presenceRule.Parameter;
            var paramName = GetParameterName(doc, paramId);

            // hasValue => "Has value", иначе "Has no value"
            string eval;
            if (hasValue && !isInverted) eval = "Has value";
            else if (hasValue && isInverted) eval = "NOT (Has value)";
            else if (!hasValue && !isInverted) eval = "Has no value";
            else eval = "NOT (Has no value)";

            rows.Add(new FilterExcelRow
            {
                CategoryName = categoriesString,
                ParamName = paramName,
                Evaluator = eval,
                Value = "",
                StorageType = GetStorageType(rule)
            });
        }
        else if (rule is SharedParameterApplicableRule sharedRule)
        {
            var paramName = sharedRule.ParameterName;

            rows.Add(new FilterExcelRow
            {
                CategoryName = categoriesString,
                ParamName = paramName,
                Evaluator = isInverted ? "NOT (Shared Param Applicable)" : "Shared Param Applicable",
                Value = "",
                StorageType = GetStorageType(rule)
            });
        }
        else
        {
            rows.Add(new FilterExcelRow
            {
                CategoryName = categoriesString,
                Evaluator = isInverted ? "NOT (Unknown)" : "(Unknown rule)",
                Value = "",
                StorageType = GetStorageType(rule)
            });
        }

        return rows;
    }

    private List<FilterExcelRow> ParseFilterValueRule(
        Document doc,
        FilterValueRule valRule,
        bool isInverted,
        string categoriesString)
    {
        var rows = new List<FilterExcelRow>();

        var paramId = valRule.GetRuleParameter();
        var paramName = GetParameterName(doc, paramId);

        var row = new FilterExcelRow
        {
            ParamName = paramName,
            CategoryName = categoriesString
        };

        if (valRule is FilterStringRule sRule)
        {
            var ruleValue = sRule.RuleString;
            var evalText = GetStringEvaluatorName(sRule.GetEvaluator());
            if (isInverted) evalText = "NOT " + evalText;

            row.Evaluator = evalText;
            row.Value = ruleValue;
            row.StorageType = GetStorageType(valRule);
        }
        else if (valRule is FilterDoubleRule doubleRule)
        {
            var dVal = doubleRule.RuleValue;
            var evalText = GetNumericEvaluatorName(doubleRule.GetEvaluator());
            if (isInverted) evalText = "NOT " + evalText;

            row.Evaluator = evalText;
            row.Value = dVal.ToString("F3");
            row.StorageType = GetStorageType(valRule);
        }
        else if (valRule is FilterIntegerRule intRule)
        {
            var iVal = intRule.RuleValue;
            var evalText = GetNumericEvaluatorName(intRule.GetEvaluator());
            if (isInverted) evalText = "NOT " + evalText;

            row.Evaluator = evalText;
            row.Value = iVal.ToString();
            row.StorageType = GetStorageType(valRule);
        }
        else if (valRule is FilterElementIdRule eIdRule)
        {
            var eid = eIdRule.RuleValue;
            var evalText = GetNumericEvaluatorName(eIdRule.GetEvaluator());
            if (isInverted) evalText = "NOT " + evalText;

            row.Evaluator = evalText;
            var el = doc.GetElement(eid);
            row.Value = el != null ? el.Name : $"#{el.Id.GetIntegerValue()}";
            row.StorageType = GetStorageType(valRule);
        }
        else
        {
            // fallback
            var evalText = isInverted ? "NOT (NumericValueRule)" : "(NumericValueRule)";
            row.Evaluator = evalText;
            row.Value = "";
            row.StorageType = GetStorageType(valRule);
        }

        rows.Add(row);
        return rows;
    }

    private string GetParameterName(Document doc, ElementId paramId)
    {
        if (paramId == ElementId.InvalidElementId)
            return "<Invalid parameter>";

        var intVal = paramId.GetIntegerValue();
        if (Enum.IsDefined(typeof(BuiltInParameter), intVal))
        {
            var bip = (BuiltInParameter)intVal;
            return LabelUtils.GetLabelFor(bip);
        }

        var paramElem = doc.GetElement(paramId);
        if (paramElem != null)
            return paramElem.Name;

        return $"Param#{intVal}";
    }

    private string GetStorageType(FilterRule rule)
    {
        if (rule is FilterStringRule) return "String";
        if (rule is FilterDoubleRule) return "Double";
        if (rule is FilterIntegerRule) return "Integer";
        if (rule is FilterElementIdRule) return "ElementId";
        if (rule is FilterGlobalParameterAssociationRule) return "ElementId (Global Param)";
        if (rule is ParameterValuePresenceRule) return "Presence Check";
        if (rule is SharedParameterApplicableRule) return "Shared Param Check";
        if (rule is FilterCategoryRule) return "Category";
        return "Unknown";
    }

    private string GetStringEvaluatorName(FilterStringRuleEvaluator evaluator)
    {
        return evaluator switch
        {
            FilterStringBeginsWith _ => "BeginsWith",
            FilterStringContains _ => "Contains",
            FilterStringEndsWith _ => "EndsWith",
            FilterStringEquals _ => "Equals",
            FilterStringGreater _ => "Greater (lexicographic)",
            FilterStringGreaterOrEqual _ => "GreaterOrEqual",
            FilterStringLess _ => "Less (lexicographic)",
            FilterStringLessOrEqual _ => "LessOrEqual",
            _ => "(StringRule)"
        };
    }

    private string GetNumericEvaluatorName(FilterNumericRuleEvaluator evaluator)
    {
        return evaluator switch
        {
            FilterNumericEquals _ => "Equals",
            FilterNumericGreater _ => "Greater",
            FilterNumericGreaterOrEqual _ => "Greater Or Equal",
            FilterNumericLess _ => "Less",
            FilterNumericLessOrEqual _ => "Less Or Equal",
            _ => "(NumericRule)"
        };
    }

    private string GetCategoriesString(Document doc, ICollection<ElementId> categories)
    {
        if (categories == null || categories.Count == 0)
            return "";

        var sb = new StringBuilder();
        foreach (var catId in categories)
        {
            var cat = Category.GetCategory(doc, catId);
            var catName = cat?.Name ?? $"#{catId.GetIntegerValue()}";
            if (sb.Length > 0) sb.Append(", ");
            sb.Append(catName);
        }

        return sb.ToString();
    }

    private void AddRenameViewTemplatesSheet(ExcelPackage package, List<View> viewTemplates)
    {
        var ws = package.Workbook.Worksheets.Add("Rename View Templates");
        ws.Cells[1, 1].Value = "Current Name";
        ws.Cells[1, 2].Value = "New Name";
        ws.Cells["A1:B1"].Style.Font.Bold = true;
        int row = 2;
        foreach (var vt in viewTemplates)
        {
            ws.Cells[row, 1].Value = vt.Name;
            row++;
        }
        ws.Cells[ws.Dimension.Address].AutoFitColumns();
    }

    private void AddRenameFiltersSheet(ExcelPackage package, Document doc)
    {
        var filters = new FilteredElementCollector(doc)
            .OfClass(typeof(ParameterFilterElement))
            .Cast<ParameterFilterElement>()
            .OrderBy(f => f.Name)
            .ToList();

        var ws = package.Workbook.Worksheets.Add("Rename Filters");
        ws.Cells[1, 1].Value = "Current Name";
        ws.Cells[1, 2].Value = "New Name";
        ws.Cells["A1:B1"].Style.Font.Bold = true;

        int row = 2;
        foreach (var f in filters)
        {
            ws.Cells[row, 1].Value = f.Name;
            row++;
        }
        ws.Cells[ws.Dimension.Address].AutoFitColumns();
    }

    private class ViewParameterInfo
    {
        public ViewParameterInfo(string name, BuiltInParameter bip)
        {
            Name = name;
            BuiltInParameter = bip;
            BIPelementId = bip == BuiltInParameter.INVALID
                ? ElementId.InvalidElementId
                : new ElementId((int)bip);
        }

        public ViewParameterInfo(string name, string paramName)
        {
            Name = name;
            ParamName = paramName;
            BIPelementId = ElementId.InvalidElementId;
        }

        public string Name { get; }
        public BuiltInParameter BuiltInParameter { get; } = BuiltInParameter.INVALID;
        public string ParamName { get; }
        public ElementId BIPelementId { get; }
    }

    private class FilterExcelRow
    {
        public string FilterName { get; set; }
        public string LogicType { get; set; }
        public string CategoryName { get; set; }
        public string ParamName { get; set; }
        public string Evaluator { get; set; }
        public string StorageType { get; set; }
        public string Value { get; set; }
    }
}