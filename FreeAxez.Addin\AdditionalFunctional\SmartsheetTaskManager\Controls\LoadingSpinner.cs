﻿using System.Windows;
using System.Windows.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Controls;

public class LoadingSpinner : Control
{
    public static readonly DependencyProperty IsExecutingProperty =
        DependencyProperty.Register("IsExecuting", typeof(bool), typeof(LoadingSpinner),
            new PropertyMetadata(false));

    public bool IsExecuting
    {
        get { return (bool)GetValue(IsExecutingProperty); }
        set { SetValue(IsExecutingProperty, value); }
    }

    static LoadingSpinner()
    {
        DefaultStyleKeyProperty.OverrideMetadata(
            typeof(LoadingSpinner), new FrameworkPropertyMetadata(typeof(LoadingSpinner)));
    }
}
