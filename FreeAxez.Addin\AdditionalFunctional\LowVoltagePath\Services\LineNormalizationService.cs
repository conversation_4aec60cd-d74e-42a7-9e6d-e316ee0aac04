using NetTopologySuite.Geometries;
using NetTopologySuite.Operation.Linemerge;
using NetTopologySuite.Operation.Distance;
using NetTopologySuite.Noding;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services;

public class LineNormalizationService
{
    private const double TOLERANCE = 2.0 / 12.0; // 2 inches

    /// <summary>
    /// Normalizes a list of raw lines by merging, fixing undershoots, splitting, merging nodes, simplifying, and removing short lines.
    /// </summary>
    public List<LineString> NormalizeLines(List<LineString> rawLines, double minSegmentLength)
    {
        var lines = MergeLines(rawLines);
        lines = FixUndershotLines(lines);
        lines = SplitLines(lines);
        lines = MergeCloseNodes(lines);
        lines = SimplifyLines(lines);
        lines = RemoveShortLines(lines, minSegmentLength);
        return lines;
    }

    /// <summary>
    /// Merges collinear and overlapping lines.
    /// </summary>
    private List<LineString> MergeLines(List<LineString> lines)
    {
        var merger = new LineMerger();
        merger.Add(lines);
        var merged = merger.GetMergedLineStrings();
        return merged.Cast<LineString>().ToList();
    }

    /// <summary>
    /// Extends lines to snap to other lines if they are within tolerance.
    /// </summary>
    private List<LineString> FixUndershotLines(List<LineString> lines)
    {
        var currentLines = new List<LineString>(lines);
        bool changedInIteration;
        const int maxIterations = 200; // Add a safety break to prevent potential infinite loops in complex scenarios.
        int iterationCount = 0;

        do
        {
            changedInIteration = false;
            iterationCount++;

            for (int i = 0; i < currentLines.Count; i++)
            {
                var line1 = currentLines[i];
                if (line1.IsClosed || line1.NumPoints < 2) continue;

                var coords = line1.Coordinates;
                var newCoords = (Coordinate[])coords.Clone();
                bool lineModified = false;

                // --- Check and extend start point ---
                var start = coords[0];
                double minStartDist = TOLERANCE;
                Coordinate snapToForStart = null;

                for (int j = 0; j < currentLines.Count; j++)
                {
                    if (i == j) continue;
                    var line2 = currentLines[j];
                    var distOp = new DistanceOp(new Point(start), line2);
                    var nearestPoints = distOp.NearestPoints();
                    var dist = new Point(nearestPoints[0]).Distance(new Point(nearestPoints[1]));

                    if (dist > 1e-9 && dist < minStartDist)
                    {
                        var snapPoint = nearestPoints[1];
                        if (snapPoint.Distance(line2.Coordinates.First()) > TOLERANCE &&
                            snapPoint.Distance(line2.Coordinates.Last()) > TOLERANCE)
                        {
                            minStartDist = dist;
                            snapToForStart = snapPoint;
                        }
                    }
                }

                if (snapToForStart != null)
                {
                    var pAfterStart = coords[1];
                    var direction = new Coordinate(start.X - pAfterStart.X, start.Y - pAfterStart.Y);
                    var length = start.Distance(pAfterStart);
                    if (length > 1e-9)
                    {
                        direction.X /= length;
                        direction.Y /= length;
                    }

                    var extensionLength = 1e-3; // Use a minimal extension to ensure intersection
                    newCoords[0] = new Coordinate(
                        snapToForStart.X + direction.X * extensionLength,
                        snapToForStart.Y + direction.Y * extensionLength
                    );
                    lineModified = true;
                }

                // --- Check and extend end point ---
                var end = coords[coords.Length - 1];
                double minEndDist = TOLERANCE;
                Coordinate snapToForEnd = null;

                for (int j = 0; j < currentLines.Count; j++)
                {
                    if (i == j) continue;
                    var line2 = currentLines[j];
                    var distOp = new DistanceOp(new Point(end), line2);
                    var nearestPoints = distOp.NearestPoints();
                    var dist = new Point(nearestPoints[0]).Distance(new Point(nearestPoints[1]));

                    if (dist > 1e-9 && dist < minEndDist)
                    {
                        var snapPoint = nearestPoints[1];
                        if (snapPoint.Distance(line2.Coordinates.First()) > TOLERANCE &&
                            snapPoint.Distance(line2.Coordinates.Last()) > TOLERANCE)
                        {
                            minEndDist = dist;
                            snapToForEnd = snapPoint;
                        }
                    }
                }

                if (snapToForEnd != null)
                {
                    var pBeforeEnd = coords[coords.Length - 2];
                    var direction = new Coordinate(end.X - pBeforeEnd.X, end.Y - pBeforeEnd.Y);
                    var length = end.Distance(pBeforeEnd);
                    if (length > 1e-9)
                    {
                        direction.X /= length;
                        direction.Y /= length;
                    }
                    
                    var extensionLength = 1e-3; // Use a minimal extension to ensure intersection
                    newCoords[newCoords.Length - 1] = new Coordinate(
                        snapToForEnd.X + direction.X * extensionLength,
                        snapToForEnd.Y + direction.Y * extensionLength
                    );
                    lineModified = true;
                }

                if (lineModified)
                {
                    if (newCoords.First().Distance(newCoords.Last()) > 1e-9)
                    {
                        currentLines[i] = new LineString(newCoords);
                        changedInIteration = true;
                    }
                }
            }
        } while (changedInIteration && iterationCount < maxIterations);

        return currentLines;
    }

    /// <summary>
    /// Splits lines at their intersection points.
    /// </summary>
    private List<LineString> SplitLines(List<LineString> lines)
    {
        var noder = new IteratedNoder(new PrecisionModel(10000));
        var segStrings = new List<ISegmentString>();
        foreach (var line in lines)
        {
            segStrings.Add(new NodedSegmentString(line.Coordinates, null));
        }
        noder.ComputeNodes(segStrings);
        var nodedLines = noder.GetNodedSubstrings();
        return nodedLines.Cast<ISegmentString>()
                         .Where(s => s.Count > 1)
                         .Select(s => new LineString(s.Coordinates))
                         .ToList();
    }

    /// <summary>
    /// Groups endpoints that are close to each other into clusters.
    /// </summary>
    private List<List<Coordinate>> GroupCloseEndpoints(List<Coordinate> endpoints)
    {
        var groups = new List<List<Coordinate>>();
        var visited = new bool[endpoints.Count];
        for (int i = 0; i < endpoints.Count; i++)
        {
            if (visited[i]) continue;
            var groupQueue = new Queue<int>();
            var currentGroup = new List<Coordinate>();
            groupQueue.Enqueue(i);
            visited[i] = true;
            while (groupQueue.Count > 0)
            {
                int currentIndex = groupQueue.Dequeue();
                currentGroup.Add(endpoints[currentIndex]);
                for (int j = 0; j < endpoints.Count; j++)
                {
                    if (visited[j]) continue;
                    if (endpoints[currentIndex].Distance(endpoints[j]) <= TOLERANCE)
                    {
                        visited[j] = true;
                        groupQueue.Enqueue(j);
                    }
                }
            }
            groups.Add(currentGroup);
        }
        return groups;
    }

    /// <summary>
    /// Merges endpoints within tolerance by snapping them to their group's centroid.
    /// </summary>
    private List<LineString> MergeCloseNodes(List<LineString> lines)
    {
        var endpoints = lines.SelectMany(l => new[] { l.Coordinates.First(), l.Coordinates.Last() })
                             .Distinct(new CoordinateEqualityComparer())
                             .ToList();
        if (endpoints.Count < 2) return lines;

        var endpointGroups = GroupCloseEndpoints(endpoints);
        var nodeMap = new Dictionary<Coordinate, Coordinate>(new CoordinateEqualityComparer());
        foreach (var group in endpointGroups.Where(g => g.Count > 1))
        {
            var centroid = new Coordinate(group.Average(c => c.X), group.Average(c => c.Y));
            foreach (var member in group)
            {
                nodeMap[member] = centroid;
            }
        }

        if (!nodeMap.Any()) return lines;

        var newLines = new List<LineString>();
        foreach (var line in lines)
        {
            var coords = line.Coordinates.Select(c => (Coordinate)c.Copy()).ToArray();
            if (nodeMap.TryGetValue(coords.First(), out var newStart)) coords[0] = newStart;
            if (nodeMap.TryGetValue(coords.Last(), out var newEnd)) coords[coords.Length - 1] = newEnd;
            if (coords.First().Distance(coords.Last()) > 1e-9)
            {
                newLines.Add(new LineString(coords));
            }
        }
        return newLines;
    }

    /// <summary>
    /// Removes redundant vertices that lie on a straight line between other vertices.
    /// </summary>
    private List<LineString> SimplifyLines(List<LineString> lines)
    {
        var simplifiedLines = new List<LineString>();
        const double collinearityTolerance = 1e-6; // Use a small tolerance for floating point math

        foreach (var line in lines)
        {
            if (line.NumPoints < 3)
            {
                simplifiedLines.Add(line);
                continue;
            }

            var newCoords = new List<Coordinate> { line.Coordinates.First() };
            for (int i = 1; i < line.NumPoints - 1; i++)
            {
                var pPrev = newCoords.Last();
                var pCurr = line.Coordinates[i];
                var pNext = line.Coordinates[i + 1];

                // Use cross-product to check for collinearity. If it's non-zero, the point is a corner.
                double crossProduct = (pCurr.Y - pPrev.Y) * (pNext.X - pCurr.X) -
                                      (pCurr.X - pPrev.X) * (pNext.Y - pCurr.Y);

                if (Math.Abs(crossProduct) > collinearityTolerance)
                {
                    newCoords.Add(pCurr);
                }
            }
            newCoords.Add(line.Coordinates.Last());

            // Final check for duplicates that might arise if the last point was collinear
            if (newCoords.Count > 1 && newCoords.Last().Equals2D(newCoords[newCoords.Count - 2], 1e-9))
            {
                newCoords.RemoveAt(newCoords.Count - 1);
            }

            if (newCoords.Count > 1)
            {
                simplifiedLines.Add(new LineString(newCoords.ToArray()));
            }
        }
        return simplifiedLines;
    }

    /// <summary>
    /// Removes lines that are shorter than the tolerance.
    /// </summary>
    private List<LineString> RemoveShortLines(List<LineString> lines, double minSegmentLength)
    {
        var validLines = new List<LineString>();
        foreach (var line in lines)
        {
            if (line.Length < minSegmentLength) continue;

            var newCoords = new List<Coordinate> { line.Coordinates.First() };
            for (int i = 1; i < line.NumPoints; i++)
            {
                var lastCoord = newCoords.Last();
                var currentCoord = line.Coordinates[i];
                if (lastCoord.Distance(currentCoord) >= minSegmentLength)
                {
                    newCoords.Add(currentCoord);
                }
            }

            // Ensure the last point is always included if the line is long enough
            var lastPoint = line.Coordinates.Last();
            if (!newCoords.Last().Equals2D(lastPoint))
            {
                if (newCoords.Last().Distance(lastPoint) < minSegmentLength)
                {
                    newCoords[newCoords.Count - 1] = lastPoint; // Replace the last point
                }
                else
                {
                    newCoords.Add(lastPoint);
                }
            }

            if (newCoords.Count > 1)
            {
                var newLine = new LineString(newCoords.ToArray());
                if (newLine.Length >= minSegmentLength)
                {
                    validLines.Add(newLine);
                }
            }
        }
        return validLines;
    }
}

internal class CoordinateEqualityComparer : IEqualityComparer<Coordinate>
{
    public bool Equals(Coordinate c1, Coordinate c2)
    {
        if (ReferenceEquals(c1, c2)) return true;
        if (c1 is null || c2 is null) return false;
        return c1.Equals2D(c2);
    }

    public int GetHashCode(Coordinate c)
    {
        return c.GetHashCode();
    }
}
