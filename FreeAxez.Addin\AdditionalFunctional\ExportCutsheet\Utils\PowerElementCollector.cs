﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.ExportCutsheet.Utils
{
    public class PowerElementCollector
    {
        public List<Element> GetPowerElements()
        {
            List<Element> separateWhips = GetSeparateWhips();
            List<FamilyInstance> tracks = GetTracks();
            List<FamilyInstance> boxes = GetBoxes();

            var powerElements = new List<Element>();
            powerElements.AddRange(separateWhips);
            powerElements.AddRange(tracks);
            powerElements.AddRange(boxes);

            if (powerElements.Count is 0)
            {
                throw new NullReferenceException("No power elements exist in project");
            }

            return powerElements;
        }

        private List<Element> GetSeparateWhips()
        {
            var separateWhipsAbbreviation = new List<string>()
            {
                "-Interlink",
                "-Supply",
                "-FeedModule",
            };

            var whips = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(FlexPipe))
                .Where(i => separateWhipsAbbreviation.Any(v => i.Name.Contains(v)))
                .ToList();

            return whips;
        }

        private List<FamilyInstance> GetTracks()
        {
            var trackFamiliesAbbreviation = new List<string>()
            {
                 "-Track-4'",
                 "-Track-8'"
            };

            var tracks = new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_ElectricalEquipment)
                .WhereElementIsNotElementType()
                .Cast<FamilyInstance>()
                .Where(i => trackFamiliesAbbreviation.Any(v => i.Symbol.FamilyName.Contains(v)))
                .ToList();

            return tracks;
        }

        private List<FamilyInstance> GetBoxes()
        {
            var floorBoxAbbreviation = new List<string>()
            {
                "-FB-",
                "-DeskMount-",
                "-Desk_Mount-"
            };

            var boxes = new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_ElectricalFixtures)
                .WhereElementIsNotElementType()
                .Cast<FamilyInstance>()
                .Where(i => i.SuperComponent == null)
                .Where(i => floorBoxAbbreviation.Any(v => i.Symbol.FamilyName.Contains(v)))
                .ToList();

            return boxes;
        }
    }
}
