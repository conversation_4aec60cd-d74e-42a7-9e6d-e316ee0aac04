﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.Frame.Views.FrameView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        SizeToContent="WidthAndHeight"
        ResizeMode="NoResize"
        Title="Frame"
        WindowStartupLocation="CenterScreen">
    <StackPanel Margin="10,5,10,10">
        <GroupBox Header="Select Grid Size">
            <StackPanel Margin="3">
                <RadioButton x:Name="gridd40" Content="Gridd 40" GroupName="gridd"/>
                <RadioButton x:Name="gridd70" Content="Gridd 70" GroupName="gridd" Margin="0,3,0,0"/>
            </StackPanel>
        </GroupBox>
        <GroupBox Header="Select Corner Size">
            <StackPanel Margin="3">
                <RadioButton x:Name="cornerLong" Content="Long" GroupName="corner"/>
                <RadioButton x:Name="cornerShort" Content="Short" GroupName="corner" Margin="0,3,0,0"/>
            </StackPanel>
        </GroupBox>
        <Button Content="Select Lines" Margin="0,20,0,0" Height="25" Width="200" Click="Select_Click"/>
    </StackPanel>
</Window>
