﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.LegendManagement.Views.LegendManagementView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.LegendManagement.Views"
        mc:Ignorable="d"
        Title="Legend Management" Height="800" Width="1500"
        ResizeMode="NoResize" WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="650"/>
            <RowDefinition Height="40"/>
        </Grid.RowDefinitions>

        <TextBox Text="{Binding SearchFilter, UpdateSourceTrigger=PropertyChanged}" Grid.Row="0"
                 Height="25" HorizontalAlignment="Left" VerticalContentAlignment="Center"
                 Width="500" Margin="0 0 0 5" Style="{StaticResource Search}"/>

        <ListView ItemsSource="{Binding Sheets}" Grid.Row="1">
            <ListView.View>
                <GridView>
                    <GridViewColumn Header="Sheet Size" DisplayMemberBinding="{Binding SheetSize.Name}" Width="100"/>
                    <GridViewColumn Header="Sheet Sorting" DisplayMemberBinding="{Binding SheetSorting.Name}" Width="300"/>
                    <GridViewColumn Header="Sheet Number" DisplayMemberBinding="{Binding Sheet.SheetNumber}" Width="200"/>
                    <GridViewColumn Header="Present Legends" Width="750">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <ListView ItemsSource="{Binding PresentLegends}" BorderThickness="0">
                                    <ListView.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Name}"/>
                                        </DataTemplate>
                                    </ListView.ItemTemplate>
                                </ListView>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <!--<GridViewColumn Header="Missing Legends" Width="350">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <ListView ItemsSource="{Binding MissingLegends}" BorderThickness="0">
                                    <ListView.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Name}"/>
                                        </DataTemplate>
                                    </ListView.ItemTemplate>
                                </ListView>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Header="Redundant Legends" Width="350">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <ListView ItemsSource="{Binding RedundantLegends}" BorderThickness="0">
                                    <ListView.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Name}"/>
                                        </DataTemplate>
                                    </ListView.ItemTemplate>
                                </ListView>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>-->
                </GridView>
            </ListView.View>
        </ListView>

        <Button Grid.Row="3" Command="{Binding CancelCommand}" Content="Close"
                Style="{StaticResource ButtonSimpleBlue}"
                HorizontalAlignment="Right" VerticalAlignment="Center" Width="80" Height="25"
                Margin="0 0 0 0"/>
    </Grid>
</Window>
