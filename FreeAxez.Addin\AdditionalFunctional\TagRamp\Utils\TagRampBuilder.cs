﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TagRamp.Abstractions;
using FreeAxez.Addin.AdditionalFunctional.TagRamp.Models;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagRamp.Utils
{
    public class TagRampBuilder
    {
        private readonly double _lengthToCenterOfTag;
        private readonly bool _isVisibleInView;
        private readonly IRampSelector _rampSelector;
        private readonly FamilySymbol _rampTagMulticategoryFamily;

        public TagRampBuilder(double lengthToCenterOfTag, bool isVisibleInView)
        {
            _lengthToCenterOfTag = lengthToCenterOfTag;
            _isVisibleInView = isVisibleInView;  
            _rampTagMulticategoryFamily = new RampFamilies().MyltiCategoryTagSymbol;

            if (_isVisibleInView)
            {
                _rampSelector = new RampComponentVisibleInView();
            }
            else
            {
                _rampSelector = new RampComponentSelector();
            }
        }

        public List<TramsitionLine> BuildAnnatation()
        {
            var output = new List<TramsitionLine>();
            var separateRampResolver = new SeparateRampResolver();
            var tramsitionLineProvider = new TramsitionLineProvider();

            //var test = new RampFamilies().RampSlopeTagFamily;
            var rampSlopeTagCreator = new RampSlopeTagCreator();
            var tramsitionLineTagCreator = new TramsitionLineTagCreator(_lengthToCenterOfTag);

            List<FamilyInstance> selectedRampComponents = _rampSelector.Select();
            List<List<FamilyInstance>> seperatedRamps = separateRampResolver.Resolve(selectedRampComponents);

            using (Transaction transaction = new Transaction(RevitManager.Document, "Annotations For Ramps"))
            {
                transaction.Start();

                foreach (List<FamilyInstance> separatedRamp in seperatedRamps)
                {
                    List<TramsitionLine> tramsitionLines = tramsitionLineProvider.Get(separatedRamp);
                    tramsitionLines.ForEach(l => rampSlopeTagCreator.Create(l));

                    TramsitionLine middleTramsitionLine = tramsitionLines[tramsitionLines.Count / 2];
                    tramsitionLineTagCreator.Create(middleTramsitionLine);

                    output.Add(tramsitionLines.First());
                }

                transaction.Commit();
            }
            return output;
        }

        public List<IndependentTag> CreateTagMulticategory()
        {
            var output = new List<IndependentTag>();
            var separateRampResolver = new SeparateRampResolver();

            List<FamilyInstance> selectedRampComponents = _rampSelector.Select();
            List<List<FamilyInstance>> seperatedRamps = separateRampResolver.Resolve(selectedRampComponents);            

            using (Transaction transaction = new Transaction(RevitManager.Document, "Ramps Multicategory Tags"))
            {
                transaction.Start();

                foreach (List<FamilyInstance> separatedRamp in seperatedRamps)
                {
                    output.Add(CreateMultycategoryTag(separatedRamp.First()));
                }

                RevitManager.Document.Regenerate();
                
                foreach (var annotation in output)
                {
                    SetLeaderPositionTag(annotation);
                }

                transaction.Commit();
            }

            return output;
        }

        private IndependentTag CreateMultycategoryTag(FamilyInstance ramp)
        {
            var activeViewId = RevitManager.UIDocument.ActiveView.Id;
            var rampReference = new Reference(ramp);
            var tagSymbol = _rampTagMulticategoryFamily;

            var rampPoint = (ramp.Location as LocationPoint).Point;
            var length = _lengthToCenterOfTag;
            var scale = RevitManager.UIDocument.ActiveView.Scale;

            var tagHeadPoint = new XYZ(rampPoint.X - length * scale,
                                          rampPoint.Y - length / 2 * scale,
                                          rampPoint.Z);

#if revit2018
            var tag = IndependentTag.Create(RevitManager.Document, activeViewId, rampReference, true, TagMode.TM_ADDBY_MULTICATEGORY, TagOrientation.Horizontal, tagHeadPoint);
            tag.ChangeTypeId(tagSymbol.Id);
#else
            var tag = IndependentTag.Create(RevitManager.Document, tagSymbol.Id, activeViewId, rampReference, true, TagOrientation.Horizontal, tagHeadPoint);
#endif
            tag.LeaderEndCondition = LeaderEndCondition.Free;
            return tag;
        }

        private void SetLeaderPositionTag(IndependentTag tag)
        {
#if revit2018 || revit2019 || revit2020 || revit2021
            var taggedLocalElementLocation = tag.GetTaggedLocalElement().Location as LocationPoint;
#else
            var taggedLocalElementLocation = tag.GetTaggedLocalElements().First().Location as LocationPoint;
#endif
            var taggedLocalElement = taggedLocalElementLocation.Point;
            var length = _lengthToCenterOfTag;
            var scale = RevitManager.UIDocument.ActiveView.Scale;

            var head = new XYZ(taggedLocalElement.X - (length * scale),
                                taggedLocalElement.Y - (length / 2 * scale),
                                taggedLocalElement.Z);

            var elbow = new XYZ(taggedLocalElement.X - (length / 3 * scale),
                                taggedLocalElement.Y - (length / 2 * scale),
                                taggedLocalElement.Z);

            tag.TagHeadPosition = head;

#if revit2018 || revit2019 || revit2020 || revit2021
            tag.LeaderEnd = taggedLocalElement;
            tag.LeaderElbow = elbow;
#else
            var reference = tag.GetTaggedReferences().First();
            tag.SetLeaderEnd(reference, taggedLocalElement);
            tag.SetLeaderElbow(reference, elbow);
#endif
        }
    }
}