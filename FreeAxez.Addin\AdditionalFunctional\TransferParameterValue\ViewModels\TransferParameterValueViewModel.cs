﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.Models;
using FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.Utils;
using FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.ViewModels
{
    public class TransferParameterValueViewModel : BaseViewModel
    {
        private readonly SelectionManager _selectionManager;

        private List<string> _availableParameterNames;
        private List<string> _duplicateParameterNames;
        private UserParameterSet _selectedParameterSet;


        public TransferParameterValueViewModel(SelectionManager selectionManager)
        {
            _selectionManager = selectionManager;

            _availableParameterNames = TransferParameterManager.GetAvailableParameterNames(_selectionManager.SourceElement);
            _duplicateParameterNames = new List<string>();

            ParameterSets = new ObservableCollection<UserParameterSet>(ParameterSetManager.GetParameterSetsFromSettings());
            _selectedParameterSet = ParameterSets.First(s => s.Name == ParameterSetManager.CustomParameterSetName);

            AddParameterCommand = new RelayCommand(OnAddParameterCommandExecute);
            RemoveParameterCommand = new RelayCommand(OnRemoveParameterCommandExecute);
            NewCommand = new RelayCommand(OnNewCommandExecute);
            RenameCommand = new RelayCommand(OnRenameCommandExecute);
            DuplicateSetCommand = new RelayCommand(OnDuplicateSetCommandExecute);
            DeleteCommand = new RelayCommand(OnDeleteCommandExecute);
            DuplicateCommand = new RelayCommand(OnDuplicateCommandExecute);
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
        }

        public ObservableCollection<UserParameterSet> ParameterSets { get; set; }

        public UserParameterSet SelectedParameterSet
        {
            get
            {
                return _selectedParameterSet;
            }
            set
            {
                if (value.Name == ParameterSetManager.CustomParameterSetName)
                {
                    DeleteCommandAvailable = false;
                    RenameCommandAvailable = false;
                }
                else
                {
                    DeleteCommandAvailable = true;
                    RenameCommandAvailable = true;
                }

                _selectedParameterSet = value;

                _availableParameterNames = TransferParameterManager.GetAvailableParameterNames(_selectionManager.SourceElement);
                _selectedParameterSet.ParameterNames.RemoveAll(s => !_availableParameterNames.Contains(s));
                _duplicateParameterNames = new List<string>(_selectedParameterSet.ParameterNames);
                _availableParameterNames.RemoveAll(s => _duplicateParameterNames.Contains(s));

                OnPropertyChanged(nameof(RenameCommandAvailable));
                OnPropertyChanged(nameof(DeleteCommandAvailable));
                OnPropertyChanged(nameof(AvailableParameterNames));
                OnPropertyChanged(nameof(DuplicateParameterNames));
            }
        }

        public bool DeleteCommandAvailable { get; set; }

        public bool RenameCommandAvailable { get; set; }

        public List<string> AvailableParameterNames
        {
            get
            {
                return _availableParameterNames.OrderBy(n => n).ToList();
            }
            set
            {
                _availableParameterNames = value;
                OnPropertyChanged();
            }
        }

        public string SelectedAvailableParameterName { get; set; }

        public List<string> DuplicateParameterNames
        {
            get
            {
                return _duplicateParameterNames.OrderBy(n => n).ToList();
            }
            set
            {
                _duplicateParameterNames = value;
                OnPropertyChanged();
            }
        }

        public string SelectedDuplicateParameterName { get; set; }

        #region Commands

        public ICommand AddParameterCommand { get; set; }
        private void OnAddParameterCommandExecute(object p)
        {
            if (string.IsNullOrEmpty(SelectedAvailableParameterName))
            {
                return;
            }

            _duplicateParameterNames.Add(SelectedAvailableParameterName);
            _availableParameterNames.Remove(SelectedAvailableParameterName);

            _selectedParameterSet.ParameterNames.Add(SelectedAvailableParameterName);

            OnPropertyChanged(nameof(AvailableParameterNames));
            OnPropertyChanged(nameof(DuplicateParameterNames));
        }

        public ICommand RemoveParameterCommand { get; set; }
        private void OnRemoveParameterCommandExecute(object p)
        {
            if (string.IsNullOrEmpty(SelectedDuplicateParameterName))
            {
                return;
            }

            _availableParameterNames.Add(SelectedDuplicateParameterName);
            _duplicateParameterNames.Remove(SelectedDuplicateParameterName);

            _selectedParameterSet.ParameterNames.Remove(SelectedDuplicateParameterName);

            OnPropertyChanged(nameof(AvailableParameterNames));
            OnPropertyChanged(nameof(DuplicateParameterNames));
        }

        public ICommand NewCommand { get; set; }
        private void OnNewCommandExecute(object p)
        {
            var name = InputDialog.ShowDialog("Name", "Name:", "", p as Window);

            if (string.IsNullOrWhiteSpace(name) || name == ParameterSetManager.CustomParameterSetName)
            {
                InfoDialog.ShowDialog("Warning", "Invalid name", p as Window);
                return;
            }

            if (ParameterSets.FirstOrDefault(s => s.Name == name) != null)
            {
                InfoDialog.ShowDialog("Warning", "Name already exists", p as Window);
                return;
            }

            var newParameterSet = new UserParameterSet() { Name = name };

            ParameterSets.Add(newParameterSet);
            SelectedParameterSet = ParameterSets.First(s => s.Name == name);

            OnPropertyChanged(nameof(AvailableParameterNames));
            OnPropertyChanged(nameof(DuplicateParameterNames));
            OnPropertyChanged(nameof(SelectedParameterSet));
        }

        public ICommand RenameCommand { get; set; }
        private void OnRenameCommandExecute(object p)
        {
            var name = InputDialog.ShowDialog("Rename", "Name:", $"{SelectedParameterSet.Name}", p as Window);

            if (name == SelectedParameterSet.Name)
            {
                return;
            }

            if (string.IsNullOrWhiteSpace(name))
            {
                InfoDialog.ShowDialog("Warning", "Invalid name", p as Window);
                return;
            }

            if (ParameterSets.FirstOrDefault(s => s.Name == name) != null)
            {
                InfoDialog.ShowDialog("Warning", "Name already exists", p as Window);
                return;
            }

            var selectedParameterSet = SelectedParameterSet;
            selectedParameterSet.Name = name;

            SelectedParameterSet = ParameterSets.First(s => s.Name == ParameterSetManager.CustomParameterSetName);
            ParameterSets.Remove(selectedParameterSet);
            ParameterSets.Add(selectedParameterSet);
            SelectedParameterSet = ParameterSets.First(s => s.Name == name);

            OnPropertyChanged(nameof(SelectedParameterSet));
        }

        public ICommand DuplicateSetCommand { get; set; }
        private void OnDuplicateSetCommandExecute(object p)
        {
            string name;

            if (SelectedParameterSet.Name == ParameterSetManager.CustomParameterSetName)
            {
                name = $"{SelectedParameterSet.Name}".Replace("<", "").Replace(">", "");
            }
            else
            {
                name = $"{SelectedParameterSet.Name} Copy";
            }


            while (ParameterSets.FirstOrDefault(s => s.Name == name) != null)
            {
                name += " Copy";
            }

            var duplicatedParameterSet = new UserParameterSet() { Name = name, ParameterNames = new List<string>(SelectedParameterSet.ParameterNames) };

            if (SelectedParameterSet.Name == ParameterSetManager.CustomParameterSetName)
            {
                SelectedParameterSet.ParameterNames = new List<string>();
            }

            ParameterSets.Add(duplicatedParameterSet);
            SelectedParameterSet = ParameterSets.First(s => s.Name == name);

            OnPropertyChanged(nameof(SelectedParameterSet));
        }

        public ICommand DeleteCommand { get; set; }
        private void OnDeleteCommandExecute(object p)
        {
            ParameterSets.Remove(SelectedParameterSet);
            SelectedParameterSet = ParameterSets.First(s => s.Name == ParameterSetManager.CustomParameterSetName);

            OnPropertyChanged(nameof(SelectedParameterSet));
        }

        public ICommand DuplicateCommand { get; set; }
        private void OnDuplicateCommandExecute(object p)
        {
            (p as Window).Close();
            ParameterSetManager.SaveParameterSetsToSettings(ParameterSets.ToList());

            if (DuplicateParameterNames.Count > 0)
            {
                var transferParameterManager = new TransferParameterManager(_selectionManager.SourceElement, _selectionManager.TargetElements, DuplicateParameterNames);
                transferParameterManager.SetValues();
            }
        }

        public ICommand CancelCommand { get; set; }
        private void OnCancelCommandExecute(object p)
        {
            (p as Window).Close();
        }

        #endregion
    }
}
