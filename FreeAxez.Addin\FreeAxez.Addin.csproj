﻿<?xml version="1.0" encoding="utf-8"?>

<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<LangVersion>latest</LangVersion>
		<PlatformTarget>x64</PlatformTarget>
		<ImplicitUsings>true</ImplicitUsings>
		<UseWPF>true</UseWPF>
		<Configurations>Debug 2020;Debug 2021;Debug 2022;Debug 2023;Debug 2024;Debug 2025;Debug 2026</Configurations>
		<Configurations>$(Configurations);Release 2020;Release 2021;Release 2022;Release 2023;Release 2024;Release 2025;Release 2026</Configurations>
		<CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
	</PropertyGroup>

	<PropertyGroup>
		<NoWarn>0168;0618;CS0649;CS0169;CA1050;CA1822;CA2211;IDE1006;CS8601;CS8602;CS8604;MSB3277;CS0114;CS0108;CS1998;CS4014;</NoWarn>
	</PropertyGroup>

	<PropertyGroup Condition="$(Configuration.Contains('Debug'))">
		<DebugType>full</DebugType>
		<DebugSymbols>true</DebugSymbols>
		<SharingType>Local</SharingType>
		<DefineConstants>$(DefineConstants);DEBUG</DefineConstants>
	</PropertyGroup>

	<PropertyGroup Condition="$(Configuration.Contains('Release'))">
		<Optimize>true</Optimize>
		<DebugType>none</DebugType>
		<SharingType>Publish</SharingType>
		<DefineConstants>$(DefineConstants);RELEASE</DefineConstants>
	</PropertyGroup>

	<PropertyGroup Condition="$(Configuration.Contains('2020'))">
		<RevitVersion>2020</RevitVersion>
		<DefineConstants>revit2020</DefineConstants>
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
	<PropertyGroup Condition="$(Configuration.Contains('2021'))">
		<RevitVersion>2021</RevitVersion>
		<DefineConstants>revit2021</DefineConstants>
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
	<PropertyGroup Condition="$(Configuration.Contains('2022'))">
		<RevitVersion>2022</RevitVersion>
		<DefineConstants>revit2022</DefineConstants>
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
	<PropertyGroup Condition="$(Configuration.Contains('2023'))">
		<RevitVersion>2023</RevitVersion>
		<DefineConstants>revit2023</DefineConstants>
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
	<PropertyGroup Condition="$(Configuration.Contains('2024'))">
		<RevitVersion>2024</RevitVersion>
		<DefineConstants>revit2024</DefineConstants>
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
	<PropertyGroup Condition="$(Configuration.Contains('2025'))">
		<RevitVersion>2025</RevitVersion>
		<DefineConstants>revit2025</DefineConstants>
		<TargetFramework>net8.0-windows</TargetFramework>
	</PropertyGroup>
	<PropertyGroup Condition="$(Configuration.Contains('2026'))">
		<RevitVersion>2026</RevitVersion>
		<DefineConstants>revit2026</DefineConstants>
		<TargetFramework>net8.0-windows</TargetFramework>
	</PropertyGroup>

	<PropertyGroup>
		<Version>$(RevitVersion)</Version>
		<GenerateAssemblyInfo>true</GenerateAssemblyInfo>
		<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
	</PropertyGroup>

	<PropertyGroup>
		<AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
		<GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="CredentialManagement" Version="1.0.2" />
		<PackageReference Include="EPPlus" Version="7.5.2" />
		<PackageReference Include="FuzzySharp" Version="2.0.2" />
		<PackageReference Include="Microsoft-WindowsAPICodePack-Shell" Version="1.1.5" />
		<PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.39" />
		<PackageReference Include="NetTopologySuite" Version="2.5.0" />
		<PackageReference Include="Nice3point.Revit.Api.RevitAPIIFC" Version="$(RevitVersion).*" />
		<PackageReference Include="Nice3point.Revit.Api.RevitAPI" Version="$(RevitVersion).*" />
		<PackageReference Include="Nice3point.Revit.Api.RevitAPIUI" Version="$(RevitVersion).*" />
		<PackageReference Include="Nice3point.Revit.Api.AdWindows" Version="$(RevitVersion).*" />
		<PackageReference Include="Nice3point.Revit.Api.UIFramework" Version="$(RevitVersion).*" />
		<PackageReference Include="JetBrains.Annotations" Version="2022.1.0" />
		<PackageReference Include="Microsoft.Azure.Storage.Blob" Version="11.2.2" />
		<PackageReference Include="Microsoft.Azure.Storage.Queue" Version="11.2.2" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="NPOI" Version="2.6.0" />
		<PackageReference Include="OfficeOpenXml.Extends" Version="1.0.6" />
		<PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
		<PackageReference Include="System.ValueTuple" Version="4.5.0" />
		<PackageReference Include="Wpf.Controls.PanAndZoom" Version="2.3.4" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Content\*.*" />
	</ItemGroup>

	<ItemGroup>
	  <None Remove="Content\Cleanup16.png" />
	  <None Remove="Content\Cleanup32.png" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\FreeAxez.Core\FreeAxez.Core.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Reference Include="System.Security" />
	</ItemGroup>

	<ItemGroup>
		<Resource Include="AdditionalFunctional\SmartsheetTaskManager\Resources\Icons\smartsheet16.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Resource>
		<Resource Include="Infrastructure\UI\Images\FA_Logo.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Resource>
		<Resource Include="Infrastructure\UI\Images\FA_Logo_Small.png" />
		<Resource Include="Infrastructure\UI\Images\noImage.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Resource>
		<Resource Include="Infrastructure\UI\Images\ft-logo.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Resource>
		<Resource Include="Infrastructure\UI\Images\revit_file.png" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Properties\Settings.Designer.cs">
	    <DesignTimeSharedInput>True</DesignTimeSharedInput>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>Settings.settings</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <None Update="AdditionalFunctional\DWGLayerManager\acad.lin">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="Properties\Settings.settings">
	    <Generator>SettingsSingleFileGenerator</Generator>
	    <LastGenOutput>Settings.Designer.cs</LastGenOutput>
	  </None>
	</ItemGroup>

  <PropertyGroup>
    <AutoCADPluginProject>$(MSBuildProjectDirectory)\..\FreeAxez.AutoCAD.Plugin\FreeAxez.AutoCAD.Plugin.csproj</AutoCADPluginProject>
    <AutoCADPluginBin>$(MSBuildProjectDirectory)\..\FreeAxez.AutoCAD.Plugin\bin\</AutoCADPluginBin>
  </PropertyGroup>

  <Target Name="BuildAndCopyAutoCADPlugins" AfterTargets="Build" Condition="Exists('$(AutoCADPluginProject)')">
    <Message Importance="high" Text="Building AutoCAD plugins from: $(AutoCADPluginProject)" />

    <ItemGroup>
      <AutoCADConfigurations Include="2022;2023;2024;2025;2026" />
    </ItemGroup>

	  <!-- Build each configuration separately using dotnet build command -->
	  <Exec Command="dotnet build &quot;$(AutoCADPluginProject)&quot; --configuration &quot;Release %(AutoCADConfigurations.Identity)&quot; --verbosity quiet" ContinueOnError="false" />

	  <ItemGroup>
		  <!-- AutoCAD Plugin DLLs (each version builds to its own folder) -->
		  <AutoCADPluginDlls Include="$(AutoCADPluginBin)Release 2022\FreeAxez.AutoCAD.Plugin.2022.dll" Condition="Exists('$(AutoCADPluginBin)Release 2022\FreeAxez.AutoCAD.Plugin.2022.dll')" />
		  <AutoCADPluginDlls Include="$(AutoCADPluginBin)Release 2023\FreeAxez.AutoCAD.Plugin.2023.dll" Condition="Exists('$(AutoCADPluginBin)Release 2023\FreeAxez.AutoCAD.Plugin.2023.dll')" />
		  <AutoCADPluginDlls Include="$(AutoCADPluginBin)Release 2024\FreeAxez.AutoCAD.Plugin.2024.dll" Condition="Exists('$(AutoCADPluginBin)Release 2024\FreeAxez.AutoCAD.Plugin.2024.dll')" />
		  <AutoCADPluginDlls Include="$(AutoCADPluginBin)Release 2025\FreeAxez.AutoCAD.Plugin.2025.dll" Condition="Exists('$(AutoCADPluginBin)Release 2025\FreeAxez.AutoCAD.Plugin.2025.dll')" />
		  <AutoCADPluginDlls Include="$(AutoCADPluginBin)Release 2026\FreeAxez.AutoCAD.Plugin.2026.dll" Condition="Exists('$(AutoCADPluginBin)Release 2026\FreeAxez.AutoCAD.Plugin.2026.dll')" />
	  </ItemGroup>

	  <Copy SourceFiles="@(AutoCADPluginDlls)" DestinationFolder="$(OutputPath)" SkipUnchangedFiles="true" Condition="'@(AutoCADPluginDlls)' != ''" />

	  <Message Importance="high" Text="AutoCAD DLLs for all versions copied to $(OutputPath)" Condition="'@(AutoCADPluginDlls)' != ''" />
	  <Message Importance="high" Text="No AutoCAD DLLs were found to copy." Condition="'@(AutoCADPluginDlls)' == ''" />
  </Target>

  <Target Name="SkipAutoCADPlugins" AfterTargets="Build" Condition="!Exists('$(AutoCADPluginProject)')">
    <Message Importance="high" Text="AutoCAD plugin project not found at: $(AutoCADPluginProject). Skipping AutoCAD plugin build." />
  </Target>

</Project>