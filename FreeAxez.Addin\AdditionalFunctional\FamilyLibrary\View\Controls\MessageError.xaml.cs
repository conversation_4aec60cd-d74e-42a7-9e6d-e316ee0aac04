using System.Windows;
using System.Windows.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls;

public partial class MessageError : UserControl
{
    public static readonly DependencyProperty MessageProperty = 
        DependencyProperty.Register("Message", typeof(string), typeof(MessageError), new PropertyMetadata(string.Empty));

    public string Message
    {
        get { return (string)GetValue(MessageProperty); }
        set { SetValue(MessageProperty, value); }
    }
    public MessageError()
    {
        InitializeComponent();
        DataContext = this;
    }
}