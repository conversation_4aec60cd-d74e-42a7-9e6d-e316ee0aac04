<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls.LibraryItemDetailsNew"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             mc:Ignorable="d"
             Height="Auto">
    <UserControl.Resources>
        <ResourceDictionary>
            <converters:UnknownToColorConverter x:Key="UnknownToColorConverter"/>
            <converters:UnknownManufacturerToColorConverter x:Key="UnknownManufacturerToColorConverter"/>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid Margin="0 0 0 5">
        <Border CornerRadius="5" 
                BorderBrush="{Binding BorderColor}" 
                BorderThickness="{Binding BorderThickness}">
            <StackPanel>
                <Grid Margin="5 5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="30"/>
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0" Margin="5 0">
                        <Image x:Name="FamilyImage" 
               Width="150" 
               Height="150"
               Source="{Binding FamilyPreview}"
               Stretch="Uniform"/>
                        <Button x:Name="ChooseImage"
                VerticalAlignment="Bottom" 
                HorizontalAlignment="Right" 
                Command="{Binding ChooseImageCommand}"
                Style="{StaticResource RoundIconButton}">
                            <ContentControl Template="{StaticResource PencilIcon}" HorizontalAlignment="Center"
                            VerticalAlignment="Center"/>
                        </Button>
                    </Grid>
                    <Grid Grid.Column="1" 
           Margin="5 0">
                        <Grid.RowDefinitions>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition Height="40"/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                        </Grid.RowDefinitions>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition/>
                            </Grid.ColumnDefinitions>
                            <TextBlock 
                TextTrimming="CharacterEllipsis" 
                MaxHeight="100" 
                Text="{Binding FileName, StringFormat='File Name: {0}'}" 
                Style="{StaticResource TextBase}"
                />
                        </Grid>
                        <TextBlock Grid.Row="1" 
                   Text="{Binding ProductName, StringFormat='Name: {0}'}" 
                   Style="{StaticResource TextBase}" 
                   Foreground="{Binding ProductName, Converter={StaticResource UnknownToColorConverter}}"/>
                        <TextBlock Grid.Row="2" 
                   Text="{Binding Version, StringFormat='Version: {0}'}" 
                   Style="{StaticResource TextBase}" 
                   Foreground="{Binding Version, Converter={StaticResource UnknownToColorConverter}}"/>
                        <TextBlock Grid.Row="3"
                   Text="{Binding RevitVersion, StringFormat='Revit: {0}'}"
                   Style="{StaticResource TextBase}" Foreground="{StaticResource Blue500}" />
                        <TextBlock Grid.Row="4" 
                   Text="{Binding Manufacturer, StringFormat='Manufacturer: {0}'}" 
                   Style="{StaticResource TextBase}" 
                   Foreground="{Binding Manufacturer, Converter={StaticResource UnknownManufacturerToColorConverter}}"/>

                        <ComboBox Grid.Row="5" 
                                  Tag="Select Family Category" 
                                  Width="250"
                                  HorizontalAlignment="Left"
                                  ItemsSource="{Binding Categories}"
                                  SelectedValuePath="Id"
                                  SelectedValue="{Binding SelectedCategoryId}"
                                  Style="{DynamicResource ComboBoxSmStyleFa}">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="{Binding CategoryName}" />
                                        <TextBlock Text=" (FA)" 
                                                   Foreground="Green"
                                                   Margin="5,0,0,0"
                                                   Visibility="{Binding IsFreeAxezCategory, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                    </StackPanel>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>
                        <TextBlock Grid.Row="6"   
                                   Padding="5 0"
                                   Style="{StaticResource TextBase}"
                                   FontWeight="Normal"
                                   Text="{Binding FileSize}"/>
                        <ProgressBar Grid.Row="7" 
                                     Name="Pb" 
                                     Height="8" 
                                     Foreground="{StaticResource Blue500}"
                                     Value="{Binding UploadProgress}"/>
                        <TextBlock Grid.Row="8">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="Text" 
                                            Value="{Binding Value, ElementName=Pb, StringFormat={}{0:##.0}% done}">
                                    </Setter>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Value, ElementName=Pb}" 
                                                     Value="100">
                                            <Setter Property="Text" 
                                                    Value="Adding Complete"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                        <StackPanel Grid.Row="9">
                            <TextBlock Text="Version Notes"
                                       Margin="0 5"
                                       Style="{StaticResource TextH6}"/>
                            <TextBox Text="{Binding ChangesDescription, 
                        UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                     Style="{StaticResource UiTextBox}"
                                     Height="100" 
                                     TextWrapping="Wrap"
                                     Tag="Enter family version notes"
                                     Name="DescriptionTextBox"
                                     HorizontalAlignment="Left"
                                     Width="400"
                                     Margin="0 0 5 0"/>
                        </StackPanel>
                    </Grid>
                    <Button  Grid.Column="2" 
                             Command="{Binding RejectFileCommand}"
                             Style="{StaticResource CloseExtraSmallButtonStyle}"
                             VerticalAlignment="Top"
                             Content="X"/>
                </Grid>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
