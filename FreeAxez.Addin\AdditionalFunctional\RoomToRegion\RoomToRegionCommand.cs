﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.DB.IFC;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.RoomToRegion
{
    [Transaction(TransactionMode.Manual)]
    public class RoomToRegionCommand : BaseExternalCommand
    {
		public override Result Execute()
		{
			double increaseSize = 0.01;
			double offsetFromWalls = (0.75 / 12.0) + increaseSize;

			List<Room> rooms;
			try
			{
				rooms = RevitManager.UIDocument.Selection.PickObjects(ObjectType.Element, new GenericCategorySelectionFilter(BuiltInCategory.OST_Rooms), "Select rooms").Select(q => RevitManager.Document.GetElement(q) as Room).ToList();
			}
			catch
			{
				return Result.Cancelled;
			}
			SpatialElementBoundaryOptions opt = new SpatialElementBoundaryOptions();
			FilledRegionType frt = new FilteredElementCollector(RevitManager.Document)
				.OfClass(typeof(FilledRegionType))
				.Cast<FilledRegionType>()
				.FirstOrDefault();

			List<FamilyInstance> doors = new FilteredElementCollector(RevitManager.Document)
				.OfClass(typeof(FamilyInstance))
				.OfCategory(BuiltInCategory.OST_Doors)
				.Cast<FamilyInstance>()
				.ToList();

			List<Tuple<List<CurveLoop>, ElementId>> curveLoopListForOverlapCheck = new List<Tuple<List<CurveLoop>, ElementId>>();
			foreach (Room room in rooms)
			{
				List<CurveLoop> loops = new List<CurveLoop>();
				foreach (List<BoundarySegment> bslist in room.GetBoundarySegments(opt))
				{
					CurveLoop loop = new CurveLoop();
					foreach (BoundarySegment bs in bslist)
					{
						loop.Append(bs.GetCurve());
					}
					loops.Add(loop);
				}
				Tuple<List<CurveLoop>, ElementId> tup = new Tuple<List<CurveLoop>, ElementId>(loops, room.Id);
				curveLoopListForOverlapCheck.Add(tup);
			}

			List<List<CurveLoop>> curveLoopListMerged = new List<List<CurveLoop>>();
			List<ElementId> done = new List<ElementId>();
			foreach (Tuple<List<CurveLoop>, ElementId> tup in curveLoopListForOverlapCheck)
			{
				if (done.Contains(tup.Item2))
				{
					continue;
				}
				Solid s1 = GeometryCreationUtilities.CreateExtrusionGeometry(IncreaseLoopsSize(tup.Item1, increaseSize), XYZ.BasisZ, 1);

				Solid doorSolids = null;
				foreach (FamilyInstance door in doors.Where(q =>
															q.FromRoom?.Id == tup.Item2 ||
															q.ToRoom?.Id == tup.Item2))
				{
					Solid s = CreateSolidFromBoundingBox(door);
					if (doorSolids == null)
					{
						doorSolids = s;
						continue;
					}
					doorSolids = BooleanOperationsUtils.ExecuteBooleanOperation(s, doorSolids, BooleanOperationsType.Union);
				}
				if (doorSolids != null)
				{
					s1 = BooleanOperationsUtils.ExecuteBooleanOperation(s1, doorSolids, BooleanOperationsType.Union);
				}

				if (curveLoopListForOverlapCheck.Count == 1)
				{
					curveLoopListMerged.Add(tup.Item1);
					continue;
				}
				bool foundOverlap = false;
				foreach (Tuple<List<CurveLoop>, ElementId> tup2 in curveLoopListForOverlapCheck)
				{
					if (tup.Item2.GetIntegerValue() == tup2.Item2.GetIntegerValue())
					{
						continue;
					}
					if (done.Contains(tup2.Item2))
					{
						continue;
					}
					Solid s2 = GeometryCreationUtilities.CreateExtrusionGeometry(IncreaseLoopsSize(tup2.Item1, increaseSize), XYZ.BasisZ, 1);

					Solid intersect = BooleanOperationsUtils.ExecuteBooleanOperation(s1, s2, BooleanOperationsType.Intersect);
					if (intersect.Volume > 0)
					{
						Solid merge = BooleanOperationsUtils.ExecuteBooleanOperation(s1, s2, BooleanOperationsType.Union);
						Face face = merge.Faces.Cast<Face>().Where(q => q is PlanarFace).Cast<PlanarFace>().First(q => Math.Abs(q.FaceNormal.Z + 1) < 0.01);
						curveLoopListMerged.Add(face.GetEdgesAsCurveLoops().ToList());
						foundOverlap = true;
						done.Add(tup.Item2);
						done.Add(tup2.Item2);
					}
				}
				if (!foundOverlap)
				{
					curveLoopListMerged.Add(tup.Item1);
					done.Add(tup.Item2);
				}
			}
			List<FilledRegion> regions = new List<FilledRegion>();
			using (Transaction t = new Transaction(RevitManager.Document, "Room to Region"))
			{
				t.Start();
				foreach (List<CurveLoop> loops in curveLoopListMerged)
				{
					try
					{
						FilledRegion fr = FilledRegion.Create(RevitManager.Document, frt.Id, RevitManager.Document.ActiveView.Id, DecreaseLoopsSize(loops, offsetFromWalls));
						regions.Add(fr);
					}
					catch (Exception ex)
					{
						TaskDialog.Show("Error", ex.Message);
						foreach (CurveLoop cl in loops)
						{
							makeLines(RevitManager.Document, cl);
						}
					}
				}
				t.Commit();
			}
			TaskDialog.Show("Info", regions.Count + " filled regions created");

			return Result.Succeeded;
		}

		public class GenericCategorySelectionFilter : ISelectionFilter
		{
			BuiltInCategory cat;
			public GenericCategorySelectionFilter(BuiltInCategory _cat)
			{
				cat = _cat;
			}

			public bool AllowElement(Element e)
			{
				if (e.Category != null &&
					e.Category.Id.GetIntegerValue() == (int)cat)
					return true;

				return false;
			}
			public bool AllowReference(Reference r, XYZ point)
			{
				return true;
			}
		}

		public static Solid CreateSolidFromBoundingBox(Element e)
		{
			BoundingBoxXYZ bbox = e.get_BoundingBox(null);

			// Corners in BBox coords

			XYZ pt0 = new XYZ(bbox.Min.X, bbox.Min.Y, bbox.Min.Z);
			XYZ pt1 = new XYZ(bbox.Max.X, bbox.Min.Y, bbox.Min.Z);
			XYZ pt2 = new XYZ(bbox.Max.X, bbox.Max.Y, bbox.Min.Z);
			XYZ pt3 = new XYZ(bbox.Min.X, bbox.Max.Y, bbox.Min.Z);

			// Edges in BBox coords

			Line edge0 = Line.CreateBound(pt0, pt1);
			Line edge1 = Line.CreateBound(pt1, pt2);
			Line edge2 = Line.CreateBound(pt2, pt3);
			Line edge3 = Line.CreateBound(pt3, pt0);

			// Create loop, still in BBox coords

			List<Curve> edges = new List<Curve>();
			edges.Add(edge0);
			edges.Add(edge1);
			edges.Add(edge2);
			edges.Add(edge3);

			double height = bbox.Max.Z - bbox.Min.Z;

			CurveLoop baseLoop = CurveLoop.Create(edges);

			List<CurveLoop> loopList = new List<CurveLoop>();
			loopList.Add(baseLoop);

			Solid preTransformBox = GeometryCreationUtilities
			  .CreateExtrusionGeometry(loopList, XYZ.BasisZ,
				height);

			Solid transformBox = SolidUtils.CreateTransformed(
			  preTransformBox, bbox.Transform);

			return transformBox;
		}

		public static void makeLines(Document doc, CurveLoop loop)
		{
			foreach (Curve c in loop)
			{
				for (double d = 0.2; d <= 1; d = d + 0.2)
				{
					XYZ pt1 = c.Evaluate(d - 0.2, true);
					XYZ pt2 = c.Evaluate(d, true);
					makeLine(doc, pt1, pt2);
				}
			}
		}

		public static ModelLine makeLine(Document doc, XYZ pt1, XYZ pt2)
		{
			if (pt1 == null || pt2 == null)
				return null;

			if (pt1.DistanceTo(pt2) < 0.01)
				return null;

			ModelLine ret = null;
			using (Transaction t = new Transaction(doc, "g"))
			{

				bool started = false;
				try
				{
					t.Start();
					started = true;
				}
				catch
				{ }

				ret = (ModelLine)doc.Create.NewModelCurve(Line.CreateBound(pt1, pt2), SketchPlane.Create(doc, makePlane(doc.Application, pt1, pt2)));

				if (started)
					t.Commit();
			}
			return ret;
		}

		private static Plane makePlane(Autodesk.Revit.ApplicationServices.Application app, XYZ pt1, XYZ pt2)
		{
			XYZ v = pt1.Subtract(pt2);
			double dxy = Math.Abs(v.X) + Math.Abs(v.Y);
			XYZ w = (dxy > 0.0001) ? XYZ.BasisZ : XYZ.BasisY;
			XYZ norm = v.CrossProduct(w).Normalize();
			return Plane.CreateByNormalAndOrigin(norm, pt1);
		}

		public static List<CurveLoop> DecreaseLoopsSize(List<CurveLoop> loops, double d)
		{
			double area0 = ExporterIFCUtils.ComputeAreaOfCurveLoops(loops);
			Solid s = GeometryCreationUtilities.CreateExtrusionGeometry(loops, XYZ.BasisZ, 1);
			for (int i = 0; i < loops.Count; i++)
			{
				CurveLoop cl = loops[i];

				List<CurveLoop> clone = new List<CurveLoop>(loops);

				CurveLoop offsetTrue = OffsetLoop(cl, d, true);
				CurveLoop offsetFalse = OffsetLoop(cl, d, false);
				clone[i] = offsetTrue;
				double area1 = ExporterIFCUtils.ComputeAreaOfCurveLoops(clone);
				if (area1 < area0)
				{
					loops[i] = offsetTrue;
				}
				else
				{
					loops[i] = offsetFalse;
				}
				area0 = ExporterIFCUtils.ComputeAreaOfCurveLoops(loops);
			}
			return loops;
		}

		public static List<CurveLoop> IncreaseLoopsSize(List<CurveLoop> loops, double d)
		{
			double area0 = ExporterIFCUtils.ComputeAreaOfCurveLoops(loops);
			Solid s = GeometryCreationUtilities.CreateExtrusionGeometry(loops, XYZ.BasisZ, 1);
			for (int i = 0; i < loops.Count; i++)
			{
				CurveLoop cl = loops[i];

				List<CurveLoop> clone = new List<CurveLoop>(loops);

				CurveLoop offsetTrue = OffsetLoop(cl, d, true);
				CurveLoop offsetFalse = OffsetLoop(cl, d, false);
				clone[i] = offsetTrue;
				double area1 = ExporterIFCUtils.ComputeAreaOfCurveLoops(clone);
				if (area1 > area0)
				{
					loops[i] = offsetTrue;
				}
				else
				{
					loops[i] = offsetFalse;
				}
				area0 = ExporterIFCUtils.ComputeAreaOfCurveLoops(loops);
			}
			return loops;
		}

		public static CurveLoop OffsetLoop(CurveLoop cl, double d, bool b)
		{
			XYZ dir = XYZ.BasisZ;
			if (!b)
			{
				dir = dir.Negate();
			}
			return CurveLoop.CreateViaOffset(cl, d, dir);
		}
	}
}
