﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Interfaces;
using FreeAxez.Addin.Infrastructure;
using System.Windows.Forms;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils
{
    public static class LevelAndGridManager
    {
        public static void FitLevelsAndElevations(Document source, Document target, IProgressReporter progressReporter,
            CancellationToken cancellationToken)
        {
            progressReporter.ReportStatus("Starting alignment of level elevations...");
            Application.DoEvents();
            cancellationToken.ThrowIfCancellationRequested();

            var sourceLevels = new FilteredElementCollector(source)
                .OfClass(typeof(Level))
                .Cast<Level>()
                .OrderBy(x => x.Elevation)
                .ToList();

            var targetLevels = new FilteredElementCollector(target)
                .OfClass(typeof(Level))
                .Cast<Level>()
                .OrderBy(x => x.Elevation)
                .ToList();

            if (sourceLevels.Count > targetLevels.Count)
            {
                string errorMessage = "The target template does not have enough levels to align with the source.";
                LogHelper.Error(errorMessage);
                progressReporter.ReportStatus(errorMessage);
                throw new Exception(errorMessage);
            }

            var minLevelOffset = 16.0;
            var elevations = sourceLevels
                .Select(x => x.Elevation)
                .ToList();

            progressReporter.ReportStatus(
                $"Found {sourceLevels.Count} levels in the source and {targetLevels.Count} levels in the target. Starting alignment...");
            Application.DoEvents();

            using (var t = new Transaction(target, "Aligning Elevations of Levels"))
            {
                CommonFailuresPreprocessor.SetFailuresPreprocessor(t);
                t.Start();

                int totalLevels = targetLevels.Count;
                for (var i = 0; i < totalLevels; i++)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var targetLevel = targetLevels[i];
                    if (i < elevations.Count)
                    {
                        targetLevel.Elevation = elevations[i];
                        progressReporter.ReportStatus(
                            $"Aligned level '{targetLevel.Name}' to elevation {elevations[i]:F2}.");
                    }
                    else
                    {
                        var previousElevation = targetLevels[i - 1].Elevation;
                        targetLevel.Elevation = previousElevation + minLevelOffset;
                        progressReporter.ReportStatus(
                            $"Set elevation of level '{targetLevel.Name}' to {previousElevation + minLevelOffset:F2} using minimum offset.");
                    }

                    double progress = (double)(i + 1) / totalLevels * 100;
                    progressReporter.ReportProgress(progress);

                    Application.DoEvents();
                }

                t.Commit();
            }

            progressReporter.ReportStatus("Level elevations aligned successfully.");
            progressReporter.ReportProgress(100);
            Application.DoEvents();
        }

        public static void DeleteTargetGrids(Document target, IProgressReporter progressReporter,
            CancellationToken cancellationToken)
        {
            progressReporter.ReportStatus("Preparing to delete all grids from the target document...");
            Application.DoEvents();
            cancellationToken.ThrowIfCancellationRequested();

            var gridIds = new FilteredElementCollector(target)
                .WhereElementIsNotElementType()
                .OfCategory(BuiltInCategory.OST_Grids)
                .ToElementIds()
                .ToList();

            if (gridIds.Count == 0)
            {
                string message = "No grids found in the target document to delete.";
                LogHelper.Information(message);
                progressReporter.ReportStatus(message);
                return;
            }

            int totalGrids = gridIds.Count;
            int processedGrids = 0;

            progressReporter.ReportStatus($"Found {totalGrids} grids to delete. Starting process...");
            Application.DoEvents();

            const int gridsPerBatch = 100;
            var gridIdBatches = gridIds
                .Select((gridId, index) => new { GridId = gridId, Index = index })
                .GroupBy(x => x.Index / gridsPerBatch)
                .Select(g => g.Select(x => x.GridId).ToList())
                .ToList();

            foreach (var batch in gridIdBatches)
            {
                cancellationToken.ThrowIfCancellationRequested();

                using (var t = new Transaction(target, "Delete Grids Batch"))
                {
                    CommonFailuresPreprocessor.SetFailuresPreprocessor(t);
                    t.Start();

                    target.Delete(batch);

                    t.Commit();
                }

                processedGrids += batch.Count;
                double progress = (double)processedGrids / totalGrids * 100;
                progressReporter.ReportProgress(progress);
                progressReporter.ReportStatus($"Deleted {processedGrids}/{totalGrids} grids.");
                Application.DoEvents();
            }

            progressReporter.ReportStatus("All grids deleted successfully.");
            progressReporter.ReportProgress(100);
            Application.DoEvents();
        }
    }
}
