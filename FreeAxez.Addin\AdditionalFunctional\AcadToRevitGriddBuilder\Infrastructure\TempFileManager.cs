using System;
using System.IO;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Infrastructure
{
    public class TempFileManager : IDisposable
    {
        public string TempDirectory { get; private set; }
        public string JsonFilePath { get; private set; }

        public TempFileManager()
        {
            CreateTempDirectory();
        }

        private void CreateTempDirectory()
        {
            var tempPath = Path.GetTempPath();
            var uniqueFolder = $"GriddBuilder_{Guid.NewGuid():N}";
            TempDirectory = Path.Combine(tempPath, uniqueFolder);
            
            Directory.CreateDirectory(TempDirectory);
            JsonFilePath = Path.Combine(TempDirectory, "base_lines.json");
        }

        public void Dispose()
        {
            CleanupTempFiles();
        }

        private void CleanupTempFiles()
        {
            try
            {
                if (Directory.Exists(TempDirectory))
                {
                    Directory.Delete(TempDirectory, true);
                }
            }
            catch (Exception ex)
            {
                // Log but don't throw - cleanup failure shouldn't break the main process
                System.Diagnostics.Debug.WriteLine($"Failed to cleanup temp directory: {ex.Message}");
            }
        }

        public bool IsReady()
        {
            return Directory.Exists(TempDirectory);
        }
    }
}
