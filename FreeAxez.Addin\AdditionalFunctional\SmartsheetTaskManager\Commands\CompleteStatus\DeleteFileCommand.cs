﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Commands.CompleteStatus
{
    public class DeleteFileCommand : CommandBase
    {
        private readonly CompleteStatusViewModel _completeStatusViewModel;

        public DeleteFileCommand(CompleteStatusViewModel completeStatusViewModel)
        {
            _completeStatusViewModel = completeStatusViewModel;
        }

        public override void Execute(object @object)
        {
            if (@object is string filePath)
            {
                _completeStatusViewModel.AttachedFilePaths.Remove(filePath);
            }
        }
    }
}
