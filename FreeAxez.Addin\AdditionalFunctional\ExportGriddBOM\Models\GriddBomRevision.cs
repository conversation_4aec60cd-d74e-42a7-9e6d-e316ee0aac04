﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Utils;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Utils;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Models
{
    public class GriddBomRevision
    {
        private readonly RevisionViewModel _revision;
        private readonly List<string> _selectedAccessoryNames;
        

        public GriddBomRevision(RevisionViewModel revision, List<string> selectedAccessoryNames)
        {
            _revision = revision;
            _selectedAccessoryNames = selectedAccessoryNames;
        }


        public List<GriddBomLevel> Levels { get; private set; }
        public string SheetName { get; private set; }
        public string RevisionDate => _revision.RevisionDate;
        public string RevisionAuthor => _revision.RevisionAuthor;
        public string RevisionNumber => _revision.RevisionNumber;


        public void CalculateBom()
        {
            Levels = new List<GriddBomLevel>();

            SheetName = string.Format(GriddBomExcelSchema.RevSheetNameTemplate,
                RevisionNumber,
                RevisionDate);

            var revitLevels = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Level))
                .Cast<Level>();

            var griddElements = GriddProductCollector.Collect(_selectedAccessoryNames).Select(p => p.Element).ToList();
            var areas = GetAreaElements();

            var griddElementIds = areas.Concat(griddElements).Select(e => e.Id.GetIntegerValue()).ToHashSet();

            var smartLookupParameter = new SmartLookupParameter();
            foreach (var revitLevel in revitLevels)
            {
                var levelElementIds = revitLevel
                    .GetDependentElements(new ElementIsElementTypeFilter(true))
                    .Select(elemId => elemId.GetIntegerValue())
                    .ToHashSet();

                var elementsForLevel = griddElementIds.Intersect(levelElementIds)
                    .Select(id => RevitManager.Document.GetElement(new ElementId(id)))
                    .ToList();

                var level = new GriddBomLevel(revitLevel, elementsForLevel, smartLookupParameter);
                level.CalculateBom();
                Levels.Add(level);
            }
        }

        private List<Floor> GetAreaElements()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsNotElementType()
                .OfCategory(BuiltInCategory.OST_Floors)
                .Cast<Floor>()
                .Where(f => f.Name == Constants.FloorTypeNameGridd
                         || f.Name == Constants.FloorTypeNameReinforced
                         || f.Name == Constants.FloorTypeNameOverall)
                .Where(GlobalBOMProductFilter.PassesFilter)
                .ToList();
        }
    }
}
