﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Markup;

namespace FreeAxez.Addin.AdditionalFunctional.FindAndReplaceViewName.Views
{
    [ContentProperty("Text")]
    public class HighlightedTextBlock : TextBlock
    {
        public static readonly DependencyProperty TextProperty =
    DependencyProperty.Register("Text", typeof(string), typeof(HighlightedTextBlock),
        new PropertyMetadata(string.Empty, OnTextChanged));

        public static readonly DependencyProperty SearchTextProperty =
            DependencyProperty.Register("SearchText", typeof(string), typeof(HighlightedTextBlock),
                new PropertyMetadata(string.Empty, OnTextChanged));

        public string Text
        {
            get { return (string)GetValue(TextProperty); }
            set { SetValue(TextProperty, value); }
        }

        public string SearchText
        {
            get { return (string)GetValue(SearchTextProperty); }
            set { SetValue(SearchTextProperty, value); }
        }

        private static void OnTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = (HighlightedTextBlock)d;
            control.UpdateText();
        }

        private void UpdateText()
        {
            Inlines.Clear();

            if (string.IsNullOrEmpty(Text))
                return;

            if (string.IsNullOrEmpty(SearchText))
            {
                Inlines.Add(new Run(Text));
                return;
            }

            // Find all occurrences of the search text
            int currentIndex = 0;
            int searchIndex;

            while ((searchIndex = Text.IndexOf(SearchText, currentIndex, System.StringComparison.Ordinal)) >= 0)
            {
                // Add the text before the match
                if (searchIndex > currentIndex)
                {
                    Inlines.Add(new Run(Text.Substring(currentIndex, searchIndex - currentIndex)));
                }

                // Add the highlighted match
                var run = new Run(Text.Substring(searchIndex, SearchText.Length))
                {
                    Background = System.Windows.Media.Brushes.Yellow,
                    FontWeight = FontWeights.Bold
                };
                Inlines.Add(run);

                // Move the current index to after this match
                currentIndex = searchIndex + SearchText.Length;
            }

            // Add any remaining text after the last match
            if (currentIndex < Text.Length)
            {
                Inlines.Add(new Run(Text.Substring(currentIndex)));
            }
        }
    }
}
