using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services;

public class RailingsGeneratorService
{
    public List<RailingModel> GenerateRailings(GeometryTreeModel geometryTree)
    {
        var railings = new List<RailingModel>();
        int idCounter = 1;

        foreach (var pathEntry in geometryTree.Paths)
        {
            var outlet = pathEntry.Key;
            var path = pathEntry.Value;

            var railing = new RailingModel
            {
                Id = idCounter++,
                Path = path,
                WireCount = outlet.WireCount,
                SourceOutlet = outlet
            };

            railings.Add(railing);
        }

        return railings;
    }
}
