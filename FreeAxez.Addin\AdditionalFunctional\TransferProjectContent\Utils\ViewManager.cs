﻿using System.Collections.ObjectModel;
using System.Text.RegularExpressions;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Interfaces;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Models;
using FreeAxez.Addin.Infrastructure;
using System.Windows.Forms;
using View = Autodesk.Revit.DB.View;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils
{
    public static class ViewManager
    {
        public static List<ViewPlan> RebaseViewsBetweenProjects(
            Document sourceDoc,
            Document targetDoc,
            ObservableCollection<ViewTemplateMapping> templateMappings,
            IProgressReporter progressReporter,
            CancellationToken cancellationToken)
        {

            var sourceViews = CollectViewsBySheetSize(sourceDoc, true);

            // Create generic views for each level in the target document
            var genericViews = CreateGenericViews(targetDoc, progressReporter, cancellationToken);

            // Store the generic views in a dictionary for quick access
            var genericViewDict = genericViews.ToDictionary(v => v.GenLevel.Name, v => v);

            var targetViews = new List<ViewPlan>();

            var independentViews = sourceViews.Where(v => !IsViewDependent(v) && v.GenLevel != null).ToList();

            int totalViews = independentViews.Count;
            int processedViews = 0;

            foreach (var sourceView in independentViews)
            {
                cancellationToken.ThrowIfCancellationRequested();

                progressReporter.ReportStatus($"Processing view: {sourceView.Name}");
                if (sourceView.Name == "O")
                {
                    continue;
                }

                try
                {
                    using (var t = new Transaction(targetDoc, $"Rebase View {sourceView.Name}"))
                    {
                        t.Start();

                        LogHelper.Information($"Started transaction for view: {sourceView.Name}");

                        // Get the level of the source view
                        var sourceLevelName = sourceView.GenLevel.Name;

                        if (!genericViewDict.ContainsKey(sourceLevelName))
                        {
                            LogHelper.Error(
                                $"No generic view found for level {sourceLevelName}. Source view: {sourceView.Name}");
                        }

                        var genericView = genericViewDict[sourceLevelName];

                        // Duplicate the generic view
                        var targetViewId = genericView.Duplicate(ViewDuplicateOption.Duplicate);
                        var targetView = targetDoc.GetElement(targetViewId) as ViewPlan;
                        targetView.Name = sourceView.Name;

                        if (targetView == null || !targetView.IsValidObject)
                        {
                            LogHelper.Error(
                                $"Failed to duplicate generic view for source view {sourceView.Name}. Generic view: {genericView.Name}");
                        }

                        LogHelper.Information($"Successfully duplicated view: {targetView.Name}");

                        // Set the view name to match the source view

                        CopyViewProperties(sourceView, targetView, sourceDoc, targetDoc);
                        AssignViewTemplate(sourceView, targetView, templateMappings);

                        targetViews.Add(targetView);

                        var dependentViewIds = sourceView.GetDependentViewIds();
                        int totalDependentViews = dependentViewIds.Count;
                        int processedDependentViews = 0;

                        LogHelper.Information(
                            $"Processing {totalDependentViews} dependent views for source view: {sourceView.Name}");

                        foreach (var dependentViewId in dependentViewIds)
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            var sourceDependentView = sourceDoc.GetElement(dependentViewId) as ViewPlan;
                            if (sourceDependentView == null || !sourceDependentView.IsValidObject)
                            {
                                LogHelper.Warning($"Dependent view is invalid or null. Skipping...");
                                continue;
                            }

                            try
                            {
                                // Duplicate the target view as dependent
                                var targetDependentViewId = targetView.Duplicate(ViewDuplicateOption.AsDependent);
                                var dependentView = targetDoc.GetElement(targetDependentViewId) as ViewPlan;

                                if (dependentView == null || !dependentView.IsValidObject)
                                {
                                    LogHelper.Error(
                                        $"Failed to create dependent view for source dependent view: {sourceDependentView.Name}");
                                    continue;
                                }

                                LogHelper.Information($"Successfully duplicated dependent view: {dependentView.Name}");

                                // Set the view name to match the source dependent view
                                dependentView.Name = sourceDependentView.Name;

                                CopyViewProperties(sourceDependentView, dependentView, sourceDoc, targetDoc);
                                AssignViewTemplate(sourceDependentView, dependentView, templateMappings);

                                targetViews.Add(dependentView);

                                processedDependentViews++;
                                double dependentProgress = (double)processedDependentViews / totalDependentViews * 100;
                                progressReporter.ReportProgress(dependentProgress);
                            }
                            catch (Exception ex)
                            {
                                LogHelper.Error(
                                    $"Error processing dependent view {sourceDependentView.Name}: {ex.Message}");
                            }
                        }

                        LogHelper.Information(
                            $"Successfully processed all dependent views for source view: {sourceView.Name}");
                        t.Commit();
                        LogHelper.Information($"Transaction committed for view: {sourceView.Name}");

                    }
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Error rebasing view {sourceView.Name}: {ex.Message}");
                }

                processedViews++;
                double progress = (double)processedViews / totalViews * 100;
                progressReporter.ReportProgress(progress);

                Application.DoEvents();
            }

            DisableCropBoxVisibilityForTargetViews(targetViews, targetDoc, progressReporter, cancellationToken);
            CreateReferenceCalloutsForViews(sourceDoc, targetDoc, targetViews, progressReporter, cancellationToken);
            try
            {
                using (var t = new Transaction(targetDoc, "Delete Generic Views"))
                {
                    CommonFailuresPreprocessor.SetFailuresPreprocessor(t);
                    t.Start();

                    foreach (var genericView in genericViews)
                    {
                        targetDoc.Delete(genericView.Id);
                    }

                    t.Commit();
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error deleting generic views: {ex.Message}");
            }

            return targetViews;
        }

        private static List<ViewPlan> CreateGenericViews(Document targetDoc, IProgressReporter progressReporter, CancellationToken cancellationToken)
        {
            // Get levels from the target document (assuming levels are identical)
            var levels = new FilteredElementCollector(targetDoc)
                .OfClass(typeof(Level))
                .Cast<Level>()
                .ToList();

            var genericViews = new List<ViewPlan>();
            int totalLevels = levels.Count;
            int processedLevels = 0;

            using (var t = new Transaction(targetDoc, "Create Generic Views"))
            {
                CommonFailuresPreprocessor.SetFailuresPreprocessor(t);
                t.Start();

                var viewFamilyType = new FilteredElementCollector(targetDoc)
                    .OfClass(typeof(ViewFamilyType))
                    .Cast<ViewFamilyType>()
                    .FirstOrDefault(vft => vft.ViewFamily == ViewFamily.FloorPlan);

                if (viewFamilyType == null)
                {
                    throw new InvalidOperationException("No ViewFamilyType found for Floor Plans in the target document.");
                }

                foreach (var level in levels)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var newView = ViewPlan.Create(targetDoc, viewFamilyType.Id, level.Id);
                    newView.Name = $"GENERIC VIEW {level.Name}";
                    if (newView == null)
                    {
                        throw new InvalidOperationException($"Failed to create a new ViewPlan for level {level.Name}.");
                    }

                    // Set "View Size" parameter to "GENERIC VIEWS"
                    var viewSizeParam = newView.LookupParameter("View Size");
                    if (viewSizeParam != null && !viewSizeParam.IsReadOnly)
                    {
                        viewSizeParam.Set("GENERIC VIEWS");
                    }
                    else
                    {
                        LogHelper.Warning($"Could not set 'View Size' for view '{newView.Name}'.");
                    }

                    genericViews.Add(newView);

                    processedLevels++;
                    double progress = (double)processedLevels / totalLevels * 100;
                    progressReporter.ReportProgress(progress);
                    progressReporter.ReportStatus($"Created generic view for level: {level.Name}");

                    Application.DoEvents();
                }

                targetDoc.Regenerate();

                t.Commit();
            }

            return genericViews;
        }

        private static void CopyViewProperties(
            View sourceView,
            View targetView,
            Document sourceDoc,
            Document targetDoc)
        {
            try
            {
                targetView.CropBoxActive = sourceView.CropBoxActive;
                targetView.CropBoxVisible = sourceView.CropBoxVisible;
                targetView.CropBox = sourceView.CropBox;

                var sourceScopeBoxParam = sourceView.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP);
                if (sourceScopeBoxParam != null && sourceScopeBoxParam.HasValue)
                {
                    var sourceScopeBoxId = sourceScopeBoxParam.AsElementId();
                    var sourceScopeBox = sourceDoc.GetElement(sourceScopeBoxId) as Element;
                    if (sourceScopeBox != null)
                    {
                        var targetScopeBox = new FilteredElementCollector(targetDoc)
                            .OfCategory(BuiltInCategory.OST_VolumeOfInterest)
                            .WhereElementIsNotElementType()
                            .FirstOrDefault(e => e.Name == sourceScopeBox.Name);

                        if (targetScopeBox != null)
                        {
                            var targetScopeBoxParam = targetView.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP);
                            if (targetScopeBoxParam != null && !targetScopeBoxParam.IsReadOnly)
                            {
                                targetScopeBoxParam.Set(targetScopeBox.Id);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Warning($"Error copying crop box or scope box properties: {ex.Message}");
            }

            try
            {
                var sourceViewSizeParam = sourceView.LookupParameter("View Size");
                if (sourceViewSizeParam != null && sourceViewSizeParam.HasValue)
                {
                    var targetViewSizeParam = targetView.LookupParameter("View Size");
                    if (targetViewSizeParam != null && !targetViewSizeParam.IsReadOnly)
                    {
                        var viewSizeValue = sourceViewSizeParam.AsString();
                        targetViewSizeParam.Set(viewSizeValue);
                    }
                    else
                    {
                        LogHelper.Warning($"Could not set 'View Size' for view '{targetView.Name}' due to a read-only parameter.");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Warning($"Error copying 'View Size' property: {ex.Message}");
            }
        }

        private static void AssignViewTemplate(
            View sourceView,
            View targetView,
            ObservableCollection<ViewTemplateMapping> templateMappings)
        {

            try
            {
                var sourceTemplateId = sourceView.ViewTemplateId;
                if (sourceTemplateId == ElementId.InvalidElementId)
                {
                    LogHelper.Information($"Source view '{sourceView.Name}' has no view template.");
                    return; 
                }

                var mapping = templateMappings.FirstOrDefault(m => m.Source.ViewTemplate.Id == sourceTemplateId);
                if (mapping == null)
                {
                    LogHelper.Warning($"No mapping found for source template in view '{sourceView.Name}'.");
                    return;
                }

                var targetTemplate = mapping.Target?.ViewTemplate;
                if (targetTemplate != null)
                {
                    var templateName = mapping.Target.Name;
                    var matchedTemplate = new FilteredElementCollector(targetView.Document)
                        .OfClass(typeof(View))
                        .WhereElementIsNotElementType()
                        .Cast<View>()
                        .FirstOrDefault(v => v.IsTemplate && string.Equals(v.Name, templateName, StringComparison.OrdinalIgnoreCase));

                    if (matchedTemplate != null)
                    {

                        targetView.ViewTemplateId = matchedTemplate.Id;
                        LogHelper.Information($"Assigned template '{templateName}' to view '{targetView.Name}' by name match.");
                    }
                    else
                    {
                        LogHelper.Warning($"No matching template found in target document for template name '{templateName}'.");
                    }
                }
                else
                {
                    LogHelper.Warning($"Target template is null for source view '{sourceView.Name}'.");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Warning($"Error assigning view template for view '{targetView.Name}': {ex}");
            }
        }




        private static void DisableCropBoxVisibilityForTargetViews(List<ViewPlan> targetViews, Document targetDoc, IProgressReporter progressReporter, CancellationToken cancellationToken)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                using (var t = new Transaction(targetDoc, "Disable CropBox Visibility"))
                {
                    CommonFailuresPreprocessor.SetFailuresPreprocessor(t);
                    t.Start();

                    int totalViews = targetViews.Count;
                    int processedViews = 0;

                    foreach (var view in targetViews)
                    {
                        if (view != null)
                        {
                            view.CropBoxVisible = false;
                            LogHelper.Information($"Disabled CropBox visibility for: {view.Name}");
                        }

                        processedViews++;
                        double progress = (double)processedViews / totalViews * 100;
                        progressReporter.ReportProgress(progress);
                    }
                    targetDoc.Regenerate();

                    t.Commit();
                }

            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error disabling CropBox visibility: {ex.Message}");
            }
        }

        public static List<ViewPlan> CollectViewsBySheetSize(Document doc, bool containNumbers)
        {
            var containsNumbersRegex = new Regex(@"\d");
            return new FilteredElementCollector(doc)
                .OfClass(typeof(ViewPlan))
                .Cast<ViewPlan>()
                .Where(sheet =>
                {
                    if (sheet.IsTemplate)
                    {
                        return false;
                    }

                    var param = sheet.LookupParameter("View Size");
                    var value = param?.AsString();
                    return !string.IsNullOrEmpty(value) &&
                           (containNumbers
                               ? containsNumbersRegex.IsMatch(value)
                               : !containsNumbersRegex.IsMatch(value));
                })
                .ToList();
        }

        private static bool IsViewDependent(View view)
        {
            return view.GetPrimaryViewId() != ElementId.InvalidElementId;
        }

        public static void DeleteViewsFromDocument(Document doc, List<ViewPlan> viewsToDelete, IProgressReporter progressReporter, CancellationToken cancellationToken)
        {
            int totalViews = viewsToDelete.Count;
            int processedViews = 0;

            foreach (var view in viewsToDelete)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (view == null || !view.IsValidObject)
                {
                    LogHelper.Warning("View is null or no longer valid. Skipping deletion.");
                    continue;
                }


                try
                {
                    using (var trans = new Transaction(doc, $"Delete View {view.Name}"))
                    {
                        string viewName = string.IsNullOrEmpty(view.Name) ? "<Unnamed View>" : view.Name;
                        progressReporter.ReportStatus($"Deleting view: {viewName}");

                        CommonFailuresPreprocessor.SetFailuresPreprocessor(trans);
                        trans.Start();

                        if (view.IsValidObject && !view.IsTemplate) 
                        {
                            doc.Delete(view.Id);
                        }
                        else
                        {
                            LogHelper.Warning($"View '{view.Name}' is no longer valid during deletion process.");
                        }

                        trans.Commit();
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Error deleting view {view.Name}: {ex.Message}");
                }

                processedViews++;
                double progress = (double)processedViews / totalViews * 100;
                progressReporter.ReportProgress(progress);

                Application.DoEvents();
            }
        }

        public static List<ViewPlan> GetCalloutsFromView(Document document, View view)
        {
            var dependentElementIds = view.GetDependentElements(new ElementClassFilter(typeof(ViewPlan)));

            var callouts = new List<ViewPlan>();
            foreach (var elementId in dependentElementIds)
            {
                var dependentView = document.GetElement(elementId) as ViewPlan;
#if !revit2020 && !revit2021 && !revit2022
                if (dependentView != null && dependentView.IsCallout)
                {
                    callouts.Add(dependentView);
                }
#endif
            }

            return callouts;
        }

        public static void CreateReferenceCalloutsForViews(
            Document sourceDocument,
            Document targetDocument,
            List<ViewPlan> targetViews,
            IProgressReporter progressReporter,
            CancellationToken cancellationToken)
        {
            foreach (var targetView in targetViews)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (targetView == null || !targetView.IsValidObject)
                {
                    LogHelper.Error("Target view is null or invalid. Skipping...");
                    continue;
                }

                progressReporter.ReportStatus($"Processing view: {targetView.Name}");

                var sourceView = FindViewByName(sourceDocument, targetView.Name);
                if (sourceView == null)
                {
                    LogHelper.Warning($"Target view matching source view '{targetView.Name}' not found.");
                    continue;
                }

                var callouts = GetCalloutsFromView(sourceDocument, sourceView);
                if (callouts.Count == 0)
                {
                    if (!string.IsNullOrEmpty(sourceView.Name) && sourceView.Name.IndexOf("KEY", StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        var additionalViewPlans = GetViewPlansByViewerName(sourceDocument, sourceView);
                        if (additionalViewPlans.Any())
                        {
                            LogHelper.Information($"Found additional view plans for view '{sourceView.Name}' with KEY in the name.");
                            callouts.AddRange(additionalViewPlans);
                        }
                    }

                    if (callouts.Count == 0)
                    {
                        LogHelper.Information($"No callouts found in source view '{sourceView.Name}'.");
                        continue;
                    }
                }

                foreach (var callout in callouts)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var bounds = GetCropRegionBounds(callout);

                    var targetReferencedView = FindViewByName(targetDocument, callout.Name);
                    if (targetReferencedView == null)
                    {
                        LogHelper.Warning($"Target referenced view matching callout '{callout.Name}' not found.");
                        continue;
                    }

                    try
                    {
                        using (var transaction = new Transaction(targetDocument, "Create Reference Callout"))
                        {
                            CommonFailuresPreprocessor.SetFailuresPreprocessor(transaction);
                            transaction.Start();

                            ViewSection.CreateReferenceCallout(
                                targetDocument,
                                targetView.Id,
                                targetReferencedView.Id,
                                bounds.MinPoint,
                                bounds.MaxPoint);

                            LogHelper.Information($"Target view name '{targetView.Name}', reference view name {targetReferencedView.Name}");

                            transaction.Commit();
                        }

                        progressReporter.ReportStatus($"Callout '{callout.Name}' created in view '{targetView.Name}'.");
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"Error creating callout '{callout.Name}': {ex.Message}");
                    }
                }
            }
        }

        public static List<ViewPlan> GetViewPlansByViewerName(Document document, View sourceView)
        {
            var viewerElements = new FilteredElementCollector(document, sourceView.Id)
                .OfCategory(BuiltInCategory.OST_Viewers)
                .WhereElementIsNotElementType()
                .ToList();

            var viewPlans = new List<ViewPlan>();

            foreach (var viewerElement in viewerElements)
            {
                if (viewerElement is Element element && element.Name != null)
                {
                    var matchingViewPlan = new FilteredElementCollector(document)
                        .OfClass(typeof(ViewPlan))
                        .Cast<ViewPlan>()
                        .FirstOrDefault(viewPlan =>
                            viewPlan.Name.Equals(element.Name, StringComparison.OrdinalIgnoreCase));

                    if (matchingViewPlan != null)
                    {
                        viewPlans.Add(matchingViewPlan);
                    }
                }
            }

            return viewPlans;
        }

        public static (XYZ MinPoint, XYZ MaxPoint) GetCropRegionBounds(ViewPlan viewPlan)
        {
            if (viewPlan == null)
                throw new ArgumentNullException(nameof(viewPlan), "ViewPlan cannot be null.");

            var cropRegionShapeManager = viewPlan.GetCropRegionShapeManager();

            XYZ minPoint = new XYZ(double.MaxValue, double.MaxValue, double.MaxValue);
            XYZ maxPoint = new XYZ(double.MinValue, double.MinValue, double.MinValue);

            foreach (var curveLoop in cropRegionShapeManager.GetCropShape())
            {
                foreach (var curve in curveLoop)
                {
                    var origin = curve.GetEndPoint(0);

                    minPoint = new XYZ(
                        Math.Min(minPoint.X, origin.X),
                        Math.Min(minPoint.Y, origin.Y),
                        Math.Min(minPoint.Z, origin.Z));

                    maxPoint = new XYZ(
                        Math.Max(maxPoint.X, origin.X),
                        Math.Max(maxPoint.Y, origin.Y),
                        Math.Max(maxPoint.Z, origin.Z));
                }
            }

            return (MinPoint: minPoint, MaxPoint: maxPoint);
        }

        public static View FindViewByName(Document document, string viewName)
        {
            return new FilteredElementCollector(document)
                .OfClass(typeof(View))
                .Cast<View>()
                .FirstOrDefault(view => view.Name.Equals(viewName, StringComparison.OrdinalIgnoreCase));
        }


        private class DuplicateTypeNamesHandler : IDuplicateTypeNamesHandler
        {
            public DuplicateTypeAction OnDuplicateTypeNamesFound(DuplicateTypeNamesHandlerArgs args)
            {
                return DuplicateTypeAction.UseDestinationTypes;
            }
        }

    }
}