﻿using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.ViewModels;
using System.Windows;
using System.Windows.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Views
{
    public partial class ElectricalCircuitOptionsWindow : Window
    {
        public ElectricalCircuitOptionsWindow()
        {
            InitializeComponent();
        }

        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            foreach (LevelViewModel revitView in dataGrid.SelectedItems)
            {
                revitView.IsCheck = (bool)(sender as CheckBox).IsChecked;
            }
        }
    }
}
