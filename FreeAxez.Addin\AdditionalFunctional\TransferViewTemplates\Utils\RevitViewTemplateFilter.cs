﻿using System;
using System.Linq;
using FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Models;

namespace FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Utils;

public static class RevitViewTemplateFilter
{
    public static bool FilterRevitViewTemplate(RevitViewTemplate template, string searchText, string selectedDiscipline,
        string selectedViewType, string selectedDocumentName)
    {
        return template != null &&
               FilterBySearchText(template, searchText) &&
               FilterByDiscipline(template, selectedDiscipline) &&
               FilterByViewType(template, selectedViewType) &&
               FilterByDocumentName(template, selectedDocumentName);
    }

    private static bool FilterBySearchText(RevitViewTemplate template, string searchText)
    {
        if (string.IsNullOrEmpty(searchText)) return true;

        var searchTerms = new[]
        {
            template?.Name,
            template?.Discipline.ToString(),
            template?.ViewType.ToString(),
            template?.RevitDocument?.Title
        };

        return searchTerms.Any(term =>
            !string.IsNullOrEmpty(term) && term.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0);
    }

    private static bool FilterByDiscipline(RevitViewTemplate template, string selectedDiscipline)
    {
        return string.IsNullOrEmpty(selectedDiscipline) || string.Equals(template.Discipline.ToString(),
            selectedDiscipline, StringComparison.OrdinalIgnoreCase);
    }

    private static bool FilterByViewType(RevitViewTemplate template, string selectedViewType)
    {
        return string.IsNullOrEmpty(selectedViewType) || string.Equals(template.ViewType.ToString(), selectedViewType,
            StringComparison.OrdinalIgnoreCase);
    }

    private static bool FilterByDocumentName(RevitViewTemplate template, string selectedDocumentName)
    {
        return string.IsNullOrEmpty(selectedDocumentName) || (template.RevitDocument != null &&
                                                              template.RevitDocument.Title.IndexOf(selectedDocumentName,
                                                                  StringComparison.OrdinalIgnoreCase) >= 0);
    }
}