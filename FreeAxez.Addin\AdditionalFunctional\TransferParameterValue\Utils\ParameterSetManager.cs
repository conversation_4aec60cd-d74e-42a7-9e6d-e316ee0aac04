﻿using FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.Models;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml.Serialization;

namespace FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.Utils
{
    public static class ParameterSetManager
    {
        private readonly static List<UserParameterSet> _presets = new List<UserParameterSet>()
        {
            new UserParameterSet()
            {
                Name = "Electrical",
                ParameterNames = new List<string>()
                {
                    "Track Assignment",
                    "Component Number",
                    "FP Type",
                }
            },
        };
        
        
        public const string CustomParameterSetName = "<Custom>";


        public static List<UserParameterSet> GetParameterSetsFromSettings()
        {
            if (string.IsNullOrEmpty(Properties.Settings.Default.userParameterSets))
            {
                SaveParameterSetsToSettings(_presets);
            }

            var parameterSets = new List<UserParameterSet>();
            var xmlSerializer = new XmlSerializer(typeof(List<UserParameterSet>));

            using (TextReader reader = new StringReader(Properties.Settings.Default.userParameterSets))
            {
                try
                {
                    parameterSets = (List<UserParameterSet>)xmlSerializer.Deserialize(reader);
                }
                catch
                {

                }
            }

            if (parameterSets.FirstOrDefault(s => s.Name == CustomParameterSetName) == null)
            {
                parameterSets.Add(new UserParameterSet() { Name = CustomParameterSetName });
            }

            parameterSets.First(s => s.Name == CustomParameterSetName).ParameterNames = new List<string>();

            return parameterSets;
        }

        public static void SaveParameterSetsToSettings(List<UserParameterSet> parameterSets)
        {
            var xmlSerializer = new XmlSerializer(typeof(List<UserParameterSet>));

            using (StringWriter textWriter = new StringWriter())
            {
                xmlSerializer.Serialize(textWriter, parameterSets);
                Properties.Settings.Default.userParameterSets = textWriter.ToString();
                Properties.Settings.Default.Save();
            }
        }
    }
}
