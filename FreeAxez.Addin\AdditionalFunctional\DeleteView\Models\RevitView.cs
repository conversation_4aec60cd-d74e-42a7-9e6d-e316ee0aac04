﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.DeleteView.Models
{
    public class RevitView : BaseViewModel
    {
        private bool _isCheck;


        public bool IsCheck
        {
            get 
            { 
                return _isCheck; 
            }
            set
            {
                _isCheck = value;
                OnPropertyChanged();
            }
        }

        public string Name { get; set; }

        public Element View { get; set; }
    }
}
