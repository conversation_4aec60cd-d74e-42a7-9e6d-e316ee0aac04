﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.LegendManagement.Stores;
using FreeAxez.Addin.AdditionalFunctional.LegendManagement.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.LegendManagement.Views;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.LegendManagement
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    [Journaling(JournalingMode.NoCommandData)]
    public class LegendManagementCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var browserOrganization = BrowserOrganization.GetCurrentBrowserOrganizationForSheets(RevitManager.Document);

            var projectLegendMappingsStore = new ProjectLegendMappingsStore(browserOrganization);
            //var correctLegendMappingsStore = new CorrectLegendMappingsStore();

            var view = new LegendManagementView();
            var viewModel = new LegendManagementViewModel(view, projectLegendMappingsStore/*, correctLegendMappingsStore*/);
            view.DataContext = viewModel;

            view.ShowDialog();

            return Result.Succeeded;
        }
    }
}
