﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.TagAllRailings.Utils;
using FreeAxez.Addin.AdditionalFunctional.TagAllRailings.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllRailings
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    internal class TagAllRailingsCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            if (RevitManager.UIDocument.ActiveView.ViewType == ViewType.ThreeD)
            {
                InfoDialog.ShowDialog("Warning", "Unable to place tags in 3D view.\nPlease select another view.");
                return Result.Cancelled;
            }

            var countOfRailingTagTypes = new FilteredElementCollector(RevitManager.Document)
                                                .OfCategory(BuiltInCategory.OST_StairsRailingTags)
                                                .WhereElementIsElementType()
                                                .Count();
            if (countOfRailingTagTypes == 0)
            {
                InfoDialog.ShowDialog("Warning", "There are no railings tag type in the project.\nPlease upload at least one type before tagging all railings.");
                return Result.Cancelled;
            }

            var countOfVisibleRailingInView = new FilteredElementCollector(RevitManager.Document, RevitManager.UIDocument.ActiveView.Id)
                                    .OfCategory(BuiltInCategory.OST_StairsRailing)
                                    .WhereElementIsNotElementType()
                                    .Count();
            if (countOfVisibleRailingInView == 0)
            {
                InfoDialog.ShowDialog("Warning", "The current view has no visible railings for tagging.");
                return Result.Cancelled;
            }

            if (TagParameter.ParameterNotAssignedToTheRailing())
            {
                InfoDialog.ShowDialog("Warning", $"The railing has no {TagParameter.TagParameterName} parameter assigned.");
                return Result.Cancelled;
            }

            var tagAllRailinsView = new TagAllRailingsView();
            tagAllRailinsView.ShowDialog();

            return Result.Succeeded;
        }
    }
}