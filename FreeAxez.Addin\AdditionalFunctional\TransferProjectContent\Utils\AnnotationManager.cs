﻿using System.Windows.Forms;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Enums;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Interfaces;
using FreeAxez.Addin.Infrastructure;
using View = Autodesk.Revit.DB.View;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils
{
    public static class AnnotationManager
    {
        public static void CopyAnnotationsAndAdjust(
            Document sourceDoc,
            Document targetDoc,
            List<ViewPlan> createdTargetViews,
            IProgressReporter progressReporter,
            CancellationToken cancellationToken)
        {
            var totalViews = createdTargetViews.Count;
            var processedViews = 0;

            foreach (var targetView in createdTargetViews)
            {
                cancellationToken.ThrowIfCancellationRequested();
                if (targetView == null || !targetView.IsValidObject)
                {
                    LogHelper.Warning($"Target view not valid. Skipping annotation copy.");
                    continue;
                }

                try
                {
                    progressReporter.ReportStatus($"Copying annotations for view: {targetView.Name}");

                    var sourceView = new FilteredElementCollector(sourceDoc)
                        .OfClass(typeof(ViewPlan))
                        .Cast<ViewPlan>()
                        .FirstOrDefault(v => v.Name.Equals(targetView.Name, StringComparison.OrdinalIgnoreCase));

                    if (sourceView == null)
                    {
                        LogHelper.Warning($"Source view '{targetView.Name}' not found. Skipping annotation copy.");
                        continue;
                    }

                    using (var transaction = new Transaction(targetDoc, $"Copy Annotations for {targetView.Name}"))
                    {
                        transaction.Start();
                        CommonFailuresPreprocessor.SetFailuresPreprocessor(transaction);

                        CopyAnnotationsBetweenViews(sourceView, targetView, cancellationToken);

                        // Adjust filled region transparency
                        AdjustFilledRegionTransparency(sourceView, targetView);

                        transaction.Commit();
                        LogHelper.Information($"Successfully processed annotations for view '{targetView.Name}'.");
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Error processing annotations for view {targetView.Name}: {ex.Message}");
                    continue;
                }

                processedViews++;
                var progress = (double)processedViews / totalViews * 100;
                progressReporter.ReportProgress(progress);

                Application.DoEvents();
            }
        }

        public static void CopyAnnotationsBetweenViews(View sourceView, View targetView,
            CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();

            try
            {
                var copyPasteOptions = GetCopyPasteOptions();
                var visibleAnnotationIds = GetAnnotations(sourceView, true);

                try
                {
                    if (visibleAnnotationIds.Count > 0)
                    {
                        ElementTransformUtils.CopyElements(sourceView, visibleAnnotationIds, targetView, null, copyPasteOptions);
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Warning($"Error copying annotations in bulk: {ex.Message}. Attempting to copy individually.");
                    foreach (var annotationId in visibleAnnotationIds)
                    {
                        try
                        {
                            ElementTransformUtils.CopyElements(sourceView, new List<ElementId> { annotationId }, targetView, null, copyPasteOptions);
                        }
                        catch (Exception innerEx)
                        {
                            LogHelper.Warning($"Failed to copy annotation with ID {annotationId}: {innerEx.Message}");
                        }
                    }
                }

                if (sourceView is ViewSheet sourceSheet && targetView is ViewSheet targetSheet &&
                    OptionsManager.CopyRevisions)
                {
                    try
                    {
                        AddRevisionsToTargetSheet(sourceSheet, targetSheet);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Warning($"Error copying revisions to target sheet: {ex.Message}");
                    }
                }

                if (sourceView is ViewSheet || (sourceView is ViewPlan && OptionsManager.CopyRevisions))
                {
                    try
                    {
                        CopyRevisionAnnotationsBetweenViews(sourceView, targetView);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Warning($"Error copying revision annotations between views: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error copying annotations between views: {ex.Message}");
            }
        }

        public static List<ElementId> GetAnnotations(View view, bool visibleOnly)
        {
            var categoryList = new List<BuiltInCategory>
            {
                BuiltInCategory.OST_Dimensions,
                BuiltInCategory.OST_Tags,
                BuiltInCategory.OST_Lines,
                BuiltInCategory.OST_DetailComponents,
                BuiltInCategory.OST_DetailComponentTags,
                BuiltInCategory.OST_RasterImages,
                BuiltInCategory.OST_TextNotes,
                BuiltInCategory.OST_ModelText,
                BuiltInCategory.OST_SpecialityEquipmentTags,
                BuiltInCategory.OST_RailingSystemTags,
                BuiltInCategory.OST_ElectricalFixtureTags,
                BuiltInCategory.OST_FilledRegion
            };

            if (!OptionsManager.CopyDimensions)
            {
                categoryList.Remove(BuiltInCategory.OST_Dimensions);
            }

            if (!OptionsManager.CopyTagsAndTextNotes)
            {
                categoryList.Remove(BuiltInCategory.OST_TextNotes);
                categoryList.Remove(BuiltInCategory.OST_Tags);
            }

            if (!OptionsManager.CopyDetailLinesAndRegions)
            {
                categoryList.Remove(BuiltInCategory.OST_Lines);
                categoryList.Remove(BuiltInCategory.OST_FilledRegion);
            }

            var output = new FilteredElementCollector(view.Document)
                .WhereElementIsNotElementType()
                .WherePasses(new ElementOwnerViewFilter(view.Id))
                .WherePasses(new ElementMulticategoryFilter(categoryList, false))
                .Where(e => e.Category != null && (visibleOnly ? !e.IsHidden(view) : e.IsHidden(view)))
                .Select(e => e.Id)
                .ToList();

            var dwgElements = new FilteredElementCollector(view.Document)
                .WhereElementIsNotElementType()
                .WherePasses(new ElementOwnerViewFilter(view.Id))
                .OfClass(typeof(ImportInstance))
                .Select(e => e.Id)
                .ToList();

            output.AddRange(dwgElements);

            return output;
        }

        public static List<ElementId> GetAnnotations(View view)
        {
            var categoryList = new List<BuiltInCategory>
            {
                BuiltInCategory.OST_Dimensions,
                BuiltInCategory.OST_Tags,
                BuiltInCategory.OST_Lines,
                BuiltInCategory.OST_DetailComponents,
                BuiltInCategory.OST_DetailComponentTags,
                BuiltInCategory.OST_RasterImages,
                BuiltInCategory.OST_TextNotes,
                BuiltInCategory.OST_ModelText,
                BuiltInCategory.OST_FilledRegion
            };


            var output = new FilteredElementCollector(view.Document)
                .WhereElementIsNotElementType()
                .WherePasses(new ElementOwnerViewFilter(view.Id))
                .WherePasses(new ElementMulticategoryFilter(categoryList, false))
                .Where(e => e.Category != null)
                .Select(e => e.Id)
                .ToList();

            var dwgElements = new FilteredElementCollector(view.Document)
                .WhereElementIsNotElementType()
                .WherePasses(new ElementOwnerViewFilter(view.Id))
                .OfClass(typeof(ImportInstance))
                .Select(e => e.Id)
                .ToList();

            output.AddRange(dwgElements);

            return output;
        }

        public static void AdjustFilledRegionTransparency(View sourceView, View targetView)
        {
            var sourceFilledRegions = new FilteredElementCollector(sourceView.Document, sourceView.Id)
                .OfClass(typeof(FilledRegion))
                .Cast<FilledRegion>()
                .ToList();

            foreach (var sourceRegion in sourceFilledRegions)
            {
                var overrides = sourceView.GetElementOverrides(sourceRegion.Id);

                var targetRegion = FindMatchingFilledRegionInTarget(targetView, sourceRegion);
                if (targetRegion != null)
                {
                    try
                    {
                        targetView.SetElementOverrides(targetRegion.Id, overrides);
                        LogHelper.Information(
                            $"Applied graphic overrides for FilledRegion '{sourceRegion.Name}' in target view.");
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Warning(
                            $"Could not apply graphic overrides for FilledRegion '{sourceRegion.Name}' in target view: {ex.Message}");
                    }
                }
                else
                {
                    LogHelper.Warning($"Matching FilledRegion for '{sourceRegion.Name}' not found in target view.");
                }
            }
        }

        private static FilledRegion FindMatchingFilledRegionInTarget(View targetView, FilledRegion sourceRegion)
        {
            var sourceArea = sourceRegion.LookupParameter("Area")?.AsDouble() ?? 0.0;

            var sourceBoundingBox = sourceRegion.get_BoundingBox(null);
            var sourceCenter = GetBoundingBoxCenter(sourceBoundingBox);

            return new FilteredElementCollector(targetView.Document, targetView.Id)
                .OfClass(typeof(FilledRegion))
                .Cast<FilledRegion>()
                .FirstOrDefault(targetRegion =>
                {
                    var targetArea = targetRegion.LookupParameter("Area")?.AsDouble() ?? 0.0;
                    if (Math.Abs(targetArea - sourceArea) >= 0.0001)
                    {
                        return false;
                    }

                    var targetBoundingBox = targetRegion.get_BoundingBox(null);
                    var targetCenter = GetBoundingBoxCenter(targetBoundingBox);

                    return sourceCenter != null && targetCenter != null &&
                           sourceCenter.IsAlmostEqualTo(targetCenter, 0.0001);
                });
        }

        private static XYZ GetBoundingBoxCenter(BoundingBoxXYZ boundingBox)
        {
            if (boundingBox == null)
                return null;

            return (boundingBox.Min + boundingBox.Max) / 2;
        }


        private static void CopyRevisionAnnotationsBetweenViews(View sourceView, View targetView)
        {
            var copyPasteOptions = GetCopyPasteOptions();
            var sourceRevisionAnnotationIds = GetRevisionAnnotations(sourceView);
            if (sourceRevisionAnnotationIds.Count > 0)
            {
                var targetRevisionAnnotationIds = ElementTransformUtils.CopyElements(sourceView,
                    sourceRevisionAnnotationIds, targetView, null, copyPasteOptions).ToList();
                SetRevisionsForRevisionClouds(sourceRevisionAnnotationIds, targetRevisionAnnotationIds,
                    sourceView.Document, targetView.Document);
            }
        }

        private static void AddRevisionsToTargetSheet(ViewSheet sourceSheet, ViewSheet targetSheet)
        {
            var sourceDoc = sourceSheet.Document;
            var targetDoc = targetSheet.Document;

            var sourceRevisionIds = sourceSheet.GetAllRevisionIds();

            var targetRevisionIds = new List<ElementId>();

            foreach (var sourceRevisionId in sourceRevisionIds)
            {
                var sourceRevision = sourceDoc.GetElement(sourceRevisionId) as Revision;
                if (sourceRevision == null) continue;

                var targetRevisions = Revision.GetAllRevisionIds(targetDoc)
                    .Select(id => targetDoc.GetElement(id) as Revision)
                    .Where(rev => rev != null)
                    .ToList();

                var matchingRevision = targetRevisions
                    .FirstOrDefault(rev => rev.Name == sourceRevision.Name);

                if (matchingRevision != null)
                {
                    targetRevisionIds.Add(matchingRevision.Id);
                }
                else
                {
                    throw new Exception($"Revision not found in target document: {sourceRevision.Description}");
                }
            }

            if (targetRevisionIds.Count > 0)
            {
                targetSheet.SetAdditionalRevisionIds(targetRevisionIds);
            }
        }

        private static List<ElementId> GetRevisionAnnotations(View view)
        {
            var revisionAnnotationCategories = new List<BuiltInCategory>
            {
                BuiltInCategory.OST_RevisionClouds,
                BuiltInCategory.OST_RevisionCloudTags
            };

            return new FilteredElementCollector(view.Document, view.Id)
                .WhereElementIsNotElementType()
                .WherePasses(new ElementOwnerViewFilter(view.Id))
                .WherePasses(new ElementMulticategoryFilter(revisionAnnotationCategories))
                .Select(e => e.Id)
                .ToList();
        }

        private static void SetRevisionsForRevisionClouds(
            List<ElementId> sourceIds,
            List<ElementId> targetIds,
            Document sourceDoc,
            Document targetDoc)
        {
            var sourceRevisions = Revision.GetAllRevisionIds(sourceDoc).ToList();
            var targetRevisions = Revision.GetAllRevisionIds(targetDoc).ToList();

            foreach (var targetId in targetIds)
            {
                var targetCloud = targetDoc.GetElement(targetId) as RevisionCloud;
                if (targetCloud == null) continue;

                var cloudIndex = targetIds.IndexOf(targetId);
                var sourceCloud = sourceDoc.GetElement(sourceIds[cloudIndex]) as RevisionCloud;

                if (sourceCloud == null) throw new Exception("The matching item is not RevisionCloud");

                var sourceRevisionIndex = sourceRevisions.IndexOf(sourceCloud.RevisionId);
                targetCloud.RevisionId = targetRevisions[sourceRevisionIndex];
            }
        }

        private static CopyPasteOptions GetCopyPasteOptions()
        {
            var copyPasteOptions = new CopyPasteOptions();
            copyPasteOptions.SetDuplicateTypeNamesHandler(new DuplicateTypeNamesHandler());
            return copyPasteOptions;
        }

        private static bool IsViewPlacedOnSheet(View view)
        {
            var doc = view.Document;


            var dependentElements = view.GetDependentElements(null);

            return dependentElements
                .Select(id => doc.GetElement(id))
                .OfType<Viewport>()
                .Any(vp => vp.OwnerViewId != ElementId.InvalidElementId);
        }

        public static void CopyLegendsAndDraftingViews(Document sourceDoc, Document targetDoc,
            IProgressReporter progressReporter, CancellationToken cancellationToken)
        {
            // Collect all legend views from the source document that are placed on sheets
            var legendViews = new FilteredElementCollector(sourceDoc)
                .OfClass(typeof(View))
                .Cast<View>()
                .Where(v => v.ViewType == ViewType.Legend && IsViewPlacedOnSheet(v))
                .ToList();

            // Collect all drafting views from the source document that are placed on sheets
            var draftingViews = new FilteredElementCollector(sourceDoc)
                .OfClass(typeof(ViewDrafting))
                .Cast<ViewDrafting>()
                .Where(v => IsViewPlacedOnSheet(v))
                .ToList();

            // Find an existing legend in the target document to use as a template for duplication
            var templateLegend = new FilteredElementCollector(targetDoc)
                .OfClass(typeof(View))
                .Cast<View>()
                .FirstOrDefault(v => v.ViewType == ViewType.Legend);

            var templateDrafting = new FilteredElementCollector(targetDoc)
                .OfClass(typeof(View))
                .Cast<View>()
                .FirstOrDefault(v => v.ViewType == ViewType.DraftingView);


            // Total number of views to process
            var totalViews = legendViews.Count + draftingViews.Count;
            var processedViews = 0;

            progressReporter.ReportStatus(
                $"Starting to copy {totalViews} views (Legends: {legendViews.Count}, Draftings: {draftingViews.Count})...");

            // Process and copy legend views
            foreach (var sourceLegend in legendViews)
            {
                cancellationToken.ThrowIfCancellationRequested();

                using (var trans = new Transaction(targetDoc, "Create Legend View"))
                {
                    CommonFailuresPreprocessor.SetFailuresPreprocessor(trans);
                    trans.Start();

                    var targetLegendId = templateLegend.Duplicate(ViewDuplicateOption.Duplicate);
                    var targetLegend = targetDoc.GetElement(targetLegendId) as View;
                    if (targetLegend != null)
                    {
                        targetLegend.Name = $"{sourceLegend.Name}_Old";
                        targetLegend.Scale = sourceLegend.Scale;
                    }

                    CopyViewContent(sourceLegend, targetLegend, cancellationToken);

                    trans.Commit();
                }

                processedViews++;
                progressReporter.ReportProgress((double)processedViews / totalViews * 100);
                progressReporter.ReportStatus(
                    $"Legend view '{sourceLegend.Name}' copied successfully ({processedViews}/{totalViews})");
            }

            // Process and copy drafting views
            foreach (var sourceDraftingView in draftingViews)
            {
                cancellationToken.ThrowIfCancellationRequested();

                using (var trans = new Transaction(targetDoc, "Create Drafting View"))
                {
                    CommonFailuresPreprocessor.SetFailuresPreprocessor(trans);
                    trans.Start();

                    var targetDraftingViewId = templateDrafting.Duplicate(ViewDuplicateOption.Duplicate);
                    var targetDraftingView = targetDoc.GetElement(targetDraftingViewId) as View;
                    targetDraftingView.Name = $"{sourceDraftingView.Name}_Old";
                    targetDraftingView.Scale = sourceDraftingView.Scale;

                    CopyViewContent(sourceDraftingView, targetDraftingView, cancellationToken);

                    trans.Commit();
                }

                processedViews++;
                progressReporter.ReportProgress((double)processedViews / totalViews * 100);
                progressReporter.ReportStatus(
                    $"Drafting view '{sourceDraftingView.Name}' copied successfully ({processedViews}/{totalViews})");
            }

            progressReporter.ReportProgress(100);
            progressReporter.ReportStatus("All legend and drafting views copied successfully.");
        }

        public static void CopyViewContent(View sourceView, View targetView, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var elementsToCopy = GetAnnotations(sourceView);

            if (elementsToCopy.Count == 0)
                return;

            var copyPasteOptions = new CopyPasteOptions();
            copyPasteOptions.SetDuplicateTypeNamesHandler(new DuplicateTypeNamesHandler());

            ElementTransformUtils.CopyElements(sourceView, elementsToCopy, targetView, null, copyPasteOptions);
        }

        public static void CopySchedules(Document sourceDoc, Document targetDoc,
            ProjectMapper mapper,
            IProgressReporter progressReporter, CancellationToken cancellationToken)
        {
            var scheduleHandlingOption = OptionsManager.ScheduleHandlingOption;

            var placedScheduleIds = GetSchedulesPlacedOnSheets(sourceDoc);

            var sourceSchedules = new FilteredElementCollector(sourceDoc)
                .OfClass(typeof(ViewSchedule))
                .Cast<ViewSchedule>()
                .Where(vs => !vs.IsTitleblockRevisionSchedule && !vs.IsInternalKeynoteSchedule && placedScheduleIds.Contains(vs.Id))
                .ToList();

            if (!sourceSchedules.Any())
            {
                progressReporter.ReportStatus("No placed schedules found to copy.");
                return;
            }

            progressReporter.ReportStatus($"Starting to copy schedules based on the '{scheduleHandlingOption}' option...");

            var totalSchedules = sourceSchedules.Count;
            var processedSchedules = 0;

            var schedulesToCopy = new List<ViewSchedule>();
            foreach (var sourceSchedule in sourceSchedules)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var mapping = mapper.MappedSchedules.FirstOrDefault(m => m.Source.Id == sourceSchedule.Id);
                var hasMatch = mapping != null && mapping.Target != null;

                bool shouldCopy = false;

                switch (scheduleHandlingOption)
                {
                    case ScheduleHandlingOption.UseTarget:
                        break;

                    case ScheduleHandlingOption.UseSource:
                        shouldCopy = true;
                        break;

                    case ScheduleHandlingOption.Combine:
                        if (!hasMatch)
                        {
                            shouldCopy = true;
                        }
                        break;
                }

                if (shouldCopy)
                {
                    schedulesToCopy.Add(sourceSchedule);
                }

                processedSchedules++;
                progressReporter.ReportProgress((double)processedSchedules / totalSchedules * 100);
            }

            if (schedulesToCopy.Any())
            {
                using (var trans = new Transaction(targetDoc, "Copy Schedules"))
                {
                    CommonFailuresPreprocessor.SetFailuresPreprocessor(trans);
                    trans.Start();

                    var copyPasteOptions = new CopyPasteOptions();
                    copyPasteOptions.SetDuplicateTypeNamesHandler(new DuplicateTypeNamesHandler());

                    var scheduleIds = schedulesToCopy.Select(x => x.Id).ToList();

                    var copiedElementIds = ElementTransformUtils.CopyElements(
                        sourceDoc,
                        scheduleIds,
                        targetDoc,
                        Transform.Identity,
                        copyPasteOptions)
                        .ToList();

                    for (int i = 0; i < schedulesToCopy.Count; i++)
                    {
                        var sourceSchedule = schedulesToCopy[i];
                        var copiedScheduleId = copiedElementIds[i];
                        var copiedSchedule = targetDoc.GetElement(copiedScheduleId) as ViewSchedule;
                        if (copiedSchedule != null)
                        {
                            var baseName = $"{sourceSchedule.Name}_Old";
                            var finalName = GetUniqueScheduleName(targetDoc, baseName);
                            copiedSchedule.Name = finalName;
                            progressReporter.ReportStatus($"Schedule '{sourceSchedule.Name}' copied as '{finalName}'.");
                        }
                    }

                    trans.Commit();
                }
            }

            progressReporter.ReportProgress(100);
            progressReporter.ReportStatus("Schedules processed based on the selected option.");
        }
        private static HashSet<ElementId> GetSchedulesPlacedOnSheets(Document doc)
        {
            var placedScheduleIds = new HashSet<ElementId>();
            var scheduleInstances = new FilteredElementCollector(doc)
                .OfClass(typeof(ScheduleSheetInstance))
                .Cast<ScheduleSheetInstance>()
                .Where(si => !si.IsTitleblockRevisionSchedule);

            foreach (var si in scheduleInstances)
            {
                var sid = si.ScheduleId;
                if (sid != ElementId.InvalidElementId)
                    placedScheduleIds.Add(sid);
            }

            return placedScheduleIds;
        }

        private static string GetUniqueScheduleName(Document doc, string baseName)
        {
            var existingNames = new FilteredElementCollector(doc)
                .OfClass(typeof(ViewSchedule))
                .Cast<ViewSchedule>()
                .Select(vs => vs.Name)
                .ToHashSet(StringComparer.OrdinalIgnoreCase);

            if (!existingNames.Contains(baseName))
                return baseName;

            int i = 1;
            string newName;
            do
            {
                newName = $"{baseName}_{i}";
                i++;
            } while (existingNames.Contains(newName));

            return newName;
        }

        private class DuplicateTypeNamesHandler : IDuplicateTypeNamesHandler
        {
            public DuplicateTypeAction OnDuplicateTypeNamesFound(DuplicateTypeNamesHandlerArgs args)
            {
                return DuplicateTypeAction.UseDestinationTypes;
            }
        }
    }
}