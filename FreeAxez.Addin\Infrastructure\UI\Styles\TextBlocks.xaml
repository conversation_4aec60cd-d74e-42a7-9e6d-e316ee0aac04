﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="ColorsPalette.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="TextBlock">
        <Setter Property="Foreground"
                Value="{StaticResource Gray800}" />
        <Setter Property="VerticalAlignment"
                Value="Center" />
    </Style>

    <Style x:Key="TextHeader"
           TargetType="TextBlock"
           BasedOn="{StaticResource {x:Type TextBlock}}">
        <Setter Property="FontWeight"
                Value="700" />
    </Style>

    <Style x:Key="TextH1"
           TargetType="TextBlock"
           BasedOn="{StaticResource TextHeader}">
        <Setter Property="FontSize"
                Value="20" />
    </Style>

    <Style x:Key="TextH2"
           TargetType="TextBlock"
           BasedOn="{StaticResource TextHeader}">
        <Setter Property="FontSize"
                Value="18" />
    </Style>

    <Style x:Key="TextH3"
           TargetType="TextBlock"
           BasedOn="{StaticResource TextHeader}">
        <Setter Property="FontSize"
                Value="16" />
    </Style>

    <Style x:Key="TextH4"
           TargetType="TextBlock"
           BasedOn="{StaticResource TextHeader}">
        <Setter Property="FontSize"
                Value="14" />
    </Style>

    <Style x:Key="TextH5"
           TargetType="TextBlock"
           BasedOn="{StaticResource TextHeader}">
        <Setter Property="FontSize"
                Value="13" />
    </Style>

    <Style x:Key="TextH6"
           TargetType="TextBlock"
           BasedOn="{StaticResource TextHeader}">
        <Setter Property="FontSize"
                Value="12" />
    </Style>

    <Style x:Key="TextXXl"
           TargetType="TextBlock"
           BasedOn="{StaticResource {x:Type TextBlock}}">
        <Setter Property="FontSize"
                Value="16" />
    </Style>

    <Style x:Key="TextXl"
           TargetType="TextBlock"
           BasedOn="{StaticResource {x:Type TextBlock}}">
        <Setter Property="FontSize"
                Value="14" />
    </Style>

    <Style x:Key="TextLarge"
           TargetType="TextBlock"
           BasedOn="{StaticResource {x:Type TextBlock}}">
        <Setter Property="FontSize"
                Value="13" />
    </Style>

    <Style x:Key="TextBase"
           TargetType="TextBlock"
           BasedOn="{StaticResource {x:Type TextBlock}}">
        <Setter Property="FontSize"
                Value="12" />
    </Style>

    <Style x:Key="TextSmall"
           TargetType="TextBlock"
           BasedOn="{StaticResource {x:Type TextBlock}}">
        <Setter Property="FontSize"
                Value="11" />
    </Style>
</ResourceDictionary>