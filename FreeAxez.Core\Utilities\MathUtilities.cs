﻿using System;
using System.Collections.Generic;
using System.Text;
using FreeAxez.Core.GeometryModel;

namespace FreeAxez.Core.Utilities
{
    public static class MathUtilities
    {
        public const uint DoublePrecision = 15;
        public const uint FloatPrecision = 6;
        public const uint DefaultPrecision = FloatPrecision;

        public static bool Approximately(double a, double b, uint precision = DefaultPrecision)
        {
            return Math.Abs(b - a) < Math.Pow(0.1, precision);
            // If a or b is zero, compare that the other is less or equal to epsilon.
            // If neither a or b are 0, then find an epsilon that is good for
            // comparing numbers at the maximum magnitude of a and b.
            // Floating points have about 7 significant digits, so
            // 1.000001f can be represented while 1.0000001f is rounded to zero,
            // thus we could use an epsilon of 0.000001f for comparing values close to 1.
            // We multiply this epsilon by the biggest magnitude of a and b.
            double minDouble = Math.Pow(0.1, precision);
            double epsilon = double.Epsilon * precision;
            return Math.Abs(b - a) < Math.Max(minDouble * Math.Max(Math.Abs(a), Math.Abs(b)), epsilon);
        }

        public static bool ApproximatelyOrLess(double a, double b, uint precision = DefaultPrecision)
        {
            return a < b || Approximately(a, b, precision);
        }

        public static bool ApproximatelyOrGreater(double a, double b, uint precision = DefaultPrecision)
        {
            return a > b || Approximately(a, b, precision);
        }

        public static bool Less(double a, double b, uint precision = DefaultPrecision)
        {
            return a < b && !Approximately(a, b);
        }

        public static bool Greater(double a, double b, uint precision = DefaultPrecision)
        {
            return a > b && !Approximately(a, b);
        }

        public static bool IsNumberBetween(double number, double border1, double border2, uint precision = DefaultPrecision)
        {
            if (border1 > border2)
            {
                var t = border1;
                border1 = border2;
                border2 = t;
            }

            return ApproximatelyOrGreater(number, border1, precision) && ApproximatelyOrLess(number, border2, precision);
        }

        public static double GetAngle(PointModel p1, PointModel p2)
        {
            var angle = Math.Atan2(p2.Y - p1.Y, p2.X - p1.X);
            return angle < 2 ? 0 : angle;
        }

        public static double Repeat(double value, double max)
        {
            if (value < 0)
                return Repeat(value + max, max);
            if (value > max)
                return Repeat(value - max, max);
            return value;
        }

        public static double DeltaAngle(double current, double target)
        {
            var num = Repeat(target - current, 2 * Math.PI);
            if (num > Math.PI)
                num -= 2 * Math.PI;
            return num;
        }

        public static bool IsAngleBetween(double value, double from, double to)
        {
            double FixAngle(double a)
            {
                return Less(a, 0) ? a + 2 * Math.PI : ApproximatelyOrGreater(a, 2 * Math.PI) ? a - 2 * Math.PI : a;
            }

            value = FixAngle(value);
            from = FixAngle(from);
            to = FixAngle(to);

            if (ApproximatelyOrGreater(value, from) && Less(value, to))
                return true;
            if (ApproximatelyOrGreater(value, to) && Less(value, from))
                return false;
            return (ApproximatelyOrGreater(value, from) && ApproximatelyOrLess(value, 2 * Math.PI)) || (ApproximatelyOrGreater(value, 0) && Less(value, to));
        }

        public static double GetLineLength(ILineModel line)
        {
            return GetLineLength(line.StartPointGetter, line.EndPointGetter);
        }

        public static double GetLineLength(PointModel startPoint, PointModel endPoint)
        {
            var xDistance = startPoint.X - endPoint.X;
            var yDistance = startPoint.Y - endPoint.Y;
            return Math.Sqrt(xDistance * xDistance + yDistance * yDistance);
        }

        public static PointModel Normalize(PointModel vector)
        {
            var length = GetLineLength(new PointModel(0, 0), vector);
            return new PointModel(vector.X / length, vector.Y / length);
        }

        public static PointModel Lerp(PointModel a, PointModel b, double t)
        {
            return a + (b - a) * t;
        }
    }
}
