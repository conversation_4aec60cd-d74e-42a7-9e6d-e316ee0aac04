﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace FreeAxez.Addin.AdditionalFunctional.TagRepeatingSupercomponent
{
    public partial class FormSubcomponent : System.Windows.Forms.Form
    {
        Document doc;
        public FormSubcomponent(Document _doc)
        {
            InitializeComponent();

            doc = _doc;
            List<CommonUtils.NameIDObject> list = new List<CommonUtils.NameIDObject>();
            foreach (Family xx in new FilteredElementCollector(doc)
                    .OfClass(typeof(Family))
                    .Cast<Family>()
                    .Where(q => q.FamilyCategoryId.GetIntegerValue() == (int)BuiltInCategory.OST_SpecialityEquipment)
                    .OrderBy(q => q.Name))
            {
                list.Add(new CommonUtils.NameIDObject(xx.Name, xx.Id.GetIntegerValue()));
            }
            cboFamily.DataSource = list;
            cboFamily.DisplayMember = "Name";
            cboFamily.ValueMember = "IdValue";

            if (list.Any())
            {
                cboFamily.SelectedIndex = 0;
            }
            else
            {
                Autodesk.Revit.UI.TaskDialog.Show("Error", "No Speciality Equipment families in the project");
            }
        }

        public Family getFamily()
        {
            if (cboFamily.SelectedItem == null)
                return null;

            int i = ((CommonUtils.NameIDObject)cboFamily.SelectedItem).IdValue;
            ElementId id = new ElementId(i);
            Family ret = doc.GetElement(id) as Family;
            return ret;
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
            Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
}
