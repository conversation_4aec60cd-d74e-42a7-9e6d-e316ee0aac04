﻿using NetTopologySuite.Geometries;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;

public class LineSegmentData
{
    public LineSegment Segment { get; set; }
    public JsonLineData Data { get; set; }
    public int Index { get; set; }
    public bool IsPrimaryEdge { get; set; }
}

public class Point
{
    public Point(double x, double y, double z = 0)
    {
        X = x;
        Y = y;
        Z = z;
    }

    public double X { get; set; }
    public double Y { get; set; }
    public double Z { get; set; }
}

// JSON format models
public class JsonPoint3D
{
    public double x { get; set; }

    public double y { get; set; }
    // Removed z coordinate - not needed for 2D processing
}

public class JsonLineData
{
    public string layer { get; set; }
    public JsonPoint3D startPoint { get; set; }
    public JsonPoint3D endPoint { get; set; }
    public double length { get; set; }
    public double angle { get; set; } // Line angle in degrees (0-360)
}

public class JsonLineCollection
{
    public string layer { get; set; }
    public int count { get; set; }
    public List<JsonLineData> lines { get; set; } = new();
}