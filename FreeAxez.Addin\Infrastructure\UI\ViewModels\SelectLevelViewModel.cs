﻿using Autodesk.Revit.DB;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.Infrastructure.UI.ViewModels
{
    public class SelectLevelViewModel : WindowViewModel
    {
        public SelectLevelViewModel()
        {
            Levels = GetLevelModels();
            ApplyCommand = new RelayCommand(OnApplyCommandExecute);
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
        }

        public List<LevelViewModel> Levels { get; set; }

        public ICommand ApplyCommand { get; set; }
        private void OnApplyCommandExecute(object p)
        {
            (p as Window).DialogResult = true;
            (p as Window).Close();
        }

        public ICommand CancelCommand { get; set; }
        private void OnCancelCommandExecute(object p)
        {
            (p as Window).Close();
        }

        private List<LevelViewModel> GetLevelModels()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Level))
                .OrderBy(l => l.get_Parameter(BuiltInParameter.LEVEL_ELEV).AsDouble())
                .Select(l => new LevelViewModel() { Name = l.Name, Level = l })
                .ToList();
        }
    }
}
