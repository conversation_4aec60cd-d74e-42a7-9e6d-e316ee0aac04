﻿using System;

namespace FreeAxez.Addin.Exceptions
{
    public class FreeAxezWorkflowException : Exception
    {
        private const string DefaultErrorMessage =
            "The error occurred due to an incorrect sequence of operations that does not align with the current workflow.";

        public FreeAxezWorkflowException()
            : base(DefaultErrorMessage)
        {
        }

        public FreeAxezWorkflowException(string message)
            : base(message)
        {
        }

        public FreeAxezWorkflowException(string message, Exception inner)
            : base(message, inner)
        {
        }
    }
}
