﻿<Window x:Class="FreeAxez.Addin.Infrastructure.UI.Views.NotificationView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FreeAxez.Addin.Infrastructure.UI.Views"
        xmlns:viewModels="clr-namespace:FreeAxez.Addin.Infrastructure.UI.ViewModels"
        mc:Ignorable="d"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        SizeToContent="WidthAndHeight">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.DataContext>
        <viewModels:MessageViewModel Title="Info" Message="Info Message" DefaultMessageType="Info"/>
    </Window.DataContext>
    <Grid Background="Transparent" SnapsToDevicePixels="True" UseLayoutRounding="True">
        <Border Width="300" 
            Padding="10"
            CornerRadius="5" 
            BorderThickness="0.6">
            <Border.Style>
                <Style TargetType="Border">
                    <Setter Property="Background" Value="{StaticResource Blue100}" />
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding CurrentMessageType}" Value="Info">
                            <Setter Property="Background" Value="{StaticResource Blue100}" />
                            <Setter Property="BorderBrush" Value="{StaticResource Blue300}" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding CurrentMessageType}" Value="Warning">
                            <Setter Property="Background" Value="{StaticResource Yellow100}" />
                            <Setter Property="BorderBrush" Value="{StaticResource Yellow300}" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding CurrentMessageType}" Value="Success">
                            <Setter Property="Background" Value="{StaticResource Green100}" />
                            <Setter Property="BorderBrush" Value="{StaticResource Green300}" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding CurrentMessageType}" Value="Error">
                            <Setter Property="Background" Value="{StaticResource Red100}" />
                            <Setter Property="BorderBrush" Value="{StaticResource Red300}" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Border.Style>
            <Grid Background="Transparent">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                </Grid.ColumnDefinitions>
                <ContentControl Grid.Column="0" VerticalAlignment="Top"  Template="{StaticResource WarningIcon}" Visibility="{Binding WarningVisibility}"/>
                <ContentControl Grid.Column="0" VerticalAlignment="Top"  Template="{StaticResource InfoIcon}" Visibility="{Binding InfoVisibility}"/>
                <ContentControl Grid.Column="0" VerticalAlignment="Top"  Template="{StaticResource SuccessIcon}" Visibility="{Binding SuccessVisibility}"/>
                <ContentControl Grid.Column="0" VerticalAlignment="Top"  Template="{StaticResource ErrorIcon}" Visibility="{Binding ErrorVisibility}"/>
                <StackPanel Grid.Column="1">
                    <TextBlock Text="{Binding Title}"
                           VerticalAlignment="Center"
                           HorizontalAlignment="Left"
                           Margin="10 0"
                           FontSize="13"
                           FontWeight="Bold">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding CurrentMessageType}" Value="Info">
                                        <Setter Property="Foreground" Value="{StaticResource Blue500}" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding CurrentMessageType}" Value="Warning">
                                        <Setter Property="Foreground" Value="{StaticResource Yellow500}" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding CurrentMessageType}" Value="Success">
                                        <Setter Property="Foreground" Value="{StaticResource Green500}" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding CurrentMessageType}" Value="Error">
                                        <Setter Property="Foreground" Value="{StaticResource Red500}" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                    <TextBlock Text="{Binding Message}"
                           HorizontalAlignment="Left"
                           TextWrapping="Wrap"
                           FontWeight="SemiBold"
                           Margin="10 0"
                           Foreground="{StaticResource Gray900}"
                           FontSize="12"/>
                </StackPanel>
                <Button Grid.Column="2" 
                        Command="{Binding CancelCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType={x:Type Window}}}"
                    VerticalAlignment="Top" 
                    Background="Transparent" 
                    BorderBrush="Transparent" 
                    Width="20" Height="20"
                    >X</Button>
            </Grid>
        </Border>
    </Grid>

</Window>
