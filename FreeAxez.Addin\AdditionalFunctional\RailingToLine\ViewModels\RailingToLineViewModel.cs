﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.RailingToLine.Utils;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.RailingToLine.ViewModels
{
    public class RailingToLineViewModel : BaseViewModel
    {
        private List<int> _specialLineStyles = new List<int>()
        {
            (int)BuiltInCategory.OST_AreaSchemeLines,
            (int)BuiltInCategory.OST_AxisOfRotation,
            (int)BuiltInCategory.OST_FabricAreaSketchEnvelopeLines,
            (int)BuiltInCategory.OST_FabricAreaSketchSheetsLines,
            (int)BuiltInCategory.OST_InsulationLines,
            (int)BuiltInCategory.OST_RoomSeparationLines,
            (int)BuiltInCategory.OST_SketchLines,
            (int)BuiltInCategory.OST_MEPSpaceSeparationLines
        };


        public RailingToLineViewModel()
        {
            var linesCategory = RevitManager.Document.Settings.Categories.get_Item(BuiltInCategory.OST_Lines);

            var graphicStyles = new List<Element>();

            foreach (Category category in linesCategory.SubCategories)
            {
                if (_specialLineStyles.Contains(category.Id.GetIntegerValue()))
                {
                    continue;
                }

                graphicStyles.Add(category.GetGraphicsStyle(GraphicsStyleType.Projection));
            }


            LineStyles = graphicStyles.OrderBy(s => s.Name).ToList();

            var selectedLineStyle = LineStyles.FirstOrDefault(s => s.Name == Properties.Settings.Default.selectedLineStyleName);
            SelectedLineStyle = selectedLineStyle == null ? LineStyles.First() : selectedLineStyle;

            ConvertCommand = new RelayCommand(OnCenvertCommandExecute);
        }


        public List<Element> LineStyles { get; set; }

        public Element SelectedLineStyle { get; set; }

        public ICommand ConvertCommand { get; set; }
        private void OnCenvertCommandExecute(object p)
        {
            (p as Window).Close();

            SaveSettings();

            var railings = PickRailings();

            if (railings.Count == 0)
            {
                TaskDialog.Show("Warning", "Railings have not been selected.");

                return;
            }

            var curves = railings.SelectMany(r => r.GetPath()).ToList();

            using (var t = new Transaction(RevitManager.Document))
            {
                t.Start("Convert Railings To Lines");

                foreach (var curve in curves)
                {
                    var detailCurve = RevitManager.Document.Create.NewDetailCurve(RevitManager.UIDocument.ActiveView, curve);
                    detailCurve.LineStyle = SelectedLineStyle;
                }

                RevitManager.Document.Delete(railings.Select(r => r.Id).ToList());

                t.Commit();
            }

            TaskDialog.Show("Report", $"Created {curves.Count()} detail lines.");
        }


        private List<Railing> PickRailings()
        {
            var railings = new List<Railing>();

            try
            {
                var railingReferences = RevitManager.UIDocument.Selection
                .PickObjects(Autodesk.Revit.UI.Selection.ObjectType.Element, new RailingSelectionFilter(), "Select the railings to convert to lines.");

                railings = railingReferences.Select(r => RevitManager.Document.GetElement(r)).Cast<Railing>().ToList();
            }
            catch (Autodesk.Revit.Exceptions.OperationCanceledException)
            {

            }

            return railings;
        }

        private void SaveSettings()
        {
            Properties.Settings.Default.selectedLineStyleName = SelectedLineStyle.Name;
            Properties.Settings.Default.Save();
        }
    }
}
