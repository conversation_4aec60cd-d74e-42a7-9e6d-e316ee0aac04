﻿using Autodesk.Revit.DB;


namespace FreeAxez.Addin.AdditionalFunctional.Frame.Models
{
    public class AngleElement
    {
        public AngleElement(XYZ origin, Curve firstLine, Curve secondLine, int index)
        {
            Origin = origin;
            FirstLine = firstLine;
            SecondLine = secondLine;
            Index = index;
        }
        public int Index { get; set; }
        public XYZ Origin { get; set; }
        public Curve FirstLine { get; set; }
        public Curve SecondLine { get; set; }
        public XYZ SecondLineDir { get; set; }
        private XYZ MidVector { get; set; }
        public FamilyInstance Instance { get; set; }


        public XYZ GetMidVector()
        {
            var pt1 = FirstLine.GetEndPoint(0);
            var pt2 = SecondLine.GetEndPoint(0);
            var pt3 = SecondLine.GetEndPoint(1);
            var firstVector = Line.CreateBound(pt1, pt2).Direction;
            SecondLineDir = Line.CreateBound(pt3, pt2).Direction;
            MidVector = firstVector.Add(SecondLineDir);
            return MidVector;
        }

        public void SetOffset(double offset, double familyWidth, double angleFamilyLength)
        {
            var value = FirstLine.Length - (angleFamilyLength - familyWidth);
            if (FirstLine.Length < offset && Index == 0)
            {
                Instance.LookupParameter("Desired Length 1").Set(FirstLine.Length + familyWidth);
            }
            else if (FirstLine.Length < offset * 2 && Index != 0)
            {
                if (Instance.FacingOrientation.Y == 1)
                {
                    if (SecondLineDir.X == -1 || SecondLineDir.Y == -1)
                    {
                        Instance.LookupParameter("Desired Length 1").Set(value);
                    }
                    else if (SecondLineDir.X == 1 || SecondLineDir.Y == 1)
                    {
                        Instance.LookupParameter("Desired Length 2").Set(value);
                    }
                }
                else if (Instance.FacingOrientation.Y == -1)
                {
                    if (SecondLineDir.X == 1 || SecondLineDir.X == -1)
                    {
                        Instance.LookupParameter("Desired Length 1").Set(value);
                    }
                    else if (SecondLineDir.Y == 1 || SecondLineDir.Y == -1)
                    {
                        Instance.LookupParameter("Desired Length 2").Set(value);
                    }
                }
                else if (Instance.FacingOrientation.X == 1)
                {
                    if (SecondLineDir.X == -1 || SecondLineDir.Y == 1)
                    {
                        Instance.LookupParameter("Desired Length 1").Set(value);
                    }
                    else if (SecondLineDir.X == 1 || SecondLineDir.Y == -1)
                    {
                        Instance.LookupParameter("Desired Length 2").Set(value);
                    }
                }
                else if (Instance.FacingOrientation.X == -1)
                {
                    if (SecondLineDir.X == 1 || SecondLineDir.Y == -1)
                    {
                        Instance.LookupParameter("Desired Length 1").Set(value);
                    }
                    else if (SecondLineDir.X == -1 || SecondLineDir.Y == 1)
                    {
                        Instance.LookupParameter("Desired Length 2").Set(value);
                    }
                }
            }
            else
            {
                // Set default length
                Instance.LookupParameter("Desired Length 1")?.Set(angleFamilyLength);
                Instance.LookupParameter("Desired Length 2")?.Set(angleFamilyLength);
            }
        }
    }
}

		
