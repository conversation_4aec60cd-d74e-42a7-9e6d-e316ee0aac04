﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.FindAndReplaceViewName.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.FindAndReplaceViewName.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Services;
using System.Text;

namespace FreeAxez.Addin.AdditionalFunctional.FindAndReplaceViewName
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    internal class FindAndReplaceViewNameCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var viewNameModles = GetViewNameModels();

            var viewModel = new FindAndReplaceViewNameViewModel(viewNameModles);
            var view = new FindAndReplaceViewNameView();
            view.DataContext = viewModel;
            RevitManager.SetRevitAsWindowOwner(view);
            var result = view.ShowDialog();

            if (result == true && viewNameModles.Count(v => v.IsNameChanged) > 0)
            {
                var report = Rename(viewNameModles);
                MessageWindow.ShowDialog(report, Infrastructure.UI.Enums.MessageType.Success);
            }

            return Result.Succeeded;
        }

        private List<ViewNameModel> GetViewNameModels()
        {
            var output = new List<ViewNameModel>();

            var viewPlans = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsNotElementType()
                .OfClass(typeof(ViewPlan))
                .Cast<ViewPlan>()
                .Where(v => v.IsTemplate == false)
                .ToList();

            foreach (var viewPlan in viewPlans)
            {
                var viewNameModel = new ViewNameModel(viewPlan);
                output.Add(viewNameModel);
            }

            var sheets = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsNotElementType()
                .OfClass(typeof(ViewSheet))
                .Cast<ViewSheet>()
                .ToList();

            foreach (var sheet in sheets)
            {
                var viewNameModle = new ViewNameModel(sheet);
                output.Add(viewNameModle);
            }

            return output;
        }

        private string Rename(List<ViewNameModel> viewNameModles)
        {
            var report = new StringBuilder();
            report.AppendLine("THE FOLLOWING VIEWS WERE RENAMED:");
            report.AppendLine();

            using (var t = new Transaction(RevitManager.Document, "Rename Views"))
            {
                t.Start();

                foreach (var viewNameModel in viewNameModles)
                {
                    if (!viewNameModel.IsNameChanged)
                    {
                        continue;
                    }

                    try
                    {
                        var view = viewNameModel.View;

                        if (viewNameModel.View is ViewPlan)
                        {
                            viewNameModel.View.Name = viewNameModel.Name;
                        }
                        else if (viewNameModel.View is ViewSheet)
                        {
                            viewNameModel.View.LookupParameter("Sheet Title").Set(viewNameModel.Name);
                        }

                        report.AppendLine($"RENAMED {viewNameModel.ViewTypeName}");
                        report.AppendLine($"{viewNameModel.OriginalName}");
                        report.AppendLine($"{viewNameModel.Name}");
                        report.AppendLine();
                    }
                    catch (Exception ex)
                    {
                        var reportMessage =
                            $"Failed to rename {viewNameModel.ViewTypeName} : {viewNameModel.OriginalName} => {viewNameModel.Name}\n" +
                            $"  {ex.Message}";

                        report.AppendLine(reportMessage);
                        LogHelper.Error(reportMessage);
                    }
                }

                t.Commit();
            }

            return report.ToString();
        }
    }
}
