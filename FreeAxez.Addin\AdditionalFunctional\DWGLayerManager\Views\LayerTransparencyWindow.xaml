<Window x:Class="FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views.LayerTransparencyWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Layer Transparency"
        Height="200"
        Width="250"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0" 
                   Text="Transparency value (0-90):" 
                   FontWeight="Bold"
                   Margin="0,0,0,10"/>

        <!-- ComboBox for transparency values -->
        <ComboBox Grid.Row="1"
                  ItemsSource="{Binding TransparencyValues}"
                  SelectedItem="{Binding SelectedTransparency}"
                  Style="{StaticResource Combobox}"
                  Margin="0,0,0,20"/>

        <!-- Spacer -->
        <Grid Grid.Row="2"/>

        <!-- Buttons -->
        <Grid Grid.Row="3" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="OK"
                    Command="{Binding OkCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    Style="{StaticResource ButtonSimpleBlue}"
                    Height="30"/>
            <Button Grid.Column="2"
                    Content="Cancel"
                    Command="{Binding CancelCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    Style="{StaticResource ButtonSimpleRed}"
                    Height="30"/>
        </Grid>
    </Grid>
</Window>
