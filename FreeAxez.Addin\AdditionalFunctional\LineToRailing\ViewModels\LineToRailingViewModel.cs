﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.LineToRailing.Utils;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.LineToRailing.ViewModels
{
    class LineToRailingViewModel : BaseViewModel
    {
        private CurveManager _curveManager;
        private double _baseOffset;


        public LineToRailingViewModel()
        {
            _curveManager = new CurveManager();

            RailingTypes = new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_StairsRailing)
                .WhereElementIsElementType()
                .Cast<RailingType>()
                .OrderBy(e => e.Name)
                .ToList();

            var selectedRailingType = RailingTypes.FirstOrDefault(t => t.Name == Properties.Settings.Default.selectedRailingTypeName);
            SelectedRailingType = selectedRailingType == null ? RailingTypes.First() : selectedRailingType;
            DeleteSelectedLines = Properties.Settings.Default.deleteSelectedLineOption;
            CreateRailingForEachLine = Properties.Settings.Default.createRailingForEachLine;
            _baseOffset = Properties.Settings.Default.baseOffsetForRailing;

            CreateRailingsCommand = new RelayCommand(OnCreateRailingsExecute);
        }


        public List<RailingType> RailingTypes { get; set; }

        public RailingType SelectedRailingType { get; set; }

        public string BaseOffset
        {
            get
            {
                return ProjectUnitsConverter.FormatLength(_baseOffset);
            }
            set
            {
                if (ProjectUnitsConverter.TryParseLength(value, out _baseOffset))
                {
                    OnPropertyChanged();
                }
            }
        }

        public bool DeleteSelectedLines { get; set; }

        public bool CreateRailingForEachLine { get; set; }

        public ICommand CreateRailingsCommand { get; set; }
        private void OnCreateRailingsExecute(object p)
        {
            (p as Window).Close();
            
            SaveSettings();

            _curveManager.PickCurvesInUI();

            if (_curveManager.SelectedCurves.Count == 0)
            {
                TaskDialog.Show("Warning", "Lines have not been selected.");
                return;
            }

            _curveManager.CreateCurveLoops(CreateRailingForEachLine);

            CreateRailings();

            if (DeleteSelectedLines)
            {
                _curveManager.DeleteSelectedLines();
            }
        }


        private void CreateRailings()
        {
            var deniedCurves = 0;

            using (var t = new Transaction(RevitManager.Document))
            {
                t.Start("Convert Lines To Railings");

                foreach (var curveLoop in _curveManager.CurveLoops)
                {
                    try
                    {
                        var railing = Railing.Create(RevitManager.Document, curveLoop, SelectedRailingType.Id, _curveManager.GetLevelId(curveLoop));
                        railing.get_Parameter(BuiltInParameter.STAIRS_RAILING_HEIGHT_OFFSET).Set(_baseOffset);
                    }
                    catch
                    {
                        deniedCurves++;
                    }
                }

                t.Commit();
            }

            if (deniedCurves == 0)
            {
                TaskDialog.Show("Report", $"Created {_curveManager.CurveLoops.Count - deniedCurves} railings.");
            }
            else
            {
                TaskDialog.Show("Report", $"Created {_curveManager.CurveLoops.Count - deniedCurves} railings.\n" +
                                          $"Can't create railings for {deniedCurves} curve loops.");
            }
        }

        private void SaveSettings()
        {
            Properties.Settings.Default.selectedRailingTypeName = SelectedRailingType.Name;
            Properties.Settings.Default.deleteSelectedLineOption = DeleteSelectedLines;
            Properties.Settings.Default.createRailingForEachLine = CreateRailingForEachLine;
            Properties.Settings.Default.baseOffsetForRailing = _baseOffset;
            Properties.Settings.Default.Save();
        }
    }
}
