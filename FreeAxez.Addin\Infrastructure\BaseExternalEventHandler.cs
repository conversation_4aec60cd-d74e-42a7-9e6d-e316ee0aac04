using System.IO;
using System.Reflection;
using Autodesk.Revit.UI;

namespace FreeAxez.Addin.Infrastructure;

public abstract class BaseExternalEventHandler : IExternalEventHandler
{
    public virtual string GetName()
    {
        return GetType().Name;
    }

    // IExternalEventHandler implementation with automatic AssemblyResolver
    public void Execute(UIApplication app)
    {
        AppDomain.CurrentDomain.AssemblyResolve += OnAssemblyResolve;
        LogHelper.Information($"AssemblyResolver attached for {GetType().Name}");

        try
        {
            ExecuteInternal(app);
        }
        catch (Exception exception)
        {
            LogHelper.Error($"Error in {GetType().Name}: {exception.Message}\n{exception.StackTrace}");
            throw; // Re-throw to maintain original behavior
        }
        finally
        {
            AppDomain.CurrentDomain.AssemblyResolve -= OnAssemblyResolve;
            LogHelper.Information($"AssemblyResolver detached for {GetType().Name}");
        }
    }

    public abstract void ExecuteInternal(UIApplication app);

    private Assembly OnAssemblyResolve(object sender, ResolveEventArgs args)
    {
        Assembly assembly = null;

        LogHelper.Warning($"Try to resolve assembly {args.Name}");

        var assemblyName = args.Name.Split(',').FirstOrDefault();
        var directory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        var assemblyFile = Path.Combine(directory, assemblyName + ".dll");
        if (File.Exists(assemblyFile))
        {
            assembly = Assembly.LoadFrom(assemblyFile);
            LogHelper.Warning($"Successfully resolving assembly {args.Name} from {assemblyFile}");
        }
        else
        {
            LogHelper.Warning($"Failed to resolve assembly {args.Name} does not exist in {assemblyFile}");
        }

        return assembly;
    }
}