﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Models.Base;
using FreeAxez.Addin.Utils;

namespace FreeAxez.Addin.Models
{
    public class Track : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_ElectricalEquipment,
            FamilyNamesContains = new List<string>()
            {
                "-Track-4'",
                "-Track-8'"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        private double _length = double.NaN;

        public Track(Element element) : base(element)
        {
        }

        public static List<Track> Collect()
        {
            return FamilyCollector.Instances.Select(e => new Track(e)).ToList();
        }

        public override double Length
        {
            get
            {
                if (double.IsNaN(_length))
                {
                    var parameter = RevitManager.Document.GetElement(Element.GetTypeId())
                        .get_Parameter(new Guid(Constants.ParameterGuidLength));

                    if (parameter == null)
                    {
                        LogHelper.Warning($"Element {Element.Id} does not contain type parameter Length with Guid \"{Constants.ParameterGuidLength}\"");
                        _length = 0.0;
                    }
                    else
                    {
                        _length = parameter.AsDouble();
                    }
                }

                return _length;
            }
        }
    }
}
