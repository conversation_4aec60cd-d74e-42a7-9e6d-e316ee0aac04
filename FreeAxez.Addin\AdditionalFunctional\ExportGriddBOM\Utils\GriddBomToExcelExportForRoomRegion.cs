﻿using System.IO;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Enums;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Models;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBOM.Spreadsheets;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using NPOI.HSSF.Util;
using NPOI.SS.UserModel;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Utils
{
    public class GriddBomToExcelExportForRoomRegion
    {
        private readonly GriddBomRevisionForRoomRegion _griddBomRevision;
        private ExcelDocument _excelDocument;
        private readonly string _filePath;

        public GriddBomToExcelExportForRoomRegion(string filePath, GriddBomRevisionForRoomRegion griddBomRevision)
        {
            _filePath = filePath;
            _griddBomRevision = griddBomRevision;
        }

        public void Export()
        {
            try
            {
                _excelDocument = ExcelDocument.CreateNew(Path.Combine(_filePath, _griddBomRevision.DocumentName), _griddBomRevision.SheetName);

                UpdateHeaderInformation(_griddBomRevision.SheetName);
                ExportProducts(_griddBomRevision.SheetName);

                _excelDocument.Save();
                MessageWindow.ShowDialog("Export Gridd BOM", "Export completed.", MessageType.Success);
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog("Export Error", "An error occurred during export: " + ex.Message, MessageType.Error);
            }
        }

        private void UpdateHeaderInformation(string sheetName)
        {
            _excelDocument.UpdateColumnWidth(sheetName, [0, 1, 2], 2);
            // Level
            _excelDocument.UpdateStyledCell(sheetName, 0, 0, "Level:", HorizontalAlignment.Right, true);
            _excelDocument.UpdateStyledCell(sheetName, 0, 1, _griddBomRevision.Level.Name, HorizontalAlignment.Center, false, HSSFColor.Yellow.Index);
            // Date
            _excelDocument.UpdateStyledCell(sheetName, 1, 0, "Date:", HorizontalAlignment.Right, true);
            _excelDocument.UpdateStyledCell(sheetName, 1, 1, _griddBomRevision.RevisionDate, HorizontalAlignment.Center, false, HSSFColor.Yellow.Index);

            // Designer
            _excelDocument.UpdateStyledCell(sheetName, 2, 0, "Designer:", HorizontalAlignment.Right, true);
            _excelDocument.UpdateStyledCell(sheetName, 2, 1, _griddBomRevision.RevisionAuthor, HorizontalAlignment.Center, false, HSSFColor.Yellow.Index);

            // Rev#
            _excelDocument.UpdateStyledCell(sheetName, 3, 0, "Rev#:", HorizontalAlignment.Right, true);
            _excelDocument.UpdateStyledCell(sheetName, 3, 1, _griddBomRevision.RevisionNumber, HorizontalAlignment.Center, false, HSSFColor.Yellow.Index);

            // Room Name or Area Name
            string title = _griddBomRevision.SelectedOption == SelectionOption.SelectedRoom ? "Room Name:" : "Area Name:";
            _excelDocument.UpdateStyledCell(sheetName, 4, 0, title, HorizontalAlignment.Right, true);
            string roomOrAreaName = _griddBomRevision.SelectedOption == SelectionOption.SelectedRegion ? _griddBomRevision.AreaName : (_griddBomRevision.SelectedElement as Room)?.Name ?? "Region";
            _excelDocument.UpdateStyledCell(sheetName, 4, 1, roomOrAreaName, HorizontalAlignment.Center, false, HSSFColor.Yellow.Index);

            // Room S или Area SF
            string areaLabel = _griddBomRevision.SelectedOption == SelectionOption.SelectedRoom ? "Room SF:" : "Area SF:";
            _excelDocument.UpdateStyledCell(sheetName, 5, 0, areaLabel, HorizontalAlignment.Right, true);

            double area = GetArea(_griddBomRevision.SelectedElement);
            _excelDocument.UpdateStyledCell(sheetName, 5, 1, area, HorizontalAlignment.Center, HSSFColor.Yellow.Index);

            _excelDocument.UpdateStyledCell(sheetName, 6, 0, "Model", HorizontalAlignment.Left, true);
            _excelDocument.UpdateStyledCell(sheetName, 6, 1, "Product Name", HorizontalAlignment.Left, true);
            _excelDocument.UpdateStyledCell(sheetName, 6, 2, "Count", HorizontalAlignment.Left, true);
        }

        private void ExportProducts(string sheetName)
        {
            int rowIndex = 7; 
            foreach (var product in _griddBomRevision.Products)
            {
                var data = product.GetData();
                for (int i = 0; i < data.Count; i++)
                {
                    _excelDocument.UpdateCell(sheetName, rowIndex, i, data[i]); 
                }
                rowIndex++;
            }
        }

        private double GetArea(Element element)
        {
            if (element is Room room)
            {
                return room.Area;
            }
            else if (element is FilledRegion region)
            {
                var areaParam = region.LookupParameter("Area");
                if (areaParam != null)
                {
                    return areaParam.AsDouble();
                }
            }
            return 0.0; 
        }
    }
}