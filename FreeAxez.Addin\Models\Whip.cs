﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.AdditionalFunctional.WhipTypeReplace.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Models.Base;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.Models
{
    public class Whip : Product
    {
        private double _length = double.NaN;
        private Level _level;


        public Whip(Element element) : base(element)
        {
        }

        public bool IsSpinLock => Element.Name.Contains("SpinLock");

        public static List<Whip> CollectFloorBoxWhips()
        {
            var floorBoxWhipTypeName = @"Whip-\d+'";
            return new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(FlexPipe))
                .Where(p => Regex.IsMatch(p.Name, floorBoxWhipTypeName))
                .Select(e => new Whip(e))
                .ToList();
        }

        public static List<Whip> CollectTrackWhips()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(FlexPipe))
                .Where(i => i.Name.Contains("Whip-Interlink") || i.Name.Contains("Whip-FeedModule"))
                .Select(e => new Whip(e))
                .ToList();
        }

        public static List<Whip> CollectSpinLocks()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(FlexPipe))
                .Where(i => i.Name.Contains("SpinLock"))
                .Select(e => new Whip(e))
                .ToList();
        }


        public override Level Level
        {
            get
            {
                if (_level == null)
                {
                    var levelId = Element.LevelId != ElementId.InvalidElementId ? 
                        Element.LevelId : Element.get_Parameter(BuiltInParameter.RBS_START_LEVEL_PARAM)?.AsElementId();

                    if (levelId != null)
                    {
                        _level = RevitManager.Document.GetElement(levelId) as Level;
                    }
                    else
                    {
                        LogHelper.Warning($"Element {Element.Id} has not level associated with the element");
                    }
                }

                return _level;
            }
        }

        public override double Length
        {
            get
            {
                if (double.IsNaN(_length))
                {
                    _length = WhipFullLength.GetRiseLength(Element as FlexPipe);
                }

                return _length;
            }
        }
    }
}
