using System;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;
using FreeAxez.Addin.Infrastructure;
using Microsoft.Win32;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;

public class DetailsFileInfoExistVm : BaseViewModel
{
    private readonly string _originalDescription;
    private readonly AdminDetailsBaseVm _parentVm;
    private LibraryItemDetailsDto _detailsItem;
    private BitmapSource _filePreview;
    private bool _filePreviewChanged;
    private bool _hasChanges;

    public DetailsFileInfoExistVm()
    {
    }

    public DetailsFileInfoExistVm(LibraryItemDetailsDto detailsItem, AdminDetailsBaseVm parentVm)
    {
        _detailsItem = detailsItem;
        _originalDescription = detailsItem?.Description;
        _parentVm = parentVm;

        ChooseImageCommand = new RelayCommand(param => ChooseImage());
        RejectFileCommand = new RelayCommand(param => RejectFile());

        LoadImage(detailsItem?.ImagePath);

        // Initialize HasChanges state
        UpdateHasChanges();
    }

    public ICommand ChooseImageCommand { get; }
    public ICommand RejectFileCommand { get; }

    public bool HasChanges
    {
        get => _hasChanges;
        private set
        {
            if (_hasChanges != value)
            {
                _hasChanges = value;
                OnPropertyChanged();
                CommandManager.InvalidateRequerySuggested();
            }
        }
    }

    public bool FilePreviewChanged
    {
        get => _filePreviewChanged;
        set
        {
            if (_filePreviewChanged != value)
            {
                _filePreviewChanged = value;
                OnPropertyChanged();
                UpdateHasChanges();
            }
        }
    }

    public LibraryItemDetailsDto DetailsItem
    {
        get => _detailsItem;
        set
        {
            _detailsItem = value;
            OnPropertyChanged();
        }
    }

    public string FileName => _detailsItem?.Name;
    public string Name => _detailsItem?.Name;
    public string FileType => _detailsItem?.FileType;
    public string RevitVersion => _detailsItem?.RevitVersion ?? "2024";
    public string CreatedBy => _detailsItem?.CreatedBy;
    public string DateCreated => _detailsItem?.DateCreated.ToString("yyyy-MM-dd HH:mm");
    public string LastDateUpdated => _detailsItem?.LastDateUpdated.ToString("yyyy-MM-dd HH:mm");

    public string Description
    {
        get => _detailsItem?.Description;
        set
        {
            if (_detailsItem != null)
            {
                _detailsItem.Description = value;
                OnPropertyChanged();
                UpdateHasChanges();
                CommandManager.InvalidateRequerySuggested();
            }
        }
    }

    public BitmapSource FilePreview
    {
        get => _filePreview;
        set
        {
            if (_filePreview != value)
            {
                _filePreview = value;
                OnPropertyChanged();
            }
        }
    }

    public void RejectFile()
    {
        if (_parentVm.RejectFileCommand.CanExecute(this)) 
            _parentVm.RejectFileCommand.Execute(this);
    }

    private void UpdateHasChanges()
    {
        var currentDescription = _detailsItem?.Description ?? string.Empty;
        var originalDescription = _originalDescription ?? string.Empty;
        HasChanges = _filePreviewChanged || !string.Equals(currentDescription, originalDescription, StringComparison.Ordinal);
    }

    private void ChooseImage()
    {
        var openFileDialog = new OpenFileDialog
        {
            Filter = "Image files (*.png;*.jpeg;*.jpg)|*.png;*.jpeg;*.jpg",
            Title = "Select an Image"
        };

        if (openFileDialog.ShowDialog() == true)
        {
            var imageUri = new Uri(openFileDialog.FileName, UriKind.Absolute);
            var image = new BitmapImage(imageUri);

            FilePreview = image;
            FilePreviewChanged = true;
            UpdateHasChanges();
        }
    }

    private async void LoadImage(string imageUrl)
    {
        if (!string.IsNullOrEmpty(imageUrl))
        {
            _filePreview = await ApiService.Instance.LoadImageFromUrlAsync(imageUrl);
            OnPropertyChanged(nameof(FilePreview));
        }
    }
}
