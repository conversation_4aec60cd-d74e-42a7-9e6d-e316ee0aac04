using System;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model
{
    public class LibraryItemDetailsDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime LastDateUpdated { get; set; }
        public string FilePath { get; set; }
        public string ImagePath { get; set; }
        public string FileType { get; set; }
        public string RevitVersion { get; set; }
        public bool IsDeleted { get; set; }
    }
}
