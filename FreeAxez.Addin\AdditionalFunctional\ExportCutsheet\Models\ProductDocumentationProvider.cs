﻿using Accord.Math;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ExportCutsheet.Dto;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Core.Services;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FreeAxez.Addin.AdditionalFunctional.ExportCutsheet.Models
{
    public class ProductDocumentationProvider
    {
        private const string ProductNamePrarameterName = "Product Name";

        public async Task<(List<string> urls, List<string> missedProducts)> GetProductDocumentationUrlsAsync(List<Element> powerElements)
        {
            var urls = new List<string>();
            var missedProducts = new List<string>();

            var revitProducts = GetProductsFromRevit(powerElements);
            var cmsProducts = await GetProductsFromCmsAsync();

            foreach (var revitProduct in revitProducts)
            {
                var cmsProduct = GetCmsProductByRevitProduct(revitProduct, cmsProducts);
                if (cmsProduct == null)
                {
                    if (missedProducts.Count == 0) missedProducts.Add("Product Name, Model");
                    missedProducts.Add($"{revitProduct.Name}, {revitProduct.Model}");
                    continue;
                }

                urls.AddRange(cmsProduct.Urls);
            }

            return (urls.Distinct().ToList(), missedProducts);
        }

        private List<ProductDto> GetProductsFromRevit(List<Element> elements)
        {
            var output = new List<ProductDto>();

            foreach (var element in elements)
            {
                var type = RevitManager.Document.GetElement(element.GetTypeId());
                var productName = type.LookupParameter(ProductNamePrarameterName)?.AsString().Trim();
                var model = type.get_Parameter(BuiltInParameter.ALL_MODEL_MODEL)?.AsString().Trim();
                
                if (string.IsNullOrWhiteSpace(productName) & string.IsNullOrWhiteSpace(model)) continue; // Empty element
                else if (output.Any(p => p.Name == productName && p.Model == model)) continue; // Already in output list

                output.Add(new ProductDto { Name = productName, Model = model });
            }

            return output;
        }

        public async Task<List<ProductDto>> GetProductsFromCmsAsync()
        {
            var output = new List<ProductDto>();

            var url = FreeAxezWebApiService.WebApiUrl + "product/list";
            using (var response = await FreeAxezHttpClient.Instance.GetAsync(url))
            {
                if (!response.IsSuccessStatusCode)
                {
                    LogHelper.Error($"Failed to get products from {url}");
                    return output;
                }
                
                var json = await response.Content.ReadAsStringAsync();
                if (string.IsNullOrWhiteSpace(json))
                {
                    LogHelper.Error($"Failed to get products from {url}");
                    return output;
                }

                output = JsonConvert.DeserializeObject<List<ProductDto>>(json)
                    .Select(p => new ProductDto { Name = p.Name?.Trim(), 
                                                  Model = p.Model?.Trim(), 
                                                  Urls = p.Urls })
                    .ToList();

                if (output.Count == 0) LogHelper.Warning($"Received zero products from {url}");
            }

            return output;
        }

        /// <summary>
        /// Searches for a suitable product by Model, if it cannot be found by Model, search by Product Name.
        /// </summary>
        public ProductDto GetCmsProductByRevitProduct(ProductDto revitProduct, List<ProductDto> cmsProducts)
        {
            var cmsProduct = cmsProducts.FirstOrDefault(p =>
                string.Equals(revitProduct.Model, p.Model, System.StringComparison.OrdinalIgnoreCase));

            if (cmsProduct == null)
            {
                cmsProduct = cmsProducts.FirstOrDefault(p =>
                    string.Equals(revitProduct.Name, p.Name, System.StringComparison.OrdinalIgnoreCase));
            }

            return cmsProduct;
        }
    }
}
