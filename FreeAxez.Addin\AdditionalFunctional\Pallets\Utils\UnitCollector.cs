﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.Pallets.Models;
using FreeAxez.Addin.Enums;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.Pallets.Utils
{
    public class UnitCollector
    {
        private List<FamilyInstance> _units;
        public List<FamilyInstance> Units 
        { 
            get
            {
                if (_units == null) _units = GetUnits();
                return _units;
            } 
        }

        private GriddType _griddType = GriddType.Unknown;
        public GriddType GriddType
        {
            get
            {
                if (_griddType == GriddType.Unknown)
                {
                    if (Units.Where(f => f.Symbol.Name == "Gridd-70").Count() > Units.Count() / 2)
                    {
                        _griddType = GriddType.Gridd70;
                    }
                    else
                    {
                        _griddType = GriddType.Gridd40;
                    }
                }

                return _griddType;
            }
        }

        private PlacementRegion _placementRegion;
        public PlacementRegion PlacementRegion
        {
            get
            {
                return _placementRegion;
            }
            set
            {
                _placementRegion = value;

                _units = null;
                _griddType = GriddType.Unknown;
            }
        }


        private List<FamilyInstance> GetUnits()
        {
            var unitsOnView = new FilteredElementCollector(RevitManager.Document, RevitManager.Document.ActiveView.Id)
                .WhereElementIsNotElementType()
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .Cast<FamilyInstance>()
                .ToList();

            if (PlacementRegion != null) 
            { 
                unitsOnView = unitsOnView
                    .Where(u => PlacementRegion.IsInside((u.Location as LocationPoint).Point))
                    .ToList();
            }

            return unitsOnView;
        }
    }
}
