﻿using System;
using System.Globalization;
using System.Windows.Data;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters;

public class FamilyStatusToStringConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is FamilyStatus status)
            switch (status)
            {
                case FamilyStatus.NotPresentInProject:
                    return "Not Present In Project";
                case FamilyStatus.OutdatedVersionInProject:
                    return "Outdated Version In Project";
                case FamilyStatus.CurrentVersionInProject:
                    return "Current Version In Project";
                default:
                    return string.Empty;
            }

        return string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}