﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.Pallets.Models
{
    public class PlacementRegion
    {
        private FilledRegion _filledRegion;
        private PlanarFace _planarFace;


        public PlacementRegion(FilledRegion filledRegion)
        {
            _filledRegion = filledRegion;

            // Region always has one solid in geometry that has one face
            _planarFace = _filledRegion
                .get_Geometry(new Options() { View = RevitManager.Document.ActiveView })
                .Cast<Solid>()
                .First()
                .Faces
                .get_Item(0) as PlanarFace;
        }


        public bool IsInside(XYZ point)
        {
            var uv = new UV(point.X - _planarFace.Origin.X, point.Y - _planarFace.Origin.Y);
            return _planarFace.IsInside(uv);
        }
    }
}
