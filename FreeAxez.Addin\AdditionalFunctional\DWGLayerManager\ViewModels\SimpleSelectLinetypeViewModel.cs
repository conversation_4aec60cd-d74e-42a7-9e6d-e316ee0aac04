using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels;

public class SimpleSelectLinetypeViewModel : BaseDialogViewModel
{
    private readonly DwgLayerManagerApiService _apiService;
    private bool _isLoading;
    private LinetypeModel _selectedLinetype;

    public SimpleSelectLinetypeViewModel(DwgLayerManagerApiService apiService, Guid currentLinetypeId = default)
    {
        _apiService = apiService ?? throw new ArgumentNullException(nameof(apiService));
        LoadedLinetypes = new ObservableCollection<LinetypeModel>();

        LoadCommand = new RelayCommand(_ => LoadLinetypes());
        DeleteLinetypeCommand = new RelayCommand(_ => DeleteSelectedLinetype(), _ => CanDeleteSelectedLinetype());

        LoadLinetypesFromServerAsync(currentLinetypeId);
    }

    public ObservableCollection<LinetypeModel> LoadedLinetypes { get; }

    public LinetypeModel SelectedLinetype
    {
        get => _selectedLinetype;
        set
        {
            Set(ref _selectedLinetype, value);
            (DeleteLinetypeCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }
    }

    public bool IsLoading
    {
        get => _isLoading;
        set => Set(ref _isLoading, value);
    }

    public string SelectedLinetypeText => SelectedLinetype?.Name ?? string.Empty;
    public string SelectedLinetypeDescription => SelectedLinetype?.Description ?? string.Empty;
    public Guid SelectedLinetypeId => SelectedLinetype?.Id ?? Guid.Empty;

    public ICommand LoadCommand { get; }
    public ICommand DeleteLinetypeCommand { get; }

    private async Task LoadLinetypesFromServerAsync(Guid currentLinetypeId)
    {
        try
        {
            IsLoading = true;
            var linetypeDtos = await _apiService.GetAllLinetypesAsync();

            LoadedLinetypes.Clear();
            foreach (var dto in linetypeDtos)
                LoadedLinetypes.Add(new LinetypeModel
                {
                    Id = dto.Id,
                    Name = dto.Name,
                    Description = dto.Description,
                    PatternRaw = dto.PatternRaw,
                    UpdatedUtc = dto.UpdatedUtc
                });

            // Select current linetype or default to Continuous
            SelectedLinetype = LoadedLinetypes.FirstOrDefault(l => l.Id == currentLinetypeId)
                               ?? LoadedLinetypes.FirstOrDefault(l =>
                                   l.Name.Equals("Continuous", StringComparison.OrdinalIgnoreCase))
                               ?? LoadedLinetypes.FirstOrDefault();
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog("Error", $"Failed to load linetypes: {ex.Message}", MessageType.Error);

            // Add fallback Continuous linetype
            LoadedLinetypes.Add(new LinetypeModel
            {
                Id = Guid.NewGuid(),
                Name = "Continuous",
                Description = "Solid line",
                PatternRaw = "",
                UpdatedUtc = DateTime.UtcNow
            });
            SelectedLinetype = LoadedLinetypes.First();
        }
        finally
        {
            IsLoading = false;
        }
    }



    private async void LoadLinetypes()
    {
        // Open load linetypes from file dialog
        var dialog = new LoadOrReloadLinetypesWindow(_apiService);
        if (dialog.ShowDialog() == true)
            // Refresh linetypes from server after dialog closes
            // This will show the newly added linetype in the list
            await LoadLinetypesFromServerAsync(SelectedLinetype?.Id ?? Guid.Empty);
    }

    private async void DeleteSelectedLinetype()
    {
        if (SelectedLinetype == null) return;

        var result = MessageBox.Show(
            $"Are you sure you want to delete linetype '{SelectedLinetype.Name}'?\n\nAll layers using this linetype will be switched to 'Continuous'.",
            "Confirm Delete",
            MessageBoxButton.YesNo,
            MessageBoxImage.Warning);

        if (result == MessageBoxResult.Yes)
        {
            var success = await _apiService.DeleteLinetypeAsync(SelectedLinetype.Id);
            if (success)
                // Refresh the list
                await LoadLinetypesFromServerAsync(Guid.Empty);
        }
    }

    private bool CanDeleteSelectedLinetype()
    {
        return SelectedLinetype != null &&
               !SelectedLinetype.Name.Equals("Continuous", StringComparison.OrdinalIgnoreCase);
    }
}