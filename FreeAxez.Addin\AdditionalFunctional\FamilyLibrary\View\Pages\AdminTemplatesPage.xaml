<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Pages.AdminTemplatesPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:pages="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages"
             xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls"
             mc:Ignorable="d"
             d:DesignHeight="300" 
             d:DesignWidth="300">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <UserControl.DataContext>
        <pages:TemplatesPageVm />
    </UserControl.DataContext>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        <controls:HeaderBar PageName="Project Teplates"/>
        <StackPanel Grid.Row="1" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    VerticalAlignment="Center">
            <TextBlock HorizontalAlignment="Center" 
                       Style="{StaticResource TextH4}"
                       Foreground="DarkGray">
                Page is under construction
            </TextBlock>
        </StackPanel>
        <StackPanel Orientation="Horizontal" 
                    Grid.Row="2" 
                    HorizontalAlignment="Right">
            <Button Content="Load" 
                    Style="{StaticResource ButtonOutlinedBlue}" 
                    Margin="0 0 10 0">
            </Button>
        </StackPanel>
    </Grid>
</UserControl>
