﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils
{
    public class LevelHelper
    {
        private readonly List<Level> _selectedLevels;
        private List<Level> _levels;


        public LevelHelper(List<Level> selectedLevels = null)
        {
            _selectedLevels = selectedLevels;

            _levels = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Level))
                .Cast<Level>()
                .OrderBy(l => l.get_Parameter(BuiltInParameter.LEVEL_ELEV).AsDouble())
                .ToList();
        }


        public List<Level> SelectedLevels => _selectedLevels;


        public bool BelongsToTheSelectedLevel(FamilyInstance familyInstance)
        {
            if (_selectedLevels == null || _selectedLevels.Count == 0) return true;

            if (_selectedLevels.Any(l => BelongsToTheLevel(familyInstance, l))) return true;

            return false;
        }

        public bool BelongsToTheLevel(FamilyInstance familyInstance, Level level)
        {
            var instanceLevelParameter = familyInstance.get_Parameter(BuiltInParameter.FAMILY_LEVEL_PARAM);
            if (level.Id.GetIntegerValue() == instanceLevelParameter.AsElementId().GetIntegerValue()) return true;
            // It is necessary for old panelboard families without level parameter
            else if (instanceLevelParameter.AsElementId().GetIntegerValue() != ElementId.InvalidElementId.GetIntegerValue()) return false;

            var locationPoint = familyInstance.Location as LocationPoint;
            if (locationPoint == null) return false;

            var instanceLevelIdFromLocation = GetLevel(locationPoint.Point.Z).Id;
            if (level.Id.GetIntegerValue() == instanceLevelIdFromLocation.GetIntegerValue()) return true;

            return false;
        }

        public bool BelongsToTheSelectedLevel(FlexPipe flexPipe)
        {
            if (_selectedLevels == null || _selectedLevels.Count == 0) return true;

            var pipeLevelId = flexPipe.get_Parameter(BuiltInParameter.RBS_START_LEVEL_PARAM).AsElementId();
            if (_selectedLevels.Any(l => l.Id.GetIntegerValue() == pipeLevelId.GetIntegerValue())) return true;

            return false;
        }

        public Level GetLevel(int id)
        {
            return _levels.FirstOrDefault(l => l.Id.GetIntegerValue() == id);
        }

        public Level GetLevel(double elevation)
        {
            var level = _levels.Where(l => l.get_Parameter(BuiltInParameter.LEVEL_ELEV).AsDouble() <= elevation)
                .OrderByDescending(l => l.get_Parameter(BuiltInParameter.LEVEL_ELEV).AsDouble())
                .FirstOrDefault();

            if (level == null) level = _levels.First();

            return level;
        }
    }
}
