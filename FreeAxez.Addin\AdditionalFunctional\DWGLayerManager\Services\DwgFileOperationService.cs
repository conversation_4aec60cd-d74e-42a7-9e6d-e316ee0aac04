using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using Microsoft.Win32;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services
{
    /// <summary>
    /// Unified service for all DWG file operations including file selection, layer loading, and advanced operations
    /// </summary>
    public class DwgFileOperationService
    {
        private readonly DwgLayerService _dwgLayerService;

        public DwgFileOperationService(DwgLayerService dwgLayerService)
        {
            _dwgLayerService = dwgLayerService ?? throw new ArgumentNullException(nameof(dwgLayerService));
        }

        #region File Selection and Basic Operations

        /// <summary>
        /// Opens file dialog to select a DWG file
        /// </summary>
        public string SelectDwgFile()
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "AutoCAD Files (*.dwg)|*.dwg|All Files (*.*)|*.*",
                Title = "Select DWG File"
            };

            return openFileDialog.ShowDialog() == true ? openFileDialog.FileName : null;
        }

        /// <summary>
        /// Loads layer information from a DWG file
        /// </summary>
        public async Task<List<DwgLayerInfo>> LoadDwgLayersAsync(string dwgFilePath, IProgress<string> progress = null)
        {
            if (string.IsNullOrEmpty(dwgFilePath) || !File.Exists(dwgFilePath))
            {
                return new List<DwgLayerInfo>();
            }

            try
            {
                progress?.Report("Extracting layer information from DWG file...");
                System.Windows.Forms.Application.DoEvents();

                var layers = await _dwgLayerService.ExtractLayersFromDwgAsync(dwgFilePath);

                progress?.Report("Processing layer data...");

                return layers;
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog("Error",
                    $"Failed to load DWG layers: {ex.Message}\n\n" +
                    "Try selecting a different AutoCAD version in the main window if the issue persists.",
                    MessageType.Error);
                throw; // Re-throw to let calling code know there was an error
            }
        }

        #endregion

        #region Advanced Operations

        public async Task<bool> PurgeUnusedLayersAsync(DwgMappingViewModel dwgMappingVM)
        {
            if (!ValidateFileAccess(dwgMappingVM.SelectedDwgFilePath))
                return false;

            try
            {
                dwgMappingVM.IsProcessing = true;
                dwgMappingVM.ProgressTitle = "Purging Unused Layers";
                dwgMappingVM.ProgressText = "Analyzing and deleting empty layers...";

                var success = await _dwgLayerService.DeleteEmptyLayersAsync(dwgMappingVM.SelectedDwgFilePath);

                if (success)
                {
                    dwgMappingVM.ProgressText = "Purge completed, reloading layers...";
                    dwgMappingVM.SetPendingSuccessMessage("Empty layers purged successfully!");
                    dwgMappingVM.LoadDwgLayersAsync();
                    return true;
                }
                else
                {
                    MessageWindow.ShowDialog("Error", "Failed to delete empty layers. Please try again.", MessageType.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog("Error", $"Error deleting empty layers: {ex.Message}", MessageType.Error);
                return false;
            }
            finally
            {
                if (!dwgMappingVM.IsProcessing) // Only reset if operation failed
                {
                    dwgMappingVM.ProgressText = "";
                    dwgMappingVM.ProgressTitle = "";
                }
            }
        }

        public async Task<bool> UpdateLayersFromBackendAsync(DwgMappingViewModel dwgMappingVM)
        {
            if (!ValidateFileAccess(dwgMappingVM.SelectedDwgFilePath))
                return false;

            try
            {
                var matchingLayers = GetMatchingLayers(dwgMappingVM);
                if (matchingLayers == null || !matchingLayers.Any())
                {
                    MessageWindow.ShowDialog("Info", "No DWG layers found with names matching FreeAxez layers.", MessageType.Info);
                    return false;
                }

                dwgMappingVM.IsProcessing = true;
                dwgMappingVM.ProgressTitle = "Updating Layers from Backend";
                dwgMappingVM.ProgressText = $"Updating {matchingLayers.Count} layers with matching names...";

                var updateRequest = CreateUpdateRequest(dwgMappingVM, matchingLayers);
                if (updateRequest == null)
                {
                    MessageWindow.ShowDialog("Info", "No valid layer mappings found to update.", MessageType.Info);
                    return false;
                }

                var success = await _dwgLayerService.UpdateLayersFromBackendAsync(dwgMappingVM.SelectedDwgFilePath, updateRequest);

                if (success)
                {
                    dwgMappingVM.ProgressText = "Update completed, reloading layers...";
                    dwgMappingVM.SetPendingSuccessMessage($"Successfully updated layers from backend!");
                    dwgMappingVM.LoadDwgLayersAsync();
                    return true;
                }
                else
                {
                    MessageWindow.ShowDialog("Error", "Failed to update layers from backend. Please try again.", MessageType.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog("Error", $"Error updating layers from backend: {ex.Message}", MessageType.Error);
                return false;
            }
            finally
            {
                if (!dwgMappingVM.IsProcessing) // Only reset if operation failed
                {
                    dwgMappingVM.ProgressText = "";
                    dwgMappingVM.ProgressTitle = "";
                }
            }
        }

        private bool ValidateFileAccess(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
            {
                MessageWindow.ShowDialog("Error", "Please select a DWG file first.", MessageType.Error);
                return false;
            }

            if (!IsFileAvailableForWriting(filePath))
            {
                MessageWindow.ShowDialog("File In Use",
                    "The DWG file is currently open in AutoCAD or another application.\n\n" +
                    "Please close the file and try again.",
                    MessageType.Warning);
                return false;
            }

            return true;
        }

        private List<object> GetMatchingLayers(DwgMappingViewModel dwgMappingVM)
        {
            var freeAxezLayerNames = dwgMappingVM.FreeAxezLayers?.Select(l => l.Name).ToHashSet(StringComparer.OrdinalIgnoreCase);
            return dwgMappingVM.DwgLayers?.Where(l => freeAxezLayerNames != null && freeAxezLayerNames.Contains(l.Name)).Cast<object>().ToList();
        }

        private object CreateUpdateRequest(DwgMappingViewModel dwgMappingVM, List<object> matchingLayers)
        {
            var freeAxezLayers = dwgMappingVM.FreeAxezLayers.ToDictionary(l => l.Name, l => l, StringComparer.OrdinalIgnoreCase);
            var layerMappings = new List<object>();
            var freeAxezLayerData = new List<object>();
            var linetypeData = new List<object>();
            var usedLinetypes = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            foreach (var dwgLayer in dwgMappingVM.DwgLayers)
            {
                if (freeAxezLayers.ContainsKey(dwgLayer.Name))
                {
                    var freeAxezLayer = freeAxezLayers[dwgLayer.Name];
                    var linetypeName = freeAxezLayer.LinetypeName ?? "Continuous";

                    layerMappings.Add(new { SourceLayer = dwgLayer.Name, TargetLayer = freeAxezLayer.Name });
                    freeAxezLayerData.Add(new
                    {
                        Name = freeAxezLayer.Name,
                        Color = $"#{freeAxezLayer.Color.R:X2}{freeAxezLayer.Color.G:X2}{freeAxezLayer.Color.B:X2}",
                        Linetype = linetypeName,
                        Lineweight = (double)freeAxezLayer.Lineweight01mm / 100.0,
                        Transparency = freeAxezLayer.TransparencyPct == 0 ? -1 : freeAxezLayer.TransparencyPct
                    });

                    if (!string.IsNullOrEmpty(linetypeName) &&
                        !linetypeName.Equals("Continuous", StringComparison.OrdinalIgnoreCase) &&
                        usedLinetypes.Add(linetypeName))
                    {
                        var linetype = dwgMappingVM.FreeAxezLinetypes?.FirstOrDefault(lt =>
                            string.Equals(lt.Name, linetypeName, StringComparison.OrdinalIgnoreCase));

                        if (linetype != null)
                        {
                            linetypeData.Add(new
                            {
                                Name = linetype.Name,
                                Description = linetype.Description ?? linetype.Name,
                                PatternRaw = linetype.PatternRaw ?? ""
                            });
                        }
                    }
                }
            }

            return layerMappings.Any() ? new
            {
                mappings = layerMappings,
                freeAxezLayers = freeAxezLayerData,
                linetypes = linetypeData
            } : null;
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Checks if file is available for writing operations
        /// </summary>
        public bool IsFileAvailableForWriting(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return false;

            try
            {
                using var stream = File.Open(filePath, FileMode.Open, FileAccess.ReadWrite, FileShare.None);
                return true;
            }
            catch (IOException)
            {
                return false;
            }
            catch (UnauthorizedAccessException)
            {
                return false;
            }
        }

        #endregion
    }
}
