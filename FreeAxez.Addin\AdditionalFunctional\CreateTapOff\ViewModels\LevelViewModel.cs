﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.CreateTapOff.ViewModels
{
    public class LevelViewModel : BaseViewModel
    {
        private bool _isCheck;

        public LevelViewModel(Level level)
        {
            Level = level;
            Name = level.Name;
            LevelId = level.Id.GetIntegerValue();
        }

        public string Name { get; }
        public Level Level { get; }
        public int LevelId { get; }
        public bool IsCheck
        {
            get
            {
                return _isCheck;
            }
            set
            {
                _isCheck = value;
                OnPropertyChanged();
            }
        }
    }
}
