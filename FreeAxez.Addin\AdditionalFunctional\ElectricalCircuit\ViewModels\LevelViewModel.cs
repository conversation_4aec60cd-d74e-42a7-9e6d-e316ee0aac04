﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.ViewModels
{
    public class LevelViewModel : BaseViewModel
    {
        private bool _isCheck;

        public string Name { get; set; }
        public Element Level { get; set; }
        public bool IsCheck
        {
            get
            {
                return _isCheck;
            }
            set
            {
                _isCheck = value;
                OnPropertyChanged();
            }
        }
    }
}
