﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.ExportForClient.Utils
{
    /// <summary>
    /// Class provides methods to purge project from unused elements.
    /// </summary>
    public class PurgeManager
    {
        private Document _doc;
        private int _numberOfPurges = 3;
        private int _numberOfModifiedElements = 0;
        private bool _countModifiedElements = false;


        public PurgeManager(Document doc)
        {
            _doc = doc;
        }


        /// <summary>
        /// Remove all unused families, types, groups etc.
        /// </summary>
        public void PurgeTypes()
        {
            // https://forums.autodesk.com/t5/revit-api-forum/purge-unused-via-the-api/m-p/9254365/highlight/true#M43907
            //Note this won't purge materials

            var desiredRule = "Project contains unused families and types";
            var perfAdviser = PerformanceAdviser.GetPerformanceAdviser();
            var allRulesList = perfAdviser.GetAllRuleIds();
            var rulesToExecute = new List<PerformanceAdviserRuleId>();

            foreach (PerformanceAdviserRuleId r in allRulesList)
            {
                if (perfAdviser.GetRuleName(r).Equals(desiredRule))
                {
                    rulesToExecute.Add(r);
                }
            }

            for (int i = 0; i < _numberOfPurges; i++)
            {
                var failureMessages = perfAdviser.ExecuteRules(_doc, rulesToExecute);

                if (failureMessages.Count() == 0)
                {
                    return;
                }

                var failingElementsIds = failureMessages[0].GetFailingElements();

                using (Transaction t = new Transaction(_doc, "Purge"))
                {
                    var options = t.GetFailureHandlingOptions();
                    options.SetFailuresPreprocessor(new WarningSwallower());
                    t.SetFailureHandlingOptions(options);

                    t.Start();

                    try
                    {
                        _doc.Delete(failingElementsIds);
                    }
                    catch
                    {
                        foreach (ElementId eid in failingElementsIds)
                        {
                            try
                            {
                                _doc.Delete(eid);
                            }
                            catch
                            {

                            }
                        }
                    }

                    t.Commit();
                }
            }
        }

        

        /// <summary>
        /// Purge all unused materials.
        /// Take to much time on big project.
        /// DONT use until fix productive.
        /// </summary>
        public void PurgeMaterials()
        {
            // Remove unused materials https://boostyourbim.wordpress.com/2016/10/21/purge-unused-materials-for-another-rtceur-api-wish/
            // TODO: White list of element ids could be useful to fix productive.
            // Check all families FreeAxez to get material and asset ids and create white list.

            _doc.Application.DocumentChanged += DocumentChangedHandler;

            var materials = new FilteredElementCollector(_doc).OfClass(typeof(Material)).ToList();
            foreach (Element material in materials)
            {
                _numberOfModifiedElements = 0;

                using (TransactionGroup tg = new TransactionGroup(_doc, "Delete Material"))
                {
                    tg.Start();

                    using (Transaction t = new Transaction(_doc, "Delete Material"))
                    {
                        t.Start();

                        _countModifiedElements = true;
                        _doc.Delete(material.Id);

                        // Commit the transaction to trigger the DocumentChanged event
                        t.Commit();
                    }

                    _countModifiedElements = false;

                    if (_numberOfModifiedElements == 1)
                    {
                        tg.Assimilate();
                    }
                    else
                    {
                        // Rollback the transaction group to undo the deletion
                        tg.RollBack();
                    }
                }
            }

            _doc.Application.DocumentChanged -= DocumentChangedHandler;
        }

        /// <summary>
        /// Purge all unused assets.
        /// Take to much time on big project.
        /// DONT use until fix productive.
        /// </summary>
        public void PurgeAssets()
        {
            // TODO: White list of element ids could be useful to fix productive.
            // Check all families FreeAxez to get material and asset ids and create white list.

            _doc.Application.DocumentChanged += DocumentChangedHandler;

            var multiClassFilter = new ElementMulticlassFilter(new List<Type>()
            {
                typeof(AppearanceAssetElement),
                typeof(PropertySetElement),
            });

            var assets = new FilteredElementCollector(_doc).WherePasses(multiClassFilter).ToList();
            foreach (Element asset in assets)
            {
                _numberOfModifiedElements = 0;

                using (TransactionGroup tg = new TransactionGroup(_doc, "Delete Asset"))
                {
                    tg.Start();
                    using (Transaction t = new Transaction(_doc, "Delete Asset"))
                    {
                        t.Start();

                        _countModifiedElements = true;
                        _doc.Delete(asset.Id);

                        // Commit the transaction to trigger the DocumentChanged event
                        t.Commit();
                    }

                    _countModifiedElements = false;

                    if (_numberOfModifiedElements == 1)
                    {
                        tg.Assimilate();
                    }
                    else
                    {
                        // Rollback the transaction group to undo the deletion
                        tg.RollBack();
                    }
                }
            }

            _doc.Application.DocumentChanged -= DocumentChangedHandler;
        }

        /// <summary>
        /// Delete all views in project and create instead one 3D view.
        /// </summary>
        public void DeleteAllViews()
        {
            var viewsToDelete = new FilteredElementCollector(_doc)
                .WhereElementIsNotElementType()
                .OfClass(typeof(View))
                .Cast<View>()
                .Where(v => v.IsTemplate == false && 
                            v.ViewType != ViewType.ProjectBrowser && 
                            v.ViewType != ViewType.SystemBrowser)
                .Where(v => v is ViewSchedule == false ||
                            v.OwnerViewId.Equals(ElementId.InvalidElementId)) // Exclude Revision Schedule
                .ToList();

            using (var t = new Transaction(_doc, "Create new 3D view"))
            {
                t.Start();

                var ThreeDViewFamilyType = new FilteredElementCollector(_doc)
                    .OfClass(typeof(ViewFamilyType))
                    .Cast<ViewFamilyType>()
                    .Where(t => t.ViewFamily == ViewFamily.ThreeDimensional)
                    .First();

                View3D.CreateIsometric(_doc, ThreeDViewFamilyType.Id);

                t.Commit();
            }

            using (var t = new Transaction(_doc, "Delete Views"))
            {
                var options = t.GetFailureHandlingOptions();
                options.SetFailuresPreprocessor(new WarningSwallower());
                t.SetFailureHandlingOptions(options);

                t.Start();
                
                foreach (var view in viewsToDelete)
                {
                    try
                    {
                        _doc.Delete(view.Id);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"Failed to delete view with type '{view.GetType()}' : {ex.Message}");
                    }
                }

                t.Commit();
            }
        }

        /// <summary>
        /// Remove all view templates from project.
        /// </summary>
        public void DeleteViewTemplates()
        {
            var viewTemplates = new FilteredElementCollector(_doc)
                .OfClass(typeof(View))
                .Cast<View>()
                .Where(v => v.IsTemplate)
                .ToList();

            using (Transaction t = new Transaction(_doc, "Delete View Templates"))
            {
                var options = t.GetFailureHandlingOptions();
                options.SetFailuresPreprocessor(new WarningSwallower());
                t.SetFailureHandlingOptions(options);

                t.Start();

                foreach (var view in viewTemplates)
                {
                    try
                    {
                        _doc.Delete(view.Id);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"Failed to delete view template with type '{view.GetType()}': {ex.Message}");
                    }
                }

                t.Commit();
            }
        }

        private void DocumentChangedHandler(object sender, Autodesk.Revit.DB.Events.DocumentChangedEventArgs e)
        {
            // Do not check when rolling back the transaction group
            if (!_countModifiedElements)
            {
                return;
            }

            List<ElementId> deleted = e.GetDeletedElementIds().ToList();
            List<ElementId> modified = e.GetModifiedElementIds().ToList();

            // How many elements were modified and deleted when this material was deleted?
            // If 1, then the material is unused and should be deleted
            _numberOfModifiedElements = deleted.Count + modified.Count;
        }
    }
}
