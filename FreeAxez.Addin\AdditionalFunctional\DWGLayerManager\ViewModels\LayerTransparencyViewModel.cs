using System.Collections.ObjectModel;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels;

public class LayerTransparencyViewModel : BaseDialogViewModel
{
    private int _selectedTransparency;

    public LayerTransparencyViewModel(int currentTransparency)
    {
        TransparencyValues = new ObservableCollection<int>();

        // Add transparency values from 0 to 90
        for (var i = 0; i <= 90; i++) TransparencyValues.Add(i);

        SelectedTransparency = currentTransparency;
    }

    public ObservableCollection<int> TransparencyValues { get; }

    public int SelectedTransparency
    {
        get => _selectedTransparency;
        set => Set(ref _selectedTransparency, value);
    }
}