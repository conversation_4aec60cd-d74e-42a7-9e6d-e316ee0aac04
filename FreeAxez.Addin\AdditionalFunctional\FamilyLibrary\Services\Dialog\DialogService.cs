﻿using System;
using System.Windows;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Dialog
{
    public class DialogService : IDialogService
    {
        private Window _currentModalWindow;
        public void ShowDialog(ModalDialogVm viewModel, Action<bool?> onDialogClose = null)
        {
            _currentModalWindow = new ModalDialog
            {
                DataContext = viewModel
            };

            viewModel.CloseDialog = result =>
            {
                CloseModalWindow(result);
                if (onDialogClose != null)
                {
                    onDialogClose(result);
                }
            };
            _currentModalWindow.ShowDialog();
        }

        private void CloseModalWindow(bool? dialogResult)
        {
            if (_currentModalWindow != null)
            {
                _currentModalWindow.Close();
                _currentModalWindow = null;
            }
        }
    }
}
