using System.Windows;
using System.Windows.Input;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels
{
    /// <summary>
    /// Base class for dialog view models with common Ok/Cancel functionality
    /// </summary>
    public abstract class BaseDialogViewModel : WindowViewModel
    {
        public ICommand OkCommand { get; }
        public ICommand CancelCommand { get; }

        protected BaseDialogViewModel()
        {
            OkCommand = new RelayCommand(param => Ok(param as Window));
            CancelCommand = new RelayCommand(param => Cancel(param as Window));
        }

        protected virtual void Ok(Window window)
        {
            window.DialogResult = true;
            window.Close();
        }

        protected virtual void Cancel(Window window)
        {
            window.DialogResult = false;
            window.Close();
        }
    }
}
