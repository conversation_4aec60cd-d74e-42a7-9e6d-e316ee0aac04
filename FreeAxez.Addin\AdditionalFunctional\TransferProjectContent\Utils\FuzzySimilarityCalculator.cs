﻿using FuzzySharp;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils
{
    public static class FuzzySimilarityCalculator
    {
        public static double CalculateSimilarity(string source, string target)
        {
            source = source?.ToLower();
            target = target?.ToLower();

            if (string.IsNullOrEmpty(source) || string.IsNullOrEmpty(target))
                return 0.0;

            if (source == target)
                return 1.0;

            var score = Fuzz.Ratio(source, target);
            return score / 100.0;
        }
    }
}
