using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.ExportForVR.Utils
{
    /// <summary>
    /// Validates if all FlexPipe types in the project have a corresponding RailingType.
    /// </summary>
    internal static class FlexPipeRailingsValidator
    {
        /// <summary>
        /// Gets a list of FlexPipe type names that do not have a corresponding RailingType.
        /// </summary>
        /// <returns>A list of missing railing type names.</returns>
        public static List<string> GetMissingRailingTypes()
        {
            var doc = RevitManager.Document;

            var flexPipeTypeNames = new FilteredElementCollector(doc)
                .OfClass(typeof(FlexPipe))
                .Select(p => p.get_Parameter(BuiltInParameter.ELEM_TYPE_PARAM).AsValueString())
                .Distinct()
                .ToList();

            if (!flexPipeTypeNames.Any())
            {
                return new List<string>(); // No flex pipes to validate
            }

            var railingTypeNames = new FilteredElementCollector(doc)
                .OfClass(typeof(RailingType))
                .Select(rt => rt.Name)
                .ToHashSet();

            return flexPipeTypeNames.Where(name => !railingTypeNames.Contains(name)).ToList();
        }
    }
}
