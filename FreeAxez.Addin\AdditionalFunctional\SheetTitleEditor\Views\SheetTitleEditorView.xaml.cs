using FreeAxez.Addin.AdditionalFunctional.SheetTitleEditor.Models;
using FreeAxez.Addin.AdditionalFunctional.SheetTitleEditor.ViewModels;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace FreeAxez.Addin.AdditionalFunctional.SheetTitleEditor.Views
{
    public partial class SheetTitleEditorView : Window
    {
        private bool _isUpdatingCheckboxes = false;

        public SheetTitleEditorView()
        {
            InitializeComponent();
        }

        private void CheckBox_CheckedChanged(object sender, RoutedEventArgs e)
        {
            // Prevent recursive calls
            if (_isUpdatingCheckboxes)
                return;

            _isUpdatingCheckboxes = true;

            try
            {
                // Get the checkbox that was changed
                var checkbox = sender as CheckBox;
                if (checkbox == null)
                    return;

                var itemsListView = FindParentListView(checkbox);
                if (itemsListView == null)
                    return;

                // Get the new state
                bool newState = checkbox.IsChecked ?? false;
                if (itemsListView.SelectedItems.Contains(checkbox.DataContext))
                {
                    // Get all selected items in the ListView
                    var selectedItems = itemsListView.SelectedItems;

                    // Update all selected items
                    foreach (SheetTitleModel item in selectedItems)
                    {
                        item.IsSelected = newState;
                    }
                }

                // Notify ViewModel about selection changes
                var viewModel = DataContext as SheetTitleEditorViewModel;
                if (viewModel != null)
                {
                    viewModel.UpdateSelectedCount();
                }
            }
            finally
            {
                _isUpdatingCheckboxes = false;
            }
        }

        private ListView FindParentListView(DependencyObject child)
        {
            // Find the parent ListView of a CheckBox
            DependencyObject parent = VisualTreeHelper.GetParent(child);
            while (parent != null && !(parent is ListView))
            {
                parent = VisualTreeHelper.GetParent(parent);
            }
            return parent as ListView;
        }
    }
}
