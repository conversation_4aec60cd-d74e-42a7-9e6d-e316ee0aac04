﻿﻿using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using Microsoft.Win32;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals
{
    public class AdminDetailsEditVm : AdminDetailsBaseVm
    {
        private DetailsFileInfoExist _currentDetailsInfo = new();
        private DetailsFileInfoExistVm _currentDetailsInfoVm;
        private bool _leaveImageFromCurrentVersion;
        private readonly LibraryItemDetailsDto _selectedDetails;

        public AdminDetailsEditVm(LibraryItemDetailsDto selectedDetails)
        {
            _selectedDetails = selectedDetails;
            _currentDetailsInfoVm = new DetailsFileInfoExistVm(selectedDetails, this);

            // Subscribe to changes in the current details info to update CanApply
            _currentDetailsInfoVm.PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(DetailsFileInfoExistVm.HasChanges))
                {
                    OnPropertyChanged(nameof(CanApply));
                }
            };

            _currentDetailsInfo.DataContext = _currentDetailsInfoVm;
            ChooseFileCommand = new RelayCommand(ExecuteChooseFile);
            ApplyCommand = new RelayCommand(ExecuteApply, CanExecuteApply);
        }

        public ICommand ChooseFileCommand { get; private set; }
        public ICommand ApplyCommand { get; private set; }

        public override bool CanApply
        {
            get
            {
                if (UploadFileInfo.Any())
                {
                    return !IsUploading;
                }

                return CurrentDetailsInfoVm?.HasChanges == true && !IsUploading;
            }
        }

        private bool CanExecuteApply(object parameter)
        {
            return CanApply;
        }

        public DetailsFileInfoExist CurrentDetailsInfo
        {
            get => _currentDetailsInfo;
            set
            {
                _currentDetailsInfo = value;
                OnPropertyChanged();
            }
        }

        public bool LeaveImageFromCurrentVersion
        {
            get => _leaveImageFromCurrentVersion;
            set
            {
                if (_leaveImageFromCurrentVersion != value)
                {
                    _leaveImageFromCurrentVersion = value;
                    OnPropertyChanged();
                }
            }
        }

        public DetailsFileInfoExistVm CurrentDetailsInfoVm
        {
            get => _currentDetailsInfoVm;
            set
            {
                _currentDetailsInfoVm = value;
                OnPropertyChanged();
            }
        }

        protected override void ExecuteChooseFile(object parameter)
        {
            // Clear any existing files before selecting new one
            UploadFileInfo.Clear();
            DetailsToUpload.Clear();

            var openFileDialog = new OpenFileDialog
            {
                Multiselect = false,
                Filter = "Revit Files (*.rvt;*.rfa;*.rte)|*.rvt;*.rfa;*.rte"
            };
            var result = openFileDialog.ShowDialog();
            if (result == true)
            {
                var tempFolderPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());

                try
                {
                    Directory.CreateDirectory(tempFolderPath);

                    var originalPath = openFileDialog.FileName;
                    var fileName = Path.GetFileName(originalPath);
                    var tempFilePath = Path.Combine(tempFolderPath, fileName);

                    // Copy file to temp folder to avoid issues with file being open in Revit
                    try
                    {
                        File.Copy(originalPath, tempFilePath, true);
                    }
                    catch (IOException ex)
                    {
                        LogHelper.Error($"Cannot access file: {fileName}, {ex.Message}");
                        Error = $"Cannot access file: {fileName}. The file may be open in Revit or another application.";
                        return;
                    }

                    var (success, errorMessage) = ProcessSingleFile(tempFilePath, fileName);
                    if (!success)
                    {
                        LogHelper.Warning($"Skipping {fileName}: {errorMessage}");
                        Error = $"{fileName} - {errorMessage}";
                        return;
                    }

                    // Notify that Apply button should be enabled
                    OnPropertyChanged(nameof(HasUploadedFiles));
                    OnPropertyChanged(nameof(CanApply));
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Error processing file: {ex.Message}");
                    Error = $"Error processing file: {ex.Message}";
                }
                finally
                {
                    // Clean up temp folder
                    try
                    {
                        if (Directory.Exists(tempFolderPath))
                            Directory.Delete(tempFolderPath, true);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Warning($"Failed to clean up temp folder: {ex.Message}");
                    }
                }
            }
        }

        private (bool Success, string ErrorMessage) ProcessSingleFile(string tempFilePath, string fileName)
        {
            Document revitDoc = null;
            try
            {
                var fileBytes = File.ReadAllBytes(tempFilePath);
                var path = ModelPathUtils.ConvertUserVisiblePathToModelPath(tempFilePath);
                revitDoc = RevitManager.Application.OpenDocumentFile(path, new OpenOptions());

                var filePreview = GetThumbnail(tempFilePath);
                var fileInfo = new FileInfo(tempFilePath);
                var fileExtension = Path.GetExtension(tempFilePath).ToUpperInvariant().Replace(".", "");

                var detailsItemDto = new LibraryItemDetailsDto
                {
                    Id = _selectedDetails.Id,
                    Name = fileName,
                    Description = _selectedDetails.Description,
                    FileType = fileExtension,
                    RevitVersion = RevitManager.RevitVersion,
                    CreatedBy = _selectedDetails.CreatedBy,
                    UpdatedBy = Properties.Settings.Default.UserEmail,
                    DateCreated = _selectedDetails.DateCreated,
                    LastDateUpdated = DateTime.Now
                };

                var fileDetail = new DetailsFileInfoNew
                {
                    DataContext = new DetailsFileInfoNewVm(detailsItemDto, this)
                    {
                        FileSize = $"{fileInfo.Length / 1.049e+6:0.0} MB",
                        UploadProgress = 100,
                        FilePreview = filePreview,
                        FileBytes = fileBytes,
                        RevitVersion = RevitManager.RevitVersion,
                        ShowRejectButton = false
                    }
                };

                UploadFileInfo.Add(fileDetail);
                DetailsToUpload.Add(detailsItemDto);

                return (true, null);
            }
            catch (Autodesk.Revit.Exceptions.FileAccessException ex)
            {
                return (false, "Created in newer version or corrupted");
            }
            catch (Exception ex)
            {
                return (false, $"Error: {ex.Message}");
            }
            finally
            {
                revitDoc?.Close(false);
            }
        }

        private async void ExecuteApply(object parameter)
        {
            IsUploading = true;

            if (UploadFileInfo.Any())
            {
                foreach (var fileDetail in UploadFileInfo)
                    if (fileDetail.DataContext is DetailsFileInfoNewVm vm)
                        try
                        {
                            var fileExtension = "." + vm.FileType.ToLowerInvariant();
                            var (filePath, fileError) = await ApiService.Instance.UploadDetailsFile(vm.FileBytes, vm.FileName, fileExtension);

                            string imagePath = _selectedDetails.ImagePath;
                            string imageError = null;

                            if (!LeaveImageFromCurrentVersion)
                            {
                                var imageResult = await ApiService.Instance.UploadDetailsImage(vm.FilePreview);
                                imagePath = imageResult.FilePath;
                                imageError = imageResult.Error;
                            }

                            if (fileError != null || imageError != null)
                            {
                                if (fileError != null) LoadingErrors.Add(fileError);
                                if (imageError != null) LoadingErrors.Add(imageError);
                                continue;
                            }

                            vm.DetailsItem.FilePath = filePath;
                            vm.DetailsItem.ImagePath = imagePath;

                            var response = await ApiService.Instance.UpdateDetailsAsync(vm.DetailsItem);
                            if (!response.IsSuccessStatusCode)
                            {
                                var errorContent = await response.Content.ReadAsStringAsync();
                                LoadingErrors.Add($"Failed to update details: {errorContent}");
                            }
                        }
                        catch (Exception ex)
                        {
                            LoadingErrors.Add($"Error: {ex.Message}");
                            LogHelper.Error($"An error occurred: {ex.Message}");
                        }
            }
            else if (CurrentDetailsInfoVm.HasChanges)
            {
                try
                {
                    if (CurrentDetailsInfoVm.FilePreviewChanged)
                    {
                        var imageResult = await ApiService.Instance.UploadDetailsImage(CurrentDetailsInfoVm.FilePreview);
                        if (imageResult.Error != null)
                            LoadingErrors.Add(imageResult.Error);
                        else
                            CurrentDetailsInfoVm.DetailsItem.ImagePath = imageResult.FilePath;
                    }

                    var updatedDetails = CurrentDetailsInfoVm.DetailsItem;
                    updatedDetails.UpdatedBy = Properties.Settings.Default.UserEmail;
                    updatedDetails.LastDateUpdated = DateTime.UtcNow;

                    var response = await ApiService.Instance.UpdateDetailsAsync(updatedDetails);
                    if (!response.IsSuccessStatusCode)
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        LoadingErrors.Add($"Failed to update details: {errorContent}");
                    }
                }
                catch (Exception ex)
                {
                    LoadingErrors.Add($"Error: {ex.Message}");
                    LogHelper.Error($"An error occurred: {ex.Message}");
                }
            }

            IsUploading = false;

            if (LoadingErrors.Count > 0)
                ShowLoadingErrorsMessage();
            else
                ShowSuccessMessage();
            CloseModal(true);
        }

        protected override void ShowSuccessMessage()
        {
            FamilyLibraryCore.ShowMessage("Success", "Details were successfully updated", MessageType.Success);
        }
    }
}
