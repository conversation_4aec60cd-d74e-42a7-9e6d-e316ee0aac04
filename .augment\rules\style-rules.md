---
type: "always_apply"
---

### General Style Principles
- **Constants instead of magic numbers**: Don't use magic numbers in code, use constants instead
- **Explicit braces**: Use explicit braces {} in code even for single-line blocks
- **Naming Conventions**: Follow Microsoft .NET Naming Guidelines
- **PascalCase**: For classes, methods, properties, enums
- **camelCase**: For private fields, parameters, local variables
- **Underscore prefix**: For private class fields (`_fieldName`)
- **CamelCase**: For constants

### Code Formatting
- **Indentation**: 4 spaces (not tabs)
- **Line length**: Maximum 120 characters
- **Empty lines**: One empty line between methods, two between classes
- **Using statements**: Sort alphabetically, System namespace first
- **Braces**: Allman style (brace on new line)

### Comments and Documentation
- **XML Documentation**: For all public members
- **Inline comments**: Only for complex business logic
- **TODO comments**: With date and author initials
- **Forbidden comments**: Commented code (delete instead of commenting)