﻿using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TagRamp.Abstractions;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.TagRamp.Models
{
    public class RampComponentVisibleInView : IRampSelector
    {
        public List<FamilyInstance> Select()
        {
            if (RevitManager.Document.ActiveView.ViewType == ViewType.ThreeD)
            {
                throw new NullReferenceException("Select plan view.");
            }

            var rampComponents = new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .WhereElementIsNotElementType()
                .Cast<FamilyInstance>()
                .Where(f => f.Symbol.FamilyName.Split(new char[] { '-', '_' })
                    .Any(item => item == "Ramp"))
                .Where(f => RevitManager.Document.ActiveView.GenLevel.Id == f.LevelId)
                .ToList();

            if (rampComponents.Count < 2)
            {
                throw new NullReferenceException("Select 2 or more ramp instances");
            }

            return rampComponents;
        }
    }
}
