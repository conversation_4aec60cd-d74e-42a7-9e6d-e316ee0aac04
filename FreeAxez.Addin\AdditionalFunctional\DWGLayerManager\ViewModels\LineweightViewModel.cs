using System.Collections.ObjectModel;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels;

public class LineweightViewModel : BaseDialogViewModel
{
    private readonly short _originalLineweight;
    private LineweightItem _selectedLineweight;

    public LineweightViewModel(short currentLineweight = -3)
    {
        _originalLineweight = currentLineweight;
        AvailableLineweights = new ObservableCollection<LineweightItem>(LineweightService.GetAllLineweights());

        // Select current lineweight
        SelectedLineweight = AvailableLineweights.FirstOrDefault(l => l.Value == currentLineweight)
                             ?? AvailableLineweights.FirstOrDefault(l => l.Value == -3); // Default
    }

    public ObservableCollection<LineweightItem> AvailableLineweights { get; }

    public LineweightItem SelectedLineweight
    {
        get => _selectedLineweight;
        set => Set(ref _selectedLineweight, value);
    }

    public string OriginalLineweight => LineweightService.GetDisplayName(_originalLineweight);

    public short SelectedLineweightValue => SelectedLineweight?.Value ?? -3;
}