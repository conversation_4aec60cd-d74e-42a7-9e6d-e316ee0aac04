using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace FreeAxez.Addin.AdditionalFunctional.ExportForVR.Utils
{
    public class FamilyReplacementManager
    {
        private readonly string _folderPath;

        // Data stores populated during validation
        private Dictionary<string, HashSet<string>> _folderFamilies;
        private Dictionary<string, HashSet<string>> _usedProjectTypes;
        private List<string> _newerVersionFiles;

        public FamilyReplacementManager(string folderPath)
        {
            _folderPath = folderPath;
        }

        public bool ValidateFamilies(out string report)
        {
            // 1. Gather all data first
            _folderFamilies = GetFamiliesFromFolder(_folderPath, out _newerVersionFiles);
            _usedProjectTypes = GetAllUsedProjectFamilyTypes();

            // 2. Perform validation and build the report
            var reportBuilder = new StringBuilder();
            var missingFamilies = _folderFamilies.Keys.Except(_usedProjectTypes.Keys).ToList();
            if (missingFamilies.Any())
            {
                reportBuilder.AppendLine("THE PROJECT DOES NOT INCLUDE FAMILIES:");
                missingFamilies.ForEach(f => reportBuilder.AppendLine($"{f}"));
                reportBuilder.AppendLine();
            }

            var commonFamilyNames = _folderFamilies.Keys.Intersect(_usedProjectTypes.Keys);
            var missingTypesReport = new StringBuilder();
            foreach (var familyName in commonFamilyNames)
            {
                var missingTypes = _usedProjectTypes[familyName].Except(_folderFamilies[familyName]).ToList();
                if (missingTypes.Any())
                {
                    missingTypes.ForEach(t => missingTypesReport.AppendLine($"{familyName} : {t}"));
                }
            }

            if (missingTypesReport.Length > 0)
            {
                reportBuilder.AppendLine("FAMILIES DO NOT HAVE TYPES FROM THE PROJECT:");
                reportBuilder.Append(missingTypesReport);
                reportBuilder.AppendLine();
            }

            if (_newerVersionFiles.Any())
            {
                reportBuilder.AppendLine("REVIT VERSION IS NOT SUITABLE:");
                _newerVersionFiles.ForEach(f => reportBuilder.AppendLine($"{Path.GetFileName(f)}"));
            }

            report = reportBuilder.ToString();
            return string.IsNullOrEmpty(report);
        }

        public string GenerateReplacementReport()
        {
            var report = new StringBuilder();

            var validFamilies = GetValidFamilies(_folderFamilies, _usedProjectTypes);

            if (validFamilies.Count > 0)
            {
                report.AppendLine("REPLACED FAMILIES:");
                report.Append(string.Join("\n", validFamilies));
            }

            var invalidFamilies = _folderFamilies.Keys.Except(validFamilies);
            if (invalidFamilies.Count() > 0)
            {
                report.AppendLine();
                report.AppendLine();
                report.AppendLine("NOT REPLACED FAMILIES:");
                report.AppendLine(string.Join("\n", invalidFamilies));
            }

            return report.ToString();
        }

        public void PerformReplacement(Document doc)
        {
            var validFamilies = GetValidFamilies(_folderFamilies, _usedProjectTypes);

            if (!validFamilies.Any()) return;

            var rfaFiles = Directory.GetFiles(_folderPath, "*.rfa", SearchOption.AllDirectories)
                                    .ToDictionary(path => Path.GetFileNameWithoutExtension(path), path => path, StringComparer.OrdinalIgnoreCase);

            using (var t = new Transaction(doc, "Replace Families"))
            {
                t.Start();
                var loadOptions = new OverrideFamilyLoadOptions();
                foreach (var familyName in validFamilies)
                {
                    if (rfaFiles.TryGetValue(familyName, out var filePath))
                    {
                        try { doc.LoadFamily(filePath, loadOptions, out _); }
                        catch (Exception ex) { LogHelper.Error($"Failed to load family '{familyName}' from '{filePath}': {ex.Message}"); }
                    }
                }
                t.Commit();
            }
        }

        private Dictionary<string, HashSet<string>> GetFamiliesFromFolder(string folderPath, out List<string> newerVersionFiles)
        {
            var families = new Dictionary<string, HashSet<string>>(StringComparer.OrdinalIgnoreCase);
            newerVersionFiles = new List<string>();
            if (!Directory.Exists(folderPath)) return families;

            foreach (var rfaFile in Directory.GetFiles(folderPath, "*.rfa", SearchOption.TopDirectoryOnly))
            {
                Document familyDoc = null;

                try
                {
                    familyDoc = RevitManager.Application.OpenDocumentFile(rfaFile);
                    var familyName = familyDoc.Title;
                    var types = new HashSet<string>(familyDoc.FamilyManager.Types.Cast<FamilyType>().Select(t => t.Name));
                    families.Add(familyName, types);
                }
                catch (Exception ex)
                { 
                    newerVersionFiles.Add(rfaFile); 
                    LogHelper.Error($"Failed to open family file {rfaFile} due to {ex.Message}");
                }
                finally
                {
                    familyDoc?.Close(false);
                    familyDoc?.Dispose();
                }
            }

            return families;
        }

        private Dictionary<string, HashSet<string>> GetAllUsedProjectFamilyTypes()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(FamilySymbol))
                .Cast<FamilySymbol>()
                .Where(s => s.GetDependentElements(new FamilyInstanceFilter(RevitManager.Document, s.Id)).Count > 0)
                .GroupBy(s => s.Family.Name, StringComparer.OrdinalIgnoreCase)
                .ToDictionary(g => g.Key, g => new HashSet<string>(g.Select(s => s.Name)), StringComparer.OrdinalIgnoreCase);
        }

        private List<string> GetValidFamilies(
            Dictionary<string, HashSet<string>> folderFamilies, Dictionary<string, HashSet<string>> usedProjectTypes)
        {
            var output = new List<string>();
            var commonFamilies = folderFamilies.Keys.Intersect(usedProjectTypes.Keys).ToList();
            foreach (var familyName in commonFamilies)
            {
                var missingUsedTypes = usedProjectTypes[familyName].Except(folderFamilies[familyName]).ToList();
                if (missingUsedTypes.Count == 0) output.Add(familyName);
            }
            return output;
        }
    }
}
