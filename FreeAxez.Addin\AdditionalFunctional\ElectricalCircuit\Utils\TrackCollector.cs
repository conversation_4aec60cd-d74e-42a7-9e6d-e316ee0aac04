﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils
{
    public class TrackCollector
    {
        private const string TrackFamilyNameSuffix = "-Track-Array";
        private const string TrackFamilyNameSuffix2 = "-Track-4'";
        private const string TrackFamilyNameSuffix3 = "-Track-8'";
        private readonly LevelHelper _levelHelper;


        public TrackCollector(LevelHelper levelHelper)
        {
            _levelHelper = levelHelper;
        }


        public List<FamilyInstance> GetTrackFamilyInstances()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_ElectricalEquipment)
                .WhereElementIsNotElementType()
                .Cast<FamilyInstance>()
                .Where(i => i.SuperComponent == null)
                .Where(i => i.Symbol.FamilyName.Contains(TrackFamilyNameSuffix) 
                         || i.Symbol.FamilyName.Contains(TrackFamilyNameSuffix2) 
                         || i.Symbol.FamilyName.Contains(TrackFamilyNameSuffix3))
                .Where(_levelHelper.BelongsToTheSelectedLevel)
                .ToList();
        }
    }
}
