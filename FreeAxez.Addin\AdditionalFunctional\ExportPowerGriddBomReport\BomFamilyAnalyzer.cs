﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Utils;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBOM.Utils;
using FreeAxez.Addin.Models.Base;
using System.IO;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerGriddBomReport
{
    public class BomFamilyAnalyzer
    {
        public void ExportGriddReport(string path)
        {
            var products = GriddProductCollector.Collect(GriddProductCollector.GetOptionalProductNames());
            ExportReport(path, products);
        }

        public void ExportPowerReport(string path)
        {
            var products = PowerProductCollector.Collect(PowerProductCollector.GetAccessoriesNames());
            ExportReport(path, products);
        }

        private void ExportReport(string path, List<Product> products)
        {
            var report = "";

            var productGroups = products.OrderBy(p => p.GetType().ToString()).GroupBy(p => p.GetType()).ToList();

            foreach (var productGroup in productGroups)
            {
                var productType = productGroup.Key.ToString();

                report += productType;
                report += "\n";

                var usedFamilies = new Dictionary<string, List<Element>>();

                foreach (var product in productGroup)
                {
                    var productKey = GetProductFamilyName(product.Element);
                    if (!usedFamilies.ContainsKey(productKey))
                    {
                        usedFamilies.Add(productKey, new List<Element>());
                    }
                    usedFamilies[productKey].Add(product.Element);
                }

                var orderedKeys = usedFamilies.Keys.OrderBy(k => k).ToList();

                foreach (var key in orderedKeys)
                {
                    report += $"    {key}    {usedFamilies[key].Count}\n";
                }

                report += "\n";
            }

            File.WriteAllText(path, report);
        }

        private string GetProductFamilyName(Element element)
        {
            if (element is FamilyInstance familyInstance)
            {
                return $"{familyInstance.Symbol.FamilyName} : {familyInstance.Symbol.Name}";
            }

            return element.Name;
        }
    }
}
