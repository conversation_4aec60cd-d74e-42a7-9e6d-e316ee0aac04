﻿using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model
{
    public class LoadDataFamiliesPage
    {
        public ObservableCollection<LibraryCategoryDto> Categories { get; set; }
        public ObservableCollection<LibraryItemVm> Families { get; set; }
        public ObservableCollection<string> RevitVersions { get; set; }
        public Dictionary<Guid, List<LibraryItemDto>> GroupedLibraryFamilies { get; set; }
    }
}
