﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Threading;

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Views;

public partial class TaskManagerView : Window
{
    public TaskManagerView()
    {
        Icon = new BitmapImage(new Uri(
        "pack://application:,,,/FreeAxez.Addin;component/AdditionalFunctional/SmartsheetTaskManager/Resources/Icons/smartsheet16.png"));

        InitializeComponent();
    }

    private void Window_Closed(object sender, EventArgs e)
    {
        var taskManagerView = sender as TaskManagerView;

        if (taskManagerView?.DataContext is TaskManagerViewModel taskManagerViewModel)
        {
            taskManagerViewModel.CancellationTokenSource.Cancel();
        }
    }

    private void Hyperlink_RequestNavigate(object sender, RequestNavigateEventArgs e)
    {
        try
        {
            var startInfo = new ProcessStartInfo()
            {
                FileName = e.Uri.AbsoluteUri,
                UseShellExecute = true
            };

            Process.Start(startInfo);
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog("Error Opening File", "Failed to open the exported file: " + ex.Message, MessageType.Error);
        }


        e.Handled = true;
    }
}
