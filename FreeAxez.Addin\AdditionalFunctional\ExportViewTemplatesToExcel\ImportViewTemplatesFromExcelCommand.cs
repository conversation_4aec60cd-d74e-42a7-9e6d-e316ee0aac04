﻿using System.Diagnostics;
using System.IO;
using System.Windows.Forms;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Helpers;
using FreeAxez.Addin.Infrastructure.UI.Services;
using OfficeOpenXml;
using View = Autodesk.Revit.DB.View;

namespace FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel
{
    [Transaction(TransactionMode.Manual)]
    public class ImportViewTemplatesFromExcelCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            var uiDoc = commandData.Application.ActiveUIDocument;
            var doc = uiDoc.Document;
            try
            {
                using var ofd = new OpenFileDialog
                {
                    Filter = "Excel files (*.xlsx)|*.xlsx",
                    Title = "Select Excel file with View Templates"
                };
                if (ofd.ShowDialog() != DialogResult.OK)
                    return Result.Cancelled;

                string excelFilePath = ofd.FileName;
                string folder = Path.GetDirectoryName(excelFilePath);
                string logFileName = $"ImportViewTemplatesFromExcel_{DateTime.Now.ToString("yyyyMMdd_HHmmss")}.txt";
                string logFilePath = Path.Combine(folder, logFileName);
                var localLogger = new LocalLogger(logFilePath);
                localLogger.Information("Starting import from Excel.");

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using var package = new ExcelPackage(new FileInfo(excelFilePath));

                // Collect dictionaries once
                var viewTemplates = new FilteredElementCollector(doc)
                    .OfClass(typeof(View))
                    .Cast<View>()
                    .Where(v => v.IsTemplate)
                    .ToDictionary(v => v.Name);

                var modelCategories = doc.Settings.Categories.Cast<Category>()
                    .Where(c => c != null && c.CategoryType == CategoryType.Model && c.CanAddSubcategory)
                    .ToDictionary(c => c.Name);

                var annotationCategories = doc.Settings.Categories.Cast<Category>()
                    .Where(c => c != null && c.CategoryType == CategoryType.Annotation)
                    .ToDictionary(c => c.Name);

                var viewTemplatesParser = new PageViewTemplatesParser();
                try
                {
                    using (var t = new Transaction(doc, "Update View Templates"))
                    {
                        t.Start();
                        viewTemplatesParser.ParseSheetAndUpdateViewTemplates(package, doc, viewTemplates, localLogger);
                        t.Commit();
                    }
                }
                catch (Exception ex)
                {
                    localLogger.Error("View Templates update error: " + ex.Message);
                }

                var modelCategoriesParser = new PageModelCategoriesParser();
                try
                {
                    using (var t = new Transaction(doc, "Update Model Categories"))
                    {
                        t.Start();
                        modelCategoriesParser.ParseSheetAndUpdateModelCategories(package, doc, viewTemplates, modelCategories, localLogger);
                        t.Commit();
                    }
                }
                catch (Exception ex)
                {
                    localLogger.Error("Model Categories update error: " + ex.Message);
                }

                var annotationCategoriesParser = new PageAnnotationCategoriesParser();
                try
                {
                    using (var t = new Transaction(doc, "Update Annotation Categories"))
                    {
                        t.Start();
                        annotationCategoriesParser.ParseSheetAndUpdateAnnotationCategories(package, doc, viewTemplates, annotationCategories, localLogger);
                        t.Commit();
                    }
                }
                catch (Exception ex)
                {
                    localLogger.Error("Annotation Categories update error: " + ex.Message);
                }

                var filtersParser = new PageFiltersParser();
                try
                {
                    using (var t = new Transaction(doc, "Update Filters"))
                    {
                        t.Start();
                        filtersParser.ParseSheetAndUpdateFilters(package, doc, viewTemplates, localLogger);
                        t.Commit();
                    }
                }
                catch (Exception ex)
                {
                    localLogger.Error("Filters update error: " + ex.Message);
                }

                RenameViewTemplates(package, doc, viewTemplates, localLogger);
                RenameFilters(package, doc, localLogger);

                localLogger.Information("Import completed successfully.");
                localLogger.Dispose();
                MessageWindow.ShowDialog("View Templates updated successfully.", Infrastructure.UI.Enums.MessageType.Success);

                try
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = logFilePath,
                        UseShellExecute = true
                    });
                }

                catch
                {
                    // ignored
                }

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog($"Error updating View Templates: {ex.Message}\n\nDetails: {ex}", Infrastructure.UI.Enums.MessageType.Error);
                return Result.Failed;
            }
        }

        private void RenameViewTemplates(ExcelPackage package, Document doc,
            Dictionary<string, View> viewTemplates, LocalLogger logger)
        {
            var ws = package.Workbook.Worksheets["Rename View Templates"];
            if (ws == null) return;

            int start = 2, end = ws.Dimension.End.Row;
            using (var tx = new Transaction(doc, "Rename View Templates"))
            {
                tx.Start();
                for (int row = start; row <= end; row++)
                {
                    string oldName = ws.Cells[row, 1].Text.Trim();
                    string newName = ws.Cells[row, 2].Text.Trim();
                    if (string.IsNullOrEmpty(newName) || newName == oldName) continue;
                    if (viewTemplates.TryGetValue(oldName, out var vt))
                    {
                        try { vt.Name = newName; }
                        catch (Exception ex) { logger.Error($"Rename VT '{oldName}'→'{newName}': {ex.Message}"); }
                    }
                }
                tx.Commit();
            }
        }

        private void RenameFilters(ExcelPackage package, Document doc, LocalLogger logger)
        {
            var ws = package.Workbook.Worksheets["Rename Filters"];
            if (ws == null) return;

            var filters = new FilteredElementCollector(doc)
                .OfClass(typeof(ParameterFilterElement))
                .Cast<ParameterFilterElement>()
                .ToDictionary(f => f.Name, f => f);

            int start = 2, end = ws.Dimension.End.Row;
            using (var tx = new Transaction(doc, "Rename Filters"))
            {
                tx.Start();
                for (int row = start; row <= end; row++)
                {
                    string oldName = ws.Cells[row, 1].Text.Trim();
                    string newName = ws.Cells[row, 2].Text.Trim();
                    if (string.IsNullOrEmpty(newName) || newName == oldName) continue;
                    if (filters.TryGetValue(oldName, out var fe))
                    {
                        try { fe.Name = newName; }
                        catch (Exception ex) { logger.Error($"Rename Filter '{oldName}'→'{newName}': {ex.Message}"); }
                    }
                }
                tx.Commit();
            }
        }
    }
}
