﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.Pallets.Views.PlacementOptionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        WindowStartupLocation="CenterScreen"
        Topmost="True"
        Title="Stage"
        ResizeMode="NoResize"
        SizeToContent="WidthAndHeight">
    <DockPanel Margin="10">
        
        <GroupBox DockPanel.Dock="Top" Header="Pallet Placement Direction">
            <StackPanel Margin="3">
                <RadioButton Content="Horizontal" IsChecked="True" GroupName="direction"/>
                <RadioButton x:Name="verticalDirection" Margin="0,3,0,0" Content="Vertical" GroupName="direction"/>
            </StackPanel>
        </GroupBox>
        
        <GroupBox Margin="0,10,0,0" DockPanel.Dock="Top" Header="Placement Region">
            <StackPanel Margin="3">
                <RadioButton Content="Whole Plan" IsChecked="True" GroupName="placementRegion"/>
                <RadioButton x:Name="selectPlacementRegion" Margin="0,3,0,0" Content="Select Region" GroupName="placementRegion" Checked="selectPlacementRegion_Checked" Unchecked="selectPlacementRegion_Checked"/>
            </StackPanel>
        </GroupBox>

        <DockPanel x:Name="selectPlacementRegionButton" Visibility="Collapsed" DockPanel.Dock="Top" Margin="0,5,0,0">
            <Label Content="Placement Region" />
            <Button x:Name="placementArea" Margin="5,0,0,0" Width="100" HorizontalAlignment="Right" Height="25" Content="Select" Click="placementArea_Click"/>
        </DockPanel>

        <DockPanel DockPanel.Dock="Top" Margin="0,5,0,0">
            <Label Content="Start Region"/>
            <Button x:Name="startArea" Margin="5,0,0,0" Width="100" HorizontalAlignment="Right" Height="25" Content="Select" Click="startArea_Click"/>
        </DockPanel>

        <DockPanel DockPanel.Dock="Top" Margin="0,0,0,0">
            <Label Content="*Required Area:" FontSize="11" FontStyle="Italic"/>
            <Label x:Name="requiredArea" FontSize="11" FontStyle="Italic"/>
        </DockPanel>

        <DockPanel DockPanel.Dock="Top" Margin="0,0,0,0">
            <Label Content="*Required Staging Area:" FontSize="11" FontStyle="Italic"/>
            <Label x:Name="requiredStagingArea" FontSize="11" FontStyle="Italic"/>
        </DockPanel>

        <DockPanel DockPanel.Dock="Top" Margin="0,0,0,0">
            <Label Content="Protected Region"/>
            <Button x:Name="protectedRegion" Margin="5,0,0,0" Width="100" HorizontalAlignment="Right" Height="25" Content="Select" Click="protectedRegion_Click"/>
        </DockPanel>

        <Button Margin="0,20,0,0" Content="Place Pallets" Height="25" Width="212" Click="placePallets_Click"/>
        
    </DockPanel>
</Window>
