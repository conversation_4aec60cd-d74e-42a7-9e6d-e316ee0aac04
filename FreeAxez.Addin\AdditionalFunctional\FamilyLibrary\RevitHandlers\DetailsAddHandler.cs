using System;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Dialog;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.RevitHandlers
{
    public class DetailsAddHandler : BaseExternalEventHandler
    {
        private object _viewModel;
        private readonly DialogManager _dialogManager;

        public DetailsAddHandler()
        {
            _dialogManager = new DialogManager();
        }

        public void SetData(object viewModel)
        {
            _viewModel = viewModel;
        }

        public override void ExecuteInternal(UIApplication app)
        {
            try
            {
                // Show dialog with callback that handles the result
                _dialogManager.ShowAddDetailsDialog(result =>
                {
                    try
                    {
                        // Update ViewModel if dialog was successful
                        if (result == true)
                        {
                            UpdateViewModel(false);
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"Error updating ViewModel after dialog: {ex.Message}");
                        UpdateViewModel(true);
                    }
                    finally
                    {
                        _viewModel = null;
                    }
                });
            }
            catch (Exception ex)
            {
                ErrorHandler.HandleError("Error in details add operation", ex, onError: () => UpdateViewModel(true));
                _viewModel = null;
            }
        }

        private void UpdateViewModel(bool isError)
        {
            if (_viewModel == null)
                return;

            try
            {
                if (_viewModel is DetailsPageVm detailsVm)
                {
                    if (!isError)
                        _ = detailsVm.LoadData();
                    detailsVm.IsLoading = false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error updating ViewModel: {ex.Message}");
            }
        }
    }
}
