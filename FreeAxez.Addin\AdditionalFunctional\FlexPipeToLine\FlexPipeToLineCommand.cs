﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.FlexPipeToLine.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;

namespace FreeAxez.Addin.AdditionalFunctional.FlexPipeToLine
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    public class FlexPipeToLineCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            if (!(RevitManager.UIDocument.ActiveView is ViewPlan))
            {
                InfoDialog.ShowDialog("Warning", "The plugin only works with plans.");
                return Result.Cancelled;
            }

            var flexPipeToLineView = new FlexPipeToLineView();
            flexPipeToLineView.ShowDialog();

            return Result.Succeeded;
        }
    }
}
