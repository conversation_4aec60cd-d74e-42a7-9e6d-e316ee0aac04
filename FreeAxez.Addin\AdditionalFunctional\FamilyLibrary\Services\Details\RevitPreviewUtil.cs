using System.IO;
using System.Text.RegularExpressions;
using System.Windows.Media.Imaging;
using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Details;

/// <summary>
///     Utility class for creating preview images of Revit views
/// </summary>
public static class RevitPreviewUtil
{
    /// <summary>
    ///     Creates a preview image for a Revit view
    /// </summary>
    public static BitmapSource CreatePreviewImage(Autodesk.Revit.DB.View view, Document doc)
    {
        if (view == null || doc == null) return null;

        var elementName = view.Name;
        var elementType = "view";

        return CreatePreviewImageInternal(view, doc, elementName, elementType);
    }

    /// <summary>
    ///     Internal method to create preview images for views
    /// </summary>
    private static BitmapSource CreatePreviewImageInternal(Autodesk.Revit.DB.View element, Document doc,
        string elementName, string elementType)
    {
        var shortName = elementName.Length > 40 ? elementName.Substring(0, 40) : elementName;
        shortName = shortName.Trim().TrimEnd('.');
        var safeName = Regex.Replace(shortName, @"[^a-zA-Z0-9]+", "_");

        if (string.IsNullOrEmpty(safeName) || safeName.All(c => c == '_'))
        {
            LogHelper.Information(
                $"{elementType} name '{elementName}' resulted in empty safe name, using fallback: {elementType}");
            safeName = char.ToUpper(elementType[0]) + elementType.Substring(1); // Capitalize first letter
        }

        var uniqueId = Guid.NewGuid().ToString().Substring(0, 8);
        var prefix = Path.Combine(Path.GetTempPath(), $"FreeAxez_{safeName}_{uniqueId}");

        try
        {
            LogHelper.Information($"Exporting {elementType} to: {prefix}");

            var ieo = new ImageExportOptions
            {
                ExportRange = ExportRange.SetOfViews,
                HLRandWFViewsFileType = ImageFileType.PNG,
                ZoomType = ZoomFitType.FitToPage,
                PixelSize = 1500,
                ImageResolution = ImageResolution.DPI_300,
                FitDirection = FitDirectionType.Horizontal,
                FilePath = prefix,
                ViewName = safeName
            };
            ieo.SetViewsAndSheets(new List<ElementId> { element.Id });

            if (prefix.Length > 200)
            {
                LogHelper.Information($"Path too long: {prefix}");
                return null;
            }

            var directory = Path.GetDirectoryName(prefix);
            if (!Directory.Exists(directory))
                Directory.CreateDirectory(directory);

            var exportTime = DateTime.Now;
            doc.ExportImage(ieo);

            var filesInDir = Directory.GetFiles(directory, "*.png");
            LogHelper.Information($"Files in {directory}: {string.Join(", ", filesInDir)}");

            var png = Directory.GetFiles(directory, "*.png")
                .Where(f => File.GetCreationTime(f) >= exportTime)
                .FirstOrDefault(f => f.Contains(safeName) || f.Contains(elementName));

            if (png == null)
            {
                LogHelper.Information($"No preview image found for {elementType}: {elementName}");
                return null;
            }

            LogHelper.Information($"Found image: {png}");

            var bmp = new BitmapImage();
            bmp.BeginInit();
            bmp.CacheOption = BitmapCacheOption.OnLoad;
            bmp.UriSource = new Uri(png);
            bmp.EndInit();
            bmp.Freeze();

            return bmp;
        }
        catch (Exception ex)
        {
            LogHelper.Error($"Error for {elementType} {elementName}: {ex.Message}");
            return null;
        }
        finally
        {
            try
            {
                var tempFiles = Directory.GetFiles(Path.GetDirectoryName(prefix), "*.png")
                    .Where(f => f.Contains(safeName) || f.Contains(elementName));
                foreach (var file in tempFiles)
                    if (File.Exists(file))
                    {
                        LogHelper.Information($"Deleting: {file}");
                        File.Delete(file);
                    }
            }
            catch (Exception ex)
            {
                LogHelper.Information($"Failed to delete files for {prefix}: {ex.Message}");
            }
        }
    }
}