﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class OutletCover : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
            },
            FamilyNamesEndWith = new List<string>
            {
                "Outlet_Cover"
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public OutletCover(Element element) : base(element)
        {
        }

        public static List<OutletCover> Collect()
        {
            return FamilyCollector.Instances.Select(f => new OutletCover(f)).ToList();
        }
    }
}
