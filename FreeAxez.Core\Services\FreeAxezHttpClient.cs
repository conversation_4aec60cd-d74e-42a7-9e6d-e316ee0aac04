﻿using System;
using System.Net.Http;
using System.Security.Authentication;

namespace FreeAxez.Core.Services
{
    public static class FreeAxezHttpClient
    {
        private static HttpClient _instance;
        public static HttpClient Instance
        {
            get
            {
                var httpClientHandler = new HttpClientHandler { SslProtocols = SslProtocols.Tls12 };
                if (_instance == null)
                {
                    _instance = new HttpClient(httpClientHandler, disposeHandler: true);
                    _instance.Timeout = TimeSpan.FromMinutes(15);
                }

                return _instance;
            }
        }
    }
}