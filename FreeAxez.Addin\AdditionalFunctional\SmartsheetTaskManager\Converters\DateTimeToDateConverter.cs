﻿using System;
using System.Globalization;
using System.Windows.Data;

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Converters;

public class DateTimeToDateConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is DateTime date)
        {
            TimeSpan timeDifferrence = DateTime.UtcNow - date;

            if (timeDifferrence.TotalSeconds < 60)
            {
                return "Just now";
            }
            else if (timeDifferrence.TotalMinutes < 60)
            {
                int minutes = (int)timeDifferrence.TotalMinutes;
                return minutes == 1 ? "1 minute ago" : $"{minutes} minutes ago";
            }
            else if (timeDifferrence.TotalHours < 24)
            {
                int hours = (int)timeDifferrence.TotalHours;
                return hours == 1 ? "1 hour ago" : $"{hours} hours ago";
            }
            else
            {
                return date.ToString("MM/dd/yyyy");
            }
        }

        return value;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
