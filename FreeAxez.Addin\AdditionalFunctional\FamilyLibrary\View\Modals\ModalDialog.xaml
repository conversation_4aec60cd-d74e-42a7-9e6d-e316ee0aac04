<Window x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals.ModalDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        mc:Ignorable="d"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        SizeToContent="WidthAndHeight">
    <WindowChrome.WindowChrome>
        <WindowChrome GlassFrameThickness="0,0,0,0" CornerRadius="0" />
    </WindowChrome.WindowChrome>
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Grid>
        <ContentControl x:Name="ModalContent" 
                        Content="{Binding Content}"/>
    </Grid>
</Window>
