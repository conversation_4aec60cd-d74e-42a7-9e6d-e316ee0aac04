﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.AdditionalFunctional.TagAllFrames.Utils;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllFrames.Models
{
    public class FrameFamily
    {
        private const string FamilyNameKey = "Frame";
        private const string AnchorFamilyNameKey = "Anchor";
        private const string CapFamilyNameKey = "Cap";
        private const string CornerFamilyNameKey = "Corner";

        public FrameFamily()
        {
            Symbols = GetFamilySymbols();
            InstancesOnView = GetInstancesOnView(RevitManager.Document.ActiveView);
        }

        public List<FamilySymbol> Symbols { get; set; }
        public List<FamilyInstance> InstancesOnView { get; set; }

        public static bool IsCorner(FamilyInstance instance)
        {
            return instance.Symbol.FamilyName.Contains(CornerFamilyNameKey);
        }

        public bool IsFrameInstance(FamilySymbol symbol)
        {
            return symbol.FamilyName.Contains(FamilyNameKey)
                && !symbol.FamilyName.Contains(AnchorFamilyNameKey)
                && !symbol.FamilyName.Contains(CapFamilyNameKey);
        }

        public bool IsFamilyNotExist(out string message)
        {
            if (Symbols.Count == 0)
            {
                message = $"There is no curb family in the project with a name that includes {FamilyNameKey}.";
                return true;
            }

            message = "";
            return false;
        }

        public bool IsNoPlacedInstancesOnView(out string message)
        {
            if (InstancesOnView.Count == 0)
            {
                message = $"There are no instances of the curb family in the current view.";
                return true;
            }

            message = "";
            return false;
        }

        public List<FamilyInstance> SelectInstances()
        {
            var output = new List<FamilyInstance>();

            try
            {
                var instancesIds = RevitManager.UIDocument.Selection.PickObjects(
                    ObjectType.Element, new FramesSelectionFilter(InstancesOnView), "Select the curbs to tag.");
                output = instancesIds.Select(r => RevitManager.Document.GetElement(r) as FamilyInstance).ToList();
            }
            catch { }

            return output;
        }

        private List<FamilySymbol> GetFamilySymbols()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsElementType()
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .Cast<FamilySymbol>()
                .Where(s => IsFrameInstance(s))
                .ToList();
        }

        private List<FamilyInstance> GetInstancesOnView(View view)
        {
            return new FilteredElementCollector(RevitManager.Document, view.Id)
                .WhereElementIsNotElementType()
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .Cast<FamilyInstance>()
                .Where(i => IsFrameInstance(i.Symbol))
                .ToList();
        }
    }
}
