﻿using System;
using System.Linq;
using System.Threading.Tasks;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;

public class AdminCategoryDeleteVm : AdminCategoryBaseVm
{
    public AdminCategoryDeleteVm(LibraryCategoryDto selectedCategory)
        : base(selectedCategory)
    {
    }

    protected override bool CanApply()
    {
        return !SelectedCategory.LibraryItems.Any();
    }

    protected override async Task ApplyAsync()
    {
        try
        {
            var response = await ApiService.Instance.DeleteCategoryAsync(SelectedCategory.Id);
            HandleResponse(response, result => CloseDialog(result));
        }
        catch (Exception ex)
        {
            Error = $"Error: {ex.Message}";
            LogHelper.Error($"An error occurred: {ex.Message}");
        }
    }
}