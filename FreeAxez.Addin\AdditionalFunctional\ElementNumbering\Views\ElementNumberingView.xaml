﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.ElementNumbering.Views.ElementNumberingView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        SizeToContent="WidthAndHeight"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterScreen"
        Title="Numbering">
    <StackPanel Margin="10">
        
        <GroupBox Header="Numbering Direction">
            <StackPanel Margin="5">
                <RadioButton x:Name="horizontalDirection" Content="Horizontal" IsChecked="True" GroupName="direction"/>
                <RadioButton x:Name="verticalDirection" Margin="0,5,0,0" Content="Vertical" GroupName="direction"/>
            </StackPanel>
        </GroupBox>

        <Label Content="Select Parameter" />
        <ComboBox Name="parametersList" Height="25" VerticalAlignment="Top" />

        <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
            <Label Content="Prefix" Width="90"/>
            <TextBox x:Name="prefix" VerticalContentAlignment="Center" Width="150"/>
        </StackPanel>
        
        <StackPanel Margin="0,10,0,0" Orientation="Horizontal">
            <Label Content="Start Number" Width="90"/>
            <TextBox x:Name="startNumber" Width="150" VerticalContentAlignment="Center" TextChanged="startNumber_TextChanged"/>
        </StackPanel>

        <Button Margin="0,20,0,0" Content="Select Elements" Height="25" Click="Button_Click"/>        
    </StackPanel>
</Window>
