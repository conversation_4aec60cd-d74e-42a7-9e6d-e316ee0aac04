﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FreeAxez.Core.GeometryModel;

namespace FreeAxez.Core.Utilities
{
    public static class GeometryUtilities
    {
        public static bool IsPointInsidePolygon(Polygon polygon, PointModel point)
        {
            // Using Cast.PrepareArea can be expensive but SliceHorizontal is convenient for scanning.

            var polygonLines = new List<CastLineData>();
            for (int i = 1; i < polygon.Points.Length; ++i)
                polygonLines.Add(new CastLineData{ Line = new LineModel(polygon.Points[i - 1], polygon.Points[i])});
            polygonLines.Add(new CastLineData { Line = new LineModel(polygon.Points.Last(), polygon.Points.First()) });

            var area = Cast.PrepareArea(new CastDataToPrepare {Lines = polygonLines}, 1000); // Cell size is not necessary for small polygons. Put all in one cell.

            var slice = Cast.SliceHorizontal(point.Y, area);

            return slice.Slices.Any(s => MathUtilities.ApproximatelyOrLess(s.Point1.X, point.X) && MathUtilities.ApproximatelyOrLess(point.X, s.Point2.X));
        }

#region Rectangle
        public static bool AreRectanglesIntersected(Rectangle r1, Rectangle r2)
        {
            return
                MathUtilities.ApproximatelyOrLess(r1.Left, r2.Right)
                && MathUtilities.ApproximatelyOrGreater(r1.Right, r2.Left)
                && MathUtilities.ApproximatelyOrLess(r1.Bottom, r2.Top)
                && MathUtilities.ApproximatelyOrGreater(r1.Top, r2.Bottom);
        }

        public static Rectangle GetRectanglesIntersection(Rectangle r1, Rectangle r2)
        {
            var left = Math.Max(r1.Left, r2.Left);
            var bottom = Math.Max(r1.Bottom, r2.Bottom);
            var right = Math.Min(r1.Right, r2.Right);
            var top = Math.Min(r1.Top, r2.Top);

            return new Rectangle(left, bottom, right - left, top - bottom);
        }

        public static bool IsPointInsideRectangle(Rectangle r, PointModel p)
        {
            return
                MathUtilities.ApproximatelyOrGreater(p.X, r.Left)
                && MathUtilities.ApproximatelyOrLess(p.X, r.Right)
                && MathUtilities.ApproximatelyOrGreater(p.Y, r.Bottom)
                && MathUtilities.ApproximatelyOrLess(p.Y, r.Top);
        }

        public static LineModel[] ConvertRectangleToLines(Rectangle r)
        {
            return new[]
            {
                new LineModel(new PointModel(r.Left, r.Bottom), new PointModel(r.Right, r.Bottom)),
                new LineModel(new PointModel(r.Right, r.Bottom), new PointModel(r.Right, r.Top)),
                new LineModel(new PointModel(r.Right, r.Top), new PointModel(r.Left, r.Top)),
                new LineModel(new PointModel(r.Left, r.Top), new PointModel(r.Left, r.Bottom))
            };
        }
        #endregion
        
        #region Lines
        public static PointModel CalculateGeometricCenter(List<LineModel> itemLines)
        {
            return new PointModel(
                (itemLines.Max(l => Math.Max(l.StartPoint.X, l.EndPoint.X)) + itemLines.Min(l => Math.Min(l.StartPoint.X, l.EndPoint.X))) * 0.5,
                (itemLines.Max(l => Math.Max(l.StartPoint.Y, l.EndPoint.Y)) + itemLines.Min(l => Math.Min(l.StartPoint.Y, l.EndPoint.Y))) * 0.5);
        }

        public static Rectangle GetLineBoundingBox(ILineModel line)
        {
            var minX = Math.Min(line.StartPointGetter.X, line.EndPointGetter.X);
            var maxX = Math.Max(line.StartPointGetter.X, line.EndPointGetter.X);
            var minY = Math.Min(line.StartPointGetter.Y, line.EndPointGetter.Y);
            var maxY = Math.Max(line.StartPointGetter.Y, line.EndPointGetter.Y);

            return new Rectangle(minX, minY, maxX - minX, maxY - minY);
        }

        public static bool AreLinesIntersected(ILineModel l1, ILineModel l2)
        {
            return AreLinesIntersected(l1.StartPointGetter, l1.EndPointGetter, l2.StartPointGetter, l2.EndPointGetter);
        }

        public static bool AreLinesIntersected(PointModel p1, PointModel p2, PointModel p3, PointModel p4)
        {
            var d = (p1.X - p2.X) * (p4.Y - p3.Y) - (p1.Y - p2.Y) * (p4.X - p3.X);
            var da = (p1.X - p3.X) * (p4.Y - p3.Y) - (p1.Y - p3.Y) * (p4.X - p3.X);
            var db = (p1.X - p2.X) * (p1.Y - p3.Y) - (p1.Y - p2.Y) * (p1.X - p3.X);

            var ta = da / d;
            var tb = db / d;
            return MathUtilities.ApproximatelyOrGreater(ta, 0)
                   && MathUtilities.ApproximatelyOrLess(ta, 1)
                   && MathUtilities.ApproximatelyOrGreater(tb, 0)
                   && MathUtilities.ApproximatelyOrLess(tb, 1);
        }
#endregion

#region Points

        public static bool EqualTo(this PointModel first, PointModel second, uint precision = MathUtilities.DefaultPrecision)
        {
            return MathUtilities.Approximately(first.X, second.X, precision) && MathUtilities.Approximately(first.Y, second.Y, precision);
        }

#endregion
    }
}
