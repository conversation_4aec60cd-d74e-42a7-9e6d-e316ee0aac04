﻿using EllipticCurve.Utils;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Abstractions;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Constants;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Contracts.Requests;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.DataTransferObjects;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager
{
    public class TaskManagerHttpClientService : TaskManagerHttpClientBase
    {
        public async override Task<RowDto> GetRecentRowDtoAsync(long sheetId, CancellationToken cancellationToken)
        {
            RowDto? recentRowDto = null!;
            using var httpClient = new HttpClient();
            httpClient.BaseAddress = HttpBaseAddress;
            var httpResponseMessage = await httpClient.GetAsync(
                @$"/{HttpClientConstants.Rows.GetRecentRow}/{sheetId}", cancellationToken);

            if (httpResponseMessage.IsSuccessStatusCode)
            {
                string json = await httpResponseMessage.Content.ReadAsStringAsync();

                if (!string.IsNullOrEmpty(json))
                {
                    recentRowDto = JsonConvert.DeserializeObject<RowDto>(json);

                    if (recentRowDto is null)
                        throw new NullReferenceException("Recent row is not found.");
                }
            }
            else await base.ThrowProblemDetailsExceptionAsync(httpResponseMessage);

            return recentRowDto;
        }

        public async override Task<HttpResponseMessage> SetCompleteStatusAsync(long sheetId,
                                                                               long recentRowDtoId,
                                                                               IEnumerable<string>? filePaths,
                                                                               string? commentText,
                                                                               CancellationToken cancellationToken)
        {
            using (HttpClient httpClient = new HttpClient())
            {
                httpClient.BaseAddress = HttpBaseAddress;

                using (var formData = new MultipartFormDataContent())
                {
                    if (filePaths is not null && filePaths.Any())
                    {
                        foreach (var filePath in filePaths)
                        {
                            var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                            formData.Add(new StreamContent(fileStream),
                                         HttpClientConstants.FormArguments.FormFiles,
                                         Path.GetFileName(filePath));
                        }
                    }

                    string? validCommentText = string.IsNullOrEmpty(commentText) ? string.Empty : commentText;
                    formData.Add(new StringContent(validCommentText), HttpClientConstants.FormArguments.CommentText);

                    string requestUri = @$"/{HttpClientConstants.Rows.SetCompleteStatus}/{sheetId}/{recentRowDtoId}";

                    HttpResponseMessage httpResponseMessage = await httpClient.PostAsync(requestUri, formData, cancellationToken);

                    if (!httpResponseMessage.IsSuccessStatusCode)
                        await base.ThrowProblemDetailsExceptionAsync(httpResponseMessage);

                    return httpResponseMessage;
                }
            }
        }

        public async override Task<HttpResponseMessage> SendUpdateRequestEmail(long sheetId,
                                                                               UpdateEmailRequest emailRequest,
                                                                               CancellationToken cancellationToken)
        {
            using (HttpClient httpClient = new HttpClient())
            {
                httpClient.BaseAddress = HttpBaseAddress;

                string jsonPayload = JsonConvert.SerializeObject(emailRequest);
                var content = new StringContent(jsonPayload, Encoding.UTF8, HttpClientConstants.HttpMediaType);

                string requestUri = @$"/{HttpClientConstants.Rows.SendUpdateRequestEmail}/{sheetId}";

                HttpResponseMessage httpResponseMessage = await httpClient.PostAsync(requestUri, content, cancellationToken);

                if (!httpResponseMessage.IsSuccessStatusCode)
                    await base.ThrowProblemDetailsExceptionAsync(httpResponseMessage);

                return httpResponseMessage;
            }
        }

        public async override Task<IEnumerable<long>> GetSubRowDtoIdsFromParent(long sheetId,
                                                                                long rowId,
                                                                                CancellationToken cancellationToken)
        {
            using (HttpClient httpClient = new HttpClient())
            {
                httpClient.BaseAddress = HttpBaseAddress;

                string requestUri = @$"/{HttpClientConstants.Rows.GetSubRowDtoIdsFromParent}/{sheetId}/{rowId}";

                HttpResponseMessage httpResponseMessage = await httpClient.GetAsync(requestUri, cancellationToken);

                if (!httpResponseMessage.IsSuccessStatusCode)
                    await base.ThrowProblemDetailsExceptionAsync(httpResponseMessage);

                string json = await httpResponseMessage.Content.ReadAsStringAsync();

                IEnumerable<long>? subRowDtoIds = JsonConvert.DeserializeObject<IEnumerable<long>>(json);

                if (subRowDtoIds is null || !subRowDtoIds.Any())
                    throw new NullReferenceException("Parent task doesn't have nested tasks.");

                return subRowDtoIds;
            }
        }

        public async override Task<ScopeInstruction?> GetScopeInstructionByNumber(double scopeNumber, CancellationToken cancellationToken)
        {
            using (var httpClient = new HttpClient())
            {
                httpClient.BaseAddress = HttpBaseAddress;

                string requestUri = HttpClientConstants.Scopes.GetByScopeNumber(scopeNumber);

                HttpResponseMessage httpResponseMessage = await httpClient.GetAsync(requestUri, cancellationToken);

                if (!httpResponseMessage.IsSuccessStatusCode && httpResponseMessage.StatusCode != System.Net.HttpStatusCode.NotFound)
                {
                    await base.ThrowProblemDetailsExceptionAsync(httpResponseMessage);
                }

                if (!httpResponseMessage.IsSuccessStatusCode && httpResponseMessage.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return null;
                }

                string json = await httpResponseMessage.Content.ReadAsStringAsync();

                ScopeInstruction scopeInstruction = JsonConvert.DeserializeObject<ScopeInstruction>(json);

                if (scopeInstruction is null)
                {
                    throw new NullReferenceException($"Can't find scope instruction: {scopeNumber}");
                }

                return scopeInstruction;
            }
        }

        public async override Task<ScopeInstruction?> GetScopeInstructionByName(string scopeName, CancellationToken cancellationToken)
        {
            using (var httpClient = new HttpClient())
            {
                httpClient.BaseAddress = HttpBaseAddress;

                string requestUri = HttpClientConstants.Scopes.GetByScopeName(scopeName);

                HttpResponseMessage httpResponseMessage = await httpClient.GetAsync(requestUri, cancellationToken);

                if (!httpResponseMessage.IsSuccessStatusCode && httpResponseMessage.StatusCode != System.Net.HttpStatusCode.NotFound)
                {
                    await base.ThrowProblemDetailsExceptionAsync(httpResponseMessage);
                }

                if (!httpResponseMessage.IsSuccessStatusCode && httpResponseMessage.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return null;
                }

                string json = await httpResponseMessage.Content.ReadAsStringAsync();

                ScopeInstruction scopeInstruction = JsonConvert.DeserializeObject<ScopeInstruction>(json);

                if (scopeInstruction is null)
                {
                    throw new NullReferenceException($"Can't find scope instruction: {scopeName}");
                }

                return scopeInstruction;
            }
        }
    }
}
