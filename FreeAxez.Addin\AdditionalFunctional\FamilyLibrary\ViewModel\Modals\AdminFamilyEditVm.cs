﻿using System.IO;
using System.Windows.Input;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using Microsoft.Win32;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;

public class AdminFamilyEditVm : AdminFamilyBaseVm
{
    private LibraryItemDetailsExist _currentFamilyInfo = new();
    private LibraryItemDetailsExistVm _currentFamilyInfoVm;
    private bool _leaveImageFromCurrentVersion;
    private readonly LibraryItemDto _selectedFamily;

    public AdminFamilyEditVm(LibraryItemDto selectedFamily)
    {
        _selectedFamily = selectedFamily;
        _currentFamilyInfoVm = new LibraryItemDetailsExistVm(selectedFamily, this);

        // Subscribe to changes in the current family info to update CanApply
        _currentFamilyInfoVm.PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(LibraryItemDetailsExistVm.HasChanges))
            {
                OnPropertyChanged(nameof(CanApply));
            }
        };

        _currentFamilyInfo.DataContext = _currentFamilyInfoVm;
        ChooseFileCommand = new RelayCommand(ExecuteChooseFile);
        ApplyCommand = new RelayCommand(ExecuteApply, CanExecuteApply);
    }

    public ICommand ChooseFileCommand { get; private set; }
    public ICommand ApplyCommand { get; private set; }

    public override bool CanApply
    {
        get
        {
            if (UploadFileInfo.Any())
            {
                return UploadFileInfo.All(f => IsValidFamily(f)) && !IsUploading;
            }

            return CurrentFamilyInfoVm?.HasChanges == true && !IsUploading;
        }
    }

    public LibraryItemDetailsExist CurrentFamilyInfo
    {
        get => _currentFamilyInfo;
        set
        {
            _currentFamilyInfo = value;
            OnPropertyChanged();
        }
    }

    public bool LeaveImageFromCurrentVersion
    {
        get => _leaveImageFromCurrentVersion;
        set
        {
            if (_leaveImageFromCurrentVersion != value)
            {
                _leaveImageFromCurrentVersion = value;
                OnPropertyChanged();
            }
        }
    }

    public LibraryItemDetailsExistVm CurrentFamilyInfoVm
    {
        get => _currentFamilyInfoVm;
        set
        {
            _currentFamilyInfoVm = value;
            OnPropertyChanged();
        }
    }

    protected override void ExecuteChooseFile(object parameter)
    {
        var openFileDialog = new OpenFileDialog
        {
            Multiselect = false,
            Filter = "Revit Families (*.rfa)|*.rfa"
        };

        if (openFileDialog.ShowDialog() != true)
            return;

        var tempFolderPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());

        try
        {
            Directory.CreateDirectory(tempFolderPath);

            var file = openFileDialog.FileName;
            var fileName = Path.GetFileName(file);
            if (FamiliesToUpload.Any(f => f.Name == fileName))
                return;

            var tempFilePath = Path.Combine(tempFolderPath, fileName);
            try
            {
                File.Copy(file, tempFilePath);
            }
            catch (IOException ex)
            {
                FileErrors.Add((fileName, $"Cannot access file: {ex.Message}"));
                ShowIncompatibleFilesMessage();
                return;
            }

            var (success, errorMessage) = ProcessSingleFile(tempFilePath, fileName);
            if (!success)
            {
                FileErrors.Add((fileName, errorMessage));
                ShowIncompatibleFilesMessage();
            }
        }
        finally
        {
            if (!string.IsNullOrEmpty(tempFolderPath) && Directory.Exists(tempFolderPath))
            {
                try
                {
                    Directory.Delete(tempFolderPath, true);
                }
                catch (Exception ex)
                {
                    LogHelper.Warning($"Failed to delete temp folder {tempFolderPath}: {ex.Message}");
                }
            }
        }
    }

    private (bool Success, string ErrorMessage) ProcessSingleFile(string tempFilePath, string fileName)
    {
        Document familyDoc = null;
        byte[] revitFileBytes = null;

        try
        {
            // Read file bytes before opening in Revit to avoid file lock
            revitFileBytes = File.ReadAllBytes(tempFilePath);

            // Open Revit document
            var path = ModelPathUtils.ConvertUserVisiblePathToModelPath(tempFilePath);
            familyDoc = RevitManager.Application.OpenDocumentFile(path, new OpenOptions());

            if (!familyDoc.IsFamilyDocument)
                return (false, "Not a valid Revit family document");

            var familyManager = familyDoc.FamilyManager;
            var version = GetParameterValueOrDefault(familyManager, "Version");
            var productName = GetParameterValueOrDefault(familyManager, "Product Name");
            var manufacturer = GetParameterValueOrDefault(familyManager, "Manufacturer");
            var description = GetParameterValueOrDefault(familyManager, "Description");

            // Close document before generating thumbnail
            familyDoc.Close(false);
            familyDoc = null;

            var familyPreview = GetThumbnail(tempFilePath);
            var fileInfo = new FileInfo(tempFilePath);

            var libraryItemDto = new LibraryItemDto
            {
                Name = fileName,
                Version = version,
                ProductName = productName,
                Description = description,
                OriginalItemId = _selectedFamily.OriginalItemId,
                CategoryId = _selectedFamily.CategoryId,
                RevitVersion = RevitManager.RevitVersion,
                UpdatedBy = Properties.Settings.Default.UserEmail
            };

            var fileDetail = new LibraryItemDetailsNew
            {
                DataContext = new LibraryItemDetailsNewVm(libraryItemDto, this)
                {
                    FileSize = $"{fileInfo.Length / 1.049e+6:0.0} MB",
                    UploadProgress = 100,
                    FamilyPreview = familyPreview,
                    Manufacturer = manufacturer,
                    SelectedCategoryId = _selectedFamily.CategoryId,
                    RevitFileBytes = revitFileBytes
                }
            };

            if (_selectedFamily.Name != libraryItemDto.Name)
                Warnings.Add(new MessageWarning { Message = "Family file names do not match." });

            if (_selectedFamily.RevitVersion != libraryItemDto.RevitVersion)
                Warnings.Add(new MessageWarning
                {
                    Message = "You are trying to load a family with a Revit version other than the current version in the library."
                });

            if (!FamilyPropertiesComparer.IsManufacturerFreeAxez(manufacturer))
                Errors.Add(new MessageError { Message = "Family must be manufactured by 'FreeAxez'." });

            if (!FamilyPropertiesComparer.IsVersionGreater(_selectedFamily.Version, libraryItemDto.Version))
                Errors.Add(new MessageError { Message = "The version of the uploaded file must be higher than the current version." });

            UploadFileInfo.Add(fileDetail);
            FamiliesToUpload.Add(libraryItemDto);
            return (true, null);
        }
        catch (Autodesk.Revit.Exceptions.FileAccessException ex)
        {
            LogHelper.Error($"File access error for {fileName}: {ex.Message}");
            return (false, "File created in a later version of Revit or is corrupted");
        }
        catch (IOException ex)
        {
            LogHelper.Error($"IO error for {fileName}: {ex.Message}");
            return (false, $"Cannot access file: {ex.Message}");
        }
        catch (Exception ex)
        {
            LogHelper.Error($"Error processing file {fileName}: {ex.Message}");
            return (false, $"Processing error: {ex.Message}");
        }
        finally
        {
            familyDoc?.Close(false); // Ensure document is closed
        }
    }

    private bool CanExecuteApply(object parameter)
    {
        if (UploadFileInfo.Any())
        {
            return UploadFileInfo.All(f => IsValidFamily(f));
        }

        return CurrentFamilyInfoVm.HasChanges;
    }

    private bool IsValidFamily(LibraryItemDetailsNew fileDetail)
    {
        var vm = fileDetail.DataContext as LibraryItemDetailsNewVm;
        if (vm == null ||
            vm.ProductName == "Unknown" ||
            vm.Version == "Unknown" ||
            vm.Manufacturer == "Unknown" ||
            !FamilyPropertiesComparer.IsManufacturerFreeAxez(vm.Manufacturer) ||
            vm.SelectedCategoryId == Guid.Empty ||
            string.IsNullOrEmpty(vm.ChangesDescription))
        {
            return false;
        }

        return FamilyPropertiesComparer.IsVersionGreater(_selectedFamily.Version, vm.LibraryItem.Version);
    }

    protected override async void ExecuteApply(object parameter)
    {
        IsUploading = true;
        if (UploadFileInfo.Any())
            foreach (var fileDetail in UploadFileInfo)
            {
                var vm = fileDetail.DataContext as LibraryItemDetailsNewVm;
                if (vm != null)
                    try
                    {
                        vm.LibraryItem.ChangesDescription = vm.ChangesDescription;

                        var (familyFilePath, familyFileError) = await ApiService.Instance.UploadFamilyFile(vm);
                        if (familyFileError != null)
                        {
                            LoadingErrors.Add(familyFileError);
                            continue;
                        }

                        vm.LibraryItem.FamilyFilePath = familyFilePath;

                        if (!LeaveImageFromCurrentVersion)
                        {
                            var (familyImagePath, familyImageError) =
                                await ApiService.Instance.UploadFamilyImage(vm.FamilyPreview);
                            if (familyImageError != null)
                            {
                                LoadingErrors.Add(familyImageError);
                                continue;
                            }

                            vm.LibraryItem.FamilyImagePath = familyImagePath;
                        }
                        else
                        {
                            vm.LibraryItem.FamilyImagePath = _selectedFamily.FamilyImagePath;
                        }

                        var response = await ApiService.Instance.AddFamilyNewVersionAsync(vm.LibraryItem);
                        if (!response.IsSuccessStatusCode)
                        {
                            var error = await response.Content.ReadAsStringAsync();
                            LoadingErrors.Add(error);
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = $"Error: {ex.Message}";
                        LoadingErrors.Add(error);
                        LogHelper.Error($"An error occurred: {ex.Message}");
                    }
            }
        else
            try
            {
                if (CurrentFamilyInfoVm.FamilyPreviewChanged)
                {
                    var (familyImagePath, familyImageError) =
                        await ApiService.Instance.UploadFamilyImage(CurrentFamilyInfoVm.FamilyPreview);
                    if (familyImageError != null)
                        LoadingErrors.Add(familyImageError);
                    else
                        CurrentFamilyInfoVm.LibraryItem.FamilyImagePath = familyImagePath;
                }

                var updatedLibraryItem = CurrentFamilyInfoVm.LibraryItem;
                updatedLibraryItem.UpdatedBy = Properties.Settings.Default.UserEmail;
                var response = await ApiService.Instance.UpdateFamilyAsync(updatedLibraryItem);
                if (!response.IsSuccessStatusCode)
                {
                    var error = await response.Content.ReadAsStringAsync();
                    LoadingErrors.Add(error);
                }
            }
            catch (Exception ex)
            {
                var error = $"Error: {ex.Message}";
                LoadingErrors.Add(error);
                LogHelper.Error($"An error occurred: {ex.Message}");
            }

        IsUploading = false;

        if (LoadingErrors.Any())
            ShowLoadingErrorsMessage();
        else
            ShowSuccessMessage();
        CloseModal(true);
    }

    protected override void ShowSuccessMessage()
    {
        FamilyLibraryCore.ShowMessage("Success", "Family successfully updated", MessageType.Success);
    }
}