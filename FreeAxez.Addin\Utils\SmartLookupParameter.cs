﻿using Autodesk.Revit.DB;
using System.Collections.Generic;

namespace FreeAxez.Addin.Utils
{
    /// <summary>
    /// Stores found definitions for parameter searches in other elements.
    /// </summary>
    public class SmartLookupParameter
    {
        private Dictionary<string, List<Definition>> _definitionByParameterName = new Dictionary<string, List<Definition>>();

        public Parameter LookupParameter(Element element, string parameterName)
        {
            Parameter parameter = null;

            if (_definitionByParameterName.ContainsKey(parameterName))
            {
                // We are looking for already found definitions
                foreach (var definition in _definitionByParameterName[parameterName])
                {
                    parameter = element.get_Parameter(definition);
                    if (parameter != null)
                    {
                        return parameter;
                    }
                }

                // Neither definition fits
                parameter = element.LookupParameter(parameterName);
                if (parameter != null)
                {
                    _definitionByParameterName[parameterName].Add(parameter.Definition);
                    return parameter;
                }
            }
            else
            {
                // No definition has been found yet
                parameter = element.LookupParameter(parameterName);
                if (parameter != null) 
                {
                    _definitionByParameterName.Add(parameterName, new List<Definition>() { parameter.Definition });
                    return parameter;
                }
            }

            // Return null
            return parameter;
        }
    }
}
