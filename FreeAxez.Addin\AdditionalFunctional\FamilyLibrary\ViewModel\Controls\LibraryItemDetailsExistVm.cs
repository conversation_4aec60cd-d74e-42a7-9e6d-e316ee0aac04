﻿using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;
using FreeAxez.Addin.Infrastructure;
using Microsoft.Win32;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;

public class LibraryItemDetailsExistVm : BaseViewModel
{
    private readonly Guid _originalCategoryId;
    private readonly string _originalVersionNotes;
    private readonly AdminFamilyBaseVm _parentVm;
    private BitmapSource _familyPreview;
    private bool _familyPreviewChanged;
    private bool _hasChanges;
    private LibraryItemDto _libraryItem;
    private Guid _selectedCategoryId;

    public LibraryItemDetailsExistVm()
    {

    }

    public LibraryItemDetailsExistVm(LibraryItemDto libraryItem, AdminFamilyBaseVm parentVm)
    {
        _libraryItem = libraryItem;
        _originalVersionNotes = libraryItem.ChangesDescription;
        _parentVm = parentVm;
        _parentVm.CategoriesLoaded += UpdateCategories;
        _selectedCategoryId = _libraryItem.CategoryId;
        _originalCategoryId = _libraryItem.CategoryId;
        LoadImage(libraryItem.FamilyImagePath);

        ChooseImageCommand = new RelayCommand(param => ChooseImage());
        RejectFileCommand = new RelayCommand(param => RejectFile());
    }

    public ICommand ChooseImageCommand { get; private set; }
    public ICommand RejectFileCommand { get; private set; }
    public ObservableCollection<LibraryCategoryDto> Categories => _parentVm.Categories;

    public bool HasChanges
    {
        get => _hasChanges;
        private set
        {
            if (_hasChanges != value)
            {
                _hasChanges = value;
                OnPropertyChanged();
                CommandManager.InvalidateRequerySuggested();
            }
        }
    }

    public bool FamilyPreviewChanged
    {
        get => _familyPreviewChanged;
        set
        {
            if (_familyPreviewChanged != value)
            {
                _familyPreviewChanged = value;
                OnPropertyChanged();
                UpdateHasChanges();
            }
        }
    }

    public Guid SelectedCategoryId
    {
        get => _selectedCategoryId;
        set
        {
            _selectedCategoryId = value;
            OnPropertyChanged();
            _libraryItem.CategoryId = value;
            UpdateHasChanges();
            CommandManager.InvalidateRequerySuggested();
        }
    }

    public string FileName
    {
        get => _libraryItem.Name;
        set
        {
            _libraryItem.Name = value;
            OnPropertyChanged();
        }
    }

    public string Version
    {
        get => _libraryItem.Version;
        set
        {
            _libraryItem.Version = value;
            OnPropertyChanged();
            CommandManager.InvalidateRequerySuggested();
        }
    }

    public string VersionNotes
    {
        get => _libraryItem.ChangesDescription;
        set
        {
            _libraryItem.ChangesDescription = value;
            OnPropertyChanged();
            UpdateHasChanges();
            CommandManager.InvalidateRequerySuggested();
        }
    }

    public string ProductName
    {
        get => _libraryItem.ProductName;
        set
        {
            _libraryItem.ProductName = value;
            OnPropertyChanged();
            CommandManager.InvalidateRequerySuggested();
        }
    }

    public string RevitVersion
    {
        get => _libraryItem.RevitVersion;
        set
        {
            _libraryItem.RevitVersion = value;
            OnPropertyChanged();
        }
    }


    public string Description
    {
        get => _libraryItem.Description;
        set
        {
            _libraryItem.Description = value;
            OnPropertyChanged();
            CommandManager.InvalidateRequerySuggested();
        }
    }

    public string CreatedBy
    {
        get => _libraryItem.CreatedBy;
        set
        {
            _libraryItem.CreatedBy = value;
            OnPropertyChanged();
        }
    }

    public DateTime DateCreated
    {
        get => _libraryItem.DateCreated;
        set
        {
            _libraryItem.DateCreated = value;
            OnPropertyChanged();
        }
    }

    public DateTime LastDateUpdated
    {
        get => _libraryItem.LastDateUpdated;
        set
        {
            _libraryItem.LastDateUpdated = value;
            OnPropertyChanged(nameof(DateCreated));
        }
    }

    public BitmapSource FamilyPreview
    {
        get => _familyPreview;
        set
        {
            if (_familyPreview != value)
            {
                _familyPreview = value;
                OnPropertyChanged();
            }
        }
    }

    public LibraryItemDto LibraryItem
    {
        get => _libraryItem;
        set
        {
            _libraryItem = value;
            OnPropertyChanged();
        }
    }

    private void UpdateCategories()
    {
        OnPropertyChanged(nameof(Categories));
    }

    private void UpdateHasChanges()
    {
        HasChanges = _selectedCategoryId != _originalCategoryId || _familyPreviewChanged || _libraryItem.ChangesDescription != _originalVersionNotes; 
    }

    public void RejectFile()
    {
        if (_parentVm.RejectFileCommand.CanExecute(this)) _parentVm.RejectFileCommand.Execute(this);
    }

    private void ChooseImage()
    {
        var openFileDialog = new OpenFileDialog
        {
            Filter = "Image files (*.png;*.jpeg;*.jpg)|*.png;*.jpeg;*.jpg",
            Title = "Select an Image"
        };

        if (openFileDialog.ShowDialog() == true)
        {
            var imageUri = new Uri(openFileDialog.FileName, UriKind.Absolute);
            var image = new BitmapImage(imageUri);

            FamilyPreview = image;
            FamilyPreviewChanged = true;
            UpdateHasChanges();
        }
    }

    private async void LoadImage(string imageUrl)
    {
        _familyPreview = await ApiService.Instance.LoadImageFromUrlAsync(imageUrl);
        OnPropertyChanged(nameof(FamilyPreview));
    }
}