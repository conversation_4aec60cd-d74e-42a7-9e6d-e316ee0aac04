﻿using Autodesk.Revit.DB;
using System.Collections.Generic;

namespace FreeAxez.Addin.AdditionalFunctional.CreateTapOff.Utils
{
    public static class TapOffParameterSetter
    {
        private static List<string> _parameterNames = new List<string>() 
        {
            "Track Assignment",
            "FB Type"
        };

        public static void SetParameters(FamilyInstance tapOff, FamilyInstance floorBox)
        {
            foreach (string parameterName in _parameterNames)
            {
                tapOff.LookupParameter(parameterName)?
                    .Set(floorBox.LookupParameter(parameterName)?.AsString());
            }
        }
    }
}
