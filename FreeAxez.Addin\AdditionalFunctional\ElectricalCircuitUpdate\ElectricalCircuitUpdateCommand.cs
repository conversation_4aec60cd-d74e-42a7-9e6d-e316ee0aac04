﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Enums;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuitUpdate
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    internal class ElectricalCircuitUpdateCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var selectLevelResult = SelectLevelWindow.ShowDialog("Electrical Circuit Update", out List<Level> selectedLevels);
            if (selectLevelResult != true)
            {
                return Result.Cancelled;
            }
            else if (selectedLevels.Count() == 0)
            {
                MessageWindow.ShowDialog("No level has been selected.", MessageType.Notify);
                return Result.Cancelled;
            }

            var levelHelper = new LevelHelper(selectedLevels);
            var circuitCollector = new CircuitCollector(levelHelper);
            var circuits = circuitCollector.Collect();
            if (circuits.Count == 0)
            {
                MessageWindow.ShowDialog("No electrical elements were found in the model.", MessageType.Notify);
                return Result.Cancelled;
            }

            // Process attached tracks to update level parameters
            var fakeTrackCircuits = CircuitCollector.CollectFakeCircuitsForUnusedTracks(circuits, levelHelper);

            var validGeometryCircuits = circuits
                .Where(c => c.CircuitElectricalStatus == CircuitElectricalStatus.Valid)
                .ToList();

            using (var t = new Transaction(RevitManager.Document, "Update Circuit Parameters"))
            {
                t.Start();

                // The motivation for creating a separate class that sets all parameters for all electrical circuits
                // is that the floor box refers to many circuits at the same time,
                // and the parameters must be set from all circuits in which this box is involved.
                var parameterSetter = new ParameterSetter(validGeometryCircuits);
                var attachedTracksParameterSetter = new ParameterSetter(fakeTrackCircuits);

                parameterSetter.ClearParameters();
                attachedTracksParameterSetter.ClearParameters();

                RevitManager.Document.Regenerate();

                parameterSetter.SetParameters();
                attachedTracksParameterSetter.SetParameters();

                t.Commit();
            }

            MessageWindow.ShowDialog($"Parameters updated for {validGeometryCircuits.Count} electrical systems.", MessageType.Success);

            return Result.Succeeded;
        }
    }
}
