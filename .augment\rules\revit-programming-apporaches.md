---
type: "always_apply"
---

### Architecture Revit Solutions
- **IExternalCommand**: Each IExternalCommand command should have its own README.md file that describes the purpose and logic of the command. This file should always match the code and be updated when the functionality of the command changes;
- Business logic should be separated from Revit interaction logic;
- For the window style for new commands, use <Window.Style><Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" /></Window.Style>;
- For communication with the user always use FreeAxez.Addin.Infrastructure.UI.Services.MessageWindow. But Error type is only for bugs. In case of validation errors etc. use Notify;
- If there is a README.md for a command that inherits BaseExternalCommand, always update it if changes affect its content;
- Preferably use the static class FreeAxez.Addin.Infrastructure.RevitManager to access the document and other properties where necessary to avoid passing arguments throughout the code;