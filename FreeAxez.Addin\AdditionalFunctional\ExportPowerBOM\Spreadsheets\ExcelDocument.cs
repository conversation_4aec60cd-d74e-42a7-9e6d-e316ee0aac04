﻿using NPOI.XSSF.UserModel;
using System.Collections.Generic;
using System.IO;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBOM.Spreadsheets
{
    internal class ExcelDocument
    {
        private string _filePath;
        private XSSFWorkbook _workbook;


        public ExcelDocument(string filePath)
        {
            using (FileStream file = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                _workbook = new XSSFWorkbook(file);
            }
            _filePath = filePath;
        }


        public List<string> GetSheetNames()
        {
            var sheetNames = new List<string>();
            for (int i = 0; i < _workbook.NumberOfSheets; i++)
            {
                sheetNames.Add(_workbook.GetSheetName(i));
            }
            return sheetNames;
        }

        public void CopySheet(string sourceSheetName, string newSheetName)
        {
            var sheet = _workbook.GetSheet(sourceSheetName);
            sheet.CopyTo(_workbook, newSheetName, true, true);
        }

        public ExcelSheet GetSheet(string sheetName)
        {
            var sheet = _workbook.GetSheet(sheetName);
            return new ExcelSheet(sheet);
        }

        public void ActivateSheet(string sheetName)
        {
            _workbook.SetSelectedTab(_workbook.GetSheetIndex(sheetName));
            _workbook.SetActiveSheet(_workbook.GetSheetIndex(sheetName));
        }

        public void Save()
        {
            using (MemoryStream ms = new MemoryStream())
            {
                _workbook.Write(ms);
                using (FileStream fs = new FileStream(_filePath, FileMode.Create, FileAccess.Write))
                {
                    ms.WriteTo(fs);
                }
            }
        }
    }
}
