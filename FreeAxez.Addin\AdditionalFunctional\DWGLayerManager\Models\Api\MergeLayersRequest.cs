namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Api;

public class MergeLayersRequest
{
    public List<LayerMapping> Mappings { get; set; } = new();
    public List<FreeAxezLayerDto> FreeAxezLayers { get; set; } = new();
    public List<LinetypeDto> Linetypes { get; set; } = new();
    public bool DryRun { get; set; } = false;
}

public class LayerMapping
{
    public string SourceLayer { get; set; }
    public string TargetLayer { get; set; }
}

public class FreeAxezLayerDto
{
    public string Name { get; set; }
    public string Color { get; set; }
    public string Linetype { get; set; }
    public double Lineweight { get; set; }
    public int Transparency { get; set; }
}