﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagAll
{
    public enum TagLocation
    {
        Up,
        Down,
        Left,
        Right
    }
    [Transaction(TransactionMode.Manual)]
    public class TagAllCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            double leaderLength;
            bool leader;
            TagOrientation tagOrientation;
            bool tagAllObjects;
            Dictionary<Category, FamilySymbol> symbols;
            TagLocation tagLocation;
            using (FormTagAll form = new FormTagAll(RevitManager.UIDocument))
            {
                if (form.ShowDialog() == System.Windows.Forms.DialogResult.Cancel)
                    return Result.Cancelled;
                leader = form.GetLeader();
                leaderLength = form.GetLeaderLength();
                tagOrientation = form.GetOrientation();
                tagAllObjects = form.GetTagAllObjects();
                tagLocation = form.GetTagLocation();
                symbols = form.GetTagSymbols();
            }
            XYZ direction = XYZ.Zero;
            if (tagLocation == TagLocation.Up)
            {
                direction = XYZ.BasisY;
            }
            else if (tagLocation == TagLocation.Down)
            {
                direction = XYZ.BasisY.Negate();
            }
            else if (tagLocation == TagLocation.Left)
            {
                direction = XYZ.BasisX.Negate();
            }
            else if (tagLocation == TagLocation.Right)
            {
                direction = XYZ.BasisX;
            }
            List<Element> elements;
            if (tagAllObjects)
                elements = new FilteredElementCollector(RevitManager.Document, RevitManager.Document.ActiveView.Id)
                    .WhereElementIsNotElementType()
                    .Where(q => q.Category != null)
                    .ToList();
            else
                elements = RevitManager.UIDocument.Selection.GetElementIds().Select(q => RevitManager.Document.GetElement(q)).ToList();
            List<IndependentTag> tags = new List<IndependentTag>();
            List<Line> lines = new List<Line>();
            using (Transaction t = new Transaction(RevitManager.Document, "Smart Tag All"))
            {
                t.Start();
                foreach (KeyValuePair<Category, FamilySymbol> pair in symbols)
                {
                    Category cat = pair.Key;
                    string catNameCleaned = cat.Name.Replace(" Tags", "");
                    FamilySymbol fs = pair.Value;
                    List<Element> elementsToTag = elements.Where(q => q.Category != null &&
                        (q.Category.Name == catNameCleaned ||
                         q.Category.Name == catNameCleaned + "s"))
                        .ToList();
                    foreach (Element e in elementsToTag)
                    {
                        XYZ pt;
                        if (e.Location is LocationPoint lp)
                            pt = lp.Point;
                        else if (e.Location is LocationCurve lc)
                            pt = lc.Curve.Evaluate(0.5, true);
                        else
                            continue;
                        if (new FilteredElementCollector(RevitManager.Document, RevitManager.Document.ActiveView.Id)
                            .OfClass(typeof(IndependentTag))
                            .Cast<IndependentTag>()
#if revit2018 || revit2019 || revit2020 || revit2021 || revit2022
                            .FirstOrDefault(q => q.TaggedLocalElementId == e.Id) != null)
#else
                            .FirstOrDefault(q => q.GetTaggedLocalElementIds().Any(a => a == e.Id)) != null)
#endif
                            continue;
#if revit2018
                        IndependentTag tag = IndependentTag.Create(RevitManager.Document, RevitManager.Document.ActiveView.Id, new Reference(e), leader, TagMode.TM_ADDBY_CATEGORY, tagOrientation, pt);
#else
                        IndependentTag tag = IndependentTag.Create(RevitManager.Document, fs.Id, RevitManager.Document.ActiveView.Id, new Reference(e), leader, tagOrientation, pt);
#endif
                        tag.ChangeTypeId(fs.Id);
                        tags.Add(tag);
                        tag.TagHeadPosition = pt.Add(direction.Multiply(leaderLength));
                        RevitManager.Document.Regenerate();
                        if (TagUtils.DoOverlapsExist(tag, lines))
                        {
                            tag.TagHeadPosition = pt.Add(direction.Negate().Multiply(leaderLength));
                        }
                        if (pt.DistanceTo(tag.TagHeadPosition) > 0.001)
                        {
                            Line fullLine = Line.CreateBound(pt, tag.TagHeadPosition);
                            lines.Add(Line.CreateBound(fullLine.Evaluate(0.1, true), fullLine.Evaluate(0.9, true)));
                        }
                    }
                }
                t.Commit();
            }
            return Result.Succeeded;
        }
    }
    public static class TagUtils
    {
        public static bool DoOverlapsExist(IndependentTag tag, List<Line> lines)
        {
            Document doc = tag.Document;
            doc.Regenerate();
            double tolerance = 5;
            XYZ pt = tag.TagHeadPosition;
            BoundingBoxXYZ box = tag.get_BoundingBox(doc.GetElement(tag.OwnerViewId) as View);
            Outline outline = new Outline(box.Min, box.Max);
            List<IndependentTag> otherTagsInViewWithinTolerance = new FilteredElementCollector(doc)
                .OfClass(typeof(IndependentTag))
                .Cast<IndependentTag>()
                .Where(q => q.Id != tag.Id)
                .Where(q => q.OwnerViewId == tag.OwnerViewId)
                .Where(q => q.TagHeadPosition.DistanceTo(pt) < tolerance)
                .ToList();
            foreach (IndependentTag tag2 in otherTagsInViewWithinTolerance)
            {
                BoundingBoxXYZ box2 = tag2.get_BoundingBox(doc.GetElement(tag2.OwnerViewId) as View);
                Outline outline2 = new Outline(box2.Min, box2.Max);
                if (outline.Intersects(outline2, 0.15))
                {
                    return true;
                }
            }
            if (lines != null)
            {
                foreach (Line l in lines)
                {
                    IntersectionResult ir = l.Project(tag.TagHeadPosition);
                    if (ir != null && ir.XYZPoint.DistanceTo(tag.TagHeadPosition) < 0.01)
                    {
                        return true;
                    }
                }
            }
            return false;
        }
    }
}