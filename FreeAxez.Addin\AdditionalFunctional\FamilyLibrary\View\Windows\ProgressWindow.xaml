<Window x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Windows.ProgressWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
        Title="{Binding Title}"
        Height="200" Width="400"
        WindowStartupLocation="CenterOwner"
        WindowStyle="None"
        ResizeMode="NoResize"
        Background="White"
        ShowInTaskbar="False"
        Topmost="True">

    <Window.Resources>
        <ResourceDictionary>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Border BorderBrush="{StaticResource Gray200}" BorderThickness="1" CornerRadius="8">
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0"
                       Text="{Binding Title}"
                       Style="{StaticResource TextH3}"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,20"/>

            <StackPanel Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Margin="0,0,0,20">
                <!-- Spinner (shown when IsIndeterminate=true) -->
                <Ellipse Style="{StaticResource LoadingSpinner}"
                         Margin="0,0,0,15"
                         Visibility="{Binding IsIndeterminate, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <!-- Progress Bar (shown when IsIndeterminate=false) -->
                <ProgressBar Height="20" Width="300"
                             Value="{Binding ProgressValue}"
                             Maximum="100"
                             Margin="0,0,0,15"
                             Visibility="{Binding IsIndeterminate, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>

                <TextBlock Text="{Binding StatusMessage, FallbackValue='Please wait...'}"
                           Style="{StaticResource TextBase}"
                           HorizontalAlignment="Center"
                           TextAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
