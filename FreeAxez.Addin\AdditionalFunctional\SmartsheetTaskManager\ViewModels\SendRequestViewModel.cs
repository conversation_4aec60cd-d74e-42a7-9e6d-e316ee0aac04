﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Abstractions;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Commands.SendRequest;
using FreeAxez.Addin.Infrastructure;
using System.Threading;
using System.Windows.Input;
using System.Windows.Threading;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels
{
    public class SendRequestViewModel : BaseViewModel
    {
        private readonly TaskManagerViewModel _taskManagerViewModel;
        private readonly Dispatcher _dispatcher;
        private readonly TaskManagerHttpClientBase _taskManagerHttpClientService;
        private string _requestText;
        private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();

        public SendRequestViewModel(TaskManagerViewModel taskManagerViewModel,
                                    Dispatcher dispatcher,
                                    TaskManagerHttpClientBase taskManagerHttpClientService)
        {
            _taskManagerViewModel = taskManagerViewModel;
            _dispatcher = dispatcher;
            _taskManagerHttpClientService = taskManagerHttpClientService;

            if (string.IsNullOrEmpty(_taskManagerViewModel.RecentRow.AssignedToEmail))
            {
                _requestText = "This task don't have assigned email or this email is not valid smartsheet contact." +
                    " Contact BIM Manager to resolve this issue.";
            }
            else
            {
                _requestText = "Send update request to " +
                    $"\"{_taskManagerViewModel.RecentRow.AssignedToEmail}\"?";
            }

            OkCommand = new OkAsyncCommand(this,
                                           _taskManagerViewModel,
                                           _dispatcher,
                                           _taskManagerHttpClientService);
            CancelCommand = new CancelCommand();
        }

        public string RequestText
        {
            get => _requestText;
            set
            {
                _requestText = value;
                OnPropertyChanged(nameof(RequestText));
            }
        }

        public ICommand OkCommand { get; }
        public ICommand CancelCommand { get; }

        internal CancellationTokenSource CancellationTokenSource => _cancellationTokenSource;
    }
}
