﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.AdditionalFunctional.CountBaseUnits.Utils;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.CountBaseUnits
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class CountBaseUnitsCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            try
            {
                var startBaseUnit = PickInstance("Select start base unit");
                var endBaseUnit = PickInstance("Select end base unit");
                var dimension = PickDimension("Select dimension");

                var dimLine = dimension.Curve as Line;
                var direction = dimLine.Direction;

                var searchLine = GetSearchLine(startBaseUnit, endBaseUnit, direction);

                var startBaseUnitFi = startBaseUnit as FamilyInstance;
                var baseUnitsInLine = GetBaseUnitsInLine(startBaseUnitFi, searchLine, startBaseUnit.LevelId);

                var countBaseUnits = baseUnitsInLine.Count;

                using (var t = new Transaction(RevitManager.Document))
                {
                    t.Start("Edit dimension text");

                    dimension.Below = $"{countBaseUnits} BASE UNIT";

                    t.Commit();
                }
            }
            catch (Exception ex)
            {
                TaskDialog.Show("Error", ex.Message);
                return Result.Failed;
            }

            return Result.Succeeded;
        }

        private Element PickInstance(string statusPrompt)
        {
            var reference = RevitManager.UIDocument.Selection.PickObject(
                ObjectType.Element, new InstanceSelectionFilter(), statusPrompt);

            return RevitManager.Document.GetElement(reference);
        }

        private Dimension PickDimension(string statusPrompt)
        {
            var reference = RevitManager.UIDocument.Selection.PickObject(
                ObjectType.Element, new DimensionSelectionFilter(), statusPrompt);

            return RevitManager.Document.GetElement(reference) as Dimension;
        }

        private Line GetSearchLine(Element startElement, Element endElement, XYZ direction)
        {
            var startPoint = (startElement.Location as LocationPoint)?.Point;
            if (startPoint == null)
            {
                throw new InvalidOperationException("Unable to determine the start point of the first unit.");
            }

            var endPoint = (endElement.Location as LocationPoint)?.Point;
            if (endPoint == null)
            {
                throw new InvalidOperationException("Unable to determine the start point of the last unit.");
            }

            var endPointProjected = ProjectPointOntoLine(endPoint, startPoint, direction);

            return Line.CreateBound(startPoint, endPointProjected);
        }

        private XYZ ProjectPointOntoLine(XYZ point, XYZ lineOrigin, XYZ lineDirection)
        {
            var vectorToPoint = point - lineOrigin;

            var projectionLength = vectorToPoint.DotProduct(lineDirection);

            return lineOrigin + lineDirection * projectionLength;
        }

        private List<FamilyInstance> GetBaseUnitsInLine(FamilyInstance startBaseUnit, Line searchLine, ElementId levelId)
        {
            var collector = new FilteredElementCollector(RevitManager.Document)
                .OfCategoryId(startBaseUnit.Category.Id)
                .WhereElementIsNotElementType()
                .OfType<FamilyInstance>()
                .Where(x => x.Symbol.Id == startBaseUnit.Symbol.Id && x.LevelId == startBaseUnit.LevelId)
                .Where(e =>
                {
                    var locationPoint = (e.Location as LocationPoint)?.Point;
                    if (locationPoint == null) return false;

              
                    return searchLine.Distance(locationPoint) < 0.02;
                }).ToList();

            return collector;
        }
    }
}
