using System;
using System.Globalization;
using System.Windows.Data;

namespace FreeAxez.Addin.Infrastructure.Converters
{
    public class LayerNameToEnabledConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string layerName)
            {
                // Disable combobox for protected layers
                return !string.Equals(layerName, "0", StringComparison.OrdinalIgnoreCase) &&
                       !string.Equals(layerName, "Defpoints", StringComparison.OrdinalIgnoreCase);
            }
            return true;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
