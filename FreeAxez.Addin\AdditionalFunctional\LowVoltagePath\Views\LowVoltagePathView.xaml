<Window x:Class="FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Views.LowVoltagePathView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:models="clr-namespace:FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models"
        mc:Ignorable="d"
        Title="Low Voltage Path Generator"
        Height="400"
        Width="500"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False">

    <Window.Resources>
        <!-- Styles from existing project -->
        <Style x:Key="SectionHeader" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <Style x:Key="OptionLabel" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="0,5,0,2"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                   Text="Configure Low Voltage Path Generation"
                   Style="{StaticResource SectionHeader}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <!-- Main Settings Panel -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- Scope Selection -->
                <TextBlock Text="Scope of Operation:" Style="{StaticResource SectionHeader}"/>
                <StackPanel Margin="20,0,0,0">
                    <RadioButton Content="Entire View" 
                                 IsChecked="{Binding Settings.ScopeType, Converter={x:Static models:ScopeTypeConverter.Instance}, ConverterParameter=EntireView}"
                                 Margin="0,5"/>
                    <RadioButton Content="Selected Elements Only" 
                                 IsChecked="{Binding Settings.ScopeType, Converter={x:Static models:ScopeTypeConverter.Instance}, ConverterParameter=SelectedElements}"
                                 Margin="0,5"/>
                </StackPanel>

                <!-- Railing Type Selection -->
                <TextBlock Text="Target Railing Type:" Style="{StaticResource SectionHeader}"/>
                <ComboBox ItemsSource="{Binding AvailableRailingTypes}"
                          SelectedItem="{Binding Settings.SelectedRailingType}"
                          DisplayMemberPath="Name"
                          Margin="20,5,0,0"
                          Height="25"
                          IsEnabled="{Binding IsExecuting, Converter={x:Static models:InverseBooleanConverter.Instance}}"/>

                <!-- Options -->
                <TextBlock Text="Options:" Style="{StaticResource SectionHeader}"/>
                <StackPanel Margin="20,0,0,0">
                    <CheckBox Content="Delete lines after converting to railings"
                              IsChecked="{Binding Settings.DeleteLines}"
                              Margin="0,5"
                              IsEnabled="{Binding IsExecuting, Converter={x:Static models:InverseBooleanConverter.Instance}}"/>
                </StackPanel>

                <!-- Information Panel -->
                <Border Background="LightYellow" 
                        BorderBrush="Orange" 
                        BorderThickness="1" 
                        Padding="10" 
                        Margin="0,20,0,0">
                    <StackPanel>
                        <TextBlock Text="Information:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBlock TextWrapping="Wrap">
                            <Run Text="• This tool will create railings based on LV/MC lines and place annotations"/>
                            <LineBreak/>
                            <Run Text="• Wire counts will be calculated from outlet parameters (LV# - Count)"/>
                            <LineBreak/>
                            <Run Text="• Existing railings connected to outlets will not be recreated"/>
                        </TextBlock>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Error Display -->
        <TextBlock Grid.Row="2" 
                   Text="{Binding Error}"
                   Foreground="Red"
                   TextWrapping="Wrap"
                   Margin="0,10,0,0"
                   Visibility="{Binding Error, Converter={x:Static models:StringToVisibilityConverter.Instance}}"/>

        <!-- Buttons -->
        <Grid Grid.Row="3" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Progress indicator -->
            <StackPanel Grid.Column="0" 
                        Orientation="Horizontal" 
                        VerticalAlignment="Center"
                        Visibility="{Binding IsExecuting, Converter={x:Static models:BooleanToVisibilityConverter.Instance}}">
                <ProgressBar Width="100" Height="20" IsIndeterminate="True" Margin="0,0,10,0"/>
                <TextBlock Text="Processing..." VerticalAlignment="Center"/>
            </StackPanel>

            <Button Grid.Column="1"
                    Content="Execute"
                    Command="{Binding ExecuteCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    Width="80"
                    Height="30"
                    Margin="0,0,10,0"
                    IsDefault="True"/>

            <Button Grid.Column="2"
                    Content="Cancel"
                    Command="{Binding CancelCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    Width="80"
                    Height="30"
                    IsCancel="True"/>
        </Grid>
    </Grid>
</Window>
