﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.AdditionalFunctional.TagAll
{
    public partial class FormTagAll : System.Windows.Forms.Form
    {
        Document doc;
        Units units;
        public FormTagAll(UIDocument uidoc)
        {
            InitializeComponent();
            doc = uidoc.Document;
            if (!uidoc.Selection.GetElementIds().Any())
            {
                rdoSelected.Enabled = false;
                rdoAll.Checked = true;
            }

            cboTagPosition.DataSource = Enum.GetValues(typeof(TagLocation)).Cast<TagLocation>();
            cboTagPosition.SelectedIndex = 0;

            setLeaderLengthEnabled();
            cboOrientation.SelectedIndex = 0;

            units = new Units(UnitSystem.Imperial);
            FormatOptions foInches = new FormatOptions
            {
                UseDefault = false,
#if revit2018 || revit2019 || revit2020 || revit2021
                DisplayUnits = DisplayUnitType.DUT_FRACTIONAL_INCHES
#else
#endif
            };

#if revit2018 || revit2019 || revit2020 || revit2021
            units.SetFormatOptions(UnitType.UT_Length, foInches);
            txtLeaderLength.Text = UnitFormatUtils.Format(units, UnitType.UT_Length, 1 , false, true);
#else
            foInches.SetUnitTypeId(UnitTypeId.FractionalInches);
            units.SetFormatOptions(SpecTypeId.Length, foInches);
            txtLeaderLength.Text = UnitFormatUtils.Format(units, SpecTypeId.Length, 1, false);
#endif

            List<string> skip = new List<string>
            {
                "Callout Heads",
                "Elevation Marks",
                "Grid Heads",
                "Level Heads",
                "Section Marks",
                "Spot Elevation Symbols",
                "Title Blocks",
                "View Titles"
            };

            foreach (Category cat in doc.Settings.Categories.Cast<Category>()
                .Where(q => q.IsTagCategory && !skip.Contains(q.Name))
                )
            {
                List<CommonUtils.NameIDObject> lst = new List<CommonUtils.NameIDObject>();

                foreach (Family f in new FilteredElementCollector(doc).OfClass(typeof(Family)).Cast<Family>()
                    .Where(q => q.FamilyCategory.Id.GetIntegerValue() == cat.Id.GetIntegerValue())
                    .OrderBy(q => q.Name)
                    )
                {                    
                    foreach (FamilySymbol fs in f.
                        GetFamilySymbolIds()
                        .Select(q => doc.GetElement(q) as FamilySymbol)
                        .OrderBy(q => q.Name)
                        )
                    {
                        lst.Add(new CommonUtils.NameIDObject(f.Name + " : " + fs.Name, fs.Id.GetIntegerValue()));
                    }
                }
                if (lst.Any())
                {
                    int i = gridTags.Rows.Add();

                    DataGridViewTextBoxCell txt = (DataGridViewTextBoxCell)gridTags.Rows[i].Cells[1];
                    txt.Value = cat.Name;

                    DataGridViewComboBoxCell cbo = (DataGridViewComboBoxCell)gridTags.Rows[i].Cells[2];
                    cbo.DisplayMember = "Name";
                    cbo.ValueMember = "IdValue";
                    cbo.DataSource = lst;
                    cbo.Value = lst[0].IdValue;
                }
            }

            gridTags.Sort(gridTags.Columns[1], ListSortDirection.Ascending);
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            if (!GetTagSymbols().Any())
            {
                MessageWindow.ShowDialog("Select at least one category to tag", Infrastructure.UI.Enums.MessageType.Notify);
                return;
            }
            DialogResult = DialogResult.OK;
            Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        public bool GetLeader()
        {
            return chkLeader.Checked;
        }

        private void chkLeader_CheckedChanged(object sender, EventArgs e)
        {
            setLeaderLengthEnabled();
        }

        private void setLeaderLengthEnabled()
        {
            if (chkLeader.Checked)
                txtLeaderLength.Enabled = true;
            else
                txtLeaderLength.Enabled = false;
        }

        public double GetLeaderLength()
        {
            return CommonUtils.GetInternalUnitsFromTextBox(doc, units, txtLeaderLength);
        }

        public TagOrientation GetOrientation()
        {
            if (cboOrientation.SelectedItem.ToString() == "Horizontal")
                return TagOrientation.Horizontal;
            return TagOrientation.Vertical;
        }

        public TagLocation GetTagLocation()
        {
            return (TagLocation)Enum.Parse(typeof(TagLocation), cboTagPosition.SelectedItem.ToString());
        }

        public bool GetTagAllObjects()
        {
            return rdoAll.Checked;
        }

        public Dictionary<Category, FamilySymbol> GetTagSymbols()
        {
            Dictionary<Category, FamilySymbol> ret = new Dictionary<Category, FamilySymbol>();
            foreach (DataGridViewRow row in gridTags.Rows)
            {
                DataGridViewCheckBoxCell chk = row.Cells[0] as DataGridViewCheckBoxCell;
                if (chk.Value == null || chk.Value.ToString() != "1")
                    continue;

                DataGridViewComboBoxCell cb = (DataGridViewComboBoxCell)row.Cells[2];
                int idInt = (int)cb.Value;
                FamilySymbol fs = doc.GetElement(new ElementId(idInt)) as FamilySymbol;

                ret.Add(fs.Family.FamilyCategory, fs);
            }
            return ret;
        }

        private void txtLeaderLength_Leave(object sender, EventArgs e)
        {
            CommonUtils.checkLengthTextBox(doc, units, (System.Windows.Forms.TextBox)sender, AllowedValues.Positive);
        }


    }
}
