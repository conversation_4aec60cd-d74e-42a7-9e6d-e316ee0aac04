﻿using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Dialog;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Model;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Services;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Utils;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.View;
using FreeAxez.Addin.Infrastructure;
using Newtonsoft.Json;

namespace FreeAxez.Addin.AdditionalFunctional.UserAccountControl.ViewModel;

public class SettingsVm : ModalDialogVm
{
    private string _error;
    private bool _isLoggedIn;
    private bool _isLoggingIn;
    private bool _isDataLoaded;
    private string _userEmail;
    private string _userPassword;

    public SettingsVm()
    {
        IsAuthenticated();
        LoginCommand = new AsyncRelayCommand(ExecuteLogin);
        LogoutCommand = new RelayCommand(ExecuteLogout);
        CancelCommand = new RelayCommand(Cancel);
        ShowResetPasswordCommand = new RelayCommand(ShowResetPasswordDialog);
    }

    public ICommand CancelCommand { get; }
    public ICommand LoginCommand { get; private set; }
    public ICommand LogoutCommand { get; private set; }
    public ICommand ShowResetPasswordCommand { get; private set; }

    public bool CanApply => !_isLoggingIn && !string.IsNullOrWhiteSpace(UserEmail) &&
                            !string.IsNullOrWhiteSpace(UserPassword);

    public bool IsLoggedIn
    {
        get => _isLoggedIn;
        set
        {
            _isLoggedIn = value;
            OnPropertyChanged();
        }
    }

    public string AssemblyVersion => AssemblyVersionUtil.GetAssemblyVersion();
    public string Email => CurrentUser.Info?.Email;

    public string FirstName => CurrentUser.Info?.FirstName;

    public string LastName => CurrentUser.Info?.LastName;

    public string[] Roles => CurrentUser.Info?.Roles;

    public string UserEmail
    {
        get => _userEmail;
        set
        {
            _userEmail = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(CanApply));
        }
    }

    public string UserPassword
    {
        get => _userPassword;
        set
        {
            _userPassword = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(CanApply));
        }
    }

    public string Error
    {
        get => _error;
        set
        {
            if (_error != value)
            {
                _error = value;
                OnPropertyChanged();
            }
        }
    }

    public bool IsLoggingIn
    {
        get => _isLoggingIn;
        set
        {
            _isLoggingIn = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(CanApply));
        }
    }

    public bool IsDataLoaded
    {
        get => _isDataLoaded;
        set
        {
            _isDataLoaded = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(CanApply));
        }
    }

    private void Cancel(object parameter)
    {
        CloseDialog(false);
    }

    private async void IsAuthenticated()
    {
        IsDataLoaded = false;
        try
        {
            var (userEmail, token) = UserAuthManager.GetCredentials();

            if (!string.IsNullOrEmpty(token))
            {
                CurrentUser.Info = await UserAuthApiService.Instance.GetUserInfoAsync(token);
                if (CurrentUser.Info != null)
                {
                    bool isAdmin = CurrentUser.Info.Roles.Any(role => role == "Admin" || role == "BIM Manager");
                    IsLoggedIn = true;
                    UserAuthManager.SetRibbonElementsEnabled(IsLoggedIn, isAdmin);
                }
                else
                {
                    UserAuthManager.RemoveCredentials();
                    IsLoggedIn = false;
                    UserAuthManager.SetRibbonElementsEnabled(IsLoggedIn);
                }
            }
            else
            {
                IsLoggedIn = false;
                UserAuthManager.SetRibbonElementsEnabled(IsLoggedIn);
            }

            OnPropertyChanged(nameof(Email));
            OnPropertyChanged(nameof(FirstName));
            OnPropertyChanged(nameof(LastName));
            OnPropertyChanged(nameof(Roles));
        }
        catch (Exception ex)
        {
            IsLoggedIn = false;
        }
        IsDataLoaded = true;
    }

    private void ShowResetPasswordDialog(object parameter)
    {
        CloseDialog(false);
        var resetPasswordView = new ResetPasswordView();
        RevitManager.SetRevitAsWindowOwner(resetPasswordView);
        resetPasswordView.ShowDialog();
    }

    private void OnDialogClose(bool? result)
    {
    }

    private async Task ExecuteLogin(object parameter)
    {
        IsLoggingIn = true;
        var response = await UserAuthApiService.Instance.GetTokenAsync(UserEmail, UserPassword);
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(responseContent);
            var token = tokenResponse.Token;
            CurrentUser.Info = await UserAuthApiService.Instance.GetUserInfoAsync(token);
            var isAdmin = CurrentUser.Info.Roles.Contains("Admin");

            UserAuthManager.SaveCredentials(UserEmail, token);
            UserAuthManager.SetRibbonElementsEnabled(true, isAdmin);

            OnPropertyChanged(nameof(Email));
            OnPropertyChanged(nameof(FirstName));
            OnPropertyChanged(nameof(LastName));
            OnPropertyChanged(nameof(Roles));

            IsLoggedIn = true;
        }
        else
        {
            var errorContent = await response.Content.ReadAsStringAsync();
            Error = errorContent;
        }

        IsLoggingIn = false;
    }

    private void ExecuteLogout(object parameter)
    {
        UserAuthManager.RemoveCredentials();
        UserAuthManager.SetRibbonElementsEnabled(false);


        CurrentUser.Info = null;
        OnPropertyChanged(nameof(Email));
        OnPropertyChanged(nameof(FirstName));
        OnPropertyChanged(nameof(LastName));
        OnPropertyChanged(nameof(Roles));

        IsLoggedIn = false;
    }
}