﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement.Models;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement.Utils
{
    internal class PlacementManager
    {
        private const double VerticalPadding = 0.166666666666667; // 2"
        private const double HorizontalPadding = 0.166666666666667; // 2"
        private const double ClearanceHeight = 0.0416666666666667 * 2; // 0 1"
        private const double ClearanceWidth = 0.0416666666666667; // 0 1/2"
        private const double StampWidth = 4.0 / 12;

        private readonly List<TrackSchedule> _schedules;
        private readonly LevelSheets _sheets;

        public PlacementManager(List<TrackSchedule> schedules, LevelSheets sheets)
        {
            _schedules = schedules;
            _sheets = sheets;
        }


        public List<ViewSheet> Place()
        {
            var processedSheets = new List<ViewSheet>();

            _sheets.RemoveAllSchedulesFromSheets();
            var sheet = _sheets.GetNextSheetForLevel();
            processedSheets.Add(sheet);

            var rowsHeight = 0.0;
            var currentRowHeight = 0.0;
            var rowWidth = 0.0;
            (var upperLeftCorner, var sheetHeight, var sheetWidth) = GetTitleBlockSize(sheet);
            foreach (var schedule in _schedules)
            {
                if (NewRowRequired(schedule.Width))
                {
                    rowsHeight = rowsHeight == 0 ? currentRowHeight :
                        rowsHeight + ClearanceHeight + currentRowHeight;
                    currentRowHeight = 0;
                    rowWidth = 0;
                }

                if (NewSheetRequired(schedule.Height))
                {
                    if (_sheets.PreviousSheetHasNotPlacedSchedules())
                    {
                        throw new InvalidOperationException(
                            $"The \"{schedule.TrackName}\" schedule is too large to fit on the sheet with the current title block.");
                    }

                    sheet = _sheets.GetNextSheetForLevel();
                    processedSheets.Add(sheet);
                    (upperLeftCorner, sheetHeight, sheetWidth) = GetTitleBlockSize(sheet);
                    rowsHeight = 0;
                    currentRowHeight = 0;
                    rowWidth = 0;
                }

                schedule.Place(sheet.Id, GetNextOrigin());

                rowWidth = rowWidth == 0 ? schedule.Width :
                    rowWidth + ClearanceWidth + schedule.Width;
                // The row height is equal to the maximum height of the specification
                currentRowHeight = schedule.Height > currentRowHeight ? schedule.Height :
                    currentRowHeight;
            }

            if (Properties.Settings.Default.panelSchedulePlacementDeleteUnused)
            {
                _sheets.RevomeUnusedSheets();
            }

            return processedSheets;


            bool NewSheetRequired(double scheduleHeight)
            {
                if (rowsHeight == 0)
                {
                    return VerticalPadding + rowsHeight +
                        scheduleHeight + VerticalPadding > sheetHeight;
                }
                return VerticalPadding + rowsHeight + ClearanceHeight +
                    scheduleHeight + VerticalPadding > sheetHeight;
            }

            bool NewRowRequired(double scheduleWidth)
            {
                return HorizontalPadding + rowWidth + ClearanceWidth +
                    scheduleWidth + HorizontalPadding + StampWidth > sheetWidth;
            }

            XYZ GetNextOrigin()
            {
                var x = rowWidth == 0 ? upperLeftCorner.X + HorizontalPadding :
                    upperLeftCorner.X + HorizontalPadding + rowWidth + ClearanceWidth;
                var y = rowsHeight == 0 ? upperLeftCorner.Y - VerticalPadding :
                    upperLeftCorner.Y - VerticalPadding - rowsHeight - ClearanceHeight;
                return new XYZ(x, y, 0);
            }
        }

        private (XYZ corner, double height, double width) GetTitleBlockSize(ViewSheet sheet)
        {
            var titleBlock = new FilteredElementCollector(RevitManager.Document, sheet.Id)
                .OfCategory(BuiltInCategory.OST_TitleBlocks)
                .FirstOrDefault();

            if (titleBlock == null)
            {
                throw new InvalidOperationException(
                    $"The \"{sheet.SheetNumber} - {sheet.Name}\" sheet does not have a title block,\n" +
                    $"so it is not possible to calculate the dimensions for placing the specifications.\n" +
                    $"Please place a title block for this sheet and try again.");
            }

            var bb = titleBlock.get_BoundingBox(sheet);
            var upperLeftCorner = new XYZ(bb.Min.X, bb.Max.Y, bb.Max.Z);
            var sheetHeight = bb.Max.Y - bb.Min.Y;
            var sheetWidth = bb.Max.X - bb.Min.X;

            return (upperLeftCorner, sheetHeight, sheetWidth);
        }
    }
}
