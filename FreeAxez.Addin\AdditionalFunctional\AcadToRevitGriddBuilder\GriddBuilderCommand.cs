﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.UI.Views;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.AutoCAD;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Infrastructure;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.UI.ViewModels;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder
{
    /// <summary>
    /// Revit command for building Gridd elements from DWG links
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    public class GriddBuilderCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            return ExecuteWorkflow();
        }

        private Result ExecuteWorkflow()
        {
            if (!CheckPrerequisites())
                return Result.Cancelled;

            var dwgLinkInfo = SelectAndValidateDwgLink();
            if (dwgLinkInfo == null)
                return Result.Cancelled;
            return ShowConfigurationDialog(dwgLinkInfo);
        }

        private bool CheckPrerequisites()
        {
            var autocadService = new AutoCADService();
            if (!autocadService.IsCoreConsoleAvailable())
            {
                TaskDialog.Show("AutoCAD Missing",
                    "AutoCAD Core Console is required for this operation.",
                    TaskDialogCommonButtons.Ok);
                return false;
            }
            return true;
        }

        private DwgLinkInfo SelectAndValidateDwgLink()
        {
            var dwgLinkManager = new DwgLinkManager();

            var dwgLinkInfo = dwgLinkManager.SelectDwgLink(RevitManager.UIDocument);
            if (dwgLinkInfo == null)
                return null;
            if (!dwgLinkManager.ValidateDwgFileAccess(dwgLinkInfo))
            {
                dwgLinkManager.ShowUpdateLinkMessage(dwgLinkInfo);
                return null;
            }

            return dwgLinkInfo;
        }

        private Result ShowConfigurationDialog(DwgLinkInfo dwgLinkInfo)
        {
            var viewModel = new GriddBuilderViewModel(RevitManager.Document, dwgLinkInfo);
            var dialog = new GriddBuilderView { DataContext = viewModel };

            RevitManager.SetRevitAsWindowOwner(dialog);

            var dialogResult = dialog.ShowDialog();
            return (dialogResult == true && dialog.Result?.Success == true) ? Result.Succeeded : Result.Cancelled;
        }
    }
}
