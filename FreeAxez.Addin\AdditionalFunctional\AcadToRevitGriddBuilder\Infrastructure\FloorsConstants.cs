namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Infrastructure
{
    public static class FloorsConstants
    {
        // Floor type names (must exist in Revit project)
        public const string GriddAreaFloorName = "GRIDD AREA";
        public const string OverallAreaFloorName = "OVERALL AREA";

        // Floor offsets (in feet) - positive values for inward offset
        public const double DefaultFloorBorderOffset = 1.0 / 12.0; // 1 inch inward border offset for gridd area floors
        public const double GriddFloorOffsetFromLevel = 3.0 / 12.0; // 3 inches offset from level for gridd floors

        // Floor thickness (in feet)
        public const double DefaultFloorThickness = 0.0625; // 3/4 inch
    }
}
