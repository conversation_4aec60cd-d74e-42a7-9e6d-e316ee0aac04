﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Abstractions;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Commands.CompleteStatus;
using FreeAxez.Addin.Infrastructure;
using System.Collections.ObjectModel;
using System.Threading;
using System.Windows.Input;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;

public class CompleteStatusViewModel : BaseViewModel
{
    private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
    private readonly TaskManagerViewModel _mainViewModel;
    private readonly TaskManagerHttpClientBase _taskManagerHttpClientService;
    private ObservableCollection<string> _attachedFilePaths = new ObservableCollection<string>();
    private string? _selectedFile;
    private string? _comment;

    public CompleteStatusViewModel(TaskManagerViewModel taskManagerViewModel,
                                   TaskManagerHttpClientBase taskManagerHttpClientService)
    {
        _taskManagerHttpClientService = taskManagerHttpClientService;
        _mainViewModel = taskManagerViewModel;

        AddFilesCommand = new AddFilesCommand(this);
        DeleteFileCommand = new DeleteFileCommand(this);
        OkCommand = new OkAsyncCommand(this, _mainViewModel, _taskManagerHttpClientService);
        CancelCommand = new CancelCommand();
    }

    public string? Comment
    {
        get => _comment;
        set
        {
            _comment = value;
            OnPropertyChanged(nameof(Comment));
        }
    }

    public ObservableCollection<string> AttachedFilePaths
    {
        get => _attachedFilePaths;
        set
        {
            _attachedFilePaths = value;
            OnPropertyChanged(nameof(AttachedFilePaths));
        }
    }

    public string? SelectedFile
    {
        get => _selectedFile;
        set
        {
            _selectedFile = value;
            OnPropertyChanged(nameof(SelectedFile));
        }
    }

    public ICommand AddFilesCommand { get; }
    public ICommand DeleteFileCommand { get; }
    public ICommand OkCommand { get; }
    public ICommand CancelCommand { get; }

    internal CancellationTokenSource CancellationTokenSource => _cancellationTokenSource;
}
