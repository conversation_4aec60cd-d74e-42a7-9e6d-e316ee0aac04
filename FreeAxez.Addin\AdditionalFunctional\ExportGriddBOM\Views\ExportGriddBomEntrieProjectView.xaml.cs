﻿using FreeAxez.Addin.Infrastructure.UI;
using System.Windows;
using System.Windows.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Views
{
    public partial class ExportGriddBomView : Window
    {
        public ExportGriddBomView()
        {
            InitializeComponent();
        }

        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            foreach (NodeViewModel node in Accessories.SelectedItems)
            {
                node.IsChecked = (bool)(sender as CheckBox).IsChecked;
            }
        }
    }
}
