﻿using FreeAxez.Addin.Infrastructure.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBOM.Views
{
    /// <summary>
    /// Interaction logic for ExportPowerBomrRoomRegionView.xaml
    /// </summary>
    public partial class ExportPowerBomrRoomRegionView : Window
    {
        public ExportPowerBomrRoomRegionView()
        {
            InitializeComponent();
        }

        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            foreach (NodeViewModel node in Accessories.SelectedItems)
            {
                node.IsChecked = (bool)(sender as CheckBox).IsChecked;
            }
        }
    }
}
