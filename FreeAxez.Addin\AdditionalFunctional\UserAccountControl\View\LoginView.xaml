﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.UserAccountControl.View.LoginView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             xmlns:viewModel="clr-namespace:FreeAxez.Addin.AdditionalFunctional.UserAccountControl.ViewModel"
             mc:Ignorable="d" 
             SizeToContent="Height"
             WindowStartupLocation="CenterScreen"
             Title="Login"
             Width="350">
    <Window.Resources>
        <ResourceDictionary>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.DataContext>
        <viewModel:LoginVm/>
    </Window.DataContext>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="30"></RowDefinition>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0" Margin="0 0 0 20">
            <TextBlock Style="{StaticResource TextBase}" 
                       Text="Email"
                       Margin="0 10 0 5">
            </TextBlock>
            <TextBox Style="{StaticResource UiTextBox}" 
                     Tag="Enter Email" 
                     Name="EmailTextBox" 
                     VerticalContentAlignment="Center"
                     Margin="0,0,0,10" 
                     Text="{Binding UserEmail, UpdateSourceTrigger=PropertyChanged}" />
            <TextBlock Style="{StaticResource TextBase}" 
                       Text="Password" 
                       Margin="0 10 0 5">
            </TextBlock>
            <Grid>
                <PasswordBox Style="{StaticResource PasswordBox}"
                             x:Name="PasswordBox"
                             PasswordChanged="PasswordBox_PasswordChanged"/>
                <TextBlock x:Name="PlaceholderText"
                           Text="Enter password"
                           Margin="9,0,0,0"
                           Foreground="{StaticResource Gray600}"
                           Panel.ZIndex="2"
                           IsHitTestVisible="False"
                           Visibility="Visible" />
            </Grid>
            <TextBlock Text="{Binding Error}" 
                       Foreground="Red" 
                       Visibility="{Binding Error, Converter={StaticResource StringToVisibilityConverter}}"/>
            <ProgressBar Height="8" 
                         Visibility="{Binding IsLoggingIn, Converter={StaticResource BooleanToVisibilityConverter}}" 
                         IsIndeterminate="True" 
                         Foreground="{StaticResource Blue500}" 
                         Margin="0 10"/>

        </StackPanel>
        <Grid Grid.Row="1" >
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Button Grid.Column="0" 
                    Style="{StaticResource ButtonOutlinedGreen}" 
                    Command="{Binding ShowResetPasswordCommand}"
                    Content="Forgot Password"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"
            />
            <Button Grid.Column="2" 
                    Content="Log In" 
                    Command="{Binding LoginCommand}" 
                    IsEnabled="{Binding CanApply}"
                    Style="{StaticResource ButtonSimpleBlue}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"
                    />
        </Grid>
    </Grid>
</Window>
