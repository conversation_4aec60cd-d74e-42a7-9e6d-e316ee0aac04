﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.Models;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.Helpers
{
    public class ReplaceUnitHelper
    {
        private readonly BaseUnitReplacementFamilyCollector _familyCollector;

        public ReplaceUnitHelper(BaseUnitReplacementFamilyCollector familyCollector)
        {
            _familyCollector = familyCollector;
        }

        public void ReplaceBaseUnits(ref List<BoxUnitsIntersection> intersections, out string report)
        {
            using (var t = new Transaction(RevitManager.Document, "Base Unit Replacement"))
            {
                t.Start();

                var correct = 0;
                var replaced = 0;
                var notReplaced = intersections.Count(i => i.Units.Count > 1);
                var lonelyBoxes = intersections.Count(i => i.Units.Count == 0);
                var replacedCutoutUnitFamilyInstances = intersections.Count(i => i.Box == null);

                foreach (var intersection in intersections)
                {
                    if (intersection.Units.Count != 1) continue;

                    var unitSymbol = intersection.Units.Single().Symbol;
                    var boxSymbol = intersection.Box?.Symbol;

                    var validUnitSymbol = _familyCollector.GetValidUnitSymbolForIntersection(unitSymbol, boxSymbol);
                    if (validUnitSymbol != null) 
                    {
                        if (unitSymbol.Id.Equals(validUnitSymbol.Id))
                        {
                            correct++;
                        }
                        else
                        {
                            intersection.Units.Single().ChangeTypeId(validUnitSymbol.Id);
                            replaced++;
                        }
                    }
                }

                report = $"The number of processed units:\n" +
                             $"Correct {correct},\n" + // New symbol is null
                             $"Replaced {replaced - replacedCutoutUnitFamilyInstances},\n" + // New symbol not null
                             $"Not Replaced {notReplaced},\n" + // Units count != 1
                             $"Lonely Boxes {lonelyBoxes},\n" + // Units count == 0
                             $"Converted back cutout base units {replacedCutoutUnitFamilyInstances}."; // Box count == 0

                t.Commit();
            }
        }
    }
}
