using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;

public class FreeAxezLayerOperations
{
    private readonly DwgLayerManagerApiService _apiService;

    public FreeAxezLayerOperations(DwgLayerManagerApiService apiService)
    {
        _apiService = apiService ?? throw new ArgumentNullException(nameof(apiService));
    }

    public async Task<ObservableCollection<LayerModel>> LoadLayersAsync()
    {
        var layers = await _apiService.GetAllLayersAsync();
        var collection = new ObservableCollection<LayerModel>();

        foreach (var layer in layers)
        {
            layer.PropertyChanged += LayerPropertyChanged;
            collection.Add(layer);
        }

        return collection;
    }

    public async Task<ObservableCollection<LinetypeModel>> LoadLinetypesAsync()
    {
        try
        {
            var linetypes = await _apiService.GetAllLinetypesAsync();
            var collection = new ObservableCollection<LinetypeModel>();

            foreach (var linetype in linetypes)
            {
                collection.Add(linetype);
            }

            return collection;
        }
        catch (Exception)
        {
            return new ObservableCollection<LinetypeModel>();
        }
    }

    public async Task<LayerModel> AddNewLayerAsync()
    {
        try
        {
            var newLayer = await _apiService.CreateNewLayerAsync();
            if (newLayer != null)
            {
                newLayer.PropertyChanged += LayerPropertyChanged;
                return newLayer;
            }
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog("Error", $"Failed to add layer: {ex.Message}", MessageType.Error);
        }

        return null;
    }

    public async Task<bool> DeleteLayerAsync(LayerModel layer)
    {
        if (layer == null) return false;

        try
        {
            var success = await _apiService.DeleteLayerAsync(layer.Id);
            if (success)
            {
                layer.PropertyChanged -= LayerPropertyChanged;
                return true;
            }
            else
            {
                MessageWindow.ShowDialog("Error", "Failed to delete layer", MessageType.Error);
                return false;
            }
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog("Error", $"Failed to delete layer: {ex.Message}", MessageType.Error);
            return false;
        }
    }

    public void SubscribeToLayerChanges(LayerModel layer)
    {
        if (layer != null)
        {
            layer.PropertyChanged += LayerPropertyChanged;
        }
    }

    public void UnsubscribeFromLayerChanges(LayerModel layer)
    {
        if (layer != null)
        {
            layer.PropertyChanged -= LayerPropertyChanged;
        }
    }

    private async void LayerPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        if (sender is LayerModel layer)
        {
            try
            {
                await _apiService.UpdateLayerAsync(layer);
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog("Error", $"Failed to save layer changes: {ex.Message}", MessageType.Error);
            }
        }
    }
}
