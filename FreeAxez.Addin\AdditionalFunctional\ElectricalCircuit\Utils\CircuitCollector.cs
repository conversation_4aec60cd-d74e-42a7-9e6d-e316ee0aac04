﻿using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Enums;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Models;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils
{
    internal class CircuitCollector
    {
        public const double WhipIntersectionTolerance = 0.2;
        
        private PanelCollector _panelCollector;
        private WhipCollector _whipCollector;
        private BoxCollector _boxCollector;
        private TrackCollector _trackCollector;


        public CircuitCollector(LevelHelper levelHelper = null)
        {
            if (levelHelper == null) levelHelper = new LevelHelper();

            _panelCollector = new PanelCollector(levelHelper);
            _whipCollector = new WhipCollector(levelHelper);
            _boxCollector = new BoxCollector(levelHelper);
            _trackCollector = new TrackCollector(levelHelper);
        }


        public List<CircuitAssembly> Collect()
        {
            var tracks = _trackCollector.GetTrackFamilyInstances().Select(e => new CircuitDevice(e)).ToList();
            var panels = _panelCollector.GetPanelFamilyInstances().Select(e => new CircuitDevice(e)).ToList();
            var boxes = _boxCollector.GetBoxFamilyInstances().Select(e => new CircuitDevice(e)).ToList();

            var boxWhips = _whipCollector.GetWhipElementsForBoxes().Select(e => new CircuitWhip(e)).ToList();
            var trackWhips = _whipCollector.GetWhipElementsForTracks().Select(e => new CircuitWhip(e)).ToList();

            var boxCircuits = CreateCircuitModels(boxWhips, boxes, tracks, panels);

            var trackWhipCircuits = CreateTrackWhipCircuites(trackWhips, tracks, new List<CircuitAssembly>());

            return boxCircuits.Concat(trackWhipCircuits).ToList();
        }

        public static List<CircuitAssembly> CollectFakeCircuitsForUnusedTracks(List<CircuitAssembly> circuits, LevelHelper levelHelper)
        {
            // TODO: Refactor unused track process approach

            // Process attached tracks to update level parameters
            var usedTracks = circuits.Where(c => c.Track != null).Select(c => c.Track).ToList();
            var trackCollector = new TrackCollector(levelHelper);
            var attachedTracks = trackCollector.GetTrackFamilyInstances().Where(t => !usedTracks.Any(ut => ut.Id.Equals(t.Id))).ToList();
            var fakeTrackCircuits = attachedTracks.Select(t => new CircuitAssembly() { Track = t, CircuitAssemblyType = CircuitAssemblyType.Track }).ToList();

            return fakeTrackCircuits;
        }

        private List<CircuitAssembly> CreateCircuitModels(
            List<CircuitWhip> boxWhips, 
            List<CircuitDevice> boxes, 
            List<CircuitDevice> tracks, 
            List<CircuitDevice> panels)
        {
            var output = new List<CircuitAssembly>();

            var levelHelper = new LevelHelper();

            var usedBoxes = new List<CircuitDevice>();
            var usedTracks = new List<CircuitDevice>();
            foreach (var whip in boxWhips)
            {
                CircuitDevice connectedBox = null;
                foreach (var box in boxes)
                {
                    if (box.IsConnected(whip))
                    {
                        connectedBox = box;
                        usedBoxes.Add(box);
                        break;
                    }
                }

                CircuitDevice connectedTrack = null;
                foreach (var track in tracks)
                {
                    if (track.IsConnected(whip))
                    {
                        connectedTrack = track;
                        usedTracks.Add(track);
                        break;
                    }
                }

                CircuitDevice assignedPanel = null;
                if (connectedTrack != null)
                {
                    foreach (var panel in panels)
                    {
                        if (whip.LevelId.Equals(panel.LevelId))
                        {
                            assignedPanel = panel;
                            break;
                        }
                    }
                }

                var circuitModel = new CircuitAssembly();
                circuitModel.Whip = whip.FlexPipe;
                circuitModel.Box = connectedBox?.FamilyInstance;
                circuitModel.Track = connectedTrack?.FamilyInstance;
                circuitModel.Panel = assignedPanel?.FamilyInstance;
                circuitModel.CircuitAssemblyType = CircuitAssemblyType.FloorBox;

                output.Add(circuitModel);
            }

            foreach (var box in boxes)
            {
                if (!usedBoxes.Contains(box))
                {
                    var circuitModel = new CircuitAssembly();
                    circuitModel.Box = box.FamilyInstance;
                    output.Add(circuitModel);
                }
            }

            return output;
        }
            
        private List<CircuitAssembly> CreateTrackWhipCircuites(
            List<CircuitWhip> whips, 
            List<CircuitDevice> tracks, 
            List<CircuitAssembly> circuits)
        {
            var trackWhipFinder = new TrackWhipFinder();
            var connections = trackWhipFinder.FindTrackWhipCombinations(tracks, whips, 1.0);

            var output = new List<CircuitAssembly>();

            foreach (var connection in connections) 
            {
                var trackElement = connection.CircuitElements.FirstOrDefault(e => e is CircuitDevice) as CircuitDevice;
                var whipElement = connection.CircuitElements.FirstOrDefault(e => e is CircuitWhip) as CircuitWhip;
                var circuitAssembly = new CircuitAssembly() 
                { 
                    Track = trackElement?.FamilyInstance, 
                    Whip = whipElement?.FlexPipe, 
                    CircuitAssemblyType = CircuitAssemblyType.Track 
                };

                output.Add(circuitAssembly);
            }

            return output;
        }
    }
}
