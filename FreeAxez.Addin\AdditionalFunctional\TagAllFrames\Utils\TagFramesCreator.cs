﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TagAllFrames.Models;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllFrames.Utils
{
    public class TagFramesCreator
    {
        private readonly FrameTagOptions _options;
        private readonly FrameFamily _frameFamily;
        private readonly FrameAnnotationFamily _frameAnnotationFamily;

        public TagFramesCreator(FrameTagOptions options)
        {
            _options = options;
            _frameFamily = new FrameFamily();
            _frameAnnotationFamily = new FrameAnnotationFamily();
        }

        public List<AnnotationSymbol> CreateAnnatation()
        {
            var output = new List<AnnotationSymbol>();

            var framesToTag = SelectFramesToTag();
            List<List<FamilyInstance>> separatedFrames = new SeparateFrameResolver().Resolve(framesToTag);

            using (var t = new Transaction(RevitManager.Document, "Frames Annotation Symbols"))
            {
                t.Start();

                foreach (List<FamilyInstance> separatedFrame in separatedFrames)
                {
                    foreach (var frame in separatedFrame)
                    {
                        output.Add(CreateAnnotation(frame));
                    }

                    RevitManager.Document.Regenerate();

                    foreach (var annotation in output)
                    {
                        SetLeaderPosition(annotation);
                    }
                }

                t.Commit();
            }

            return output;
        }

        private List<FamilyInstance> SelectFramesToTag()
        {
            if (_options.TagFramesVisibleInView)
            {
                return _frameFamily.InstancesOnView;
            }
            return _frameFamily.SelectInstances();
        }

        private AnnotationSymbol CreateAnnotation(FamilyInstance frame)
        {
            var framePoint = (frame.Location as LocationPoint).Point;
            var length = _options.LengthToCenterOfTag;
            var scale = RevitManager.UIDocument.ActiveView.Scale;

            var annotationPoint = new XYZ(framePoint.X - length * scale,
                                          framePoint.Y - length / 2 * scale,
                                          framePoint.Z);

            var tagSymbol = _frameAnnotationFamily.TagSymbol;
            if (FrameFamily.IsCorner(frame))
            {
                tagSymbol = _frameAnnotationFamily.CornerTagSymbol;
            }

            var annotation = RevitManager.Document.Create.NewFamilyInstance(
                annotationPoint, tagSymbol, RevitManager.UIDocument.ActiveView) as AnnotationSymbol;

            annotation.addLeader();

            return annotation;
        }

        private void SetLeaderPosition(AnnotationSymbol annotation)
        {
            var leader = annotation.GetLeaders().FirstOrDefault();
            if (leader == null)
            {
                return;
            }

            var leaderPoint = (annotation.Location as LocationPoint).Point;
            var length = _options.LengthToCenterOfTag;
            var scale = RevitManager.UIDocument.ActiveView.Scale;

            var end = new XYZ(leaderPoint.X + (length * scale),
                              leaderPoint.Y + (length / 2 * scale),
                              leaderPoint.Z);

            var elbow = new XYZ(end.X - (length / 3 * scale),
                                end.Y - (length / 2 * scale),
                                end.Z);

            leader.End = end;
            leader.Elbow = elbow;
        }
    }
}
