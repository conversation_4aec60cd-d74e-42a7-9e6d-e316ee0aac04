﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class BorderEndCover : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
                "Nested_Border_End_Cover_v2",
                "Nested_Border_End_Cover_Arc_Concave",
                "Nested_Border_End_Cover_Arc_Convex",
                "Nested_Border_End_Cover_Radial_Unit",
                "Nested_Border_End_Cover_Arc_Unit"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public BorderEndCover(Element element) : base(element)
        {
        }

        public static List<BorderEndCover> Collect()
        {
            return FamilyCollector.Instances.Where(i => !i.Symbol.Name.Contains("Long")).Select(g => new BorderEndCover(g)).ToList();
        }
    }
}
