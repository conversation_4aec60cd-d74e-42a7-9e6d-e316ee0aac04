﻿using System.Windows;
using System.Windows.Threading;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.RevitHandlers;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary;

/// <summary>
///     Core class for Family Library - manages ExternalEvents
/// </summary>
public static class FamilyLibraryCore
{
    private static string _currentRevitVersion;

    public static string CurrentRevitVersion
    {
        get => _currentRevitVersion;
        private set => _currentRevitVersion = value;
    }
    // ExternalEvent handlers
    public static DetailsDownloadToRevitHandler? DetailsDownloadToRevitHandler;
    public static FamilyDownloadToRevitHandler? FamilyDownloadToRevitHandler;
    public static FamilyAddHandler? FamilyAddHandler;
    public static FamilyEditHandler? FamilyEditHandler;
    public static DetailsAddHandler? DetailsAddHandler;
    public static DetailsEditHandler? DetailsEditHandler;

    // ExternalEvents
    public static ExternalEvent? DetailsDownloadToRevitEvent;
    public static ExternalEvent? FamilyDownloadToRevitEvent;
    public static ExternalEvent? FamilyAddEvent;
    public static ExternalEvent? FamilyEditEvent;
    public static ExternalEvent? DetailsAddEvent;
    public static ExternalEvent? DetailsEditEvent;

    /// <summary>
    ///     Initialize Family Library core components
    /// </summary>
    public static void Initialize()
    {
        if (DetailsDownloadToRevitEvent != null)
        {
            LogHelper.Information("FamilyLibraryCore already initialized");
            return;
        }

        LogHelper.Information("FamilyLibraryCore initialization started.");

        // Initialize WPF Application if needed
        InitializeWpfApplication();
        InitializeRevitVersion();

        // Details download handler
        DetailsDownloadToRevitHandler = new DetailsDownloadToRevitHandler();
        DetailsDownloadToRevitEvent = ExternalEvent.Create(DetailsDownloadToRevitHandler);

        // Family download to Revit handler
        FamilyDownloadToRevitHandler = new FamilyDownloadToRevitHandler();
        FamilyDownloadToRevitEvent = ExternalEvent.Create(FamilyDownloadToRevitHandler);

        // Family add handler
        FamilyAddHandler = new FamilyAddHandler();
        FamilyAddEvent = ExternalEvent.Create(FamilyAddHandler);

        // Family edit handler
        FamilyEditHandler = new FamilyEditHandler();
        FamilyEditEvent = ExternalEvent.Create(FamilyEditHandler);

        // Details add handler
        DetailsAddHandler = new DetailsAddHandler();
        DetailsAddEvent = ExternalEvent.Create(DetailsAddHandler);

        // Details edit handler
        DetailsEditHandler = new DetailsEditHandler();
        DetailsEditEvent = ExternalEvent.Create(DetailsEditHandler);

        LogHelper.Information("FamilyLibraryCore initialization completed.");
    }

    /// <summary>
    ///     Initialize WPF Application if it doesn't exist (like in BimSmithCore)
    /// </summary>
    private static void InitializeWpfApplication()
    {
        try
        {
            // Check if WPF Application is already initialized
            if (Application.Current != null)
            {
                LogHelper.Information("WPF Application already exists");
                return;
            }

            // Try to get current dispatcher
            var dispatcher = Dispatcher.CurrentDispatcher;
            if (dispatcher == null)
            {
                LogHelper.Information("Creating new WPF Application for Family Library");

                // Create new WPF Application (like in BimSmithCore DispatcherHelper)
                var app = new Application();
                LogHelper.Information("WPF Application created successfully");
            }
            else
            {
                LogHelper.Information("Using existing Dispatcher");
            }
        }
        catch (Exception ex)
        {
            LogHelper.Error($"Failed to initialize WPF Application: {ex.Message}");
            // Don't throw - continue without WPF Application
        }
    }

    private static void InitializeRevitVersion()
    {
        try
        {
            var uiApp = RevitManager.CommandData?.Application;
            if (uiApp != null)
            {
                _currentRevitVersion = uiApp.Application.VersionNumber;
                LogHelper.Information($"Current Revit version: {_currentRevitVersion}");
            }
            else
            {
                LogHelper.Warning("Revit UIApplication is not available. Cannot determine Revit version.");
                _currentRevitVersion = string.Empty; // Или значение по умолчанию
            }
        }
        catch (Exception ex)
        {
            LogHelper.Error($"Failed to initialize Revit version: {ex.Message}");
            _currentRevitVersion = string.Empty;
        }
    }

    /// <summary>
    ///     Shows a message dialog using the custom MessageWindow
    /// </summary>
    public static void ShowMessage(string title, string message, MessageType messageType)
    {
        try
        {
            MessageWindow.ShowDialog(title, message, messageType);
        }
        catch (Exception ex)
        {
            LogHelper.Error($"Error showing message dialog: {ex.Message}");
            // Fallback to standard MessageBox if custom dialog fails
            MessageBox.Show(message, title, MessageBoxButton.OK);
        }
    }

    public static bool IsRevitVersionCompatible(string itemVersion, string currentVersion)
    {
        if (string.IsNullOrEmpty(itemVersion) || string.IsNullOrEmpty(currentVersion))
            return false;

        if (int.TryParse(itemVersion, out int itemVersionNum) && int.TryParse(currentVersion, out int currentVersionNum))
        {
            return itemVersionNum <= currentVersionNum;
        }

        return string.Compare(itemVersion, currentVersion, StringComparison.OrdinalIgnoreCase) <= 0;
    }

}