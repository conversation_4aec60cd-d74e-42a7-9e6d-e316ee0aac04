﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="ColorsPalette.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="{x:Type ListView}"
           x:Key="ListViewBase">
        <Setter Property="Background"
                Value="White" />
        <Setter Property="BorderBrush"
                Value="{StaticResource Gray300}" />
        <Setter Property="BorderThickness"
                Value="1" />
        <Setter Property="Foreground"
                Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility"
                Value="Auto" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility"
                Value="Auto" />
        <Setter Property="ScrollViewer.CanContentScroll"
                Value="true" />
        <Setter Property="ScrollViewer.PanningMode"
                Value="Both" />
        <Setter Property="Stylus.IsFlicksEnabled"
                Value="False" />
        <Setter Property="VerticalContentAlignment"
                Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListView}">
                    <Border x:Name="Bd"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="1"
                            CornerRadius="4"
                            SnapsToDevicePixels="true">
                        <ScrollViewer Focusable="false"
                                      Padding="{TemplateBinding Padding}">
                            <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </ScrollViewer>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter Property="Background"
                                    TargetName="Bd"
                                    Value="White" />
                            <Setter Property="BorderBrush"
                                    TargetName="Bd"
                                    Value="{StaticResource Gray200}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsGrouping"
                                           Value="true" />
                                <Condition Property="VirtualizingPanel.IsVirtualizingWhenGrouping"
                                           Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter Property="ScrollViewer.CanContentScroll"
                                    Value="false" />
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>