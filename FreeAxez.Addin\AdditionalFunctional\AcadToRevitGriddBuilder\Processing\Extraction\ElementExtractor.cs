using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Core.Elements;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.Geometry;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.Border;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.Extraction;

/// <summary>
/// Unified service for extracting and creating FreeAxez elements from CAD data
/// </summary>
public class ElementExtractor
{
    public (List<BaseUnit> baseUnits, List<BaseUnitHalf> baseUnitHalves, List<BaseUnitQuarter> baseUnitQuarters,
        List<PlateCorner> plateCorners, List<PlateChannel> plateChannels, List<BorderElement> borderElements)
        ExtractAllElements(List<JsonLineData> allLines, Transform dwgTransform = null, string dwgFilePath = null)
    {
        var usedSegments = new HashSet<LineSegmentData>();
        int idCounter = 1;

        // Process layers directly
        var baseLayerLines = allLines.Where(l => l.layer == "BASE").ToList();
        var cornerLayerLines = allLines.Where(l => l.layer == "EZ_CORNER").ToList();
        var channelLayerLines = allLines.Where(l => l.layer == "CHANNEL").ToList();
        var borderLayerLines = allLines.Where(l => l.layer.Contains("OPT")).ToList();
        var areaLines = allLines.Where(l => l.layer == "A-AREA-PATT").ToList();

        // Diagnostic: Check for duplicate border lines across different OPT layers
        var borderLayerGroups = borderLayerLines.GroupBy(l => l.layer).ToList();
        System.Console.WriteLine($"[ElementExtractor] Border layers found: {string.Join(", ", borderLayerGroups.Select(g => $"{g.Key}({g.Count()})"))}");

        // Check for geometric duplicates across OPT layers
        var geometricGroups = borderLayerLines
            .GroupBy(l => new {
                X1 = Math.Round(Math.Min(l.startPoint.x, l.endPoint.x), 3),
                Y1 = Math.Round(Math.Min(l.startPoint.y, l.endPoint.y), 3),
                X2 = Math.Round(Math.Max(l.startPoint.x, l.endPoint.x), 3),
                Y2 = Math.Round(Math.Max(l.startPoint.y, l.endPoint.y), 3)
            })
            .Where(g => g.Count() > 1)
            .ToList();

        if (geometricGroups.Any())
        {
            System.Console.WriteLine($"[ElementExtractor] Found {geometricGroups.Count} groups of duplicate lines across OPT layers:");
            foreach (var group in geometricGroups.Take(5)) // Show first 5 groups
            {
                var layers = string.Join(", ", group.Select(l => l.layer).Distinct());
                System.Console.WriteLine($"  Line ({group.Key.X1:F3},{group.Key.Y1:F3})-({group.Key.X2:F3},{group.Key.Y2:F3}) appears on layers: {layers}");
            }
        }

        var baseComponents = CreateStandardComponents(baseLayerLines);
        var cornerComponents = CreateStandardComponents(cornerLayerLines);
        var channelComponents = CreateStandardComponents(channelLayerLines);
        // Create elements directly
        var baseUnits = BaseFaElement.CreateFromComponents<BaseUnit>(baseComponents, idCounter, usedSegments, dwgTransform);
        var baseUnitHalves = BaseFaElement.CreateFromComponents<BaseUnitHalf>(baseComponents, idCounter, usedSegments, dwgTransform);
        var baseUnitQuarters = BaseFaElement.CreateFromComponents<BaseUnitQuarter>(baseComponents, idCounter, usedSegments, dwgTransform);
        var plateCorners = BaseFaElement.CreateFromComponents<PlateCorner>(cornerComponents, idCounter, usedSegments, dwgTransform);
        var plateChannels = BaseFaElement.CreateFromComponents<PlateChannel>(channelComponents, idCounter, usedSegments, dwgTransform);

        // Create border elements directly from lines (single processing like old code)
        // TEMPORARILY COMMENTED OUT FOR FASTER TESTING
        // var borderElements = CreateBorderElementsDirectly(borderLayerLines, idCounter, usedSegments, areaLines, dwgTransform);
        var borderElements = new List<BorderElement>(); // Empty list for now

        return (baseUnits, baseUnitHalves, baseUnitQuarters, plateCorners, plateChannels, borderElements);
    }





    /// <summary>
    /// Create border elements directly from lines using single processing (like old code)
    /// </summary>
    private List<BorderElement> CreateBorderElementsDirectly(
        List<JsonLineData> borderLayerLines,
        int idCounter,
        HashSet<LineSegmentData> usedSegments,
        List<JsonLineData> areaLines,
        Transform dwgTransform)
    {
        if (!borderLayerLines.Any())
            return new List<BorderElement>();

        // Convert lines to segments
        var segments = BaseFaElement.ConvertLinesToSegmentsUniversal(borderLayerLines);

        // Group into rectangles using BorderGroupingService (single processing)
        var borderComponents = BorderGroupingService.FindBorderRectangles(segments);

        // Create BorderElement objects with area context processing
        var borderProcessor = new BorderProcessor();
        return borderProcessor.CreateBorderElements(borderComponents, areaLines, idCounter, usedSegments, dwgTransform);
    }

    /// <summary>
    /// Creates components from layer data for standard FreeAxez elements
    /// </summary>
    private List<List<LineSegmentData>> CreateStandardComponents(List<JsonLineData> lines)
    {
        if (lines == null || !lines.Any())
            return new List<List<LineSegmentData>>();

        var segments = BaseFaElement.ConvertLinesToSegmentsUniversal(lines);
        return LineGroupingService.BuildConnectedComponents(segments);
    }


}

