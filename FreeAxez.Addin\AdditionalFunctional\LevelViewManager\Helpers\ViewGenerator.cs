﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.AdditionalFunctional.LevelViewManager.Helpers
{
    public class ViewGenerator
    {
        private readonly Level _targetLevel;
        private readonly ProgressBarHelper _progressBarHelper;
        private readonly LevelNumberFormatter _levelNumberFormatter;

        public enum ViewDuplicateType
        {
            ViewPlane,
            Schedule,
            Sheet,
        }

        public ViewGenerator(
            Level targetLevel,
            ProgressBarHelper progressBarHelper,
            LevelNumberFormatter levelNumberFormatter)
        {
            _targetLevel = targetLevel;
            _progressBarHelper = progressBarHelper;
            _levelNumberFormatter = levelNumberFormatter;
        }

        public List<View> DuplicateViews()
        {
            var levelBasedViews = GetLevelBasedViewPlanes();
            var views = Duplicate(levelBasedViews, _targetLevel, ViewDuplicateType.ViewPlane);
            return views;
        }

        public List<View> DuplicateSchedules()
        {
            var levelBasedViews = GetLevelBasedSchedules();
            var views = Duplicate(levelBasedViews, _targetLevel, ViewDuplicateType.Schedule);
            return views;
        }

        public List<View> DuplicateSheets(List<View> targetViewPlanes, List<View> targetSchedules)
        {
            var levelBasedViews = GetLevelBasedSheets();
            var views = Duplicate(levelBasedViews, _targetLevel, ViewDuplicateType.Sheet, targetViewPlanes, targetSchedules);
            return views;
        }

        private List<View> Duplicate(
            List<View> levelBasedViews, 
            Level targetLevel, 
            ViewDuplicateType viewDuplicateType,
            List<View> targetViewPlanes = null,
            List<View> targetSchedules = null)
        {
            var output = new List<View>();

            using (var t = new Transaction(RevitManager.Document, "Create View"))
            {
                t.Start();

                var processedViews = 0;
                var usedNames = viewDuplicateType == ViewDuplicateType.Sheet 
                    ? levelBasedViews.Select(p => _levelNumberFormatter.ViewNameKey((p as ViewSheet).SheetNumber)).ToList() 
                    : levelBasedViews.Select(p => _levelNumberFormatter.ViewNameKey(p.Name)).ToList();
                var targetLevelNumber = LevelHelper.GetLevelNumber(targetLevel);

                foreach (var view in levelBasedViews)
                {
                    if (_progressBarHelper.CancellationTokenSource.IsCancellationRequested)
                    {
                        break;
                    }

                    _progressBarHelper.ReportProgress((double)++processedViews / levelBasedViews.Count * 100);

                    var name = viewDuplicateType == ViewDuplicateType.Sheet 
                        ? _levelNumberFormatter.ReplaceLevelNumberInSheetNumber((view as ViewSheet).SheetNumber, targetLevelNumber)
                        : _levelNumberFormatter.ReplaceLevelNumber(view.Name, targetLevelNumber);

                    var nameKey = _levelNumberFormatter.ViewNameKey(name);

                    if (usedNames.Contains(nameKey))
                    {
                        continue;
                    }
                    usedNames.Add(nameKey);

                    _progressBarHelper.ReportStatus($"Create \"{name}\"");

                    View newView = null;
                    switch (viewDuplicateType)
                    {
                        case ViewDuplicateType.ViewPlane:
                            newView = CreateViewPlane(view, _targetLevel, name);
                            TrySetBrowserParametersForViewPlane(view, newView, targetLevelNumber);
                            break;
                        case ViewDuplicateType.Schedule:
                            newView = CreateSchedules(view, _targetLevel, name);
                            TrySetBrowserParametersForSchedule(view, newView, targetLevelNumber);
                            break;
                        case ViewDuplicateType.Sheet:
                            newView = CreateSheet(view, _targetLevel, name, targetViewPlanes, targetSchedules);
                            TrySetBrowserParametersForSheet(view, newView, targetLevelNumber);
                            break;
                    }

                    output.Add(newView);
                }

                t.Commit();
            }

            return output;
        }

        private List<View> GetLevelBasedViewPlanes()
        {
            var views = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsNotElementType()
                .OfClass(typeof(ViewPlan))
                .Cast<View>()
                .ToList();

            var levelBasedViews = views
                .Where(v => _levelNumberFormatter.IsViewDependsOnLevel(v.Name))
                .OrderByDescending(v => v.Name) // Last level first
                .ToList();

            return levelBasedViews;
        }

        private List<View> GetLevelBasedSchedules()
        {
            var views = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsNotElementType()
                .OfClass(typeof(ViewSchedule))
                .Cast<View>()
                .ToList();

            var levelBasedViews = views
                .Where(v => _levelNumberFormatter.IsViewDependsOnLevel(v.Name))
                .OrderByDescending(v => v.Name) // Last level first
                .ToList();

            return levelBasedViews;
        }

        private List<View> GetLevelBasedSheets()
        {
            var views = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsNotElementType()
                .OfClass(typeof(ViewSheet))
                .Cast<View>()
                .ToList();

            var levelBasedViews = views
                .Where(v => _levelNumberFormatter.IsSheetDependsOnLevel((v as ViewSheet).SheetNumber))
                .OrderByDescending(v => (v as ViewSheet).SheetNumber) // Last level first
                .ToList();

            return levelBasedViews;
        }

        private View CreateViewPlane(View basedView, Level targetLevel, string name)
        {
            var view = ViewPlan.Create(RevitManager.Document, basedView.GetTypeId(), targetLevel.Id);
            RevitManager.Document.Regenerate(); // Regeneration of the view is mandatory, otherwise it will freeze on large projects

            view.ViewTemplateId = basedView.ViewTemplateId;
            view.Name = name;
            view.CropBoxActive = basedView.CropBoxActive;
            view.CropBox = basedView.CropBox;
            view.CropBoxVisible = basedView.CropBoxVisible;

            var scopeBoxId = basedView.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).AsElementId();
            view.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(scopeBoxId);

            return view;
        }

        private View CreateSchedules(View basedView, Level targetLevel, string name)
        {
            var scheduleId = basedView.Duplicate(ViewDuplicateOption.Duplicate);
            var schedule = RevitManager.Document.GetElement(scheduleId) as ViewSchedule;
            schedule.Name = name;

            // Update level filter
            var scheduleDefinition = schedule.Definition;
            for (int i = 0; i < scheduleDefinition.GetFilterCount(); i++)
            {
                var filter = scheduleDefinition.GetFilter(i);
                if (filter.IsElementIdValue 
                    && scheduleDefinition.GetField(filter.FieldId).ParameterId
                        .Equals(new ElementId(BuiltInParameter.SCHEDULE_LEVEL_PARAM)))
                {
                    filter.SetValue(targetLevel.Id);
                    scheduleDefinition.SetFilter(i, filter);
                }
            }

            return schedule;
        }

        private View CreateSheet(View basedView, Level targetLevel, string name, List<View> targetViewPlanes, List<View> targetSchedules)
        {
            var sheet = basedView as ViewSheet;

            var targetLevelNumber = LevelHelper.GetLevelNumber(targetLevel);
            var sheetNumber = _levelNumberFormatter.ReplaceLevelNumberInSheetNumber(sheet.SheetNumber, targetLevelNumber);

#if revit2020 || revit2021
            var newSheetId = OutdatedDuplicateSheet(sheet).Id;
#else
            var newSheetId = sheet.Duplicate(SheetDuplicateOption.DuplicateSheetWithDetailing);
#endif
            var newSheet = RevitManager.Document.GetElement(newSheetId) as ViewSheet;

            newSheet.SheetNumber = sheetNumber;
            newSheet.Name = basedView.Name;

            #region Update level based schedules
            var scheduleSheetInstances = newSheet
                .GetDependentElements(new ElementClassFilter(typeof(ScheduleSheetInstance)))
                .Select(i => RevitManager.Document.GetElement(i))
                .Cast<ScheduleSheetInstance>()
                .ToList();

            foreach (var scheduleSheetInstance in scheduleSheetInstances)
            {
                if (_progressBarHelper.CancellationTokenSource.IsCancellationRequested)
                {
                    break;
                }

                var schedule = RevitManager.Document.GetElement(scheduleSheetInstance.ScheduleId) as ViewSchedule;

                if (_levelNumberFormatter.IsViewDependsOnLevel(schedule.Name))
                {
                    var nextLevelScheduleName = _levelNumberFormatter.ReplaceLevelNumber(schedule.Name, targetLevelNumber);
                    var nextLevelSchedule = targetSchedules
                        .FirstOrDefault(s => _levelNumberFormatter.ViewNameKey(s.Name) == _levelNumberFormatter.ViewNameKey(nextLevelScheduleName));
                    if (nextLevelSchedule != null)
                    {

#if revit2020 || revit2021
                        ScheduleSheetInstance.Create(
                            RevitManager.Document, newSheetId, nextLevelSchedule.Id, scheduleSheetInstance.Point);
#else
                        ScheduleSheetInstance.Create(
                            RevitManager.Document, newSheetId, nextLevelSchedule.Id, scheduleSheetInstance.Point, scheduleSheetInstance.SegmentIndex);
#endif

                    }

                    RevitManager.Document.Delete(scheduleSheetInstance.Id);
                }
            }
#endregion

            #region Add viewports
            var viewPortIds = sheet.GetAllViewports();
            var viewPorts = viewPortIds.Select(RevitManager.Document.GetElement).Cast<Viewport>().ToList();

#if revit2020 || revit2021 || revit2022
            var placedViewIds = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsNotElementType()
                .OfClass(typeof(Viewport))
                .Cast<Viewport>()
                .Select(p => p.ViewId)
                .ToList();
#endif

            foreach (var viewPort in viewPorts)
            {
                if (_progressBarHelper.CancellationTokenSource.IsCancellationRequested)
                {
                    break;
                }

                var view = RevitManager.Document.GetElement(viewPort.ViewId) as View;

                if (_levelNumberFormatter.IsViewDependsOnLevel(view.Name))
                {
                    var nextViewName = _levelNumberFormatter.ReplaceLevelNumber(view.Name, targetLevelNumber);
                    var nextView = targetViewPlanes
                        .FirstOrDefault(v => _levelNumberFormatter.ViewNameKey(v.Name) == _levelNumberFormatter.ViewNameKey(nextViewName));

                    if (nextView != null)
                    {
                        if (Viewport.CanAddViewToSheet(RevitManager.Document, newSheetId, nextView.Id))
                        {
#if revit2020 || revit2021 || revit2022
                            if (placedViewIds.Any(vid => vid.Equals(nextView.Id)) == false)
                            {
                                var newViewport = Viewport.Create(RevitManager.Document, newSheetId, nextView.Id, viewPort.GetBoxCenter());
                                placedViewIds.Add(nextView.Id);
                            }
#else
                            if (nextView.GetPlacementOnSheetStatus() == ViewPlacementOnSheetStatus.NotPlaced) // 
                            {
                                var newViewport = Viewport.Create(RevitManager.Document, newSheetId, nextView.Id, viewPort.GetBoxCenter());
                            }
#endif
                        }
                    }
                }
            }
#endregion

            return newSheet;
        }

        private void TrySetBrowserParametersForViewPlane(View basedView, View targetView, int targetLevelNumber)
        {
            TrySetBrowserParameter(basedView, targetView, "View Size", _levelNumberFormatter, targetLevelNumber);
            TrySetBrowserParameter(basedView, targetView, "Category", _levelNumberFormatter, targetLevelNumber);
            TrySetBrowserParameter(basedView, targetView, "View Category", _levelNumberFormatter, targetLevelNumber);
        }

        private void TrySetBrowserParametersForSchedule(View basedView, View targetView, int targetLevelNumber)
        {
            TrySetBrowserParameter(basedView, targetView, "View Category", _levelNumberFormatter, targetLevelNumber);
            TrySetBrowserParameter(basedView, targetView, "View Sub Category", _levelNumberFormatter, targetLevelNumber);
        }

        private void TrySetBrowserParametersForSheet(View basedView, View targetView, int targetLevelNumber)
        {
            TrySetBrowserParameter(basedView, targetView, "Sheet Size", _levelNumberFormatter, targetLevelNumber);
            TrySetBrowserParameter(basedView, targetView, "Sheet Category", _levelNumberFormatter, targetLevelNumber);
            TrySetBrowserParameter(basedView, targetView, "Sheet Sorting", _levelNumberFormatter, targetLevelNumber);

            TrySetBrowserParameter(basedView, targetView, "Sheet Title", _levelNumberFormatter, targetLevelNumber);
            TrySetBrowserParameter(basedView, targetView, "Sheet Title (Short)", _levelNumberFormatter, targetLevelNumber);
        }

        private void TrySetBrowserParameter(
            View sourceView, View targetView, string parameterName, LevelNumberFormatter levelNumberFormatter, int levelNumber)
        {
            var sourceParameter = sourceView.GetParameters(parameterName).FirstOrDefault(p => p.Id.GetIntegerValue() > 0);
            var targetParameter = targetView.GetParameters(parameterName).FirstOrDefault(p => p.Id.GetIntegerValue() > 0);

            if (sourceParameter == null || targetParameter == null || targetParameter.IsReadOnly)
            {
                return;
            }

            var value = sourceParameter.AsString();
            if (!string.IsNullOrWhiteSpace(value))
            {
                if (levelNumberFormatter.ContainsLevel(value))
                {
                    value = levelNumberFormatter.ReplaceLevelNumber(value, levelNumber);
                }

                if (targetParameter.AsString() == value)
                {
                    return;
                }

                targetParameter.Set(value);
            }
        }

#if revit2020 || revit2021
        private ViewSheet OutdatedDuplicateSheet(ViewSheet sheet)
        {
            FamilyInstance titleblock = new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_TitleBlocks)
                .OfClass(typeof(FamilyInstance))
                .Cast<FamilyInstance>()
                .FirstOrDefault(q => q.OwnerViewId == sheet.Id);

            var titleBlockId = titleblock == null ? ElementId.InvalidElementId : titleblock.GetTypeId();

            ViewSheet newSheet = ViewSheet.Create(RevitManager.Document, titleBlockId);
            newSheet.SheetNumber = sheet.SheetNumber + " Copy";
            newSheet.Name = sheet.Name;

            ElementType noneViewportType = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsElementType()
                .OfClass(typeof(ElementType))
                .Cast<ElementType>()
                .Where(t => t.FamilyName == "Viewport")
                .FirstOrDefault(t => t.get_Parameter(BuiltInParameter.VIEWPORT_ATTR_LABEL_TAG).AsElementId().Equals(ElementId.InvalidElementId));

            foreach (var eid in sheet.GetDependentElements(new ElementClassFilter(typeof(Viewport))))
            {
                var viewport = RevitManager.Document.GetElement(eid) as Viewport;
                var view = RevitManager.Document.GetElement(viewport.ViewId) as View;

                if (view.ViewType == ViewType.Legend)
                {
                    BoundingBoxXYZ vpbb = viewport.get_BoundingBox(sheet);
                    XYZ initialCenter = (vpbb.Max + vpbb.Min) / 2;

                    Viewport newvp = Viewport.Create(RevitManager.Document, newSheet.Id, view.Id, XYZ.Zero);

                    if (noneViewportType != null)
                    {
                        newvp.ChangeTypeId(noneViewportType.Id);
                    }

                    BoundingBoxXYZ newvpbb = newvp.get_BoundingBox(newSheet);
                    XYZ newCenter = (newvpbb.Max + newvpbb.Min) / 2;

                    ElementTransformUtils.MoveElement(RevitManager.Document, newvp.Id, new XYZ(
                    initialCenter.X - newCenter.X,
                    initialCenter.Y - newCenter.Y,
                    0));
                }
            }

            foreach (ElementId scheduleSheetInstanceId in sheet.GetDependentElements(new ElementClassFilter(typeof(ScheduleSheetInstance))))
            {
                var scheduleSheetInstance = RevitManager.Document.GetElement(scheduleSheetInstanceId) as ScheduleSheetInstance;

                if (scheduleSheetInstance.IsTitleblockRevisionSchedule)
                {
                    continue;
                }

                ViewSchedule viewSchedule = RevitManager.Document.GetElement(scheduleSheetInstance.ScheduleId) as ViewSchedule;

                BoundingBoxXYZ sibb = scheduleSheetInstance.get_BoundingBox(sheet);
                XYZ initialCenter = (sibb.Max + sibb.Min) / 2;

                ScheduleSheetInstance newssi = ScheduleSheetInstance.Create(RevitManager.Document, newSheet.Id, viewSchedule.Id, XYZ.Zero);

                BoundingBoxXYZ newsibb = newssi.get_BoundingBox(newSheet);
                XYZ newCenter = (newsibb.Max + newsibb.Min) / 2;

                ElementTransformUtils.MoveElement(RevitManager.Document, newssi.Id, new XYZ(
                    initialCenter.X - newCenter.X,
                    initialCenter.Y - newCenter.Y,
                    0));
            }

            return newSheet;
        }
#endif
    }
}
