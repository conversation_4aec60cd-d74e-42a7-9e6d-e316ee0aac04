﻿using Autodesk.Revit.DB;
using System.Collections.Generic;


namespace FreeAxez.Addin.AdditionalFunctional.Frame.Models
{
    public class StraightElementPoints
    {
        public StraightElementPoints(Curve curve, int index, int totalcount)
        {
            Curve = curve;
            Index = index;
            TotalCount = totalcount;
            StartPoint = Curve.GetEndPoint(0);
            EndPoint = Curve.GetEndPoint(1);
            Curve = Line.CreateBound(StartPoint, EndPoint);
            Direction = Line.CreateBound(StartPoint, EndPoint).Direction;
        }


        private static int TotalCount { get; set; }
        public Curve Curve { get; set; }
        private int Index { get; set; }
        public XYZ StartPoint { get; set; }
        public XYZ EndPoint { get; set; }
        public XYZ Direction { get; set; }
        public Curve RebuildCurve { get; set; }
        public List<XYZ> PointsList { get; set; }


        public void CreateStartAndEndOffsetForLine(double offset)
        {
            if (TotalCount == 1)
            {
                RebuildCurve = Line.CreateBound(StartPoint, EndPoint);
            }
            else if (Index == 0)
            {
                RebuildCurve = Line.CreateBound(StartPoint, Curve.Evaluate(Curve.Length - offset, false));
            }
            else if (Index == TotalCount - 1)
            {
                RebuildCurve = Line.CreateBound(Curve.Evaluate(offset, false), EndPoint);
            }
            else
            {
                RebuildCurve = Line.CreateBound(Curve.Evaluate(offset, false), Curve.Evaluate(Curve.Length - offset, false));
            }
        }

        public void DivideLine(double moduleLength)
        {
            var points = new List<XYZ>();
            var len = moduleLength;
            while (RebuildCurve.Length > len)
            {
                points.Add(RebuildCurve.Evaluate(len, false));
                len += moduleLength;
            }
            PointsList = points;
        }
    }
}

		
