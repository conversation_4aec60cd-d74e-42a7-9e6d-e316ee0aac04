﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using System.Net.Http;
using System.Windows.Interop;

namespace FreeAxez.Addin.AdditionalFunctional.ScopeInstructions
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class ScopeInstructionsCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var httpClient = new HttpClient();
            var scopeInstructionService = new ScopeInstructionService(httpClient);
            var scopeInstructionsView = new ScopeInstructionsView();
            var scopeInstructionsViewModel = new ScopeInstructionsViewModel(scopeInstructionsView, scopeInstructionService);
            scopeInstructionsView.DataContext = scopeInstructionsViewModel;

#if revit2018
#else
            var handler = new WindowInteropHelper(scopeInstructionsView);
            handler.Owner = RevitManager.UIApplication.MainWindowHandle;
#endif

            scopeInstructionsView.ShowDialog();

            return Result.Succeeded;
        }
    }
}
