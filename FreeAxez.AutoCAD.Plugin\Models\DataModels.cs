using System.Collections.Generic;
using Newtonsoft.Json;

namespace FreeAxez.AutoCAD.Plugin.Models
{
    #region Models for Merge Layers functionality

    internal sealed class MergeLayersRequest
    {
        [JsonProperty("mappings")]        public List<LayerMapping>  Mappings  { get; set; } = new();
        [JsonProperty("freeAxezLayers")]   public List<FreeAxezLayer> FreeAxezLayers { get; set; } = new();
        [JsonProperty("linetypes")]        public List<LinetypeDto>  Linetypes { get; set; } = new();
        [JsonProperty("dryRun")]           public bool DryRun { get; set; }
    }

    internal sealed class LayerMapping
    {
        public string SourceLayer { get; set; } = string.Empty;
        public string TargetLayer { get; set; } = string.Empty;
    }

    internal sealed class FreeAxezLayer
    {
        public string Name         { get; set; } = string.Empty;
        public string Color        { get; set; } = "ByLayer";
        public string Linetype     { get; set; } = "Continuous";
        public double Lineweight   { get; set; } = 0;
        public int    Transparency { get; set; } = -1;
    }

    internal sealed class LinetypeDto
    {
        public string Name        { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string PatternRaw  { get; set; } = string.Empty;
    }

    internal class UpdateLayersRequest
    {
        [JsonProperty("mappings")]        public List<LayerMapping>  Mappings  { get; set; } = new();
        [JsonProperty("freeAxezLayers")]   public List<FreeAxezLayer> FreeAxezLayers { get; set; } = new();
        [JsonProperty("linetypes")]        public List<LinetypeDto>  Linetypes { get; set; } = new();
    }

    #endregion

    #region Data models for JSON serialization

    public class Point3D
    {
        public double x { get; set; }
        public double y { get; set; }
    }

    public class LineData
    {
        public string layer { get; set; }
        public Point3D startPoint { get; set; }
        public Point3D endPoint { get; set; }
        public double length { get; set; }
        public double angle { get; set; }
    }

    public class LineCollection
    {
        public string layer { get; set; }
        public int count { get; set; }
        public List<LineData> lines { get; set; } = new List<LineData>();
    }

    #endregion

    #region Data models for layer extraction

    public class LayerInfo
    {
        public string Name { get; set; }
        public string Color { get; set; }
        public string Linetype { get; set; }
        public string LinetypeDescription { get; set; }
        public double Lineweight { get; set; }
        public int Transparency { get; set; }
        public int TotalObjects { get; set; }

        public int ModelSpaceObjects { get; set; }
        public int PaperSpaceObjects { get; set; }
        public int BlockDefsetionObjects { get; set; }
        public bool Purgeable { get; set; }
    }

    #endregion

    #region Helper structures

    public struct LayerUsage
    {
        public int Model { get; set; }
        public int Sheets { get; set; }
        public int Blocks { get; set; }
        public int Total => Model + Sheets + Blocks;
    }

    #endregion
}
