﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Views;
using FreeAxez.Addin.Infrastructure;
using System;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class TaskManagerCommand : BaseExternalCommand
    {
        private static TaskManagerView? _taskManagerView;

        public override Result Execute()
        {
            try
            {
                if (_taskManagerView is not null && _taskManagerView.IsVisible)
                {
                    _taskManagerView.Close();
                }

                _taskManagerView = new TaskManagerView();
                _taskManagerView.DataContext = new TaskManagerViewModel(_taskManagerView);
                _taskManagerView.Show();
            }
            catch (Exception)
            {
                _taskManagerView?.Close();
                return Result.Failed;
            }

            return Result.Succeeded;
        }
    }
}
