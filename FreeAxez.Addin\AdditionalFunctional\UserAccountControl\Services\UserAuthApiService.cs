﻿using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Model;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Services
{
    public class UserAuthApiService
    {
        //private const string BaseApiUrl = "https://api-freeaxez-uat.bimsmith.com/api";
        //private static readonly string BaseApiUrl = "https://localhost:44376/api";
        private const string BaseApiUrl = "https://api-freeaxez.bimsmith.com/api";
        private readonly HttpClient _httpClient;

        public UserAuthApiService()
        {
            _httpClient = new HttpClient();
        }

        public static UserAuthApiService Instance { get; } = new UserAuthApiService();

        public async Task<HttpResponseMessage> GetTokenAsync(string email, string password)
        {
            var loginData = new
            {
                email = email,
                password = password
            };

            string jsonLogin = JsonConvert.SerializeObject(loginData);
            var content = new StringContent(jsonLogin, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{BaseApiUrl}/auth/token", content);
            return response;
        }

        public async Task<HttpResponseMessage> ForgotPassword(string email)
        {
            var data = new
            {
                email = email,
            };

            string jsonLogin = JsonConvert.SerializeObject(data);
            var content = new StringContent(jsonLogin, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{BaseApiUrl}/auth/forgotpassword", content);
            return response;
        }

        public async Task<UserInfo> GetUserInfoAsync(string token)
        {
            _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

                var response = await _httpClient.GetAsync($"{BaseApiUrl}/auth/userinfo");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var userInfo = JsonConvert.DeserializeObject<UserInfo>(responseContent);
                    return userInfo;
                } 
                
                if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    return null;
                }
                
                return null;
        }
    }
}
