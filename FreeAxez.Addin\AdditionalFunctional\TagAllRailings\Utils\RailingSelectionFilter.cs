﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.UI.Selection;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllRailings.Utils
{
    public class RailingSelectionFilter : ISelectionFilter
    {
        private List<ElementId> _railingIds; 


        public RailingSelectionFilter(List<Railing> railings)
        {
            _railingIds = railings.Select(r => r.Id).ToList();
        }


        public bool AllowElement(Element elem)
        {
            if (_railingIds.Contains(elem.Id))
            {
                return true;
            }

            return false;
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            return false;
        }
    }
}
