﻿using FreeAxez.Core.Services;
using MathNet.Numerics;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Security.Cryptography;
using System.Threading.Tasks;

namespace FreeAxez.Addin.Infrastructure
{
    public class InstructionsDownloader
    {
        private readonly string ContentPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "Anguleris Technologies", "FreeAxez", "VideoInstructions");

        private readonly HttpClient _httpClient;

        public InstructionsDownloader()
        {
            _httpClient = FreeAxezHttpClient.Instance;
        }

        public IEnumerable<Instruction> Instructions { get; private set; }

        public async Task DownloadAsync()
        {
            try
            {
                HttpResponseMessage httpResponseMessage = await _httpClient.GetAsync(
                        $@"{FreeAxezWebApiService.WebApiUrl}admin/pluginInstructions");

                if (httpResponseMessage.IsSuccessStatusCode)
                {
                    string json = await httpResponseMessage.Content.ReadAsStringAsync();

                    IEnumerable<Instruction> instructions = JsonConvert.DeserializeObject<List<Instruction>>(json);

                    await DownloadVideoIfDoesntExist(instructions);

                    Instructions = instructions;
                }
            }
            catch { }
        }

        private async Task DownloadVideoIfDoesntExist(IEnumerable<Instruction> instructions)
        {
            foreach (var instruction in instructions)
            {
                string filePath = Path.Combine(ContentPath, $"{instruction.CommandName}.mp4");

                if (File.Exists(filePath))
                {
                    string localVideoHash = CalculateVideoHash(filePath);
                    if (!string.Equals(localVideoHash, instruction.VideoHash, StringComparison.OrdinalIgnoreCase))
                    {
                        await DownloadVideoToProjectFolder(instruction, filePath);
                    }
                }
                else
                {
                    await DownloadVideoToProjectFolder(instruction, filePath);
                }
            }
        }

        private string CalculateVideoHash(string filePath)
        {
            byte[] fileBytes = File.ReadAllBytes(filePath);

            using (var md5 = MD5.Create())
            {
                byte[] hashBytes = md5.ComputeHash(fileBytes);
                return BitConverter.ToString(hashBytes).Replace("-", "");
            }
        }

        private async Task DownloadVideoToProjectFolder(Instruction instruction, string filePath)
        {
            Uri videoUri = new Uri(instruction.VideoUrl);

            using (var response = await _httpClient.GetAsync(videoUri))
            using (var fileStream = new FileStream(filePath, FileMode.Create))
            {
                if (response.IsSuccessStatusCode)
                {
                    await response.Content.CopyToAsync(fileStream);
                }
            }
        }
    }
}
