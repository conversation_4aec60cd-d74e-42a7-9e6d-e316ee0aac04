using System;
using System.Threading;
using System.Windows.Input;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Details
{
    public class ProgressWindowVm : BaseViewModel
    {
        private readonly CancellationTokenSource _cancellationTokenSource;
        private string _currentOperation;
        private int _progress;
        private int _maxProgress;
        private string _progressText;

        public ProgressWindowVm()
        {
            _cancellationTokenSource = new CancellationTokenSource();
            CancelCommand = new RelayCommand(Cancel);
            CurrentOperation = "Preparing...";
            ProgressText = "0 of 0 views copied";
        }

        public CancellationToken CancellationToken => _cancellationTokenSource.Token;

        public string CurrentOperation
        {
            get => _currentOperation;
            set
            {
                if (_currentOperation != value)
                {
                    _currentOperation = value;
                    OnPropertyChanged();
                }
            }
        }

        public int Progress
        {
            get => _progress;
            set
            {
                if (_progress != value)
                {
                    _progress = value;
                    OnPropertyChanged();
                    UpdateProgressText();
                }
            }
        }

        public int MaxProgress
        {
            get => _maxProgress;
            set
            {
                if (_maxProgress != value)
                {
                    _maxProgress = value;
                    OnPropertyChanged();
                    UpdateProgressText();
                }
            }
        }

        public string ProgressText
        {
            get => _progressText;
            set
            {
                if (_progressText != value)
                {
                    _progressText = value;
                    OnPropertyChanged();
                }
            }
        }

        public ICommand CancelCommand { get; }

        private void Cancel(object parameter)
        {
            _cancellationTokenSource.Cancel();
        }

        private void UpdateProgressText()
        {
            if (MaxProgress > 0)
            {
                var percentage = (double)Progress / MaxProgress * 100;
                ProgressText = $"{Progress} of {MaxProgress} views copied ({percentage:F0}%)";
            }
            else
            {
                ProgressText = "0 of 0 views copied";
            }
        }

        public void UpdateProgress(int current, int total, string currentViewName)
        {
            Progress = current;
            MaxProgress = total;
            CurrentOperation = string.IsNullOrEmpty(currentViewName)
                ? "Preparing..."
                : $"Copying: {currentViewName}";
        }
    }
}
