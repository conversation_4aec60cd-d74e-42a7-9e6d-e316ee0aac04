﻿using System;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;

public class FamilyPropertiesComparer
{
    public static bool IsVersionGreater(string currentVersion, string newVersion)
    {
        var currentParts = ExtractVersionParts(currentVersion);
        var newParts = ExtractVersionParts(newVersion);

        if (currentParts.year != newParts.year) return newParts.year > currentParts.year;

        if (currentParts.major != newParts.major) return newParts.major > currentParts.major;

        return newParts.minor > currentParts.minor;
    }

    public static bool IsVersionSame(string clientVersion, string serverVersion)
    {
        var sanitizedClientVersion = clientVersion.Replace("\"", "");
        var sanitizedServerVersion = serverVersion.Replace("\"", "");
        return string.Equals(sanitizedClientVersion, sanitizedServerVersion, StringComparison.OrdinalIgnoreCase);
    }

    public static (int year, int major, int minor) ExtractVersionParts(string version)
    {
        var sanitizedVersion = version.Replace("\"", "");
        var parts = sanitizedVersion.Split(new[] { ' ', 'v', '.' }, StringSplitOptions.RemoveEmptyEntries);

        int.TryParse(parts.Length > 0 ? parts[0] : "0", out var year);
        int.TryParse(parts.Length > 1 ? parts[1] : "0", out var major);
        var minor = 0;

        if (parts.Length > 2)
        {
            var minorPart = parts[2];
            var numericPart = new string(minorPart.TakeWhile(char.IsDigit).ToArray());
            int.TryParse(numericPart, out minor);
        }

        return (year, major, minor);
    }

    public static bool IsManufacturerFreeAxez(string manufacturer)
    {
        string trimmedInput = manufacturer.Trim('\'', '\"');
        return trimmedInput.Equals("FreeAxez", StringComparison.OrdinalIgnoreCase);
    }
}