﻿using FreeAxez.Addin.AdditionalFunctional.ExportCAD.Models;
using FreeAxez.Addin.AdditionalFunctional.ExportCAD.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace FreeAxez.Addin.AdditionalFunctional.ExportCAD.Views
{
    /// <summary>
    /// Interaction logic for ExportCADView.xaml
    /// </summary>
    public partial class ExportCADView : Window
    {
        public ExportCADView()
        {
            InitializeComponent();
            DataContext = new ExportCADViewModel();            
        }

        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            foreach (RevitView revitView in dataGrid.SelectedItems)
            {
                revitView.IsCheck = (bool)(sender as <PERSON>B<PERSON>).IsChecked;
            }
        }
    }
}
