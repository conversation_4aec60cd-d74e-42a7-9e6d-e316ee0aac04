﻿using NPOI.SS.UserModel;
using SixLabors.ImageSharp;
using System.Collections.Generic;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Spreadsheets
{
    public class OrderCellStyle
    {
        public HorizontalAlignment? Alignment { get; set; }
        public Color? Foreground { get; set; }
        public string DataFormat { get; set; }
        public HashSet<Border> Borders { get; set; } = new HashSet<Border>();
        public bool FontBold { get; set; }


        public override bool Equals(object obj)
        {
            if (!(obj is OrderCellStyle))
            {
                return false;
            }

            var other = obj as OrderCellStyle;

            if (Alignment == other.Alignment &&
                Foreground == other.Foreground &&
                DataFormat == DataFormat &&
                Borders.SetEquals(other.Borders) &&
                FontBold == other.FontBold)
            {
                return true;
            }
            return false;
        }

        public override int GetHashCode()
        {
            unchecked
            {
                int hash = 17;
                hash = hash * 23 + Alignment?.GetHashCode() ?? 0;
                hash = hash * 23 + Foreground?.GetHashCode() ?? 0;
                hash = hash * 23 + (DataFormat?.GetHashCode() ?? 0);
                hash = hash * 23 + FontBold.GetHashCode();

                if (Borders != null)
                {
                    foreach (var border in Borders)
                    {
                        hash = hash * 23 + border.GetHashCode();
                    }
                }

                return hash;
            }
        }
    }
}
