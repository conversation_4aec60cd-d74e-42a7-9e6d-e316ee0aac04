namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Constants
{
    /// <summary>
    /// Constants for Low Voltage Path functionality
    /// </summary>
    public static class LowVoltageConstants
    {
        #region Parameter Names

        /// <summary>
        /// Parameter name for wire quantity in railings and annotations
        /// </summary>
        public const string QuantityParameterName = "Quantity";

        /// <summary>
        /// Parameter name for sheet title
        /// </summary>
        public const string SheetTitleParameterName = "Sheet Title";

        #endregion

        #region Family Names

        /// <summary>
        /// Family name for low voltage pathway arrow annotations
        /// </summary>
        public const string AnnotationFamilyName = "FA-Low_Voltage_Pathway_Arrow_Annotation";

        #endregion

        #region Regex Patterns

        /// <summary>
        /// Regex pattern for matching low voltage line styles (LV1, LV_4, MC - 2, etc.)
        /// </summary>
        public const string LowVoltageLinePattern = @"(LV|MC)[\s-_]*\d";

        /// <summary>
        /// Regex pattern for matching low voltage count parameters (LV1 - Count, MC 2 - Count, etc.)
        /// </summary>
        public const string LowVoltageCountParameterPattern = @"^(LV|MC)[\s-_]*\d[\s-_]*Count$";

        #endregion

        #region Tolerances and Distances

        /// <summary>
        /// Tolerance for annotation placement near railings (1 inch in feet)
        /// </summary>
        public const double AnnotationToleranceFeet = 0.0833334;

        /// <summary>
        /// Distance tolerance for outlet-to-line association (2 feet)
        /// </summary>
        public const double OutletToLineToleranceFeet = 2.0;

        #endregion

        #region Default Values

        /// <summary>
        /// Default wire count for outlets when parameter is not found
        /// </summary>
        public const int DefaultWireCount = 1;

        #endregion

        #region File Paths

        /// <summary>
        /// Default export path for geometry debugging
        /// </summary>
        public const string DefaultGeometryExportPath = @"C:\Users\<USER>\Desktop\geometry.json";

        #endregion
    }
}
