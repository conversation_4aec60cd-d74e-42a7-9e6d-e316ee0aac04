﻿using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils
{
    public static class OptionsManager
    {
        public static ViewHandlingOption ViewHandlingOption { get; set; } = ViewHandlingOption.Combine;
        public static ScheduleHandlingOption ScheduleHandlingOption { get; set; } = ScheduleHandlingOption.Combine;
        public static bool CopyRevisions { get; set; } = true;
        public static bool CopyDimensions { get; set; } = true;
        public static bool CopyTagsAndTextNotes { get; set; } = true;
        public static bool CopyDetailLinesAndRegions { get; set; } = true;
    }
}
