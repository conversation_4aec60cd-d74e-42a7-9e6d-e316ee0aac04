﻿using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using System.ComponentModel;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Navigation;

namespace FreeAxez.Addin.AdditionalFunctional.ScopeInstructions
{
    public partial class ScopeInstructionsView : Window
    {
        public ScopeInstructionsView()
        {
            InitializeComponent();
        }

        private void Hyperlink_RequestNavigate(object sender, RequestNavigateEventArgs e)
        {
            try
            {
                var startInfo = new ProcessStartInfo()
                {
                    FileName = e.Uri.AbsoluteUri,
                    UseShellExecute = true
                };

                Process.Start(startInfo);
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog("Error Opening File", "Failed to open the exported file: " + ex.Message, MessageType.Error);
            }

            e.Handled = true;
        }

        private void GridViewColumnHeaderClickedHandler(object sender, RoutedEventArgs e)
        {
            if (e.OriginalSource is GridViewColumnHeader gridViewColumnHeader)
            {
                ICollectionView collectionView = CollectionViewSource.GetDefaultView(scopeInstructions.ItemsSource);

                if (collectionView.SortDescriptions.Count > 0)
                {
                    if (collectionView.SortDescriptions[0].Direction == ListSortDirection.Ascending)
                    {
                        collectionView.SortDescriptions[0] = new SortDescription("ScopeName", ListSortDirection.Descending);
                    }
                    else
                    {
                        collectionView.SortDescriptions[0] = new SortDescription("ScopeName", ListSortDirection.Ascending);
                    }
                }
                else
                {
                    collectionView.SortDescriptions.Add(new SortDescription("ScopeName", ListSortDirection.Ascending));
                }
            }
        }
    }
}
