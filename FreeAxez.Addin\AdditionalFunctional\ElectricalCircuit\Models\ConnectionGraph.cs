﻿namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Models
{
    public class ConnectionGraph
    {
        private Dictionary<CircuitElement, List<CircuitElement>> _connections = new Dictionary<CircuitElement, List<CircuitElement>>();

        public void AddConnection(CircuitElement source, CircuitElement target)
        {
            if (!_connections.ContainsKey(source))
                _connections[source] = new List<CircuitElement>();

            if (!_connections.ContainsKey(target))
                _connections[target] = new List<CircuitElement>();

            if (!_connections[source].Contains(target))
                _connections[source].Add(target);

            if (!_connections[target].Contains(source))
                _connections[target].Add(source);
        }

        public int GetConnectionCount(CircuitElement circuitElement)
        {
            return _connections.ContainsKey(circuitElement) ? _connections[circuitElement].Count : 0;
        }

        public List<CircuitElement> GetConnectedElements(CircuitElement circuitElement)
        {
            return _connections.ContainsKey(circuitElement) ? _connections[circuitElement] : new List<CircuitElement>();
        }
    }
}
