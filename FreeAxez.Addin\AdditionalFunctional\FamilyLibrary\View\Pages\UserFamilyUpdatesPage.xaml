<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Pages.UserFamilyUpdatesPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             xmlns:pages="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages"
             xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls"
             mc:Ignorable="d"
             d:DesignHeight="900"
             d:DesignWidth="900">
    <UserControl.Resources>
        <ResourceDictionary>
            <converters:FamilyStatusToStringConverter x:Key="FamilyStatusToStringConverter" />
            <converters:FamilyStatusToVisibilityConverter x:Key="FamilyStatusToVisibilityConverter" />
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" />
            <converters:RemoveQuotesConverter x:Key="RemoveQuotesConverter" />
            <converters:AsyncImageConverter x:Key="AsyncImageConverter" />
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <UserControl.DataContext>
        <pages:FamilyUpdatesPageVm />
    </UserControl.DataContext>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <controls:HeaderBar PageName="Family Updates"/>
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <DockPanel Margin="10 0"
                       VerticalAlignment="Center">
                <ComboBox HorizontalAlignment="Left"
                          Style="{StaticResource ComboBoxStyleFilter}"
                          Tag="Categories"
                          Width="200"
                          ItemsSource="{Binding Categories}"
                          SelectedItem="{Binding SelectedCategory}"
                          DisplayMemberPath="CategoryName"/>
                <ComboBox Style="{StaticResource ComboBoxStyleFilter}"
                          Tag="Revit version"
                          Width="200"
                          Margin="10 0"
                          ItemsSource="{Binding RevitVersions}"
                          SelectedItem="{Binding SelectedRevitVersion}"
                          HorizontalAlignment="Left"/>
                <Button Width="80"
                        FontWeight="Normal"
                        Height="25"
                        Style="{StaticResource ButtonOutlinedRed}"
                        Command="{Binding ResetFiltersCommand}"
                        HorizontalAlignment="Right">
                    X Clear All
                </Button>
            </DockPanel>
            <DataGrid
             Style="{DynamicResource DataGridWithoutBorders}"
             Background="#F8F9FF"
             ColumnHeaderStyle="{DynamicResource DataGridColumnHeader}"
             ItemsSource="{Binding Families}"
             HorizontalScrollBarVisibility="Hidden"
             Grid.Row="1"
             AutoGenerateColumns="False"
             CanUserDeleteRows="False"
             CanUserResizeColumns="True"
             Margin="10,0"
             CanUserAddRows="False"
             CanUserReorderColumns="False"
             HeadersVisibility="Column"
             SelectionMode="Single"
             SelectionUnit="FullRow"
             IsReadOnly="True"
             x:Name="DfTypes">
                <DataGrid.Columns>
                    <DataGridTemplateColumn Header="Image"
                                            Width="SizeToCells"
                                            IsReadOnly="True">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Image Width="80"
                                       Height="80"
                                       Stretch="Uniform">
                                    <Image.Source>
                                        <Binding Path="FamilyImagePath"
                                                 Converter="{StaticResource AsyncImageConverter}">
                                            <Binding.FallbackValue>
                                                <BitmapImage UriSource="/FamiliesLibrary;component/Assets/noImage.png" />
                                            </Binding.FallbackValue>
                                        </Binding>
                                    </Image.Source>
                                </Image>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Name"
                                            Width="*"
                                            MinWidth="100"
                                            SortMemberPath="ProductName">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding ProductName, Converter={StaticResource RemoveQuotesConverter}}"
                                           TextWrapping="Wrap"
                                           FontWeight="SemiBold" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="File Name"
                                            Width="*"
                                            MinWidth="120"
                                            SortMemberPath="Name">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Name}"
                                           TextWrapping="Wrap"
                                           FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Version"
                                            Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Version, Converter={StaticResource RemoveQuotesConverter}}"
                                           TextWrapping="Wrap"
                                           FontWeight="SemiBold"
                                           ToolTip="{Binding DateCreated}"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Revit"
                                            Width="80"
                                            CanUserSort="False">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border Background="#1670aa"
                                        CornerRadius="5"
                                        Padding="10 0"
                                        Height="20"
                                        BorderThickness="0"
                                        VerticalAlignment="Center"
                                        HorizontalAlignment="Center">
                                    <TextBlock Text="{Binding RevitVersion}"
                                            Foreground="White"
                                            FontWeight="Bold"
                                            VerticalAlignment="Center"
                                            HorizontalAlignment="Center" />
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Version Notes"
                                            Width="*"
                                            MinWidth="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding ChangesDescription}"
                                           TextWrapping="Wrap"
                                           FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Category"
                                            Width="120"
                                            SortMemberPath="Category.CategoryName">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Category.CategoryName}"
                                           TextWrapping="Wrap"
                                           FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Created By"
                                            Width="120"
                                            SortMemberPath="CreatedBy">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding CreatedBy}"
                                           TextWrapping="Wrap"
                                           FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Updated"
                                            Width="100"
                                            SortMemberPath="LastDateUpdated">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding LastDateUpdated}"
                                           TextWrapping="Wrap"
                                           FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <DataGridTemplateColumn Header="Status"
                                            Width="100"
                                            SortMemberPath="Status">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Status, Converter={StaticResource FamilyStatusToStringConverter}}"
                                           TextWrapping="Wrap"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center" >
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Status}"
                                                             Value="NotPresentInProject">
                                                    <Setter Property="Foreground"
                                                            Value="#D32F2F"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}"
                                                             Value="OutdatedVersionInProject">
                                                    <Setter Property="Foreground"
                                                            Value="#FF9800"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}"
                                                             Value="CurrentVersionInProject">
                                                    <Setter Property="Foreground"
                                                            Value="#388E3C"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Actions"
                                            Width="*">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Margin="5 5"
                                            Command="{Binding DataContext.DownloadToRevitCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                            CommandParameter="{Binding}"
                                            Visibility="{Binding Status, Converter={StaticResource FamilyStatusToVisibilityConverter}}"
                                            IsEnabled="{Binding DataContext.IsLoading, Converter={StaticResource InverseBooleanConverter}, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                            Style="{StaticResource RoundIconButton}">
                                        <ContentControl Template="{StaticResource LoadToProjectIcon}" HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
                </DataGrid>
        </Grid>
    </Grid>
</UserControl>
