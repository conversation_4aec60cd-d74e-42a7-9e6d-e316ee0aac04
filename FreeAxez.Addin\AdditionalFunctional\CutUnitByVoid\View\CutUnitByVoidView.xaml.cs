﻿using System.Windows;

namespace FreeAxez.Addin.AdditionalFunctional.CutUnitByVoid.View
{
    public partial class CutUnitByVoidView : Window
    {
        public CutUnitByVoidView()
        {
            InitializeComponent();
        }

        private void SelectCuttingInstance_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
