﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.WhipExport.Views.WhipExportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:FreeAxez.Addin.AdditionalFunctional.WhipExport.ViewModels"
        MinHeight="500" MinWidth="500"
        Height="690"
        SizeToContent="Width"
        WindowStartupLocation="CenterScreen"
        Title="Check Whip">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Window.DataContext>
        <vm:WhipExportViewModels/>
    </Window.DataContext>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <DataGrid Grid.Row="0" Name="grid" AutoGenerateColumns="False" HeadersVisibility="Column" 
                  CanUserDeleteRows="False" CanUserAddRows="False" EnableRowVirtualization="True" IsReadOnly="True"
                  Style="{StaticResource DataGridWithBorders}" ItemsSource="{Binding Whips}">
            <DataGrid.Columns>
                <DataGridTemplateColumn Header="Id" SortMemberPath="Id">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="{Binding Id}" 
                                    Command="{Binding DataContext.IdCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                    CommandParameter="{Binding Id}"
                                    Style="{StaticResource ButtonOutlinedBlue}" 
                                    Height="25"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="Level" Binding="{Binding Level}"/>
                <DataGridTextColumn Header="Track Assignment" Binding="{Binding TrackAssignment}" />
                <DataGridTextColumn Header="Type Name" Binding="{Binding TypeName}"/>
                <DataGridTextColumn Header="Projection Length" Binding="{Binding ProjectionLength}"/>
                <DataGridTextColumn Header="Full Length" Binding="{Binding FullLength}"/>
                <DataGridTextColumn Header="Type Length" Binding="{Binding TypeLength}"/>
                <DataGridTextColumn Header="Correct Type Length" Binding="{Binding CorrectTypeLength}">
                    <DataGridTextColumn.CellStyle>
                        <Style TargetType="DataGridCell" BasedOn="{StaticResource DataGridCellStyle}">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding CorrectTypeLength}" Value="True">
                                    <Setter Property="Foreground" Value="{StaticResource Green600}"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding CorrectTypeLength}" Value="False">
                                    <Setter Property="Foreground" Value="{StaticResource Red600}"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGridTextColumn.CellStyle>
                </DataGridTextColumn>
            </DataGrid.Columns>
        </DataGrid>
        <StackPanel Grid.Row="1" Margin="0,10,1,0" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="Export" 
                    Command="{Binding ExportCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}" 
                    Style="{StaticResource ButtonOutlinedBlue}"
                    Margin="0,0,10,0" 
                    Height="35"  
                    Width="100"/>
            <Button Content="Close" 
                    Command="{Binding CloseCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}" 
                    Style="{StaticResource ButtonOutlinedRed}" 
                    Height="35"
                    Width="100"/>
        </StackPanel>
    </Grid>
</Window>