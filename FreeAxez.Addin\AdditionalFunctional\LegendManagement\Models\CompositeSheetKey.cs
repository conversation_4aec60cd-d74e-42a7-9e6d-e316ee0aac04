﻿namespace FreeAxez.Addin.AdditionalFunctional.LegendManagement.Models
{
    public class CompositeSheetKey
    {
        public CompositeSheetKey(SheetSize sheetSize, SheetSorting sheetSorting, Sheet sheet)
        {
            SheetSize = sheetSize;
            SheetSorting = sheetSorting;
            Sheet = sheet;
        }

        public SheetSize SheetSize { get; }
        public SheetSorting SheetSorting { get; }
        public Sheet Sheet { get; }

        public override bool Equals(object obj)
        {
            if (obj is CompositeSheetKey compositeSheetKey)
            {
                return SheetSize.Equals(compositeSheetKey.SheetSize) &&
                       SheetSorting.Equals(compositeSheetKey.SheetSorting) &&
                       Sheet.Equals(compositeSheetKey.Sheet);
            }
            
            return false;
        }

        public override int GetHashCode()
        {
            int hash = 17;

            hash = hash * 31 + SheetSize.GetHashCode();
            hash = hash * 31 + SheetSorting.GetHashCode();
            hash = hash * 31 + Sheet.GetHashCode();

            return hash;
        }
    }
}
