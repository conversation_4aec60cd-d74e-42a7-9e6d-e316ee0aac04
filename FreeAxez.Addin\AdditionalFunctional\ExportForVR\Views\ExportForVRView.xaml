﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.ExportForVR.Views.ExportForVRView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        MinHeight="450" MinWidth="500"
        Height="480" Width="500"
        WindowStartupLocation="CenterScreen"
        Title="Export For VR">
    <Grid Margin="5">
        <Grid.RowDefinitions>
            <RowDefinition Height="30"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="33"/>
            <RowDefinition/>
            <RowDefinition Height="35"/>
        </Grid.RowDefinitions>
        <DockPanel Margin="0,5,0,0" Grid.Row="0" LastChildFill="True">
            <Label Width="130" HorizontalContentAlignment="Left"  DockPanel.Dock="Left" Content="Folder to save:" VerticalAlignment="Center"/>
            <Button Margin="5,0,0,0" DockPanel.Dock="Right" Content="Browse" Width="60" Command="{Binding BrowseCommand}"/>
            <TextBox VerticalContentAlignment="Center" Text="{Binding FolderPath}"/>
        </DockPanel>
        <DockPanel Margin="0,5,0,0" Grid.Row="1" LastChildFill="True">
            <Label Width="130" HorizontalContentAlignment="Left"  DockPanel.Dock="Left" Content="Lightweight Families:" VerticalAlignment="Center"/>
            <Button Margin="5,0,0,0" DockPanel.Dock="Right" Content="Browse" Width="60" Command="{Binding BrowseLightweightCommand}"/>
            <TextBox VerticalContentAlignment="Center" Text="{Binding LightweightFamiliesPath}"/>
        </DockPanel>
        <StackPanel Orientation="Horizontal" Margin="5,5,0,0" Grid.Row="2">
            <CheckBox VerticalAlignment="Center" IsChecked="{Binding CopyFurniture}"/>
            <TextBlock Margin="5,0,0,0" Text="Copy the furniture from the linked files" VerticalAlignment="Center"/>
        </StackPanel>
        <DataGrid Margin="0,10,0,0" x:Name="dataGrid" Grid.Row="3" ItemsSource="{Binding Levels}" AutoGenerateColumns="False" CanUserAddRows="False" HeadersVisibility="Column" SelectionUnit="FullRow" SelectionMode="Extended">
            <DataGrid.Columns>
                <DataGridTemplateColumn Header="Export" SortMemberPath="IsCheck">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <CheckBox HorizontalAlignment="Center" IsChecked="{Binding IsCheck, UpdateSourceTrigger=PropertyChanged}" Checked="CheckBox_Checked" Unchecked="CheckBox_Checked"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Binding="{Binding Name}" Header="Level" MinWidth="200" IsReadOnly="True"/>
            </DataGrid.Columns>
        </DataGrid>
        <StackPanel Margin="0,5,0,0" Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="Export" Width="100" Command="{Binding ExportCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
            <Button Margin="5,0,0,0" Content="Cancel" Width="100" Command="{Binding CancelCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
        </StackPanel>
    </Grid>
</Window>
