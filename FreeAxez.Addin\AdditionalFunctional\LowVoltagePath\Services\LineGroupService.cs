using NetTopologySuite.Geometries;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services;

public class LineGroupService
{
    private const double TOLERANCE = 2.0 / 12.0; // 2 inches

    public List<List<LineString>> GroupLines(List<LineString> lines)
    {
        var groups = new List<List<LineString>>();
        var visited = new bool[lines.Count];

        for (int i = 0; i < lines.Count; i++)
        {
            if (!visited[i])
            {
                var newGroup = new List<LineString>();
                var queue = new Queue<int>();

                visited[i] = true;
                queue.Enqueue(i);
                newGroup.Add(lines[i]);

                while (queue.Count > 0)
                {
                    var currentIndex = queue.Dequeue();
                    var currentLine = lines[currentIndex];

                    for (int j = 0; j < lines.Count; j++)
                    {
                        if (!visited[j])
                        {
                            if (currentLine.IsWithinDistance(lines[j], TOLERANCE))
                            {
                                visited[j] = true;
                                queue.Enqueue(j);
                                newGroup.Add(lines[j]);
                            }
                        }
                    }
                }
                groups.Add(newGroup);
            }
        }
        return groups;
    }
}
