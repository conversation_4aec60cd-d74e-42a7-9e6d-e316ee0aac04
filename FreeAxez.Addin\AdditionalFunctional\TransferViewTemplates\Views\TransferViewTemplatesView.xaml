﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Views.TransferViewTemplatesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:viewModel="clr-namespace:FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.ViewModels"
             mc:Ignorable="d" 
             Width="800"
             Height="800"
             WindowStartupLocation="CenterScreen"
             Title="Transfer View Templates">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Window.DataContext>
        <viewModel:TransferViewTemplatesViewModel/>
    </Window.DataContext>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height ="Auto"/>
            <RowDefinition Height ="*"/>
            <RowDefinition Height ="Auto"/>
        </Grid.RowDefinitions>
        <StackPanel>
            <TextBlock Margin="0 15 0 5"
                Style="{StaticResource TextH5}"
                Text="Transfer View Templates From Selected File"/>
            <Grid >
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="100"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"></RowDefinition>
                    <RowDefinition Height="*"></RowDefinition>
                </Grid.RowDefinitions>
                <TextBox Grid.Column="0"
                         Grid.Row="0"
                         Tag="Select Source Revit File"
                         Style="{StaticResource UiTextBox}"
                         IsReadOnly="True"
                         Text="{Binding SourceFileViewModel.SourceFilePath}"
                />
                <Button Grid.Column="2"
                        Grid.Row="0"
                        Content="Select..." 
                        Style="{StaticResource ButtonOutlinedGreen}"
                        Command="{Binding SelectSourceFileCommand}"
                />
                <CheckBox Grid.Row="1" 
                          Grid.Column="0" 
                          Grid.ColumnSpan="2"
                          Style="{StaticResource CheckBoxStyle}"
                          Margin="0 10 0 0"
                          IsChecked="{Binding SourceFileViewModel.IncludeLinkedDocuments, Mode=TwoWay}"
                          >Include Linked Documents</CheckBox>
            </Grid>
            <StackPanel Margin="0 15">
                <TextBlock Style="{StaticResource TextH5}" Text="Action For Duplicated View Template Names" Margin="0 5"/>
                <RadioButton 
                    Content="Rename Existing To 'TemplateName Old'."
                    IsChecked="{Binding SourceFileViewModel.SaveCurrentViewTemplateAsOld, Mode=TwoWay}"
                />
                <RadioButton 
                    Content="Delete Existing."
                    IsChecked="{Binding SourceFileViewModel.DeleteCurrentViewTemplate, Mode=TwoWay}"
                >
                </RadioButton>
            </StackPanel>
            <TextBox x:Name="SearchTextBox" 
                     Tag="Search..."
                     VerticalContentAlignment="Center" 
                     Text="{Binding SourceFileViewModel.SearchText, UpdateSourceTrigger=PropertyChanged}"
                     Style="{StaticResource Search}"/>
            <Grid Margin="0 10 0 0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="120"/>
                </Grid.ColumnDefinitions>
                <ComboBox Grid.Column="0"
                          Style="{StaticResource ComboBoxStyleFilter}" 
                          Tag="Discipline"
                          VerticalAlignment="Center"
                          SelectedItem="{Binding SourceFileViewModel.SelectedDiscipline}"
                          ItemsSource="{Binding SourceFileViewModel.Disciplines}"/>

                <ComboBox Grid.Column="2" 
                          Style="{StaticResource ComboBoxStyleFilter}" 
                          Tag="View Type" 
                          VerticalAlignment="Center"
                          SelectedItem="{Binding SourceFileViewModel.SelectedViewType}"
                          ItemsSource="{Binding SourceFileViewModel.ViewTypes}"/>

                <ComboBox Grid.Column="4"
                          Style="{StaticResource ComboBoxStyleFilter}" 
                          Tag="Document"
                          VerticalAlignment="Center"
                          SelectedItem="{Binding SourceFileViewModel.SelectedDocumentName}"
                          ItemsSource="{Binding SourceFileViewModel.DocumentNames}"/>
                <Button Grid.Column="6" 
                        Content="X Reset Filters" 
                        Command="{Binding SourceFileViewModel.ResetFiltersCommand}"
                        Style="{StaticResource ButtonOutlinedRed}" 
                        VerticalAlignment="Center"/>
            </Grid>
        </StackPanel>

        <DataGrid AutoGenerateColumns="False"
          ItemsSource="{Binding SourceFileViewModel.RevitViewTemplates}"
          SelectionMode="Extended"
          SelectionUnit="FullRow"
          CanUserAddRows="False"
          CanUserDeleteRows="False"
          CanUserReorderColumns="False"
          IsReadOnly="True"
          RowHeaderWidth="0" 
          Style="{StaticResource DataGridWithoutBorders}"
          ColumnHeaderStyle="{DynamicResource DataGridColumnHeader}"
          Margin="0,10"
          Grid.Row="1">
            <DataGrid.RowStyle>
                <Style TargetType="DataGridRow">
                    <EventSetter Event="PreviewMouseLeftButtonDown" Handler="DataGridRow_PreviewMouseLeftButtonDown"/>
                </Style>
            </DataGrid.RowStyle>
            <DataGrid.Columns>
                <DataGridTemplateColumn Header=""  Width="40" CanUserSort="False">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <CheckBox Style="{StaticResource CheckBoxStyle}" 
                                      VerticalAlignment="Center"
                                      HorizontalAlignment="Center"
                                      IsChecked="{Binding IsChecked, UpdateSourceTrigger=PropertyChanged,Mode=TwoWay}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Binding="{Binding Discipline}"
                            Header="Discipline"
                            Width="Auto"/>
                <DataGridTextColumn Binding="{Binding ViewType}"
                            Header="View Type"
                            Width="Auto"/>
                <DataGridTextColumn Binding="{Binding Name}"
                            Header="Name"
                            Width="200"/>
                <DataGridTextColumn Binding="{Binding RevitDocument.Title, Mode=OneWay}"
                            Header="Document Name"
                            Width="*"/>
            </DataGrid.Columns>
        </DataGrid>
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Button Grid.Column="0"
                Style="{StaticResource ButtonOutlinedGreen}"
                Command="{Binding CheckAllCommand}"
                >
                <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource CircleCheckIcon}" 
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                    <TextBlock Text="Check All"
                               Margin="5 0"
                               Foreground="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            <Button Grid.Column="2"
                    Style="{StaticResource ButtonOutlinedPurple}"
                    Command="{Binding UncheckAllCommand}"
            >
                <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource CircleUncheckIcon}" 
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                    <TextBlock Text="Uncheck All"
                               Foreground="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}"
                               Margin="5 0"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            <Button  Grid.Column="4"
                     Style="{StaticResource ButtonSimpleRed}"
                     Content="Cancel"
                     Command="{Binding CancelCommand}"
                     CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"
            />
            <Button  Grid.Column="6"
                     Style="{StaticResource ButtonSimpleBlue}"
                     IsEnabled="{Binding SourceFileViewModel.IsAnyTemplateChecked}"
                     Command="{Binding CopyViewTemplates}"
                     CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"
            >
                <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource CopyFileIcon}" 
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                    <TextBlock Text="Copy Templates"
                               Margin="5 0"
                               Foreground="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </Grid>
    </Grid>
</Window>

