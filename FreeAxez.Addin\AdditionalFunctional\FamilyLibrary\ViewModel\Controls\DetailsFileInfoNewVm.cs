using System;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;
using FreeAxez.Addin.Infrastructure;
using Microsoft.Win32;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;

public class DetailsFileInfoNewVm : BaseViewModel
{
    private readonly AdminDetailsBaseVm _parentVm;
    private LibraryItemDetailsDto _detailsItem;
    private byte[] _fileBytes;
    private BitmapSource _filePreview;
    private string _fileSize;
    private string _revitVersion;
    private bool _showRejectButton = true;
    private int _uploadProgress;

    public DetailsFileInfoNewVm(LibraryItemDetailsDto detailsItem, AdminDetailsBaseVm parentVm)
    {
        _detailsItem = detailsItem;
        _parentVm = parentVm;

        ChooseImageCommand = new RelayCommand(param => ChooseImage());
        RejectFileCommand = new RelayCommand(param => RejectFile());
    }

    public ICommand ChooseImageCommand { get; }
    public ICommand RejectFileCommand { get; }

    public LibraryItemDetailsDto DetailsItem
    {
        get => _detailsItem;
        set
        {
            _detailsItem = value;
            OnPropertyChanged();
        }
    }

    public string FileName => _detailsItem?.Name;
    public string Name => _detailsItem?.Name;
    public string FileType => _detailsItem?.FileType;

    public string RevitVersion
    {
        get => _revitVersion ?? "2024";
        set
        {
            _revitVersion = value;
            OnPropertyChanged();
        }
    }

    public string CreatedBy => _detailsItem?.CreatedBy;
    public string DateCreated => _detailsItem?.DateCreated.ToString("yyyy-MM-dd HH:mm");
    public string LastDateUpdated => _detailsItem?.LastDateUpdated.ToString("yyyy-MM-dd HH:mm");

    public string Description
    {
        get => _detailsItem?.Description;
        set
        {
            if (_detailsItem != null)
            {
                _detailsItem.Description = value;
                OnPropertyChanged();
            }
        }
    }

    public BitmapSource FilePreview
    {
        get => _filePreview;
        set
        {
            _filePreview = value;
            OnPropertyChanged();
        }
    }

    public string FileSize
    {
        get => _fileSize;
        set
        {
            _fileSize = value;
            OnPropertyChanged();
        }
    }

    public byte[] FileBytes
    {
        get => _fileBytes;
        set
        {
            _fileBytes = value;
            OnPropertyChanged();
        }
    }

    public int UploadProgress
    {
        get => _uploadProgress;
        set
        {
            _uploadProgress = value;
            OnPropertyChanged();
        }
    }

    public bool ShowRejectButton
    {
        get => _showRejectButton;
        set
        {
            _showRejectButton = value;
            OnPropertyChanged();
        }
    }

    public void RejectFile()
    {
        if (_parentVm.RejectFileCommand.CanExecute(this)) 
            _parentVm.RejectFileCommand.Execute(this);
    }

    private void ChooseImage()
    {
        var openFileDialog = new OpenFileDialog
        {
            Filter = "Image files (*.png;*.jpeg;*.jpg)|*.png;*.jpeg;*.jpg",
            Title = "Select an Image"
        };

        if (openFileDialog.ShowDialog() == true)
        {
            var imageUri = new Uri(openFileDialog.FileName, UriKind.Absolute);
            var image = new BitmapImage(imageUri);

            FilePreview = image;
        }
    }
}
