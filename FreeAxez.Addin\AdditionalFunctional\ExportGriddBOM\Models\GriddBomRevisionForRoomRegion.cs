﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Enums;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Utils;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.ViewModels;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.GeometryUtils;
using FreeAxez.Addin.Infrastructure.GeometryUtils.FreeAxez.Addin.Infrastructure.GeometryUtils;
using FreeAxez.Addin.Models.Base;
using FreeAxez.Addin.Utils;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Models
{
    public class GriddBomRevisionForRoomRegion
    {
        private readonly RevisionViewModel _revision;
        private readonly List<string> _selectedAccessoryNames;
        private readonly Element _selectedElement;
        private readonly SelectionOption _selectedOption;
        private readonly PointInPoly _pointInPolyChecker;
        private List<List<UV>> _regionPolygons;
        private Level _level;
        private string _areaName;

        public GriddBomRevisionForRoomRegion(RevisionViewModel revision, List<string> selectedAccessoryNames,
            Element selectedElement, SelectionOption selectedOption, string areaName = "")
        {
            _revision = revision;
            _selectedAccessoryNames = selectedAccessoryNames;
            _selectedElement = selectedElement;
            _selectedOption = selectedOption;
            _pointInPolyChecker = new PointInPoly();
            _areaName = areaName;

            DetermineLevel();

            if (_selectedElement is FilledRegion region)
            {
                InitializeRegionPolygons(region);
            }
        }

        public Element SelectedElement => _selectedElement;
        public SelectionOption SelectedOption => _selectedOption;
        public string SheetName { get; private set; }
        public string DocumentName { get; private set; }
        public Level Level => _level;
        public string RevisionDate => _revision.RevisionDate;
        public string RevisionNumber => _revision.RevisionNumber;
        public string RevisionAuthor => _revision.RevisionAuthor;
        public List<GriddBomProduct> Products { get; private set; }
        public string AreaName
        {
            get => _areaName;
            set => _areaName = value; 
        }

        public void CalculateBom()
        {
            DocumentName = $"Rev{RevisionNumber} {AreaName} {DateTime.Now:MM-dd-yy-HH_mm_ss}.xlsx";
            SheetName = $"Rev{RevisionNumber} {AreaName} {DateTime.Now:MM-dd-yy}";
            Products = CollectProductsForRegionOrRoom();
        }

        private void DetermineLevel()
        {
            if (_selectedElement == null)
            {
                _level = null;
                return;
            }

            if (_selectedElement is FilledRegion region)
            {
                var view = RevitManager.Document.ActiveView;
                _level = view.GenLevel;
            }
            else if (_selectedElement is Room room)
            {
                _level = room.Level;
            }
            else
            {
                _level = RevitManager.Document.GetElement(_selectedElement.LevelId) as Level;
            }
        }

        private List<GriddBomProduct> CollectProductsForRegionOrRoom()
        {
            var allProducts = GriddProductCollector.Collect(_selectedAccessoryNames);

            if (_selectedElement == null) return new List<GriddBomProduct>();

            var preFilteredProducts = allProducts.Where(p => p.Element.LevelId == _level.Id)
                .ToList();

            var filteredProducts = preFilteredProducts.Where(p =>
            {
                if (_selectedElement is Room room)
                {
                    var location = p.Element.Location as LocationPoint;
                    return location != null && room.IsPointInRoom(location.Point);
                }

                if (_selectedElement is FilledRegion)
                {
                    return RegionContainsElement(p.Element);
                }

                return false;
            }).ToList();

            return ProcessProducts(filteredProducts);
        }

        private void InitializeRegionPolygons(FilledRegion region)
        {
            _regionPolygons = region.GetBoundaries().Select(cl =>
                cl.Select(c => new UV(c.GetEndPoint(0).X, c.GetEndPoint(0).Y)).ToList()
            ).ToList();
        }

        private bool RegionContainsElement(Element element)
        {
            if (_regionPolygons == null || !_regionPolygons.Any()) return false;

            var location = element.Location as LocationPoint;
            if (location == null) return false;

            var pointToCheck = new UV(location.Point.X, location.Point.Y);
            return _regionPolygons.Any(polygon => _pointInPolyChecker.PolygonContains(polygon, pointToCheck));
        }

        private List<GriddBomProduct> ProcessProducts(List<Product> products)
        {
            var smartLookupParameter = new SmartLookupParameter();

            var groupsByProductKey = products
                .Where(p => p.Element is FamilyInstance)
                .GroupBy(p => GriddBomProduct.GetProductKey(p.Element as FamilyInstance, smartLookupParameter));

            var griddBomProducts = new List<GriddBomProduct>();
            foreach (var group in groupsByProductKey)
            {
                if (string.IsNullOrEmpty(group.Key)) continue;
                var griddBomProduct = new GriddBomProduct(group.Select(p => p.Element as FamilyInstance).ToList(), smartLookupParameter);
                griddBomProduct.CalculateBom();
                griddBomProducts.Add(griddBomProduct);
            }

            return griddBomProducts.OrderBy(p => p.Model).ToList();
        }
    }
}