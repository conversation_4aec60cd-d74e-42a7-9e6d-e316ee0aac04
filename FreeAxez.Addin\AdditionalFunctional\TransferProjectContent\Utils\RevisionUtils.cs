﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils
{
    public class RevisionUtils
    {
        public static void CopyRevisionsAndSettings(Document source, Document target)
        {
            using (var t = new Transaction(target, "Copy Revisions"))
            {
                CommonFailuresPreprocessor.SetFailuresPreprocessor(t);
                t.Start();

                CopyRevisionSettings(source, target);
                var revisionMapping = CopyRevisions(source, target);
#if !revit2020 && !revit2021
                CopyNumberingSettings(source, target, revisionMapping);
#endif
                SetIssuedFlags(source, target, revisionMapping);
                t.Commit();
            }

            LogHelper.Information("The revisions have been copied");
        }

        private static void CopyRevisionSettings(Document source, Document target)
        {
            var sourceSettings = RevisionSettings.GetRevisionSettings(source);
            var targetSettings = RevisionSettings.GetRevisionSettings(target);
            targetSettings.RevisionNumbering = sourceSettings.RevisionNumbering;
            targetSettings.RevisionCloudSpacing = sourceSettings.RevisionCloudSpacing;

#if revit2020 || revit2021
            targetSettings.SetAlphanumericRevisionSettings(sourceSettings.GetAlphanumericRevisionSettings());
            targetSettings.SetNumericRevisionSettings(sourceSettings.GetNumericRevisionSettings());
#endif
        }

        private static void SetIssuedFlags(Document source, Document target, Dictionary<ElementId, Revision> revisionMapping)
        {
            foreach (var kvp in revisionMapping)
            {
                var sourceRevision = source.GetElement(kvp.Key) as Revision;
                var targetRevision = kvp.Value;

                if (sourceRevision != null)
                {
                    targetRevision.Issued = sourceRevision.Issued;
                }
            }
        }

        private static Dictionary<ElementId, Revision> CopyRevisions(Document source, Document target)
        {
            var revisionMapping = new Dictionary<ElementId, Revision>();
            var targetRevisions = Revision.GetAllRevisionIds(target)
                .Select(target.GetElement).Cast<Revision>().ToList();

            var sourceRevisions = Revision.GetAllRevisionIds(source)
                .Select(source.GetElement)
                .Cast<Revision>()
                .ToList();

            foreach (var sourceRevision in sourceRevisions)
            {
                var targetRevision = Revision.Create(target);
                targetRevision.RevisionDate = sourceRevision.RevisionDate;
                targetRevision.Description = sourceRevision.Description;
                targetRevision.IssuedTo = sourceRevision.IssuedTo;
                targetRevision.IssuedBy = sourceRevision.IssuedBy;
                targetRevision.Visibility = sourceRevision.Visibility;

#if revit2020 || revit2021
                targetRevision.NumberType = sourceRevision.NumberType;
#endif

                // Do NOT set Issued here
                // targetRevision.Issued = sourceRevision.Issued;

                // Store the mapping
                revisionMapping[sourceRevision.Id] = targetRevision;
            }

            target.Delete(targetRevisions.Select(r => r.Id).ToList());

            return revisionMapping;
        }

#if !revit2020 && !revit2021
        private static void CopyNumberingSettings(Document source, Document target, Dictionary<ElementId, Revision> revisionMapping)
        {
            foreach (var kvp in revisionMapping)
            {
                var sourceRevision = source.GetElement(kvp.Key) as Revision;
                var targetRevision = kvp.Value;

                if (sourceRevision == null || targetRevision == null)
                    continue;

                if (sourceRevision.RevisionNumberingSequenceId == ElementId.InvalidElementId)
                {
                    targetRevision.RevisionNumberingSequenceId = ElementId.InvalidElementId;
                    continue;
                }

                var sourceNumberingSequence =
                    source.GetElement(sourceRevision.RevisionNumberingSequenceId) as RevisionNumberingSequence;

                if (sourceNumberingSequence == null)
                    continue;

                var targetNumberingSequence = GetOrCreate(target, sourceNumberingSequence);
                targetRevision.RevisionNumberingSequenceId = targetNumberingSequence.Id;
            }
        }

        private static RevisionNumberingSequence GetOrCreate(
            Document targetDocument, RevisionNumberingSequence sourceNumberingSequence)
        {
            var output = RevisionNumberingSequence.GetAllRevisionNumberingSequences(targetDocument)
                .Select(targetDocument.GetElement)
                .Cast<RevisionNumberingSequence>()
                .FirstOrDefault(r => r.Name == sourceNumberingSequence.Name);

            if (output != null)
            {
                // Adjust settings each time
                if (sourceNumberingSequence.HasValidAlphanumericRevisionSettings())
                {
                    output.SetAlphanumericRevisionSettings(
                        sourceNumberingSequence.GetAlphanumericRevisionSettings());
                }
                else if (sourceNumberingSequence.HasValidNumericRevisionSettings())
                {
                    output.SetNumericRevisionSettings(
                        sourceNumberingSequence.GetNumericRevisionSettings());
                }
                else
                {
                    throw new System.Exception("Can't get revision numbering sequence settings");
                }

                return output;
            }

            // Create new RevisionNumberingSequence based on sourceNumberingSequence
            if (sourceNumberingSequence.NumberType == RevisionNumberType.Numeric)
            {
                output = RevisionNumberingSequence.CreateNumericSequence(
                    targetDocument, sourceNumberingSequence.Name, sourceNumberingSequence.GetNumericRevisionSettings());
            }
            else if (sourceNumberingSequence.NumberType == RevisionNumberType.Alphanumeric)
            {
                output = RevisionNumberingSequence.CreateAlphanumericSequence(
                    targetDocument, sourceNumberingSequence.Name, sourceNumberingSequence.GetAlphanumericRevisionSettings());
            }

            return output;
        }
#endif
    }
}
