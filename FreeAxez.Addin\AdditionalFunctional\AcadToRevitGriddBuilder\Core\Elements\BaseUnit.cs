﻿using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using Point = FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data.Point;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Core.Elements;

public class BaseUnit : BaseFaElement
{
    private static readonly BaseUnitConfiguration _config = new();

    private BaseUnit(int id, List<LineSegmentData> segments) : base(id, segments)
    {
    }

    public BaseUnit() : base()
    {
    }

    public override ElementTypeConfiguration Configuration => _config;

    protected override void CalculateCenter(List<LineSegmentData> segments)
    {
        var primaries = segments.Where(d => Configuration.IsValidLength(d.Segment.Length)).ToList();

        if (primaries.Count == 0)
            throw new InvalidOperationException("No valid primary segments found for BaseUnit center calculation");

        if (primaries.Count >= 4)
        {
            // Обычный случай - усредняем центры всех сторон
            double sx = 0, sy = 0;
            foreach (var d in primaries)
            {
                var m = d.Segment.MidPoint;
                sx += m.X;
                sy += m.Y;
            }
            Center = new Point(sx / primaries.Count, sy / primaries.Count, 0);
        }
        else if (primaries.Count == 2)
        {
            // Случай с 2 сторонами - пересечение перпендикуляров
            Center = CalculateCenterFromTwoSides(primaries);
        }
        else
        {
            throw new InvalidOperationException($"Cannot calculate center for BaseUnit with {primaries.Count} primary segments");
        }
    }

    protected override void CalculateRotationAngle(List<LineSegmentData> segments)
    {
        var primary = segments.FirstOrDefault(d => Configuration.IsValidLength(d.Segment.Length));
        if (primary == null)
            throw new InvalidOperationException("No valid primary segment found for BaseUnit rotation calculation");

        var angle = primary.Data.angle;
        while (angle < 0) angle += 360;
        while (angle >= 360) angle -= 360;
        RotationAngle = Math.Abs(angle % 90) < 5 ? 0 : angle;
    }

    protected override bool IsValidComponentForThisType(List<LineSegmentData> component, HashSet<LineSegmentData> usedSegments)
    {
        if (component.All(d => usedSegments.Contains(d)))
            return false;

        // Check if component has enough primary lines for BaseUnit
        var primaryLines = component.Where(d => !usedSegments.Contains(d) &&
                                                Configuration.IsValidLength(d.Segment.Length)).ToList();
        return primaryLines.Count >= 2;
    }

    private Point CalculateCenterFromTwoSides(List<LineSegmentData> twoSides)
    {
        var side1 = twoSides[0].Segment;
        var side2 = twoSides[1].Segment;

        // Центры сторон
        var center1 = side1.MidPoint;
        var center2 = side2.MidPoint;

        // Направляющие векторы сторон
        var dir1 = new { X = side1.P1.X - side1.P0.X, Y = side1.P1.Y - side1.P0.Y };
        var dir2 = new { X = side2.P1.X - side2.P0.X, Y = side2.P1.Y - side2.P0.Y };

        // Перпендикулярные векторы (поворот на 90°)
        var perp1 = new { X = -dir1.Y, Y = dir1.X };
        var perp2 = new { X = -dir2.Y, Y = dir2.X };

        // Находим пересечение двух прямых:
        // Прямая 1: center1 + t1 * perp1
        // Прямая 2: center2 + t2 * perp2

        var denominator = perp1.X * perp2.Y - perp1.Y * perp2.X;

        if (Math.Abs(denominator) < 1e-10)
        {
            // Перпендикуляры параллельны - используем среднее (редкий случай)
            return new Point((center1.X + center2.X) / 2, (center1.Y + center2.Y) / 2, 0);
        }

        var dx = center2.X - center1.X;
        var dy = center2.Y - center1.Y;

        var t1 = (dx * perp2.Y - dy * perp2.X) / denominator;

        var intersectionX = center1.X + t1 * perp1.X;
        var intersectionY = center1.Y + t1 * perp1.Y;

        return new Point(intersectionX, intersectionY, 0);
    }
}