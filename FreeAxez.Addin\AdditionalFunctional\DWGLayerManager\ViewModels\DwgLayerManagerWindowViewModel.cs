using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels
{
    public class DwgLayerManagerWindowViewModel : WindowViewModel
    {
        private readonly LayerManagerViewModel _layerManagerVM;
        private readonly DwgFileOperationService _fileOperationService;

        public DwgMappingViewModel DwgMappingVM { get; }
        public ICommand OpenLayerSettingsCommand { get; }
        public ICommand DeleteEmptyLayersCommand { get; }
        public ICommand UpdateLayersFromBackendCommand { get; }

        // Properties for UI state management
        public bool IsDwgFileSelected => !string.IsNullOrEmpty(DwgMappingVM?.SelectedDwgFilePath) &&
                                         File.Exists(DwgMappingVM.SelectedDwgFilePath);

        public bool CanPurgeUnused => IsDwgFileSelected && !DwgMappingVM.IsProcessing &&
                                      DwgMappingVM.DwgLayers?.Any(l => l.Purgeable) == true;

        public bool CanUpdateLayersFromBackend
        {
            get
            {
                if (!IsDwgFileSelected || DwgMappingVM.IsProcessing)
                    return false;

                // Check if any DWG layer names match FreeAxez layer names
                var dwgLayerNames = DwgMappingVM.DwgLayers?.Select(l => l.Name).ToHashSet(StringComparer.OrdinalIgnoreCase);
                var freeAxezLayerNames = DwgMappingVM.FreeAxezLayers?.Select(l => l.Name).ToHashSet(StringComparer.OrdinalIgnoreCase);

                return dwgLayerNames != null && freeAxezLayerNames != null &&
                       dwgLayerNames.Any(name => freeAxezLayerNames.Contains(name));
            }
        }

        // Proxy properties for AutoCAD version selection
        public ObservableCollection<int> AvailableAutoCADVersions => _layerManagerVM.AvailableAutoCADVersions;
        public int SelectedAutoCADVersion
        {
            get => _layerManagerVM.SelectedAutoCADVersion;
            set => _layerManagerVM.SelectedAutoCADVersion = value;
        }

        public DwgLayerManagerWindowViewModel()
        {
            var apiService = new DwgLayerManagerApiService();
            _layerManagerVM = new LayerManagerViewModel(apiService);

            // Use the same AutoCADVersionService instance for consistency
            var autoCADInvoker = new AutoCADInvoker(_layerManagerVM.AutoCADVersionService);
            var dwgLayerService = new DwgLayerService(autoCADInvoker);
            _fileOperationService = new DwgFileOperationService(dwgLayerService);

            DwgMappingVM = new DwgMappingViewModel(apiService);
            OpenLayerSettingsCommand = new RelayCommand(_ => OpenLayerSettings());
            DeleteEmptyLayersCommand = new RelayCommand(_ => DeleteEmptyLayersAsync(), _ => CanPurgeUnused);
            UpdateLayersFromBackendCommand = new RelayCommand(_ => UpdateLayersFromBackendAsync(), _ => CanUpdateLayersFromBackend);

            // Subscribe to property changes to update command states
            DwgMappingVM.PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(DwgMappingVM.SelectedDwgFilePath) ||
                    e.PropertyName == nameof(DwgMappingVM.IsProcessing) ||
                    e.PropertyName == nameof(DwgMappingVM.DwgLayers))
                {
                    OnPropertyChanged(nameof(IsDwgFileSelected));
                    OnPropertyChanged(nameof(CanPurgeUnused));
                    OnPropertyChanged(nameof(CanUpdateLayersFromBackend));
                    ((RelayCommand)DeleteEmptyLayersCommand).RaiseCanExecuteChanged();
                    ((RelayCommand)UpdateLayersFromBackendCommand).RaiseCanExecuteChanged();
                }
            };

            // Subscribe to AutoCAD version changes to reload DWG layers
            _layerManagerVM.OnAutoCADVersionChanged += () =>
            {
                // Only reload if a DWG file is selected
                if (!string.IsNullOrEmpty(DwgMappingVM.SelectedDwgFilePath))
                {
                    DwgMappingVM.LoadDwgLayersAsync();
                }
            };
        }

        private void OpenLayerSettings()
        {
            var settingsWindow = new FreeAxezLayersWindow(_layerManagerVM);
            var result = settingsWindow.ShowDialog();

            // Refresh FreeAxez layers after settings window is closed
            // This ensures new layers appear in the dropdown
            DwgMappingVM.RefreshFreeAxezLayersPreservingMappings();
        }

        private async void DeleteEmptyLayersAsync()
        {
            await _fileOperationService.PurgeUnusedLayersAsync(DwgMappingVM);
        }

        private async void UpdateLayersFromBackendAsync()
        {
            await _fileOperationService.UpdateLayersFromBackendAsync(DwgMappingVM);
        }


    }
}
