# GriddBuilder - CAD to Revit Element Placement

## Overview
GriddBuilder is a Revit plugin that extracts geometric data from DWG links and automatically places FreeAxez Gridd elements in the Revit model.

## Architecture

### 📁 Project Structure
```
GriddBuilder/
├── 📄 GriddBuilderCommand.cs          # Main Revit command
├── 📁 Core/Elements/                  # Element definitions with encapsulated logic
├── 📁 Processing/                     # Data processing pipeline
│   ├── AutoCAD/                      # CAD data extraction
│   ├── Extraction/                   # Element extraction from CAD data
│   └── Geometry/                     # Geometric calculations
├── 📁 Revit/Placement/               # Revit element placement
├── 📁 Workflow/                      # Central workflow coordination
├── 📁 Infrastructure/                # Utility classes
├── 📁 Data/                          # Data models
└── 📁 UI/                           # User interface
    ├── Views/                        # WPF views
    ├── ViewModels/                   # MVVM view models
    └── Converters/                   # UI value converters
```

### 🔄 Workflow Process
1. **Command Execution** → `GriddBuilderCommand`
2. **DWG Selection** → `DwgLinkManager`
3. **User Configuration** → `GriddBuilderView` + `GriddBuilderViewModel`
4. **Workflow Execution** → `GriddBuilderWorkflow`
5. **CAD Data Extraction** → `AutoCADService`
6. **Element Creation** → `ElementExtractor`
7. **Revit Placement** → `ElementPlacer`
8. **Area Processing** → `AreaProcessor` (floors from A-AREA-PATT)
9. **Result Reporting** → `BuildResult`

### 🏗️ Core Elements
All elements inherit from `BaseFaElement` and encapsulate their own logic:

- **BaseUnit** - Standard 11 27/32" base units
- **BaseUnitHalf** - Half-cut base units with custom parameters
- **BaseUnitQuarter** - Quarter-cut triangular units
- **PlateCorner** - Corner plate elements
- **PlateChannel** - Channel plate elements

Each element:
- ✅ Creates itself from CAD components
- ✅ Validates its own geometry
- ✅ Places itself in Revit
- ✅ Sets its own parameters

### 🎯 Key Features
- **Unified Factory Pattern** - `BaseFaElement.CreateFromComponents<T>()`
- **Self-Contained Elements** - Each element manages its own behavior
- **Separate Transactions** - Each element type placed in its own transaction
- **Comprehensive Error Handling** - Detailed success/failure reporting
- **Asynchronous Processing** - Non-blocking UI during execution

### 🔧 Configuration
- **Base Unit Height**: 40 or 70 (saved in user settings)
- **DWG Link Selection**: Interactive selection from Revit model
- **Layer Processing**: BASE, EZ_CORNER, CHANNEL, A-AREA-PATT layers

### 📊 Supported Element Types
| Element Type | Family Name | Layer | Geometry Rules |
|--------------|-------------|-------|----------------|
| BaseUnit | FreeAxez-Base_Unit | BASE | 4+ primary lines (11 27/32") |
| BaseUnitHalf | FreeAxez-Base_Unit_Half | BASE | 1 primary + 2 perpendicular sides |
| BaseUnitQuarter | FreeAxez-Base_Unit_Quarter | BASE | 2+ quarter lines (5 29/32") |
| PlateCorner | FreeAxez-Plate_Corner | EZ_CORNER | 4 corner lines |
| PlateChannel | FreeAxez-Plate_Channel | CHANNEL | Rectangle with specific dimensions |
| Area Floors | Floor Types: GRIDD AREA, OVERALL AREA | A-AREA-PATT | Closed line loops (processed separately) |

### 🚀 Usage
1. Run the GriddBuilder command in Revit
2. Select a DWG link from the model
3. Choose base unit height (40 or 70)
4. Click "Build Elements"
5. Review the placement summary

### 🧪 Testing
The plugin includes comprehensive error handling and validation:
- AutoCAD Core Console availability check
- DWG file access validation
- Geometric validation for each element type
- Transaction-level error isolation

### 📝 Dependencies
- **Revit API** - Element placement and transactions
- **AutoCAD Core Console** - DWG data extraction
- **NetTopologySuite** - Geometric calculations
- **Newtonsoft.Json** - Data serialization
- **WPF** - User interface

---
*Built with clean architecture principles and SOLID design patterns.*
