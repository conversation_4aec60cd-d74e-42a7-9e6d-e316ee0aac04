﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FlexPipeToLine.Utils
{
    public class FlexPipeToLineConverter
    {
        private readonly Element _selectedLineStyle;
        private readonly List<FlexPipe> _flexPipes;
        private const double MaxLengthOfCornerLine = 4.0 / 12.0;


        public FlexPipeToLineConverter(Element selectedLineStyle, List<FlexPipe> flexPipes)
        {
            _selectedLineStyle = selectedLineStyle;
            _flexPipes = flexPipes;
        }


        /// <summary>
        /// Create detail curves using straight lines instead flex pipes splines.
        /// Delete flex pipes from model.
        /// </summary>
        /// <returns>Count of created detail curves.</returns>
        public int Convert()
        {
            var detailCurves = new List<DetailCurve>();
            var flexPipesToDelete = new List<ElementId>();

            using (var t = new Transaction(RevitManager.Document))
            {
                t.Start("Convert Flex Pipes To Lines");

                foreach (var flexPipe in _flexPipes)
                {
                    try
                    {
                        var newCurves = GetCurvesFromFlexPipe(flexPipe);
                        detailCurves.AddRange(CreateDetailCurves(newCurves));
                        flexPipesToDelete.Add(flexPipe.Id);
                    }
                    catch { }
                }

                if (flexPipesToDelete.Any())
                {
                    RevitManager.Document.Delete(flexPipesToDelete);
                }

                t.Commit();
            }

            return detailCurves.Count;
        }

        /// <summary>
        /// Generates a list of simplified, straightened curves from a FlexPipe's geometry.
        /// </summary>
        /// <param name="flexPipe">The FlexPipe to process.</param>
        /// <returns>A list of simplified curves.</returns>
        public static List<Curve> GetCurvesFromFlexPipe(FlexPipe flexPipe)
        {
            var linesBetweenControlPoints = GetLinesBetweenControlPoints(flexPipe);
            if (!linesBetweenControlPoints.Any()) return new List<Curve>();

            var linesWithoutCornerLines = RemoveCornerLines(linesBetweenControlPoints);
            var increasedCurves = IncreaseCurves(linesWithoutCornerLines);
            var intersectionPoints = GetIntersectionPoints(increasedCurves, linesWithoutCornerLines);
            return CreateNewCurves(intersectionPoints);
        }

        private static List<Curve> GetLinesBetweenControlPoints(FlexPipe flexPipe)
        {
            var curves = new List<Curve>();

            var points = ((flexPipe.Location as LocationCurve).Curve as HermiteSpline).ControlPoints;
            if (points.Count == 2)
            {
                curves.Add(Line.CreateBound(points[0], points[1]));
            }
            else
            {
                var startPoint = points[0];
                var endPoint = points[1];

                for (int i = 2; i < points.Count; i++)
                {
                    var curveDirection = endPoint - startPoint;
                    var nextDirection = points[i] - endPoint;
                    if (curveDirection.AngleTo(nextDirection) < Math.PI / 180)
                    {
                        endPoint = points[i];
                        continue;
                    }
                    curves.Add(Line.CreateBound(startPoint, endPoint));
                    startPoint = endPoint;
                    endPoint = points[i];
                }

                curves.Add(Line.CreateBound(startPoint, endPoint));
            }

            return curves;
        }

        private static List<Curve> RemoveCornerLines(List<Curve> curves)
        {
            var copy = new List<Curve>(curves);
            copy.RemoveAll(c => copy.IndexOf(c) != 0 &&
                                copy.IndexOf(c) != copy.Count - 1 &&
                                c.Length < MaxLengthOfCornerLine);
            return copy;
        }

        private static List<Curve> IncreaseCurves(List<Curve> curves)
        {
            return curves.Select(c => Line.CreateBound(c.Evaluate(-100, false), c.Evaluate(200, false))).Cast<Curve>().ToList();
        }

        private static List<XYZ> GetIntersectionPoints(List<Curve> increasedCurves, List<Curve> originalCurves)
        {
            var points = new List<XYZ>();

            // Add start point
            points.Add(originalCurves[0].GetEndPoint(0));

            for (int i = 0; i < increasedCurves.Count() - 1; i++)
            {
                var originalEnd = originalCurves[i].GetEndPoint(1);
                var originalStart = originalCurves[i + 1].GetEndPoint(0);

                var comparisonResult = increasedCurves[i].Intersect(increasedCurves[i + 1], out var intersections);
                if (comparisonResult == SetComparisonResult.Disjoint)
                {
                    // Lines would be parallel
                    // Leave original points instead intersection point
                    points.Add(originalEnd);
                    points.Add(originalStart);
                    continue;
                }

                var intersectionPoint = intersections.Cast<IntersectionResult>().First().XYZPoint;

                // Check distance to intersection point because it could be too far
                // In this case also leave original points instead intersection point
                var originalDistance = Math.Round(originalEnd.DistanceTo(originalStart), 7);
                var firstDistance = Math.Round(originalEnd.DistanceTo(intersectionPoint), 7);
                var secondDistance = Math.Round(originalStart.DistanceTo(intersectionPoint), 7);

                if (firstDistance > originalDistance && secondDistance > originalDistance)
                {
                    points.Add(originalEnd);
                    points.Add(originalStart);
                }
                else
                {
                    points.Add(intersectionPoint);
                }
            }

            // Add end point
            points.Add(originalCurves.Last().GetEndPoint(1));

            return points;
        }

        private static List<Curve> CreateNewCurves(List<XYZ> points)
        {
            var curves = new List<Curve>();
            for (int i = 0; i < points.Count - 1; i++)
            {
                if (points[i].IsAlmostEqualTo(points[i + 1])) continue;
                curves.Add(Line.CreateBound(points[i], points[i + 1]));
            }
            return curves;
        }

        private List<DetailCurve> CreateDetailCurves(List<Curve> curves)
        {
            var detailCurves = new List<DetailCurve>();
            foreach (var curve in curves)
            {
                var detailCurve = RevitManager.Document.Create.NewDetailCurve(RevitManager.UIDocument.ActiveView, curve);
                detailCurve.LineStyle = _selectedLineStyle;
                detailCurves.Add(detailCurve);
            }

            return detailCurves;
        }
    }
}
