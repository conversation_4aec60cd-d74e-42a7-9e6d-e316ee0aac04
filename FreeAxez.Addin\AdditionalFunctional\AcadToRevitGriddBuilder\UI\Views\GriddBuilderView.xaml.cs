using System;
using System.Windows;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Workflow;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.UI.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.UI.Views
{
    /// <summary>
    /// Simplified view for Gridd Builder configuration
    /// </summary>
    public partial class GriddBuilderView : Window
    {
        public BuildResult Result { get; private set; }

        public GriddBuilderView()
        {
            InitializeComponent();
            Loaded += OnLoaded;
        }

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            if (DataContext is GriddBuilderViewModel viewModel)
            {
                viewModel.BuildCompleted += OnBuildCompleted;
                viewModel.Cancelled += OnCancelled;
            }
        }

        private void OnBuildCompleted(object sender, BuildResult result)
        {
            Result = result;
            ShowResultAndClose(result);
        }

        private void OnCancelled(object sender, EventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void ShowResultAndClose(BuildResult result)
        {
            MessageWindow.ShowDialog("Build Complete", result.GetSummaryMessage(),
                result.Success ? MessageType.Success : MessageType.Error);

            DialogResult = result.Success;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            UnsubscribeFromEvents();
            base.OnClosed(e);
        }

        private void UnsubscribeFromEvents()
        {
            if (DataContext is GriddBuilderViewModel viewModel)
            {
                viewModel.BuildCompleted -= OnBuildCompleted;
                viewModel.Cancelled -= OnCancelled;
            }
        }
    }
}
