﻿﻿using System;
using System.Collections.Generic;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model
{
    public class DetailsItemDto : BaseDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime LastDateUpdated { get; set; }
        public string FilePath { get; set; }
        public string ImagePath { get; set; }
        public string FileType { get; set; }
        public bool IsDeleted { get; set; }
    }
}
