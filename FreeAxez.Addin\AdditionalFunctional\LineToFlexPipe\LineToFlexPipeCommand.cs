﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.AdditionalFunctional.LineToFlexPipe.Utils;
using FreeAxez.Addin.Infrastructure;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.LineToFlexPipe
{
    [Autodesk.Revit.Attributes.Transaction(Autodesk.Revit.Attributes.TransactionMode.Manual)]
    public class LineToFlexPipeCommand : BaseExternalCommand
    {
        public static List<string> errors;
        public override Result Execute()
        {
            RevitManager.Application.IsPipingEnabled = true;

            List<CurveElement> curves = RevitManager.UIDocument.Selection.GetElementIds().Select(q => RevitManager.Document.GetElement(q)).Where(q => q is CurveElement).Cast<CurveElement>().ToList();

            if (!(RevitManager.Document.ActiveView is ViewPlan))
            {
                TaskDialog.Show("Error", "Run this command with a Plan View active to define the level for the new flex pipe");
                return Result.Cancelled;
            }

            //try
            //{
            //    curves = uidoc
            //    .Selection
            //    .PickObjects(ObjectType.Element, new CurveElementSelectionFilter(), "Select curves")
            //    .Select(q => doc.GetElement(q) as CurveElement)
            //    .Where(q => q.GeometryCurve is Line)
            //    .ToList();
            //}
            //catch (Exception ex)
            //{
            //    return Result.Cancelled;
            //}

            if (curves.Count == 0)
            {
                TaskDialog.Show("Error", "No lines selected. Select lines before running the command.");
                return Result.Cancelled;
            }

            FlexPipeType ptFromUI;
            //int numOfPoints = 4;
            double diam;
            bool roundCorner;
            bool deleteLines;
            double levelOffset;
            double whipDropValue;
            double whipRiseValue;
            double whipOverheadValue;
            double spliceValue;
            bool firstPigtailValue;
            bool secondPigtailValue;
            using (FormFlexPipe2 form = new FormFlexPipe2(RevitManager.Document))
            {
                form.ShowDialog();
                if (form.DialogResult == System.Windows.Forms.DialogResult.Cancel)
                    return Result.Cancelled;
                ptFromUI = form.getPipeType();
                //numOfPoints = form.getPoints();
                diam = form.getPipeDiam();
                roundCorner = form.GetRounded();
                deleteLines = form.GetDeleteLines();
                levelOffset = form.getLevelOffset();
                whipDropValue = form.GetWhipDropValue();
                whipRiseValue = form.GetWhipRiseValue();
                whipOverheadValue = form.GetWhipOverheadValue();
                spliceValue = form.GetSpliceValue();
                firstPigtailValue = form.GetFirstPigtailValue();
                secondPigtailValue = form.GetSecondPigtailValue();
            }


            string PipingSystemTypeName = "Other";
            PipingSystemType pst = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(PipingSystemType))
                .Cast<PipingSystemType>()
                .FirstOrDefault(q => q.Name == PipingSystemTypeName);

            if (pst == null)
                pst = new FilteredElementCollector(RevitManager.Document)
                    .OfClass(typeof(PipingSystemType))
                    .Cast<PipingSystemType>()
                    .FirstOrDefault();

            Level level = ((ViewPlan)RevitManager.Document.ActiveView).GenLevel;

            List<List<Curve>> curveGroups = LineMerger.GetMergedCurves(curves);
            if (curveGroups.Count == 0)
            {
                TaskDialog.Show("Error", "An error occurred while trying to merge lines.");
                return Result.Cancelled;
            }

            errors = new List<string>();
            using (TransactionGroup tg = new TransactionGroup(RevitManager.Document, "Flex pipe"))
            {
                tg.Start();
                using (Transaction t = new Transaction(RevitManager.Document, "a"))
                {
                    FailureHandlingOptions options = t.GetFailureHandlingOptions();
                    options.SetFailuresPreprocessor(new MyPreProcessor());
                    t.SetFailureHandlingOptions(options);
                    t.Start();

                    var orderedWhipType = new FilteredElementCollector(RevitManager.Document)
                        .OfClass(typeof(FlexPipeType))
                        .Cast<FlexPipeType>()
                        .OrderBy(t => GetWhipTypeLength(t))
                        .ThenBy(t => t.Name)
                        .ToList();

                    foreach (List<Curve> curveGroup in curveGroups)
                    {
                        FlexPipeType pt;
                        if (ptFromUI == null)
                        {
                            double longest = curveGroup.Sum(q => q.Length);
                            pt = orderedWhipType.FirstOrDefault(t => GetWhipTypeLength(t) >= longest);
                            if (pt == null) pt = orderedWhipType.Last();
                        }
                        else
                        {
                            pt = ptFromUI;
                        }

                        List<XYZ> ptsWithOffset = LinesToHermiteSpline.GetPointsWithOffset(curveGroup, roundCorner, levelOffset);

                        Autodesk.Revit.DB.Plumbing.FlexPipe pipe = Autodesk.Revit.DB.Plumbing.FlexPipe.Create(RevitManager.Document,
                                        pst.Id,
                                        pt.Id,
                                        level.Id,
                                        ((Line)curveGroup.First()).Direction,
                                        ((Line)curveGroup.Last()).Direction,
                                        ptsWithOffset);

                        pipe.get_Parameter(BuiltInParameter.RBS_PIPE_DIAMETER_PARAM).Set(diam);

                        SetDoublePipeParameterValue(pipe, "Whip Drop", whipDropValue);
                        SetDoublePipeParameterValue(pipe, "Whip Rise", whipRiseValue);
                        SetDoublePipeParameterValue(pipe, "Whip Overhead", whipOverheadValue);
                        SetDoublePipeParameterValue(pipe, "Splice", spliceValue);
                        SetBooleanPipeParameterValue(pipe, "Pigtail 1", firstPigtailValue);
                        SetBooleanPipeParameterValue(pipe, "Pigtail 2", secondPigtailValue);

                        List<Connector> pipeConnectors = pipe.ConnectorManager.Connectors.Cast<Connector>().ToList();
                        foreach (Connector pipeConnector in pipeConnectors)
                        {
                            List<Element> list = new FilteredElementCollector(RevitManager.Document).WherePasses(
                                new LogicalOrFilter(
                                    new ElementClassFilter(typeof(Autodesk.Revit.DB.Plumbing.FlexPipe)),
                                    new ElementClassFilter(typeof(FamilyInstance))))
                                .WherePasses(new BoundingBoxContainsPointFilter(pipeConnector.Origin, 0.001))
                                .ToList();
                            foreach (Element e in list)
                            {
                                ConnectorManager cm;
                                if (e is Autodesk.Revit.DB.Plumbing.FlexPipe fp)
                                    cm = fp.ConnectorManager;
                                else if (e is FamilyInstance fi)
                                    cm = fi.MEPModel.ConnectorManager;
                                else
                                    continue;

                                if (cm == null)
                                    continue;

                                foreach (Connector c in cm.Connectors)
                                {
                                    if (c.Domain == Domain.DomainPiping && 
                                        c.Origin.DistanceTo(pipeConnector.Origin) < 0.001)
                                    {
                                        pipeConnector.ConnectTo(c);
                                    }
                                }
                            }

                        }

                    }

                    if (deleteLines)
                    {
                        RevitManager.Document.Delete(curves.Select(q => q.Id).ToList());
                    }

                    t.Commit();
                }
                tg.Assimilate();
            }
            if (errors.Any())
            {
                TaskDialog td = new TaskDialog("Error")
                {
                    MainInstruction = "Could not create flex pipes for all selected lines",
                    MainContent = string.Join(Environment.NewLine, errors)
                };
                td.Show();
            }

            return Result.Succeeded;
        }

        private void SetDoublePipeParameterValue(FlexPipe flexPipe, string parameterName, double parameterValue)
        {
            Parameter parameter = flexPipe.LookupParameter(parameterName);
            parameter?.Set(parameterValue);
        }

        public void SetBooleanPipeParameterValue(FlexPipe flexPipe, string parameterName, bool parameterValue)
        {
            Parameter parameter = flexPipe.LookupParameter(parameterName);

            if (parameter is not null && parameter.StorageType == StorageType.Integer)
            {
                int parsedParameterValue = parameterValue ? 1 : 0;
                parameter.Set(parsedParameterValue);
            }
        }

        /// <summary>
        /// Get number from type name and interpret it as length.
        /// If the type name does not contain a number, returns 0.
        /// The number will be interpreted as feet.
        /// </summary>
        private double GetWhipTypeLength(FlexPipeType pipeType)
        {
            var lengthPattern = @"\d+";
            var match = Regex.Match(pipeType.Name, lengthPattern);
            if (match.Success)
            {
                return double.Parse(match.Value);
            }
            return 0;
        }

        public class MyPreProcessor : IFailuresPreprocessor
        {
            FailureProcessingResult IFailuresPreprocessor.PreprocessFailures(FailuresAccessor failuresAccessor)
            {
                foreach (FailureMessageAccessor fma in failuresAccessor.GetFailureMessages())
                {
                    errors.Add(fma.GetDescriptionText() + ": " + string.Join(",", fma.GetFailingElementIds().Select(q => q.GetIntegerValue()).ToList()));
                }
                failuresAccessor.DeleteAllWarnings();

                return FailureProcessingResult.Continue;
            }
        }

        private class CurveElementSelectionFilter : ISelectionFilter
        {
            public bool AllowElement(Element e)
            {
                if (e is CurveElement)
                    return true;

                return false;
            }
            public bool AllowReference(Reference r, XYZ point)
            {
                return true;
            }
        }
    }
}
