﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.IO;

namespace FreeAxez.Addin.AdditionalFunctional.LevelViewManager.Helpers
{
    public class DwgHelper
    {
        private const string GhostFileTemplate = "Ghost DWG - L{0}.dwg";

        public void LinkGhostDwg(Level level)
        {
            var path = Path.Combine(
                Path.GetTempPath(), 
                string.Format(GhostFileTemplate, LevelHelper.GetLevelNumber(level)));

            CreateDwg(path);
            LinkDwg(path, level);

            DeleteTempFiles(path);
        }

        private void DeleteTempFiles(string path)
        {
            var directory = Path.GetDirectoryName(path);
            var fileName = Path.GetFileNameWithoutExtension(path);

            foreach (var file in Directory.GetFiles(directory))
            {
                if (Path.GetFileNameWithoutExtension(file) == fileName)
                {
                    try
                    {
                        File.Delete(file);
                    }
                    catch { }
                }
            }
        }

        private void LinkDwg(string path, Level level)
        {
            // link CAD firstly
            DWGImportOptions opt = new DWGImportOptions();
            opt.Placement = ImportPlacement.Origin;
            opt.AutoCorrectAlmostVHLines = true;
            opt.ThisViewOnly = false; // not this view only
            opt.Unit = ImportUnit.Default;

            using (Transaction t = new Transaction(RevitManager.Document, "Add Link Ghost DWG"))
            {
                t.Start();

                var viewFamilyType = GetViewFamilyType(ViewFamily.FloorPlan);
                var viewPlane = ViewPlan.Create(RevitManager.Document, viewFamilyType.Id, level.Id);

                RevitManager.Document.Regenerate();

                RevitManager.Document.Link(path, opt, viewPlane, out ElementId linkId);

                RevitManager.Document.Delete(viewPlane.Id);

                t.Commit();
            }
        }

        private void CreateDwg(string path)
        {
            using (var t = new Transaction(RevitManager.Document, "Create Ghost View"))
            {
                t.Start();

                var viewFamilyType = GetViewFamilyType(ViewFamily.Drafting);

                var draftingView = ViewDrafting.Create(RevitManager.Document, viewFamilyType.Id);

                draftingView.Scale = 128; // 3/32" = 1'-0"

                var text = "REPLACE WITH PROJECTS DWG\r\nOR DELETE CAD FROM VIEWS";

                TextNoteType textNoteType
                  = new FilteredElementCollector(RevitManager.Document)
                    .OfClass(typeof(TextNoteType))
                    .Cast<TextNoteType>()
                    .First();

                var textNoteTypeName = Guid.NewGuid().ToString();
                var tempTextNoteType = textNoteType.Duplicate(textNoteTypeName);
                
                tempTextNoteType.get_Parameter(BuiltInParameter.TEXT_FONT).SetValueString("Arial");
                tempTextNoteType.get_Parameter(BuiltInParameter.TEXT_SIZE).SetValueString("3/32\"");
                tempTextNoteType.get_Parameter(BuiltInParameter.LINE_COLOR).Set(0);
                tempTextNoteType.get_Parameter(BuiltInParameter.LINE_PEN).Set(1);
                tempTextNoteType.get_Parameter(BuiltInParameter.TEXT_STYLE_BOLD).Set(0);
                tempTextNoteType.get_Parameter(BuiltInParameter.TEXT_STYLE_ITALIC).Set(0);
                tempTextNoteType.get_Parameter(BuiltInParameter.TEXT_STYLE_UNDERLINE).Set(0);
                tempTextNoteType.get_Parameter(BuiltInParameter.TEXT_BACKGROUND).Set(1); // Transparent
                tempTextNoteType.get_Parameter(BuiltInParameter.TEXT_WIDTH_SCALE).Set(1.0);
                tempTextNoteType.get_Parameter(BuiltInParameter.TEXT_BOX_VISIBILITY).Set(0);

                RevitManager.Document.Regenerate();

                var basePoint = BasePoint.GetProjectBasePoint(RevitManager.Document);
                TextNote.Create(RevitManager.Document, draftingView.Id, basePoint.Position, text, tempTextNoteType.Id);

                RevitManager.Document.Regenerate();

                var directory = Path.GetDirectoryName(path);
                var fileName = Path.GetFileNameWithoutExtension(path);

                RevitManager.Document.Export(directory, fileName, [draftingView.Id], new DWGExportOptions());

                RevitManager.Document.Delete(draftingView.Id);
                RevitManager.Document.Delete(tempTextNoteType.Id);

                t.Commit();
            }
        }

        private ViewFamilyType GetViewFamilyType(ViewFamily viewFamily)
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(ViewFamilyType))
                .Cast<ViewFamilyType>()
                .First(t => t.ViewFamily == viewFamily);
        }
    }
}
