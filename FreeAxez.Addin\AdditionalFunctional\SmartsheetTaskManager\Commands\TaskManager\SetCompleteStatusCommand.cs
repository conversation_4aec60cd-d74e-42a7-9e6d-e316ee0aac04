﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Abstractions;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Views;
using FreeAxez.Addin.Infrastructure;
using System.ComponentModel;
using System.Windows.Threading;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Commands.TaskManager;

public class SetCompleteStatusCommand : CommandBase
{
    private readonly TaskManagerViewModel _taskManagerViewModel;
    private readonly TaskManagerView _taskManagerView;
    private readonly Dispatcher _dispatcher;
    private readonly TaskManagerHttpClientBase _taskManagerHttpClientService;

    public SetCompleteStatusCommand(TaskManagerViewModel taskManagerViewModel,
                                    TaskManagerView taskManagerView,
                                    Dispatcher dispatcher,
                                    TaskManagerHttpClientBase taskManagerHttpClientService)
    {
        _taskManagerViewModel = taskManagerViewModel;
        _taskManagerView = taskManagerView;
        _dispatcher = dispatcher;
        _taskManagerHttpClientService = taskManagerHttpClientService;

        _taskManagerViewModel.PropertyChanged += OnViewModelPropertyChanged;
    }

    public override void Execute(object parameter)
    {
        _taskManagerViewModel.ErrorResponse = string.Empty;

        var completeStatusView = new CompleteStatusView
        {
            DataContext = new CompleteStatusViewModel(_taskManagerViewModel, _taskManagerHttpClientService),
            Owner = _taskManagerView
        };

        completeStatusView.Show();
    }

    public override bool CanExecute(object parameter)
    {
        return base.CanExecute(parameter)
            && !_taskManagerViewModel.IsExecuting
            && _taskManagerViewModel.RowColumnDtos is not null;
    }

    private void OnViewModelPropertyChanged(object sender, PropertyChangedEventArgs eventArguments)
    {
        if (eventArguments.PropertyName == nameof(TaskManagerViewModel.IsExecuting))
        {
            _dispatcher.Invoke(OnCanExecuteChanged);
        }
    }
}
