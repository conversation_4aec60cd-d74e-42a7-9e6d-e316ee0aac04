using System.Windows;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views
{
    public partial class LayerTransparencyWindow : Window
    {
        public LayerTransparencyWindow(int currentTransparency = 0)
        {
            InitializeComponent();
            DataContext = new LayerTransparencyViewModel(currentTransparency);
        }
    }
}
