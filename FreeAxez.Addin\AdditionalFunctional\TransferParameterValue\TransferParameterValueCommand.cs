﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.Utils;
using FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.Views;
using FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.TransferParameterValue
{
    [Transaction(TransactionMode.Manual)]
    class TransferParameterValueCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var selectionManager = new SelectionManager();
            if (!selectionManager.TryPickSourceElement() || !selectionManager.TryPickTargetElements())
            {
                return Result.Cancelled;
            }

            var window = new TransferParameterValueView(new TransferParameterValueViewModel(selectionManager));
            window.ShowDialog();

            return Result.Succeeded;
        }
    }
}
