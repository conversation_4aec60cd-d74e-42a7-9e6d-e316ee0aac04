﻿using FreeAxez.Core.Constants;
using FreeAxez.Core.Dto;
using Microsoft.Azure.Storage;
using Microsoft.Azure.Storage.Blob;
using Microsoft.Azure.Storage.Queue;
using Newtonsoft.Json;
using Serilog;
using System;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FreeAxez.Core.Services
{
    public class FreeAxezStorageService
    {
        public CloudBlobContainer Container { get; private set; }

        private readonly CloudQueue _queue;
        private readonly CloudQueue _failedQueue;

        private const string ContainerName = "freeaxez-container";

        private const string QueueName = "freeaxez-queue";
        //private const string QueueName = "freeaxez-queue-dev";

        private const string FailQueueName = "freeaxez-failed-queue";

        private const string InstructionFileTemplate = "video/{0}/{1}";
        private const string ProductFileTemplate = "product/{0}/{1}";
        private const string SourceFileTemplate = "source/{0}.rvt";
        private const string OutputFileTemplate = "output/{0}_{1}.rvt";
        private const string BuildDataTemplate = "builddata/buildjson/";
        private const string BuildImageTemplate = "builddata/buildimage/";
        private const string FamilyFileTemplate = "familyLibrary/families/{0}.rvt";
        private const string FamilyImageTemplate = "familyLibrary/familyImages/";

        private readonly string _environmentName;

        public FreeAxezStorageService(string connectionString, string environmentName)
        {
            _environmentName = environmentName;
            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(connectionString);
            CloudBlobClient blobClient = storageAccount.CreateCloudBlobClient();
            Container = blobClient.GetContainerReference(GetResourceName(ContainerName));

            bool isCreated = Container.CreateIfNotExists();
            if (isCreated)
            {
                BlobContainerPermissions containerPermissions = new BlobContainerPermissions
                {
                    PublicAccess = BlobContainerPublicAccessType.Blob
                };
                Container.SetPermissions(containerPermissions);
            }

            CloudQueueClient queueClient = storageAccount.CreateCloudQueueClient();
            _queue = queueClient.GetQueueReference(GetResourceName(QueueName));
            _queue.CreateIfNotExists();
            CloudQueueClient failedQueueClient = storageAccount.CreateCloudQueueClient();
            _failedQueue = failedQueueClient.GetQueueReference(GetResourceName(FailQueueName));
            _failedQueue.CreateIfNotExists();
        }

        public string UploadFamilyFile(string familyCode, Stream content)
        {
            var familyPath = String.Format(FamilyFileTemplate, familyCode);
            CloudBlockBlob cloudBlockBlob = Container.GetBlockBlobReference(familyPath);
            try
            {
                cloudBlockBlob.UploadFromStream(content);
            }
            catch
            {
                familyPath = null;
            }

            if (familyPath == null || !cloudBlockBlob.Exists())
            {
                return null;
            }

            var baseUrl = Container.Uri.ToString();
            var fullUrl = $"{baseUrl}/{familyPath}";
            return fullUrl;
        }
        public string UploadFamilyImage(Guid id, Stream content)
        {
            var imagePath = $"{FamilyImageTemplate}{id}.png";
            CloudBlockBlob cloudBlockBlob = Container.GetBlockBlobReference(imagePath);
            try
            {
                cloudBlockBlob.Properties.ContentType = "image/png";
                cloudBlockBlob.UploadFromStream(content);
            }
            catch
            {
                imagePath = null;
            }

            if (imagePath == null || !cloudBlockBlob.Exists())
            {
                return null;
            }

            var baseUrl = Container.Uri.ToString();
            var fullUrl = $"{baseUrl}/{imagePath}";
            return fullUrl;
        }

        public bool DownloadFileFromBlob(string blobPath, out byte[] fileContent)
        {
            fileContent = null;
            var blob = Container.GetBlockBlobReference(blobPath);

            if (!blob.Exists())
            {
                return false;
            }

            using (var mem = new MemoryStream())
            {
                blob.DownloadToStream(mem);
                fileContent = mem.ToArray();
            }

            return true;
        }

        public string AddSourceFile(Guid projectId, Stream content)
        {
            Log.Information("FreeAxezStorageService.AddSourceFile projectId: {@prId}", projectId);
            var filePath = String.Format(SourceFileTemplate, projectId);
            CloudBlockBlob cloudBlockBlob = Container.GetBlockBlobReference(filePath);
            try
            {
                cloudBlockBlob.UploadFromStream(content);
            }
            catch (Exception e)
            {
                Log.Error(e, "{@trace}");
                filePath = null;
            }

            if (filePath == null || !cloudBlockBlob.Exists())
            {
                Log.Warning("FreeAxezStorageService.AddSourceFile File load failed. ProjectId: {@projectId}", projectId);
                return null;
            }

            Log.Information("FreeAxezStorageService.AddSourceFile File loaded. Path: {@path}", filePath);
            return filePath;
        }

        public string SaveInstructionVideo(Stream content, string name)
        {
            var id = Guid.NewGuid();
            var filePath = String.Format(InstructionFileTemplate, id, name);
            var cloudBlockBlob = Container.GetBlockBlobReference(filePath);
            var fileUrl = cloudBlockBlob.Uri.AbsoluteUri;

            try
            {
                cloudBlockBlob.UploadFromStream(content);
            }
            catch (Exception e)
            {
                Log.Error(e, "{@trace}");
                fileUrl = null;
            }

            if (fileUrl == null || !cloudBlockBlob.Exists())
            {
                Log.Warning("FreeAxezStorageService.AddSourceFile Video load failed.");
                return null;
            }

            Log.Information("FreeAxezStorageService.AddSourceFile Video loaded. Path: {@path}", filePath);
            return fileUrl;
        }
        
        public string SaveProductFile(Stream content, string name)
        {
            var id = Guid.NewGuid();
            var filePath = String.Format(ProductFileTemplate, id, name);
            var cloudBlockBlob = Container.GetBlockBlobReference(filePath);
            var fileUrl = cloudBlockBlob.Uri.AbsoluteUri;

            try
            {
                cloudBlockBlob.UploadFromStream(content);
            }
            catch (Exception e)
            {
                Log.Error(e, "{@trace}");
                fileUrl = null;
            }

            if (fileUrl == null || !cloudBlockBlob.Exists())
            {
                Log.Warning("FreeAxezStorageService.AddSourceFile Product load failed.");
                return null;
            }

            Log.Information("FreeAxezStorageService.AddSourceFile Product loaded. Path: {@path}", filePath);
            return fileUrl;
        }

        public byte[] GetSourceFile(Guid projectId)
        {
            Log.Information("FreeAxezStorageService.GetSourceFile. projectId: {@prId}", projectId);
            var reff = Container.GetBlobReference(String.Format(SourceFileTemplate, projectId));

            if (!reff.Exists())
            {
                reff = Container.GetBlobReference(String.Format(SourceFileTemplate, projectId));
                if (!reff.Exists())
                {
                    Log.Information("FreeAxezStorageService.GetSourceFile. Not found. projectId: {@prId}", projectId);
                    return null;
                }
            }

            using (var mem = new MemoryStream())
            {
                Log.Information("FreeAxezStorageService.GetSourceFile. Loaded. projectId: {@prId}", projectId);
                reff.DownloadToStream(mem);
                return mem.ToArray();
            }
        }

        public string SaveBuildDataJson(Guid regionId, string buildData)
        {
            var jsonName = regionId.ToString();
            Log.Information("FreeAxezStorageService.SaveBuildDataJson. regionId: {@regId}", jsonName);

            var fileName = $"{BuildDataTemplate}{jsonName}";
            CloudBlockBlob blockBlob = Container.GetBlockBlobReference(fileName);
            blockBlob.Properties.ContentEncoding = "gzip";
            var bytes = ZipString(buildData);
            blockBlob.UploadFromByteArray(bytes, 0, bytes.Length);

            return blockBlob.Uri.AbsoluteUri;
        }

        public string SaveBuildImage(Guid regionId, byte[] image)
        {
            var imageName = regionId.ToString();
            Log.Information("FreeAxezStorageService.SaveBuildImage. regionId: {@regId}", imageName);

            var fileName = $"{BuildImageTemplate}{imageName}";
            CloudBlockBlob blockBlob = Container.GetBlockBlobReference(fileName);
            blockBlob.Properties.ContentEncoding = "gzip";
            var bytes = ZipBytes(image);
            blockBlob.UploadFromByteArray(bytes, 0, bytes.Length);

            return blockBlob.Uri.AbsoluteUri;
        }

        public string GetOutputUrl(Guid projectId, Guid optionId, string fileName)
        {
            Log.Information("FreeAxezStorageService.GetOutputFile. projectId: {@prId}, optionId: {@opId}",
                projectId, optionId);
            var blobRef = String.Format(OutputFileTemplate, projectId, optionId);
            CloudBlockBlob blockBlob = Container.GetBlockBlobReference(blobRef);
            var sasToken = GetSasToken(fileName, blockBlob);
            return blockBlob.Uri.AbsoluteUri + sasToken;
        }
        public string GetBlobUrl(string blobRef)
        {
            Log.Information("FreeAxezStorageService.GetOutputFile for region. fileName: {@fileName},", blobRef);

            //if (blobRef.Contains(".json"))
            //{
            //    blobRef = blobRef.Replace(".json", ".zip");
            //}

            var blobName = blobRef.Split(new[] { $"{GetResourceName(ContainerName)}/" }, StringSplitOptions.None).Last();
            CloudBlockBlob blockBlob = Container.GetBlockBlobReference(blobName);
            var fileName = blobRef.Split('/').Last();
            var sasToken = GetSasToken(fileName, blockBlob);
            return blockBlob.Uri.AbsoluteUri + sasToken;
        }

        public void DeleteRegionFiles(string regionId)
        {
            var jsonBlob = $"{BuildDataTemplate}{regionId}.json";
            var imageBlob = $"{BuildImageTemplate}{regionId}.jpeg";
            CloudBlockBlob blockBlob = Container.GetBlockBlobReference(jsonBlob);
            if (blockBlob.Exists())
                blockBlob.Delete();
            blockBlob = Container.GetBlockBlobReference(imageBlob);
            if (blockBlob.Exists())
                blockBlob.Delete();
        }

        public string AddOutputFile(Guid projectId, Guid optionId, Stream content)
        {
            Log.Information("FreeAxezStorageService.AddOutputFile. projectId: {@prId}, optionId: {@opId}",
                projectId, optionId);
            var filePath = String.Format(OutputFileTemplate, projectId, optionId);
            CloudBlockBlob cloudBlockBlob = Container.GetBlockBlobReference(filePath);
            cloudBlockBlob.UploadFromStream(content);

            Log.Information("FreeAxezStorageService.AddOutputFile. Loaded. projectId: {@prId}, optionId: {@opId}, filePath: {@filePath}",
                projectId, optionId, filePath);
            return cloudBlockBlob.Uri.ToString();
        }

        public void AddMessage(Guid projectId, Guid optionId, string revitVersion)
        {
            Log.Information("FreeAxezStorageService.AddMessage. projectId: {@prId}, optionId: {@opId}, revitVersion: {@rvtVersion}",
                projectId, optionId, revitVersion);
            var messageData = JsonConvert.SerializeObject(new QueueMessageDto { ProjectId = projectId, OptionId = optionId, RevitVersion = revitVersion });
            var message = new CloudQueueMessage(messageData);
            _queue.AddMessage(message);
        }

        public CloudQueueMessage ReadMessage()
        {
            var msg = _queue.GetMessage(TimeSpan.FromSeconds(5));
            Log.Information("FreeAxezStorageService.ReadMessage. returned message: {@msg}", msg);
            return msg;
        }

        public CloudQueueMessage PeekMessage()
        {
            var msg = _queue.PeekMessage();
            Log.Information("FreeAxezStorageService.PeekMessage. returned message: {@msg}", msg);
            return msg;
        }

        public int GetMessagesCount()
        {
            _queue.FetchAttributes();
            return _queue.ApproximateMessageCount.Value;
        }

        public int GetCurrentMessagePosition(string Id)
        {
            var messages = _queue.PeekMessages(32);
            var curMessage = messages.FirstOrDefault(x => x.AsString.Contains(Id));
            if (curMessage != null)
                return messages.ToList().IndexOf(curMessage)+1;
            else return -1;
        }

        public QueueInfoDto GetQueueInfo(string Id)
        {
            return new QueueInfoDto()
            {
                CurrentProjectQueuePosition = GetCurrentMessagePosition(Id),
                QueueLength = GetMessagesCount()
            };
        }

        public void DeleteMessage(CloudQueueMessage message)
        {
            Log.Information("FreeAxezStorageService.DeleteMessage. message to delete: {@msg}", message);
            _queue.DeleteMessage(message);
        }

        public void AddFailedMessage(QueueMessageDto dto)
        {
            Log.Information("FreeAxezStorageService.AddFailedMessage. QueueMessageDto: {@dto}", dto);
            var messageData = JsonConvert.SerializeObject(dto);
            var message = new CloudQueueMessage(messageData);
            _failedQueue.AddMessage(message);
        }

        public string GetBuildData(Guid regionId)
        {
            var fileName = $"{BuildDataTemplate}{regionId.ToString()}";
            var reff = Container.GetBlockBlobReference(fileName);
            if (reff.Exists())
            {
                using (MemoryStream originalStream = new MemoryStream())
                {
                    reff.DownloadToStream(originalStream);
                    originalStream.Position = 0;
                    using (MemoryStream decompressedStream = new MemoryStream())
                    {
                        using (GZipStream decompressionStream = new GZipStream(originalStream, CompressionMode.Decompress))
                        {
                            decompressionStream.CopyTo(decompressedStream);
                        }

                        decompressedStream.Position = 0;
                        var sr = new StreamReader(decompressedStream);
                        var text = sr.ReadToEnd();
                        return text;
                    }
                }

            }
            else
            {
                fileName = $"{BuildDataTemplate}{regionId.ToString()}.json";
                reff = Container.GetBlockBlobReference(fileName);
                if (!reff.Exists())
                {
                    return null;
                }

                var data = reff.DownloadText();
                return data;
            }
        }

        //public async Task<bool> Rename(Guid projectId, string name)
        //{
        //    var blobRef = String.Format("source/{0}_{1}", projectId, name);
        //    var src = Container.GetBlockBlobReference(blobRef);
        //    if(src == null || !src.Exists())
        //    {
        //        return false;
        //    }
        //    CloudBlockBlob sourceBlob = (CloudBlockBlob) await Container.GetBlobReferenceFromServerAsync(blobRef);

        //    CloudBlockBlob target = Container.GetBlockBlobReference(String.Format(SourceFileTemplate, projectId));

        //    await target.StartCopyAsync(sourceBlob);

        //    while (target.CopyState.Status == CopyStatus.Pending)
        //        await Task.Delay(100);

        //    if (target.CopyState.Status != CopyStatus.Success)
        //    {
        //        throw new Exception("Rename failed: " + target.CopyState.Status);
        //    }

        //    return true;
        //}
        #region Private methods
        private string GetSasToken(string fileName, CloudBlockBlob blockBlob)
        {
            //Create an ad-hoc Shared Access Policy with read permissions which will expire in 12 hours
            SharedAccessBlobPolicy policy = new SharedAccessBlobPolicy()
            {
                Permissions = SharedAccessBlobPermissions.Read,
                SharedAccessExpiryTime = DateTime.UtcNow.AddDays(14),
            };
            //Set content-disposition header for force download
            SharedAccessBlobHeaders headers = new SharedAccessBlobHeaders()
            {
                ContentDisposition = string.Format("attachment;filename=\"{0}\"", fileName),
            };
            var sasToken = blockBlob.GetSharedAccessSignature(policy, headers);
            return sasToken;
        }

        private void CopyTo(Stream src, Stream dest)
        {
            byte[] bytes = new byte[4096];

            int cnt;

            while ((cnt = src.Read(bytes, 0, bytes.Length)) != 0)
            {
                dest.Write(bytes, 0, cnt);
            }
        }

        private byte[] ZipString(string str)
        {
            var bytes = Encoding.UTF8.GetBytes(str);

            return ZipBytes(bytes);
        }

        private byte[] ZipBytes(byte[] bytes)
        {
            using (var msi = new MemoryStream(bytes))
            using (var mso = new MemoryStream())
            {
                using (var gs = new GZipStream(mso, CompressionMode.Compress))
                {
                    CopyTo(msi, gs);
                }

                return mso.ToArray();
            }
        }

        private byte[] UnZipBytes(byte[] bytes)
        {
            using (var msi = new MemoryStream(bytes))
            using (var mso = new MemoryStream())
            {
                using (var gs = new GZipStream(mso, CompressionMode.Decompress))
                {
                    CopyTo(msi, gs);
                }

                return mso.ToArray();
            }
        }

        private string GetResourceName(string resourceName)
        {
            return _environmentName == EnvironmentConstants.ReleaseEnvironmentName ? resourceName : $"{_environmentName}-{resourceName}".ToLower();
        }
        #endregion
    }
}