﻿using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Models;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Spreadsheets;
using NPOI.SS.UserModel;
using System.Collections.Generic;
using System.Linq;
using CellType = NPOI.SS.UserModel.CellType;
using Color = SixLabors.ImageSharp.Color;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.ViewModels
{
    public class PowerBomDetailsViewModel : ISheetViewModel
    {
        private readonly Color _headerColor = Color.LightGray;
        private readonly HashSet<Border> _borders = new HashSet<Border>()
        {
            Border.Left,
            Border.Top,
            Border.Right,
            Border.Bottom,
        };

        private PowerBomRevision _powerBomRevision;


        public PowerBomDetailsViewModel(PowerBomRevision powerBomRevision)
        {
            _powerBomRevision = powerBomRevision;
        }


        public string ProjectName { get; private set; }
        public string RevisionDate { get; private set; }
        public string PlanReferenced { get; private set; }
        public string SheetName { get; private set; }
        public List<OrderRow> Report { get; private set; }


        public void Calculate()
        {
            ProjectName = _powerBomRevision.ProjectName;
            RevisionDate = _powerBomRevision.RevisionDate;
            PlanReferenced = $"Power Plan Rev {_powerBomRevision.RevisionNumber}";
            SheetName = $"Rev{_powerBomRevision.RevisionNumber} {_powerBomRevision.RevisionDate} Details";
            Report = CreateReport();
        }

        private List<OrderRow> CreateReport()
        {
            var rows = new List<OrderRow>();

            var orderedProductGroups = _powerBomRevision.ProductGroups
                .OrderBy(pg => pg, new ProductGroupPriorityComparer())
                .ThenBy(pg => pg.FloorBoxType)
                .ThenBy(pg => pg.ProductGroupName)
                .ToList();

            CreateHeaderRow(ref rows);
            foreach (var group in orderedProductGroups)
            {
                CreateRows(group, ref rows);
                CreateTotalRow(group, ref rows);
            }

            return rows;
        }

        private void CreateHeaderRow(ref List<OrderRow> rows)
        {
            var row = new OrderRow();
            var headers = GetHeaders();
            foreach (var header in headers)
            {
                var cell = new OrderCell()
                {
                    CellStyle = new OrderCellStyle()
                    {
                        Foreground = _headerColor,
                        FontBold = true,
                        Borders = _borders,
                        Alignment = HorizontalAlignment.Center
                    },
                    Value = header
                };
                row.Cells.Add(cell);
            }
            rows.Add(row);
        }

        private void CreateRows(ProductGroup group, ref List<OrderRow> rows)
        {
            var columnCount = GetHeaders().Count;

            // Header row
            var headerRow = new OrderRow();
            for (int i = 0; i < columnCount; i++)
            {
                var cell = new OrderCell()
                {
                    CellStyle = new OrderCellStyle()
                    {
                        Alignment = HorizontalAlignment.Center,
                        Foreground = _headerColor,
                        FontBold = true,
                        Borders = _borders
                    }
                };
                headerRow.Cells.Add(cell);
            }

            if (!string.IsNullOrEmpty(group.FloorBoxType))
            {
                headerRow.Cells.First().Value = $"{group.FloorBoxType}";
            }
            headerRow.Cells[1].Value = group.ProductGroupName;

            rows.Add(headerRow);

            var headerRowIndex = rows.Count;

            // Sub groups rows
            var subGroups = group.ProductViewModels
                .GroupBy(p => p.Description + p.FloorBoxType + p.ModelWithPhase + p.Length + p.Phase)
                .Select(g => g.ToList())
                .OrderBy(g =>
                {
                    double.TryParse(g.First().Length, out double parsedLength);
                    return parsedLength;
                })
                .ThenBy(g => g.First().Description)
                .ThenBy(g => g.First().FloorBoxType)
                .ThenBy(g => g.First().ModelWithPhase)
                .ThenBy(g => g.First().Phase)
                .ToList();

            foreach (var subGroup in subGroups)
            {
                var product = subGroup.First();

                var row = new OrderRow();

                // Empty FloorBoxType
                var cell = new OrderCell()
                {
                    CellStyle = new OrderCellStyle()
                    {
                        Borders = _borders,
                        Alignment = HorizontalAlignment.Center
                    }
                };
                row.Cells.Add(cell);

                // Descriptiion
                cell = new OrderCell()
                {
                    Value = product.Description,
                    CellStyle = new OrderCellStyle()
                    {
                        Borders = _borders,
                        Alignment = HorizontalAlignment.Right
                    }
                };
                row.Cells.Add(cell);

                // Model
                cell = new OrderCell()
                {
                    Value = product.ModelWithPhase,
                    CellStyle = new OrderCellStyle()
                    {
                        Borders = _borders,
                        Alignment = HorizontalAlignment.Right
                    }
                };
                row.Cells.Add(cell);

                // Length
                cell = new OrderCell()
                {
                    Value = product.Length,
                    CellType = CellType.Numeric,
                    CellStyle = new OrderCellStyle()
                    {
                        Borders = _borders,
                        Alignment = HorizontalAlignment.Left
                    }
                };
                row.Cells.Add(cell);

                // Phase
                cell = new OrderCell()
                {
                    Value = product.Phase,
                    CellStyle = new OrderCellStyle()
                    {
                        Borders = _borders,
                        Alignment = HorizontalAlignment.Center
                    }
                };
                row.Cells.Add(cell);

                // Count per level
                foreach (var levelName in _powerBomRevision.ModelLevels)
                {
                    cell = new OrderCell()
                    {
                        Value = subGroup.Count(p => p.LevelName == levelName).ToString(),
                        CellType = CellType.Numeric,
                        CellStyle = new OrderCellStyle()
                        {
                            Borders = _borders,
                            Alignment = HorizontalAlignment.Center
                        }
                    };
                    row.Cells.Add(cell);
                }

                // Quantity
                cell = new OrderCell()
                {
                    Value = $@"SUM(<<{row.Cells.Count - _powerBomRevision.ModelLevels.Count + 1}:{rows.Count + 1}>>:" +
                                $@"<<{row.Cells.Count}:{rows.Count + 1}>>)",
                    CellType = CellType.Formula,
                    CellStyle = new OrderCellStyle()
                    {
                        Borders = _borders,
                        Alignment = HorizontalAlignment.Center
                    }
                };
                row.Cells.Add(cell);

                // Ids
                cell = new OrderCell()
                {
                    Value = string.Join(",", subGroup.Select(p => p.Product.Id)),
                    CellType = CellType.String,
                    CellStyle = new OrderCellStyle()
                    {
                        Alignment = HorizontalAlignment.Fill
                    }
                };
                if (cell.Value.Length >= 32767) cell.Value = "The value is too long";
                row.Cells.Add(cell);

                rows.Add(row);
            }
        }

        private void CreateTotalRow(ProductGroup group, ref List<OrderRow> rows)
        {
            var row = new OrderRow();

            var cell = new OrderCell()
            {
                CellType = CellType.Numeric,
                CellStyle = new OrderCellStyle()
                {
                    Borders = _borders,
                }
            };
            row.Cells.Add(cell);
            row.Cells.Add(cell);

            cell = new OrderCell()
            {
                Value = "Floor Totals",
                CellStyle = new OrderCellStyle()
                {
                    FontBold = true,
                    Borders = _borders,
                    Alignment = HorizontalAlignment.Right,
                }
            };
            row.Cells.Add(cell);

            cell = new OrderCell()
            {
                CellType = CellType.Numeric,
                CellStyle = new OrderCellStyle()
                {
                    Borders = _borders,
                }
            };
            row.Cells.Add(cell);
            row.Cells.Add(cell);

            var subGroups = group.ProductViewModels
                .GroupBy(p => p.Description + p.FloorBoxType + p.ModelWithPhase + p.Length + p.Phase)
                .ToList();

            for (int i = 0; i < GetHeaders().Count - 5; i++)
            {
                cell = new OrderCell()
                {
                    Value = $@"SUM(<<{row.Cells.Count + 1}:{rows.Count - (subGroups.Count - 1)}>>:" +
                                $@"<<{row.Cells.Count + 1}:{rows.Count}>>)",
                    CellType = CellType.Formula,
                    CellStyle = new OrderCellStyle()
                    {
                        FontBold = true,
                        Borders = _borders,
                        Alignment = HorizontalAlignment.Center
                    }
                };
                row.Cells.Add(cell);
            }

            rows.Add(row);
        }

        private List<string> GetHeaders()
        {
            var headers = new List<string>
            {
                "FB Type",
                "Description",
                "Model",
                "Length",
                "Phase"
            };
            headers.AddRange(_powerBomRevision.ModelLevels);
            headers.Add("Quantity");
            return headers;
        }
    }
}
