﻿using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastucture;
using FreeAxez.Addin.Services;
using FreeAxez.Addin.Utils;
using FreeAxez.Core.Services;

namespace FreeAxez.Addin.Commands
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class AddRegionCommand : BaseExternalCommand
    {
        private readonly FreeAxezWebApiService _webApiService;
        private readonly SelectionService _selectionService;
        private readonly ProjectGeometryGatherer _geometryGatherer;
        private readonly string _fileVersion;

        public AddRegionCommand()
        {
            _webApiService = new FreeAxezWebApiService();
            _selectionService = new SelectionService();
            _geometryGatherer = new ProjectGeometryGatherer();
            _fileVersion = FileVersionInfo.GetVersionInfo
                               (Assembly.GetExecutingAssembly().Location)?.FileVersion;
        }

        public override Result Execute()
        {
            if (!WebView2Helper.CheckWebView2Installed())
            {
                return Result.Cancelled;
            }

            if (!_webApiService.CheckCurrentVersion(_fileVersion))
            {
                RevitDialogHelper.ShowNotification("You use not actual version of Revit Plugin. " +
                    "Please, go to: https://freeaxez.bimsmith.com/download             and get actual version");

                ApplicationExecutor.RunUpdatePage();
                return Result.Failed;
            }

            LogHelper.Information("Add region command.");
            var projectUniqueId = RevitManager.Document.ProjectInformation.UniqueId;
            var fileName = Path.GetFileName(RevitManager.Document.PathName);
            var projectIdGP = GlobalParameterHelper.GetProjectId(RevitManager.Document);
            LogHelper.Information($"Project id: {projectUniqueId}, fileName: {fileName}");
            Guid projectId;
            if (projectIdGP == null || !projectIdGP.HasValue)
            {
                if (!_webApiService.IsProjectExists(projectUniqueId, fileName))
                {
                    RevitDialogHelper.ShowNotification("Please create FreeAxez project first.");
                    return Result.Succeeded;
                }
                else
                {
                    var prId = _webApiService.GetProjectId(projectUniqueId, fileName);
                    projectId = prId.Value;
                    using (Transaction trans = new Transaction(RevitManager.Document, "Set global parameter"))
                    {
                        trans.Start();
                        GlobalParameterHelper.SetProjectId(projectId, RevitManager.Document);
                        trans.Commit();
                    }
                    RevitManager.Document.Save();
                }
            }
            else
            {
                projectId = projectIdGP.Value;
            }

            var regions = _selectionService.SelectRegions(RevitManager.UIApplication);
            var project = _geometryGatherer.GatherRegionsGeometry(regions, RevitManager.Document);

            if (project == null)
                return Result.Failed;

            if (regions == null || !regions.Any())
            {
                RevitDialogHelper.ShowNotification("Please select at least one region.");
                return Result.Cancelled;
            }

            RevitManager.Document.Save();
            var cleanDocService = new CleanDocumentService();
            var tempDocPath = cleanDocService.CleanCopyOfDocument(RevitManager.Document);
            if (string.IsNullOrEmpty(tempDocPath)) return Result.Failed;
            var success = _webApiService.AddRegionsToProject(project, tempDocPath);
            if (!success) return Result.Failed;
            cleanDocService.DeleteDocument(tempDocPath);

            Thread.Sleep(2000);
            ApplicationExecutor.Run(projectId, fileName);

            LogHelper.Information("Add region command completed.");
            return Result.Succeeded;
        }
    }
}