﻿namespace FreeAxez.Addin.AdditionalFunctional.Frame.Utils
{
    public static class FrameConstants
    {
        public const double ModuleLength = 100.0; // Use array frame family instead of single
        public const double FamilyWidth = 0.1;

        public const double LengthFamilyCornerLong = 1.58333;
        public const double LengthFamilyCornerShort = 0.500;

        public const double MinLengthForCornerLong = LengthFamilyCornerLong - (FamilyWidth / 2);
        public const double MinLengthForCornerShort = LengthFamilyCornerShort - (FamilyWidth / 2); //0.453

        public const double MinLength = 2.0 / 12;
    }
}
