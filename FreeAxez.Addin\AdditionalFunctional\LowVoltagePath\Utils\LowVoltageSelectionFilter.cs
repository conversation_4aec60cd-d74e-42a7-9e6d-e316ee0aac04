using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Constants;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Utils
{
    /// <summary>
    /// Selection filter for low voltage elements (lines and electrical fixtures)
    /// </summary>
    public class LowVoltageSelectionFilter : ISelectionFilter
    {
        private readonly Regex _lowVoltageLineRegex;

        public LowVoltageSelectionFilter()
        {
            _lowVoltageLineRegex = new Regex(LowVoltageConstants.LowVoltageLinePattern);
        }

        public bool AllowElement(Element elem)
        {
            // Allow CurveElements with LV/MC line styles
            if (elem is CurveElement curveElement)
            {
                return _lowVoltageLineRegex.IsMatch(curveElement.LineStyle.Name);
            }

            // Allow electrical fixtures with low voltage parameters
            if (elem.Category?.Id.IntegerValue == (int)BuiltInCategory.OST_ElectricalFixtures)
            {
                if (elem is FamilyInstance familyInstance)
                {
                    return HasLowVoltageWires(familyInstance);
                }
            }

            return false;
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            return false;
        }

        /// <summary>
        /// Checks if a family instance has low voltage wire parameters with values > 0
        /// </summary>
        private bool HasLowVoltageWires(FamilyInstance familyInstance)
        {
            var regex = new Regex(LowVoltageConstants.LowVoltageCountParameterPattern);

            foreach (Parameter param in familyInstance.ParametersMap)
            {
                if (regex.IsMatch(param.Definition.Name) && 
                    param.StorageType == StorageType.Integer && 
                    param.AsInteger() > 0)
                {
                    return true;
                }
            }

            return false;
        }
    }
}
