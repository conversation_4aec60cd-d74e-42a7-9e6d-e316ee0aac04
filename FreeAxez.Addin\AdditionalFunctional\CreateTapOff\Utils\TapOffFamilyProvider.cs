﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Exceptions;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.CreateTapOff.Utils
{
    public class TapOffFamilyProvider
    {
        private const string DualCircutKey = "DualCircuit";
        private const string Phase3Key = "3Phase";
        private const string TapOffKey = "TapOff";
        private const string GenericKey = "Generic";

        private List<FamilySymbol> _tapOffSymbols;

        public TapOffFamilyProvider()
        {
            _tapOffSymbols = GetTapOffFamilySymbols();
        }

        public bool ValidateTapOffFamilies(out string message)
        {
            if (_tapOffSymbols.Count == 0 
                || _tapOffSymbols.FirstOrDefault(s => IsDualCircuit(s)) == null 
                || _tapOffSymbols.FirstOrDefault(s => Is3PhaseCircuit(s)) == null)
            {
                message = $"The project does not contain the necessary Tap Off families for DualCircuit or 3Phase.";
                return true;
            }

            message = "";
            return false;
        }

        public List<FamilyInstance> GetAllTapOffInstances(List<int> selectedLevelIds)
        {
            return _tapOffSymbols
                .SelectMany(symbol => symbol.GetFamilyInstances())
                .Where(instance => IsOnSelectedLevel(instance, selectedLevelIds))
                .ToList();
        }

        public FamilySymbol GetTapOffSymbol(FamilySymbol trackSymbol)
        {
            var validTapOffSymbols = new List<FamilySymbol>();

            if (IsDualCircuit(trackSymbol)) validTapOffSymbols = _tapOffSymbols.Where(IsDualCircuit).ToList();
            else if (Is3PhaseCircuit(trackSymbol)) validTapOffSymbols = _tapOffSymbols.Where(Is3PhaseCircuit).ToList();

            if (validTapOffSymbols.Count == 0)
            {
                throw new FreeAxezWorkflowException(
                    $"The '{trackSymbol.FamilyName} : {trackSymbol.Name}' track phase could not be recognized.");
            }

            return validTapOffSymbols.FirstOrDefault(s => !s.Name.Contains(GenericKey)) ?? validTapOffSymbols.FirstOrDefault();
        }

        private List<FamilySymbol> GetTapOffFamilySymbols()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_ElectricalFixtures)
                .WhereElementIsElementType()
                .Cast<FamilySymbol>()
                .Where(fs => fs.FamilyName.Contains(TapOffKey))
                .ToList();
        }

        private bool IsOnSelectedLevel(FamilyInstance familyInstance, List<int> selectedLevelIds)
        {
            return selectedLevelIds.Contains(familyInstance.LevelId.GetIntegerValue());
        }

        private bool IsDualCircuit(FamilySymbol familySymbol)
        {
            return familySymbol.FamilyName.Contains(DualCircutKey);
        }

        private bool Is3PhaseCircuit(FamilySymbol familySymbol)
        {
            return familySymbol.FamilyName.Contains(Phase3Key);
        }
    }
}
