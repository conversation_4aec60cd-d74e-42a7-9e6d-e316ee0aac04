﻿using Autodesk.Revit.ApplicationServices;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using System;
using System.IO;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.ExportForClient.Utils
{
    public static class RevitFile
    {
        /// <summary>
        /// Save document as with revit save dialog in background.
        /// </summary>
        /// <returns>True if the document was saved, false otherwise.</returns>
        public static bool SaveAs(Document documentToSave, out string savedDocumentPath)
        {
            savedDocumentPath = String.Empty;

            var saveDialog = new FileSaveDialog("Revit Files (*.rvt)|*.rvt");
            saveDialog.Title = "Save As";
            saveDialog.InitialFileName = $"Export_{documentToSave.Title}.rvt";

            if (saveDialog.Show() != ItemSelectionDialogResult.Confirmed)
            {
                return false;
            }

            documentToSave.Save();
            
            var path = documentToSave.PathName;
            savedDocumentPath = ModelPathUtils.ConvertModelPathToUserVisiblePath(saveDialog.GetSelectedModelPath());
            File.Copy(path, savedDocumentPath, true);

            return true;
        }

        public static Document OpenDocumentInBackground(Application app, string paht)
        {
            var options = new OpenOptions();
            options.DetachFromCentralOption = DetachFromCentralOption.DetachAndDiscardWorksets;
            options.Audit = false;

            return app.OpenDocumentFile(new FilePath(paht), options);
        }

        /// <summary>
        /// Delete revit files with names match pattern ExportProjectName.0000.
        /// </summary>
        /// <param name="path">Path to revit file.</param>
        public static void DeleteBackupFiles(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
            {
                return;
            }

            var exportName = Path.GetFileNameWithoutExtension(path);
            var pattern = exportName + @"\.\d{4}";
            var files = Directory.GetFiles(Path.GetDirectoryName(path));

            foreach (var file in files)
            {
                var name = Path.GetFileNameWithoutExtension(file);

                if (Regex.IsMatch(name, pattern))
                {
                    File.Delete(file);
                }
            }
        }

        /// <summary>
        /// Save the revit file with the compact and close it.
        /// </summary>
        public static void SaveAndClose(Document document)
        {
            var options = new SaveOptions();
            options.Compact = true;
            document.Save(options);
            document.Close();
        }
    }
}
