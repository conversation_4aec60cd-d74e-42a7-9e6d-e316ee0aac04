﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.LevelViewManager.Helpers
{
    public class LevelHelper
    {
        public Level CreateNewLevel()
        {
            var levels = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsNotElementType()
                .OfCategory(BuiltInCategory.OST_Levels)
                .Cast<Level>()
                .OrderBy(l => l.Elevation)
                .ToList();

            var levelHeigth = 10.0;
            if (levels.Count > 1)
            {
                levelHeigth = Math.Abs(levels[levels.Count - 1].Elevation - levels[levels.Count - 2].Elevation);
            }

            var nextLevelElevation = levels[levels.Count - 1].Elevation + levelHeigth;

            Level output;

            using (var t = new Transaction(RevitManager.Document, "Add New Level"))
            {
                t.Start();
                output = Level.Create(RevitManager.Document, nextLevelElevation);
                output.Name = NextLevelName(levels[levels.Count - 1].Name);
                t.Commit();
            }

            return output;
        }

        public static int GetLevelNumber(Level level)
        {
            var levelNumberString = Regex.Match(level.Name, @"\d+").Value;
            var levelNumber = int.Parse(levelNumberString);

            return levelNumber;
        }

        public static bool LevelNameContainsNumber(string levelName)
        {
            return Regex.IsMatch(levelName, @"\d+");
        }

        public Level GetLastLevel()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsNotElementType()
                .OfCategory(BuiltInCategory.OST_Levels)
                .Cast<Level>()
                .OrderBy(l => l.Elevation)
                .Last();
        }

        private string NextLevelName(string lastLevelName)
        {
            var lastLevelNumberString = Regex.Match(lastLevelName, @"\d+").Value;
            var lastLevelNumber = int.Parse(lastLevelNumberString);
            return lastLevelName.Replace(lastLevelNumberString, $"{lastLevelNumber + 1}");
        }
    }
}
