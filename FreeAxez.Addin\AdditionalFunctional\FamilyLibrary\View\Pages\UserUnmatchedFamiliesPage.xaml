<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Pages.UserUnmatchedFamiliesPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:pages="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages"
             xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             mc:Ignorable="d"
             d:DesignHeight="300" 
             d:DesignWidth="300">
    <UserControl.Resources>
        <ResourceDictionary>
            <converters:RemoveQuotesConverter x:Key="RemoveQuotesConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <UserControl.DataContext>
        <pages:UnmatchedFamiliesPageVm/>
    </UserControl.DataContext>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <controls:HeaderBar PageName="Families"/>
        <Grid Grid.Row="1">
            <DataGrid
             Style="{StaticResource DataGridWithoutBorders}"
             Background="#F8F9FF"
             ColumnHeaderStyle="{StaticResource DataGridColumnHeader}"
             ItemsSource="{Binding UnmatchedFamilies}"
             HorizontalScrollBarVisibility="Hidden"
             AutoGenerateColumns="False"
             CanUserDeleteRows="False"
             CanUserResizeColumns="True"
             Margin="10,0"
             CanUserAddRows="False"
             CanUserReorderColumns="False"
             HeadersVisibility="Column"
             SelectionMode="Single"
             SelectionUnit="FullRow"
             IsReadOnly="True"
             x:Name="DfTypes">
                <DataGrid.Columns>
                    <DataGridTemplateColumn Header="Family Name" 
                                            Width="*" 
                                            SortMemberPath="FamilyName">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding FamilyName}"
                                           TextWrapping="Wrap" 
                                           FontWeight="SemiBold" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Family Type" 
                                            Width="*" 
                                            SortMemberPath="FamilySymbolName">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                 Text="{Binding FamilySymbolName, Converter={StaticResource RemoveQuotesConverter}}"
                                 TextWrapping="Wrap"
                                 FontWeight="Normal" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Version" 
                                            Width="*">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Version, Converter={StaticResource RemoveQuotesConverter}}"
                                           TextWrapping="Wrap"
                                           FontWeight="SemiBold"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Description" 
                                            Width="*">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="This family from current project is not contained in the library 
                                           and is considered 'unregistered' or 'external'."
                                           TextWrapping="Wrap"
                                           FontWeight="Light"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</UserControl>