﻿using System;
using System.Collections.Generic;

namespace FreeAxez.Core.Dto
{
    public class ProjectDto
    {
        public Guid Id { get; set; }
        public int RevitProjectId { get; set; }

        ///<summary>
        ///actually is NOT unique. use with file name
        /// </summary>
        public string RevitUniqueId { get; set; }
        public string RevitName { get; set; }
        public string BlobPath { get; set; }
        public string RevitVersion { get; set; }
        public List<OptionDto> Options { get; set; }
        public AutoFillOptionsDto AutoFillOptions { get; set; }
        public bool IsExternal { get; set; }
        public string AddinVersion { get; set; }
    }
}