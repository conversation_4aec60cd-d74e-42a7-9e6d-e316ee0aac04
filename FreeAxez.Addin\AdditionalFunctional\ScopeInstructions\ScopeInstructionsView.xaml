﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.ScopeInstructions.ScopeInstructionsView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.ScopeInstructions"
        mc:Ignorable="d"
        Title="Scope Instructions" Height="350" Width="500"
        ResizeMode="NoResize" WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <CollectionViewSource x:Key="ScopeInstructionsViewSource" Source="{Binding ScopeInstructions}" />
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="255"/>
            <RowDefinition Height="40"/>
        </Grid.RowDefinitions>

        <ListView ItemsSource="{Binding ScopeInstructions}" Margin="0 10 0 0" Grid.Row="0"
                  GridViewColumnHeader.Click="GridViewColumnHeaderClickedHandler" x:Name="scopeInstructions">
            <ListView.View>
                <GridView>
                    <GridViewColumn Header="Scope Number" 
                                    DisplayMemberBinding="{Binding ScopeNumber, StringFormat={}{0:F2}}"
                                    Width="90"/>
                    <GridViewColumn Header="Scope Name" Width="290">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock TextWrapping="Wrap" Text="{Binding ScopeName}"/>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Header="Actions" Width="75">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Grid.Column="0" Margin="5 0 0 0" HorizontalAlignment="Center" TextAlignment="Center" VerticalAlignment="Center">
                                    <Hyperlink NavigateUri="{Binding Url}"
                                                RequestNavigate="Hyperlink_RequestNavigate">
                                        watch
                                    </Hyperlink>
                                </TextBlock>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>

        <Button Command="{Binding CancelCommand}"
                Content="Close" Grid.Row="1" Style="{StaticResource ButtonSimpleBlue}"
                HorizontalAlignment="Right" Margin="0 5 0 0"/>
    </Grid>
</Window>
