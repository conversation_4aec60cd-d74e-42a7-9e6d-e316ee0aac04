﻿namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Models
{
    public class Connection
    {
        public Connection(CircuitElement source, CircuitElement target)
        {
            CircuitElements = new List<CircuitElement>();
            if (source != null) CircuitElements.Add(source);
            if (target != null) CircuitElements.Add(target);
        }

        public List<CircuitElement> CircuitElements { get; set; }
    }
}
