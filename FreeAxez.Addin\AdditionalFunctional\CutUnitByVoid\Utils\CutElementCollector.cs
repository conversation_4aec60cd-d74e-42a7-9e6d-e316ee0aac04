﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Services;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.CutUnitByVoid.Utils
{
    public static class CutElementCollector
    {
        public static List<Element> Collect(Element element)
        {
            var solid = GeometryHelper.GetVoidSolid(element);
            var solidWithOffset = CreateCuttingInstanceSolidWithOffset(element, 0.05);

            var solidForCollect = solidWithOffset ?? solid;

            return Collect(solidForCollect);
        }

        public static List<Element> Collect(Solid cutSolid)
        {
            // Work only with a non void solid
            //var solidFilter = new ElementIntersectsSolidFilter(cutSolid);

            var output = new FilteredElementCollector(RevitManager.Document, RevitManager.Document.ActiveView.Id)
                .WhereElementIsNotElementType()
                .WherePasses(new ElementCategoryFilter(BuiltInCategory.OST_SpecialityEquipment))
                .WherePasses(new BoundingBoxIntersectsFilter(GetOutline(cutSolid)))
                .Where(e => IsValidFamilyName(e as FamilyInstance))
                //.Where(e => solidFilter.PassesFilter(e))
                .Where(e => IntersectSolid(e as FamilyInstance, cutSolid))
                .ToList();

            return output;
        }

        private static bool IsValidFamilyName(FamilyInstance familyInstance)
        {
            var familyNameIgnoreList = new List<string>()
            {
                "Border",
                "Curb",
                "Frame",
            };

            return familyInstance.Symbol.FamilyName.Contains("FreeAxez")
                && !familyNameIgnoreList.Any(name => familyInstance.Symbol.FamilyName.Contains(name));
        }

        private static bool IntersectSolid(FamilyInstance familyInstance, Solid solid)
        {
            var elementSolids = GeometryHelper.GetAllSolids(familyInstance);
            foreach (var elementSolid in elementSolids)
            {
                try
                {
                    var intersectionSolid = BooleanOperationsUtils
                        .ExecuteBooleanOperation(elementSolid, solid, BooleanOperationsType.Intersect);

                    if (intersectionSolid?.Volume > 0)
                    {
                        return true;
                    }
                }
                catch
                {
                    // Works when the solid is void.
                    // If it was not possible to obtain the geometry,
                    // it means that there is definitely an intersection,
                    // and in this case you should try to cut out the elements.
                    return true;
                }
            }

            return false;
        }

        private static Outline GetOutline(Solid solid)
        {
            var bb = solid.GetBoundingBox();
            var origin = bb.Transform.Origin;
            return new Outline(bb.Min.Add(origin), bb.Max.Add(origin));
        }

        public static Solid CreateCuttingInstanceSolidWithOffset(Element cuttingInstance, double offset)
        {
            try
            {
                var cuttingInstanceSolid = GeometryHelper.GetVoidSolid(cuttingInstance);
                if (cuttingInstanceSolid == null)
                {
                    // Unable to get Solid from cuttingInstance
                    return null;
                }

                var bottomFace = GeometryHelper.GetFaceWithNormal(cuttingInstanceSolid, new XYZ(0, 0, 1));
                if (bottomFace == null)
                {
                    // Unable to find face with normal Z = -1
                    return null;
                }

                var offsetLoops = GeometryHelper.GetOffsetCurveLoopsFromFace(bottomFace, offset);
                if (offsetLoops == null || offsetLoops.Count == 0)
                {
                    // Unable to get offset CurveLoops
                    return null;
                }

                Solid newSolid = GeometryHelper.CreateExtrusionGeometry(offsetLoops, bottomFace.FaceNormal, 12.0);
                if (newSolid == null)
                {
                    // Unable to create extruded Solid
                    return null;
                }

                return newSolid;

            }
            catch
            {
                // If any exceptions occur, return null
                return null;
            }
        }
    }
}
