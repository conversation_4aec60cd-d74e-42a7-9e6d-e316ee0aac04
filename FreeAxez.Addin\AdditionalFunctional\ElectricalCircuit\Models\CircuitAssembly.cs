﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Enums;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Models
{
    public class CircuitAssembly
    {
        public FamilyInstance Box { get; set; }
        public FamilyInstance Track { get; set; }
        public Element Whip { get; set; }
        public FamilyInstance Panel { get; set; }
        public int BoxId => Box.Id.GetIntegerValue();
        public int TrackId => Track.Id.GetIntegerValue();
        public int WhipId => Whip.Id.GetIntegerValue();
        public int PanelId => Panel.Id.GetIntegerValue();

        public string Level
        {
            get
            {
                if (Track != null)
                {
                    return Track.get_Parameter(BuiltInParameter.FAMILY_LEVEL_PARAM).AsValueString();
                }
                else if (Box != null)
                {
                    return Box.get_Parameter(BuiltInParameter.FAMILY_LEVEL_PARAM).AsValueString();
                }
                else
                {
                    return (Whip as MEPCurve).ReferenceLevel.Name;
                }
            }
        }

        public CircuitAssemblyType CircuitAssemblyType { get; set; }

        public CircuitModelStatus CircuitModelStatus
        {
            get
            {
                if (CircuitAssemblyType == CircuitAssemblyType.Track)
                {
                    if (Track != null && Whip != null) return CircuitModelStatus.Valid;

                    if (Track != null && Whip == null) return CircuitModelStatus.NoFeedModule;
                    if (Track == null && Whip != null && !Whip.Name.Contains("Whip-FeedModule")) return CircuitModelStatus.NoFeedModule;
                    if (Track == null && Whip != null && Whip.Name.Contains("Whip-FeedModule")) return CircuitModelStatus.SingleFeedModule;
                }
                else
                {
                    if (Track != null && Whip != null && Box != null && Panel != null) return CircuitModelStatus.Valid;

                    if (Track != null && Whip != null && Box != null && Panel == null) return CircuitModelStatus.NoPanel;
                    if (Track == null && Whip != null && Box != null) return CircuitModelStatus.NoTrack;
                    if (Track != null && Whip == null && Box != null) return CircuitModelStatus.NoWhip;
                    if (Track != null && Whip != null && Box == null) return CircuitModelStatus.NoBox;

                    if (Track == null && Whip != null && Box == null && Panel == null) return CircuitModelStatus.SingleWhip;
                    if (Track == null && Whip == null && Box != null && Panel == null) return CircuitModelStatus.SingleBox;
                }

                return CircuitModelStatus.Unknown;
            }
        }

        public CircuitElectricalStatus CircuitElectricalStatus
        {
            get
            {
                if (CircuitAssemblyType == CircuitAssemblyType.Track) 
                { 
                    return CircuitElectricalStatus.Unknown; 
                }

                if (Track != null && Whip != null && Box != null && Panel != null && ValidCircuit())
                {
                    return CircuitElectricalStatus.Valid;
                }
                else if (Track != null && Whip != null && HasTrackWhip()) 
                {
                    return CircuitElectricalStatus.Valid;
                }

                return CircuitElectricalStatus.Invalid;
            }
        }

        public string TrackAssignment
        {
            get
            {
                if (Track != null)
                {
                    return Track.LookupParameter(ParameterSetter.TrackAssignmentParameterName)?.AsString();
                }
                else if (Box != null)
                {
                    return Box.LookupParameter(ParameterSetter.TrackAssignmentParameterName)?.AsString();
                }
                else
                {
                    return Whip.LookupParameter(ParameterSetter.TrackAssignmentParameterName)?.AsString();
                }
            }
        }

        public string ComponentNumber
        {
            get
            {
                if (Box != null)
                {
                    return Box.LookupParameter(ParameterSetter.ComponentNumberParameterName)?.AsString();
                }
                else if (Whip != null)
                {
                    return Whip.LookupParameter(ParameterSetter.ComponentNumberParameterName)?.AsString();
                }
                return "";
            }
        }

        public string PanelName
        {
            get
            {
                return Panel?.LookupParameter(ParameterSetter.PanelNameParameterName)?.AsString().Trim();
            }
        }

        public CreationResult CircuitCreationResult { get; private set; }

        /// <summary>
        /// If the box is connected to an electrical system in which the basic equipment does not match the panel, 
        /// then the box must be removed from this system to free the connector.
        /// </summary>
        public static void RemoveBoxesFromInvalidElectricalSystems(List<CircuitAssembly> circuitModels)
        {
            foreach (var physicalCircuit in circuitModels)
            {
                if (physicalCircuit.Box == null) continue;

                var boxElectricalCircuits = physicalCircuit.GetBoxElectricalSystems();
                if (boxElectricalCircuits.Count == 0) continue;

                var electricalCircuitsToDelete = new List<ElectricalSystem>();
                foreach (var electricalCircuit in boxElectricalCircuits)
                {
                    if (electricalCircuit.BaseEquipment?.Id.GetIntegerValue() != physicalCircuit.Panel?.Id.GetIntegerValue())
                    {
                        if (electricalCircuit.Elements.Size == 1)
                        {
                            electricalCircuitsToDelete.Add(electricalCircuit);
                        }
                        else
                        {
                            electricalCircuit.Remove(new List<ElementId>() { physicalCircuit.Box.Id });
                        }
                    }
                }
                RevitManager.Document.Delete(electricalCircuitsToDelete.Select(c => c.Id).ToList());
            }
        }

        public void CreateCircuit()
        {
            if (Panel == null || Box == null)
            {
                // Not a complete system
                CircuitCreationResult = CreationResult.Failed;
                return;
            }

            var boxConnectors = GetElectricalConnectors(Box);
            var panelConnectors = GetElectricalConnectors(Panel);
            if (boxConnectors.Count == 0 | panelConnectors.Count == 0)
            {
                // There are no electrical connectors
                CircuitCreationResult = CreationResult.Failed;
                return;
            }

            var existingCircuits = GetBoxElectricalSystems();
            foreach (var existingCircuit in existingCircuits)
            {
                if (existingCircuit != null 
                        && existingCircuit.BaseEquipment?.Id.GetIntegerValue() == Panel.Id.GetIntegerValue())
                {
                    CircuitCreationResult = CreationResult.Unchanged;
                    return;
                }
            }

            var freeBoxConnector = boxConnectors.FirstOrDefault(c => c.AllRefs == null || c.AllRefs.Size == 0);
            if (freeBoxConnector == null)
            {
                // There are no free connectors
                CircuitCreationResult = CreationResult.Failed;
                return;
            }

            try
            {
                var boxCircuit = ElectricalSystem.Create(freeBoxConnector, ElectricalSystemType.PowerCircuit);
                boxCircuit.SelectPanel(Panel);
                CircuitCreationResult = CreationResult.Created;
            }
            catch
            {
                CircuitCreationResult = CreationResult.Failed;
            }
        }

        private bool ValidCircuit()
        {
            var boxElectricalSystems = GetBoxElectricalSystems();
            if (boxElectricalSystems.Any(s => s.BaseEquipment?.Id.GetIntegerValue() == Panel?.Id.GetIntegerValue()))
            {
                return true;
            }
            return false;
        }

        private bool HasTrackWhip()
        {
            return Whip.Name.Contains("Whip-Interlink") || Whip.Name.Contains("Whip-FeedModule");
        }

        private List<ElectricalSystem> GetBoxElectricalSystems()
        {
            var output = new List<ElectricalSystem>();

#if (revit2020)
            var electricalSystemSet = Box.MEPModel.ElectricalSystems;
            if (electricalSystemSet == null || electricalSystemSet.Size == 0)
            {
                return output;
            }

            foreach (ElectricalSystem electricalSystem in electricalSystemSet)
            {
                output.Add(electricalSystem);
            }
#else
            output.AddRange(Box.MEPModel.GetElectricalSystems().ToList());
#endif
            return output;
        }

        private List<Connector> GetElectricalConnectors(FamilyInstance element)
        {
            var output = new List<Connector>();
            foreach (Connector connector in element.MEPModel.ConnectorManager.Connectors)
            {
                if (connector.Domain == Domain.DomainElectrical)
                {
                    output.Add(connector);
                }
            }
            return output;
        }
    }
}
