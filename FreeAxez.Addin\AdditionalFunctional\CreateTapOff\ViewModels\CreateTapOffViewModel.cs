﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.AdditionalFunctional.CreateTapOff.Utils;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Models;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.CreateTapOff.ViewModels
{
    public class CreateTapOffViewModel : WindowViewModel
    {
        public CreateTapOffViewModel()
        {
            Levels = GetLevelViewModels();
            CreateCommand = new RelayCommand(OnCreateCommandExecute);
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
        }

        public List<LevelViewModel> Levels { get; set; }

        public ICommand CreateCommand { get; set; }
        private void OnCreateCommandExecute(object p)
        {
            if (!Levels.Any(level => level.IsCheck))
            {
                MessageWindow.ShowDialog("No level has been selected.", MessageType.Notify);
                return;
            }

            var selectedLevels = Levels.Where(level => level.IsCheck).ToList();

            var familyProvider = new TapOffFamilyProvider();
            if (familyProvider.ValidateTapOffFamilies(out string missedFamilies))
            {
                MessageWindow.ShowDialog(missedFamilies, MessageType.Notify);
                LogHelper.Warning(missedFamilies);
                return;
            }

            var circuits = GetCircuits(selectedLevels.Select(l => l.Name).ToList());
            var levelsWithoutCircuits = selectedLevels
                .Select(l => l.Name).Except(circuits.Select(c => c.Level)).ToList();
            if (!circuits.Any())
            {
                MessageWindow.ShowDialog(
                    "There are no circuits on the selected levels " +
                    "for which Tap Off instances can be created." +
                    "\n\n" +
                    "To create a Tap Off, it is enough to have a geometric intersection " +
                    "between the instances of Track, Whip and Box.", 
                    MessageType.Notify);

                return;
            }
            else if (levelsWithoutCircuits.Any())
            {
                var result = MessageWindow.ShowDialog(
                    "In some selected levels, there are no circuits " +
                    "for which Tap Off instances can be created:\n" +
                    string.Join("\n", levelsWithoutCircuits) +
                    "\n\n" +
                    "To create a Tap Off, it is enough to have a geometric intersection " +
                    "between the instances of Track, Whip and Box." +
                    "\n\n" +
                    "Want to skip empty levels and continue?",
                    MessageType.Warning);

                if (result != true) return;
            }

            var instances = CreateTapOffInstances(circuits, familyProvider, selectedLevels);

            (p as Window).Close();

            if (instances.Any())
            {
                MessageWindow.ShowDialog(
                    $"{instances.Count} tap off instances created.", MessageType.Info);
            }
        }

        public ICommand CancelCommand { get; set; }
        private void OnCancelCommandExecute(object p)
        {
            (p as Window).Close();
        }

        private List<LevelViewModel> GetLevelViewModels()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Level))
                .Cast<Level>()
                .OrderBy(l => l.get_Parameter(BuiltInParameter.LEVEL_ELEV).AsDouble())
                .Select(l => new LevelViewModel(l))
                .ToList();
        }

        private List<CircuitAssembly> GetCircuits(List<string> levelNames)
        {
            var circuitCollector = new CircuitCollector();
            var circuits = circuitCollector.Collect();
            return circuits
                .Where(c => levelNames.Contains(c.Level))
                .Where(c => c.Box != null 
                         && c.Track != null 
                         && c.Whip != null)
                .ToList();
        }

        private List<FamilyInstance> CreateTapOffInstances(
            List<CircuitAssembly> circuits, 
            TapOffFamilyProvider familyProvider, 
            List<LevelViewModel> checkedLevels)
        {
            var output = new List<FamilyInstance>();

            using (var t = new Transaction(RevitManager.Document, "Create Tap Off"))
            {
                t.Start();

                // Delete existing instances on selected levels
                var existingInstances = familyProvider.GetAllTapOffInstances(
                    checkedLevels.Select(l => l.LevelId).ToList());
                RevitManager.Document.Delete(existingInstances.Select(i => i.Id).ToList());

                // Create new instances
                var reservedPositions = new List<XYZ>();
                foreach (var circuit in circuits)
                {
                    var tapSymbol = familyProvider.GetTapOffSymbol(circuit.Track.Symbol);
                    if (!tapSymbol.IsActive) tapSymbol.Activate();

                    var receptaclePositions = TapOffPositionLocator.GetReceptaclePositions(circuit.Track);
                    var origin = TapOffPositionLocator.GetPostition(
                        circuit.Track, circuit.Whip, receptaclePositions);

                    FamilyInstance instance = default;

                    bool isOriginReserved = reservedPositions.Any(xyz => xyz.IsAlmostEqualTo(origin));

                    if (isOriginReserved)
                    {
                        XYZ closestFreePosition = receptaclePositions
                            .Where(receptaclePosition =>
                            {
                                bool isReceptaclePositionReserved =  reservedPositions.Any(
                                    reservedPosition => 
                                        reservedPosition.IsAlmostEqualTo(receptaclePosition));

                                return !isReceptaclePositionReserved;
                            })
                            .GroupBy(xyz => xyz.DistanceTo(origin))
                            .OrderBy(grouping => grouping.Key)
                            .SelectMany(grouping => grouping)
                            .FirstOrDefault();

                        if (closestFreePosition is null)
                        {
                            bool? doesContinue = MessageWindow.ShowDialog(
                                $"There are no more free TapOff positions " +
                                $"on the track: \"{circuit.Track.Id}\".\n" +
                                $"Do you want to continue or cancel?", MessageType.Warning);

                            if (doesContinue.Value == false)
                            {
                                return Enumerable
                                    .Empty<FamilyInstance>()
                                    .ToList();
                            }

                            if (doesContinue.Value == true)
                            {
                                break;
                            }
                        }

                        instance = CreateTapOffInstance(
                            circuit.Track, 
                            circuit.Whip as FlexPipe, 
                            tapSymbol, 
                            closestFreePosition, 
                            checkedLevels);

                        if (instance is not null)
                        {
                            reservedPositions.Add(closestFreePosition);

                            TapOffParameterSetter.SetParameters(instance, circuit.Box);
                            output.Add(instance);
                        }
                    }
                    else
                    {
                        instance = CreateTapOffInstance(
                            circuit.Track, circuit.Whip as FlexPipe, tapSymbol, origin, checkedLevels);

                        if (instance is not null)
                        {
                            reservedPositions.Add(origin);
                            TapOffParameterSetter.SetParameters(instance, circuit.Box);
                            output.Add(instance);
                        }
                    }
                }

                t.Commit();
            }

            return output;
        }

        private FamilyInstance CreateTapOffInstance(
            FamilyInstance track, 
            FlexPipe whip, 
            FamilySymbol symbol, 
            XYZ origin, 
            List<LevelViewModel> checkedLevels)
        {
            // Create instance
            var level = RevitManager.Document.GetElement(track.LevelId) as Level;

            origin = new XYZ(origin.X, origin.Y, 0); // Place on level without offset

            FamilyInstance instance = default;
            if (checkedLevels.Any(checkedLevelViewModel => checkedLevelViewModel.Level.Id == level.Id))
            {
                instance = RevitManager.Document.Create.NewFamilyInstance(
                    origin, symbol, level, Autodesk.Revit.DB.Structure.StructuralType.NonStructural);
                
                var trackElev = 
                    track.get_Parameter(BuiltInParameter.INSTANCE_ELEVATION_PARAM)?.AsDouble() ?? 0;
                if (trackElev != 0)
                {
                    instance.get_Parameter(BuiltInParameter.INSTANCE_ELEVATION_PARAM)?.Set(trackElev);
                }

                // Rotate
                var angle = instance.HandOrientation.AngleOnPlaneTo(
                    track.HandOrientation.Negate(), XYZ.BasisZ);

                ElementTransformUtils.RotateElement(
                    RevitManager.Document, 
                    instance.Id, 
                    Line.CreateBound(origin, origin.Add(XYZ.BasisZ)), 
                    angle);
            }

            return instance;
        }
    }
}
