﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.Models
{
    public class BoxUnitsIntersection
    {
        public FamilyInstance Box { get; set; }
        public List<FamilyInstance> Units { get; set; }

        public string Level => Box?.get_Parameter(BuiltInParameter.FAMILY_LEVEL_PARAM).AsValueString() 
                            ?? Units.First().get_Parameter(BuiltInParameter.FAMILY_LEVEL_PARAM).AsValueString();

        public int BoxId => Box.Id.GetIntegerValue();

        public string BoxFamilyName => Box.Symbol.FamilyName;

        public string BoxTypeName => Box.Name;

        public string UnitIds => string.Join("\n", Units.Select(u => u.Id.GetIntegerValue()));

        public string UnitFamilyNames => string.Join("\n", Units.Select(u => u.Symbol.FamilyName));

        public string UnitTypeNames => string.Join("\n", Units.Select(u => u.Name));
        public string Correct => Units.Count == 1 ? "True" : "False";
    }
}