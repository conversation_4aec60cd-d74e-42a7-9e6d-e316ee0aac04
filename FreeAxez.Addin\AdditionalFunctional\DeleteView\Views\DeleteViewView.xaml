﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.DeleteView.Views.DeleteViewView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:vm="clr-namespace:FreeAxez.Addin.AdditionalFunctional.DeleteView.ViewModels"
        mc:Ignorable="d" 
        MinHeight="450" MinWidth="500"
        Height="450"
        SizeToContent="Width"
        WindowStartupLocation="CenterScreen"
        Title="Delete Views">
    <Window.DataContext>
        <vm:DeleteViewViewModel/>
    </Window.DataContext>
    <Grid Margin="5">
        <Grid.RowDefinitions>
            <RowDefinition/>
            <RowDefinition Height="35"/>
        </Grid.RowDefinitions>
        <DataGrid x:Name="dataGrid" ItemsSource="{Binding RevitViews}" AutoGenerateColumns="False" CanUserAddRows="False" HeadersVisibility="Column" SelectionUnit="FullRow" SelectionMode="Extended" EnableRowVirtualization="False">
            <DataGrid.Columns>
                <DataGridTemplateColumn SortMemberPath="IsCheck" Header="Delete">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <CheckBox HorizontalAlignment="Center" IsChecked="{Binding IsCheck, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Checked="CheckBox_Checked" Unchecked="CheckBox_Checked"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Binding="{Binding Name}" Header="View" MinWidth="200" IsReadOnly="True"/>
            </DataGrid.Columns>
        </DataGrid>
        <StackPanel Margin="0,5,0,0" Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="Check All" Width="100" Command="{Binding CheckAllCommand}"/>
            <Button Margin="5,0,0,0" Content="Uncheck All" Width="100" Command="{Binding UncheckAllCommand}"/>
            <Button Margin="5,0,0,0" Content="Delete" Width="100" Command="{Binding DeleteViewsCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
            <Button Margin="5,0,0,0" Content="Cancel" Width="100" Command="{Binding CancelCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
        </StackPanel>
    </Grid>
</Window>
