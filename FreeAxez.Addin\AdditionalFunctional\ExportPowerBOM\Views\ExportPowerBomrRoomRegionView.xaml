﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.ExportPowerBOM.Views.ExportPowerBomrRoomRegionView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.ExportPowerBOM.Views"
        xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
        xmlns:viewModels="clr-namespace:FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.ViewModels"
        mc:Ignorable="d"
        Title="ExportPowerBomrRoomRegionView" Height="450" Width="800">
    <Window.DataContext>
        <viewModels:ExportGriddBomRoomRegionViewModel/>
    </Window.DataContext>
    <Window.Resources>
        <ResourceDictionary>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <StackPanel>
            <TextBlock Text="Folder To Export To:"
                       Style="{StaticResource TextH5}"/>
            <Grid Margin="0 5 0 10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBox Grid.Column="0"  
                         Text="{Binding ExportFolderPath}"
                         TextWrapping="NoWrap" 
                         Tag="Select a folder to export data to ..."
                         IsReadOnly="True"
                         Style="{StaticResource UiTextBox}"
                         VerticalContentAlignment="Center" 
                         Margin="0 0 10 0" />
                <Button Grid.Column="1" 
                        Content="Browse..." 
                        Style="{StaticResource ButtonOutlinedGreen}"
                        Command="{Binding BrowseFolderCommand}" />
            </Grid>

            <StackPanel Orientation="Vertical" Margin="0 10 0 0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="Revision Number:" VerticalAlignment="Center" Style="{StaticResource TextH5}" Grid.Column="0"/>
                    <TextBlock Text="Exported Area Name:" VerticalAlignment="Center" Style="{StaticResource TextH5}" Grid.Column="2" Margin="10,0,0,0" Visibility="{Binding IsRegionSelected, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </Grid>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Orientation="Horizontal" Grid.Column="0" Margin="0 10 0 0">
                        <TextBlock Text="Rev#" FontWeight="Bold" Margin="5,0,5,0" VerticalAlignment="Center"/>
                        <TextBox Text="{Binding RevisionNumber, UpdateSourceTrigger=PropertyChanged}" 
                                 Width="50" 
                                 Margin="0" 
                                 Style="{StaticResource UiTextBox}"
                                 VerticalAlignment="Center"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Grid.Column="2" Margin="0 10 0 0">
                        <TextBox Text="{Binding AreaName, UpdateSourceTrigger=PropertyChanged}" 
                                 Width="150" 
                                 Margin="10,0,0,0" 
                                 Style="{StaticResource UiTextBox}"
                                 VerticalAlignment="Center"
                                 Visibility="{Binding IsRegionSelected, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    </StackPanel>
                </Grid>
            </StackPanel>

            <TextBlock Text="Accessories"
                       Margin="0 10 0 5"
                       Style="{StaticResource TextH5}"/>
        </StackPanel>
        <ListBox Grid.Row="1" 
                 x:Name="Accessories" 
                 ItemsSource="{Binding Accessories}" 
                 SelectionMode="Extended">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal">
                        <CheckBox IsChecked="{Binding IsChecked, Mode=TwoWay}" 
                                  Style="{StaticResource CheckBoxStyle}"
                                  Margin="0,0,5,0" Checked="CheckBox_Checked" 
                                  Unchecked="CheckBox_Checked"/>
                        <TextBlock Text="{Binding Name}"/>
                    </StackPanel>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
        <StackPanel Grid.Row="2">
            <Grid Margin="0 10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Button Grid.Column="0"
                        Style="{StaticResource ButtonOutlinedGreen}"
                        Command="{Binding CheckAllCommand}">
                    <StackPanel Orientation="Horizontal">
                        <ContentControl Template="{StaticResource CircleCheckIcon}" 
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                        <TextBlock Text="Check All"
                                   Margin="5 0"
                                   Foreground="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button Grid.Column="2"
                        Style="{StaticResource ButtonOutlinedPurple}"
                        Command="{Binding UncheckAllCommand}">
                    <StackPanel Orientation="Horizontal">
                        <ContentControl Template="{StaticResource CircleUncheckIcon}" 
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                        <TextBlock Text="Uncheck All"
                                   Foreground="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}"
                                   Margin="5 0"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </Grid>
            <CheckBox  IsChecked="{Binding OpenFile}" 
                       Style="{StaticResource CheckBoxStyle}"
                       Margin="0 10 0 10"
                       Content="Open the file after export" 
                       VerticalAlignment="Center"/>
            <Button Style="{StaticResource ButtonSimpleBlue}"
                    Command="{Binding ExportCommand}" 
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}" >
                <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource ExcelIcon}" 
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                    <TextBlock Text="Export"
                               Foreground="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}"
                               Margin="5 0"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </StackPanel>
    </Grid>
</Window>
