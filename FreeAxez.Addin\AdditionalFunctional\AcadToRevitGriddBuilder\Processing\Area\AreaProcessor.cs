using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Infrastructure;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Workflow;
using NetTopologySuite.Geometries;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.Area
{
    /// <summary>
    /// Unified processor for area data - converts CAD lines to floors in Revit
    /// </summary>
    public class AreaProcessor
    {

        /// <summary>
        /// Processes area lines from JSON and creates floors in Revit
        /// </summary>
        public void ProcessAreaData(List<JsonLineData> areaLines, Document document, Level level, Transform dwgTransform, BuildResult result)
        {

            try
            {
                if (areaLines == null || !areaLines.Any())
                {
                    result.AreaProcessingMessage = "No area lines found in A-AREA-PATT layer";
                    return;
                }

                // Step 1: Group lines into connected components
                var areaComponents = GroupAreaLines(areaLines);

                if (!areaComponents.Any())
                {
                    result.AreaProcessingMessage = "No valid area components found";
                    return;
                }

                // Step 2: Convert to floor profiles
                var allProfiles = ConvertAreaComponentsToFloorProfiles(areaComponents, level, dwgTransform);

                if (!allProfiles.Any())
                {
                    result.AddWarning("No valid floor profiles could be created from area components");
                    return;
                }

                // Step 3: Group profiles into floor shapes and openings
                var (floorShapes, openingShapes) = GroupAreaProfilesByType(allProfiles);

                // Step 4: Combine floors with their openings for gridd area floors
                var floorWithOpeningsProfiles = CombineFloorsWithOpenings(floorShapes, openingShapes);

                // Step 5: Create floors in Revit
                var (overallAreaFloors, griddAreaFloors, createdFloors, errors) = CreateFloorsFromAreaProfiles(
                    floorShapes, floorWithOpeningsProfiles, level, document);

                // Step 6: Update result
                if (errors.Any())
                {
                    result.Success = false;
                    result.Errors.AddRange(errors);
                }

                result.UpdateAreaProcessing(overallAreaFloors, griddAreaFloors, createdFloors,
                    $"Successfully created {overallAreaFloors + griddAreaFloors} floors from area data");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.AddError($"Error processing area data: {ex.Message}");
            }
        }

        private List<List<LineSegmentData>> GroupAreaLines(List<JsonLineData> areaLines)
        {
            // Convert JSON lines to line segments
            var lineSegments = areaLines.Select((line, index) => new LineSegmentData
            {
                Index = index,
                Data = line,
                Segment = new NetTopologySuite.Geometries.LineSegment(
                    new NetTopologySuite.Geometries.Coordinate(line.startPoint.x, line.startPoint.y),
                    new NetTopologySuite.Geometries.Coordinate(line.endPoint.x, line.endPoint.y)
                ),
                IsPrimaryEdge = true // All area lines are considered primary
            }).ToList();

            // Group connected lines using Union-Find algorithm
            return GroupConnectedLines(lineSegments);
        }

        private List<List<LineSegmentData>> GroupConnectedLines(List<LineSegmentData> lineSegments)
        {
            var groups = new List<List<LineSegmentData>>();
            var visited = new HashSet<int>();

            foreach (var segment in lineSegments)
            {
                if (visited.Contains(segment.Index))
                    continue;

                var group = new List<LineSegmentData>();
                var queue = new Queue<LineSegmentData>();
                queue.Enqueue(segment);

                while (queue.Count > 0)
                {
                    var current = queue.Dequeue();
                    if (visited.Contains(current.Index))
                        continue;

                    visited.Add(current.Index);
                    group.Add(current);

                    // Find connected segments
                    foreach (var other in lineSegments)
                    {
                        if (visited.Contains(other.Index))
                            continue;

                        if (AreSegmentsConnected(current, other))
                        {
                            queue.Enqueue(other);
                        }
                    }
                }

                if (group.Count > 0)
                {
                    groups.Add(group);
                }
            }

            return groups;
        }

        private bool AreSegmentsConnected(LineSegmentData segment1, LineSegmentData segment2)
        {
            const double tolerance = 0.01; // 1/100 inch tolerance

            var p1Start = segment1.Segment.P0;
            var p1End = segment1.Segment.P1;
            var p2Start = segment2.Segment.P0;
            var p2End = segment2.Segment.P1;

            // Check if any endpoint of segment1 is close to any endpoint of segment2
            return p1Start.Distance(p2Start) < tolerance ||
                   p1Start.Distance(p2End) < tolerance ||
                   p1End.Distance(p2Start) < tolerance ||
                   p1End.Distance(p2End) < tolerance;
        }

        /// <summary>
        /// Converts area components to floor profiles
        /// </summary>
        private List<List<CurveLoop>> ConvertAreaComponentsToFloorProfiles(
            List<List<LineSegmentData>> areaComponents, Level level, Transform dwgTransform)
        {
            var allProfiles = new List<List<CurveLoop>>();

            foreach (var component in areaComponents)
            {
                try
                {
                    var profiles = ConvertComponentToFloorProfiles(component, level, dwgTransform);
                    if (profiles.Any())
                        allProfiles.AddRange(profiles);
                }
                catch (Exception ex)
                {
                    // Skip invalid components
                    continue;
                }
            }

            return allProfiles;
        }

        /// <summary>
        /// Groups area profiles by type (floor shapes vs openings)
        /// </summary>
        private (List<List<CurveLoop>> floorShapes, List<List<CurveLoop>> openingShapes) GroupAreaProfilesByType(
            List<List<CurveLoop>> allProfiles)
        {
            var floorShapes = new List<List<CurveLoop>>();
            var openingShapes = new List<List<CurveLoop>>();

            foreach (var profile in allProfiles)
            {
                if (profile.Count == 1)
                {
                    // Single curve loop - treat as floor shape
                    floorShapes.Add(profile);
                }
                else
                {
                    // Multiple curve loops - first is outer boundary, rest are openings
                    var outerLoop = new List<CurveLoop> { profile[0] };
                    floorShapes.Add(outerLoop);

                    for (int i = 1; i < profile.Count; i++)
                    {
                        var opening = new List<CurveLoop> { profile[i] };
                        openingShapes.Add(opening);
                    }
                }
            }

            return (floorShapes, openingShapes);
        }

        /// <summary>
        /// Combines floors with their openings for gridd area floors
        /// </summary>
        private List<List<CurveLoop>> CombineFloorsWithOpenings(
            List<List<CurveLoop>> floorShapes, List<List<CurveLoop>> openingShapes)
        {
            var combinedProfiles = new List<List<CurveLoop>>();

            foreach (var floorShape in floorShapes)
            {
                var combinedProfile = new List<CurveLoop>(floorShape);

                // Find openings that are inside this floor
                foreach (var opening in openingShapes)
                {
                    if (IsOpeningInsideFloor(floorShape[0], opening[0]))
                    {
                        combinedProfile.AddRange(opening);
                    }
                }

                combinedProfiles.Add(combinedProfile);
            }

            return combinedProfiles;
        }

        /// <summary>
        /// Creates floors from area profiles
        /// </summary>
        private (int overallAreaFloors, int griddAreaFloors, List<Floor> createdFloors, List<string> errors)
            CreateFloorsFromAreaProfiles(List<List<CurveLoop>> floorShapes, List<List<CurveLoop>> floorWithOpeningsProfiles,
            Level level, Document document)
        {
            var createdFloors = new List<Floor>();
            var errors = new List<string>();
            int overallAreaFloors = 0;
            int griddAreaFloors = 0;

            try
            {
                // Get floor types
                var overallAreaFloorType = GetFloorType(document, "OVERALL AREA");
                var griddAreaFloorType = GetFloorType(document, "GRIDD AREA");

                using (var transaction = new Transaction(document, "Create Area Floors"))
                {
                    transaction.Start();

                    // Create overall area floors (simple shapes)
                    foreach (var profile in floorShapes)
                    {
                        try
                        {
                            var floor = CreateFloor(document, profile, overallAreaFloorType, level);
                            if (floor != null)
                            {
                                createdFloors.Add(floor);
                                overallAreaFloors++;
                            }
                        }
                        catch (Exception ex)
                        {
                            errors.Add($"Failed to create overall area floor: {ex.Message}");
                        }
                    }

                    // Create gridd area floors (with openings)
                    foreach (var profile in floorWithOpeningsProfiles)
                    {
                        try
                        {
                            var floor = CreateFloor(document, profile, griddAreaFloorType, level);
                            if (floor != null)
                            {
                                createdFloors.Add(floor);
                                griddAreaFloors++;
                            }
                        }
                        catch (Exception ex)
                        {
                            errors.Add($"Failed to create gridd area floor: {ex.Message}");
                        }
                    }

                    transaction.Commit();
                }
            }
            catch (Exception ex)
            {
                errors.Add($"Error creating floors: {ex.Message}");
            }

            return (overallAreaFloors, griddAreaFloors, createdFloors, errors);
        }

        /// <summary>
        /// Gets floor type by name
        /// </summary>
        private FloorType GetFloorType(Document document, string typeName)
        {
            var floorType = new FilteredElementCollector(document)
                .OfClass(typeof(FloorType))
                .Cast<FloorType>()
                .FirstOrDefault(ft => ft.Name.Equals(typeName, StringComparison.OrdinalIgnoreCase));

            if (floorType == null)
                throw new InvalidOperationException($"Floor type '{typeName}' not found in document.");

            return floorType;
        }

        /// <summary>
        /// Creates a floor from curve loops
        /// </summary>
        private Floor CreateFloor(Document document, List<CurveLoop> profile, FloorType floorType, Level level)
        {
            if (!profile.Any()) return null;

#if revit2020 || revit2021
            var curveArray = GetCurveArray(profile[0]);
            return document.Create.NewFloor(curveArray, floorType, level, false);
#else
            return Floor.Create(document, profile, floorType.Id, level.Id);
#endif
        }

        /// <summary>
        /// Converts component to floor profiles
        /// </summary>
        private List<List<CurveLoop>> ConvertComponentToFloorProfiles(List<LineSegmentData> component, Level level, Transform dwgTransform)
        {
            // Convert line segments to curves
            var curves = new List<Curve>();
            foreach (var segment in component)
            {
                var startPoint = new XYZ(segment.Segment.P0.X / 12.0, segment.Segment.P0.Y / 12.0, level.Elevation);
                var endPoint = new XYZ(segment.Segment.P1.X / 12.0, segment.Segment.P1.Y / 12.0, level.Elevation);

                if (dwgTransform != null && !dwgTransform.IsIdentity)
                {
                    startPoint = dwgTransform.OfPoint(startPoint);
                    endPoint = dwgTransform.OfPoint(endPoint);
                }

                curves.Add(Line.CreateBound(startPoint, endPoint));
            }

            // Create curve loop
            var curveLoop = CurveLoop.Create(curves);
            return new List<List<CurveLoop>> { new List<CurveLoop> { curveLoop } };
        }

        /// <summary>
        /// Checks if opening is inside floor
        /// </summary>
        private bool IsOpeningInsideFloor(CurveLoop floorLoop, CurveLoop openingLoop)
        {
            // Simple check - if opening center is inside floor boundary
            var openingCenter = GetCurveLoopCenter(openingLoop);
            return IsPointInsideCurveLoop(openingCenter, floorLoop);
        }

        /// <summary>
        /// Gets center point of curve loop
        /// </summary>
        private XYZ GetCurveLoopCenter(CurveLoop curveLoop)
        {
            var points = curveLoop.Select(c => c.GetEndPoint(0)).ToList();
            var centerX = points.Average(p => p.X);
            var centerY = points.Average(p => p.Y);
            var centerZ = points.Average(p => p.Z);
            return new XYZ(centerX, centerY, centerZ);
        }

        /// <summary>
        /// Checks if point is inside curve loop
        /// </summary>
        private bool IsPointInsideCurveLoop(XYZ point, CurveLoop curveLoop)
        {
            // Simple ray casting algorithm
            int intersections = 0;
            var testRay = Line.CreateBound(point, new XYZ(point.X + 1000, point.Y, point.Z));

            foreach (var curve in curveLoop)
            {
                var intersection = curve.Intersect(testRay);
                if (intersection == SetComparisonResult.Overlap)
                    intersections++;
            }

            return intersections % 2 == 1;
        }

#if revit2020 || revit2021
        /// <summary>
        /// Converts curve loop to curve array for older Revit versions
        /// </summary>
        private CurveArray GetCurveArray(CurveLoop curveLoop)
        {
            var curveArray = new CurveArray();
            foreach (var curve in curveLoop)
            {
                curveArray.Append(curve);
            }
            return curveArray;
        }
#endif
    }
}
