﻿using FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Models;
using FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.ViewModels
{
    public class TagAllCurbsViewModel : BaseViewModel
    {
        public double _lengthToCenter;

        public TagAllCurbsViewModel()
        {
            _lengthToCenter = Properties.Settings.Default.TagAllCurbsLengthToCenterOfTag;
            TagAll = Properties.Settings.Default.TagCurbsVisibleInView;
            TagSelected = !TagAll;

            CreateCommand = new RelayCommand(OnCreateCommandExecute);
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
        }

        public bool TagAll { get; set; }
        public bool TagSelected { get; set; }
        public string LengthToCenter
        {
            get
            {
                return ProjectUnitsConverter.FormatLengthToFractionalInches(_lengthToCenter);
            }
            set
            {
                if (ProjectUnitsConverter.TryParseLengthFromFractionalInches(value, out _lengthToCenter))
                {
                    OnPropertyChanged();
                }
            }
        }

        public ICommand CreateCommand { get; set; }
        private void OnCreateCommandExecute(object p)
        {
            SaveSettings();
            (p as Window).Close();

            var tagCreator = new TagCurbsCreator(new CurbTagOptions(_lengthToCenter, TagAll));

            var createdTags = tagCreator.CreateAnnatation();
            InfoDialog.ShowDialog("Report", $"Created {createdTags.Count} tags.");
        }

        public ICommand CancelCommand { get; set; }
        private void OnCancelCommandExecute(object p)
        {
            (p as Window).Close();
        }

        private void SaveSettings()
        {
            Properties.Settings.Default.TagAllCurbsLengthToCenterOfTag = _lengthToCenter;
            Properties.Settings.Default.TagCurbsVisibleInView = TagAll;
            Properties.Settings.Default.Save();
        }
    }
}

