using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Threading;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Interfaces;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.AutoCAD;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Workflow;
using FreeAxez.Addin.Infrastructure;
using Settings = FreeAxez.Addin.Properties.Settings;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.UI.ViewModels
{
    /// <summary>
    /// Simplified ViewModel for Gridd Builder configuration and execution
    /// </summary>
    public class GriddBuilderViewModel : BaseViewModel, IProgressReporter
    {
        private readonly Document _document;
        private readonly DwgLinkInfo _dwgLinkInfo;
        private int _baseUnitHeight = 40;
        private bool _isProcessing;
        private double _progressValue;
        private string _stepStatus;
        private string _taskStatus;
        private CancellationTokenSource _cancellationTokenSource;

        public GriddBuilderViewModel(Document document, DwgLinkInfo dwgLinkInfo)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _dwgLinkInfo = dwgLinkInfo ?? throw new ArgumentNullException(nameof(dwgLinkInfo));

            BuildCommand = new RelayCommand(_ => ExecuteBuild(), _ => CanExecuteBuild());
            CancelCommand = new RelayCommand(_ => Cancel());

            LoadUserSettings();
        }

        // Display properties
        public string DwgLinkName => _dwgLinkInfo.GetDisplayName();
        public string LevelName => _dwgLinkInfo.Level?.Name ?? "Unknown Level";

        public int BaseUnitHeight
        {
            get => _baseUnitHeight;
            set
            {
                if (Set(ref _baseUnitHeight, value))
                {
                    SaveUserSettings();
                    BuildCommand.RaiseCanExecuteChanged();
                }
            }
        }

        public bool IsProcessing
        {
            get => _isProcessing;
            set
            {
                if (Set(ref _isProcessing, value))
                {
                    BuildCommand.RaiseCanExecuteChanged();
                }
            }
        }

        public double ProgressValue
        {
            get => _progressValue;
            set => Set(ref _progressValue, value);
        }

        public string StepStatus
        {
            get => _stepStatus;
            set => Set(ref _stepStatus, value);
        }

        public string TaskStatus
        {
            get => _taskStatus;
            set => Set(ref _taskStatus, value);
        }

        // Commands
        public RelayCommand BuildCommand { get; }
        public RelayCommand CancelCommand { get; }

        // Events
        public event EventHandler<BuildResult> BuildCompleted;
        public event EventHandler Cancelled;

        private void LoadUserSettings()
        {
            var savedHeight = Settings.Default.GriddBuilderBaseUnitHeight;
            if (savedHeight == 40 || savedHeight == 70)
            {
                _baseUnitHeight = savedHeight;
                OnPropertyChanged(nameof(BaseUnitHeight));
            }
        }

        private void SaveUserSettings()
        {
            Settings.Default.GriddBuilderBaseUnitHeight = BaseUnitHeight;
            Settings.Default.Save();
        }

        private bool CanExecuteBuild()
        {
            return !IsProcessing &&
                   _dwgLinkInfo.IsValid &&
                   IsValidHeight(BaseUnitHeight);
        }

        private static bool IsValidHeight(int height) => height == 40 || height == 70;

        private void ExecuteBuild()
        {
            if (!CanExecuteBuild()) return;

            IsProcessing = true;
            ProgressValue = 0;
            StepStatus = "";
            TaskStatus = "";

            _cancellationTokenSource = new CancellationTokenSource();
            var cancellationToken = _cancellationTokenSource.Token;

            // Use Dispatcher.BeginInvoke to keep UI responsive while staying in main thread
            Dispatcher.CurrentDispatcher.BeginInvoke(new Action(() =>
            {
                try
                {
                    var result = ExecuteWorkflow(this, cancellationToken);
                    BuildCompleted?.Invoke(this, result);
                }
                catch (OperationCanceledException)
                {
                    var cancelledResult = BuildResult.CreateFailure(DwgLinkName, "Operation was cancelled by user");
                    BuildCompleted?.Invoke(this, cancelledResult);
                }
                catch (Exception ex)
                {
                    var errorResult = BuildResult.CreateFailure(DwgLinkName, ex.Message);
                    BuildCompleted?.Invoke(this, errorResult);
                }
                finally
                {
                    IsProcessing = false;
                    ProgressValue = 0;
                    StepStatus = "";
                    TaskStatus = "";
                    _cancellationTokenSource = null;
                }
            }), DispatcherPriority.Background);
        }

        private BuildResult ExecuteWorkflow(IProgressReporter progressReporter, CancellationToken cancellationToken)
        {
            var workflow = new GriddBuilderWorkflow(new AutoCADService());
            return workflow.Execute(_document, _dwgLinkInfo, BaseUnitHeight, progressReporter, cancellationToken);
        }

        private void Cancel()
        {
            if (IsProcessing && _cancellationTokenSource != null)
            {
                // Cancel the operation
                _cancellationTokenSource.Cancel();
                ReportStatus("Operation cancelled by user");
            }
            else
            {
                // Just close the window if not processing
                Cancelled?.Invoke(this, EventArgs.Empty);
            }
        }

        // IProgressReporter implementation
        public void ReportProgress(double value)
        {
            ProgressValue = value;
        }

        public void ReportStatus(string status)
        {
            TaskStatus = status;
        }

        public void ReportStepStatus(string status)
        {
            StepStatus = status;
        }
    }
}
