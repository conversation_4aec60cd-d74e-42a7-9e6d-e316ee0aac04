﻿using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;

namespace FreeAxez.Addin.AdditionalFunctional.LegendManagement.Models
{
    public class SheetSize
    {
        public SheetSize(long id, string name)
        {
            Id = id;
            Name = name;
        }

        public long Id { get; }
        public string Name { get; }

        public override bool Equals(object obj)
        {
            if (obj is null)
            {
                return false;
            }

            if (obj is SheetSize sheetSize)
            {
                return sheetSize.Name.Equals(Name, System.StringComparison.OrdinalIgnoreCase);
            }

            return false;
        }


        public override int GetHashCode()
        {
            return Name.GetHashCode();
        }
    }
}
