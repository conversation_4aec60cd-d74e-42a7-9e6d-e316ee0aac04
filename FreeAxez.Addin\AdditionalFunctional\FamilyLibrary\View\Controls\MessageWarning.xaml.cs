using System.Windows;
using System.Windows.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls;

public partial class MessageWarning : UserControl
{
    public static readonly DependencyProperty MessageProperty = 
        DependencyProperty.Register("Message", typeof(string), typeof(MessageWarning), new PropertyMetadata(string.Empty));

    public string Message
    {
        get { return (string)GetValue(MessageProperty); }
        set { SetValue(MessageProperty, value); }
    }
    public MessageWarning()
    {
        InitializeComponent();
        DataContext = this;
    }
}