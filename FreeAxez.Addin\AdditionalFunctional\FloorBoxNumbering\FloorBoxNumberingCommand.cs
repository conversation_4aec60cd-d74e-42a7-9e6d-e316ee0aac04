﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.AdditionalFunctional.FloorBoxNumbering.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Models;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.FloorBoxNumbering
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    public class FloorBoxNumberingCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            if (!(RevitManager.UIDocument.ActiveView is ViewPlan))
            {
                MessageWindow.ShowDialog("The plugin only works with plans.", MessageType.Notify);
                return Result.Cancelled;
            }

            var settingWindow = new FloorBoxNumberingView();
            settingWindow.ShowDialog();
            if (settingWindow.DialogResult != true)
            {
                return Result.Cancelled;
            }

            var selectionRadioButtons = new RadioButton[]
            {
                settingWindow.floorBoxRadioButton,
                settingWindow.grommetRadioButton,
                settingWindow.junctionBoxRadioButton,
                settingWindow.powerLVReceptaclesRadioButton
            };

            RadioButton checkedSelectionRadioButton = selectionRadioButtons
                .FirstOrDefault(radioButton => radioButton.IsChecked ?? false)
                ?? selectionRadioButtons.First();

            ISelectionFilter selectionFilter = checkedSelectionRadioButton switch
            {
                RadioButton radioButton when radioButton == settingWindow.floorBoxRadioButton
                    => FloorBox.CreateSelectionFilter(),
                RadioButton radioButton when radioButton == settingWindow.grommetRadioButton
                    => Grommet.CreateSelectionFilter(),
                RadioButton radioButton when radioButton == settingWindow.junctionBoxRadioButton
                    => JunctionBox.CreateSelectionFilter(),
                RadioButton radioButton when radioButton == settingWindow.powerLVReceptaclesRadioButton
                    => new LogicalOrSelectionFilter(new List<ISelectionFilter>()
                    {
                        Receptacle.CreateSelectionFilter(),
                        PowerVoiceDataFloorBox.CreateSelectionFilter(),
                    }),
                _ => FloorBox.CreateSelectionFilter()
            };

            var boxes = SelectFloorBoxes(selectionFilter);
            if (!boxes.Any())
            {
                MessageWindow.ShowDialog("No floor boxes selected.", MessageType.Notify);
                return Result.Cancelled;
            }

            boxes = SortElements(boxes);

            MarkElements(boxes, out int lastNumber);
            SaveNewStartNumberToProperties(lastNumber);

            MessageWindow.ShowDialog($"The mark was set for {boxes.Count} floor boxes.", MessageType.Info);

            return Result.Succeeded;
        }

        private List<Element> SelectFloorBoxes(ISelectionFilter selectionFilter)
        {
            var boxes = new List<Element>();

            try
            {
                boxes = RevitManager.UIDocument.Selection
                    .PickObjects(ObjectType.Element,selectionFilter, "Select floor boxes for marking.")
                    .Select(RevitManager.Document.GetElement)
                    .ToList();
            }
            catch { }

            return boxes;
        }

        private List<Element> SortElements(List<Element> boxes)
        {
            if (Properties.Settings.Default.FloorBoxNumberingHorizontalDirection)
            {
                boxes = boxes.OrderBy(b => (b.Location as LocationPoint).Point.X).ThenByDescending(b => (b.Location as LocationPoint).Point.Y).ToList();
            }
            else
            {
                boxes = boxes.OrderByDescending(b => (b.Location as LocationPoint).Point.Y).ThenBy(b => (b.Location as LocationPoint).Point.X).ToList();
            }

            return boxes;
        }

        private void MarkElements(List<Element> boxes, out int lastNumber)
        {
            var startNumber = Properties.Settings.Default.FloorBoxNumberingStartNumber;
            var prefix = Properties.Settings.Default.FloorBoxNumberingPrefix;

            using (var t = new Transaction(RevitManager.Document, "Floor Boxes Marking"))
            {
                t.Start();

                foreach (var box in boxes)
                {
                    var markValue = $"{prefix}{startNumber}";
                    box.LookupParameter("Component Number")?.Set(markValue);
                    startNumber++;
                }

                t.Commit();
            }

            lastNumber = startNumber;
        }

        private void SaveNewStartNumberToProperties(int startNumber)
        {
            Properties.Settings.Default.FloorBoxNumberingStartNumber = startNumber;
            Properties.Settings.Default.Save();
        }
    }
}
