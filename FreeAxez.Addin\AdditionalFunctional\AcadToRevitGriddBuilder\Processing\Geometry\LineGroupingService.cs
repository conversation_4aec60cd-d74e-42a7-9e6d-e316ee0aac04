﻿using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using NetTopologySuite;
using NetTopologySuite.Geometries;
using NetTopologySuite.Index.Strtree;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.Geometry;

public class LineGroupingService
{
    // Common constants
    private const double ConnectionTolerance = 2.0;

    private readonly GeometryFactory _gf;

    public LineGroupingService()
    {
        _gf = NtsGeometryServices.Instance.CreateGeometryFactory(0);
    }

    public static List<List<LineSegmentData>> BuildConnectedComponents(List<LineSegmentData> segs)
    {
        var n = segs.Count;
        var ds = new DisjointSet(n);
        var tree = new STRtree<int>();
        for (var i = 0; i < n; i++)
            tree.Insert(
                new Envelope(segs[i].Segment.MinX, segs[i].Segment.MaxX, segs[i].Segment.MinY, segs[i].Segment.MaxY),
                i);
        for (var i = 0; i < n; i++)
        {
            var s = segs[i].Segment;
            var env = new Envelope(s.MinX, s.MaxX, s.MinY, s.MaxY);
            env.ExpandBy(ConnectionTolerance);
            foreach (var j in tree.Query(env))
                if (j > i && s.Distance(segs[j].Segment) <= ConnectionTolerance)
                    ds.Union(i, j);
        }

        var dict = new Dictionary<int, List<LineSegmentData>>();
        for (var i = 0; i < n; i++)
        {
            var r = ds.Find(i);
            if (!dict.ContainsKey(r)) dict[r] = new List<LineSegmentData>();
            dict[r].Add(segs[i]);
        }

        return dict.Values.ToList();
    }

    private class DisjointSet
    {
        private readonly int[] parent, rank;

        public DisjointSet(int n)
        {
            parent = Enumerable.Range(0, n).ToArray();
            rank = new int[n];
        }

        public int Find(int x)
        {
            return parent[x] == x ? x : parent[x] = Find(parent[x]);
        }

        public void Union(int a, int b)
        {
            a = Find(a);
            b = Find(b);
            if (a == b) return;
            if (rank[a] < rank[b])
            {
                parent[a] = b;
            }
            else
            {
                parent[b] = a;
                if (rank[a] == rank[b]) rank[a]++;
            }
        }
    }
}