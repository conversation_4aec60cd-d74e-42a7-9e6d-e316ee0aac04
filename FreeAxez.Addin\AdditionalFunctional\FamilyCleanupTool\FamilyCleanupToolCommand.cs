﻿using System.Text.RegularExpressions;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyCleanupTool
{
    [Transaction(TransactionMode.Manual)]
    public class FamilyCleanupToolCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            ReplaceOldFamilies();
            TaskDialog.Show("Family Cleanup Completed",
                "Duplicated families have been removed from the project.");
            return Result.Succeeded;
        }

        public void ReplaceOldFamilies()
        {
            LogHelper.Information($"Starting families cleaning up: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}");

            var allFamilies = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Family))
                .Cast<Family>()
                .ToList();

            var familyNameDict = allFamilies.ToDictionary(f => f.Name, f => f);

            var regex = new Regex(@"^(.*?)(\d+)$");
            var familiesWithNumbers = allFamilies
                .Where(f => regex.IsMatch(f.Name))
                .ToList();

            var oldFamilies = new List<Family>();
            var familyNameMapping = new Dictionary<string, string>();

            foreach (var family in familiesWithNumbers)
            {
                var match = regex.Match(family.Name);
                if (match.Success)
                {
                    var baseName = match.Groups[1].Value;
                    if (familyNameDict.ContainsKey(baseName))
                    {
                        oldFamilies.Add(family);
                        familyNameMapping[family.Name] = baseName;
                    }
                }
            }

            var oldFamilyNames = oldFamilies.Select(f => f.Name).ToHashSet();

            var oldFamilyInstances = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(FamilyInstance))
                .Cast<FamilyInstance>()
                .Where(fi => oldFamilyNames.Contains(fi.Symbol.Family.Name))
                .ToList();

            var familySymbolsDict = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(FamilySymbol))
                .Cast<FamilySymbol>()
                .GroupBy(fs => $"{fs.Family.Name}|{fs.Name}")
                .ToDictionary(g => g.Key, g => g.First());

            using (var trans = new Transaction(RevitManager.Document, "Replace Old Family Instances"))
            {
                CommonFailuresPreprocessor.SetFailuresPreprocessor(trans);
                trans.Start();

                var parametersToSkip = new HashSet<string> { "Version", "Product Name", "Description" };

                foreach (var oldInstance in oldFamilyInstances)
                {
                    var oldFamilyName = oldInstance.Symbol.Family.Name;
                    var oldTypeName = oldInstance.Symbol.Name;

                    if (familyNameMapping.TryGetValue(oldFamilyName, out var newFamilyName))
                    {
                        var key = $"{newFamilyName}|{oldTypeName}";
                        if (familySymbolsDict.TryGetValue(key, out var newFamilySymbol))
                        {
                            var parameterValues = GetParameterValues(oldInstance, parametersToSkip);
                            oldInstance.ChangeTypeId(newFamilySymbol.Id);
                            ApplyParameterValues(oldInstance, parameterValues);
                        }
                    }
                }

                trans.Commit();
            }

            using (var trans = new Transaction(RevitManager.Document, "Delete Old Families"))
            {
                trans.Start();

                RevitManager.Document.Delete(oldFamilies.Select(f => f.Id).ToList());

                trans.Commit();
            }

            LogHelper.Information($"Finished families cleaning up: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}");
        }

        private Dictionary<string, (StorageType, object)> GetParameterValues(FamilyInstance instance,
            HashSet<string> parametersToSkip)
        {
            var parameterValues = new Dictionary<string, (StorageType, object)>();
            foreach (Parameter param in instance.ParametersMap)
            {
                if (parametersToSkip.Contains(param.Definition.Name)) continue;

                var storageType = param.StorageType;
                object value = storageType switch
                {
                    StorageType.Double => param.AsDouble(),
                    StorageType.Integer => param.AsInteger(),
                    StorageType.String => param.AsString(),
                    StorageType.ElementId => param.AsElementId(),
                    _ => null
                };

                if (value != null) parameterValues[param.Definition.Name] = (storageType, value);
            }

            return parameterValues;
        }

        private void ApplyParameterValues(FamilyInstance instance,
            Dictionary<string, (StorageType, object)> parameterValues)
        {
            var parameters = CollectParameters(instance);

            foreach (var kvp in parameterValues)
            {
                if (parameters.TryGetValue(kvp.Key, out var param) && !param.IsReadOnly)
                {
                    switch (kvp.Value.Item1)
                    {
                        case StorageType.Double:
                            param.Set((double)kvp.Value.Item2);
                            break;
                        case StorageType.Integer:
                            param.Set((int)kvp.Value.Item2);
                            break;
                        case StorageType.String:
                            param.Set((string)kvp.Value.Item2);
                            break;
                        case StorageType.ElementId:
                            param.Set((ElementId)kvp.Value.Item2);
                            break;
                    }
                }
            }
        }

        private Dictionary<string, Parameter> CollectParameters(FamilyInstance instance)
        {
            return instance.ParametersMap
                .Cast<Parameter>()
                .ToDictionary(p => p.Definition.Name);
        }
    }
}