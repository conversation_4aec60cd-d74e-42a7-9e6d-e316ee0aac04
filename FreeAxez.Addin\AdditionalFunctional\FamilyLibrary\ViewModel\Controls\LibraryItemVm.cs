﻿using System.Collections.ObjectModel;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;

public class LibraryItemVm : BaseViewModel
{
    private bool _isRowDetailsVisible;

    private ObservableCollection<LibraryItemDto> _remainingVersions;

    public LibraryItemVm(LibraryItemDto item)
    {
        LibraryItem = item;
    }

    public LibraryItemDto LibraryItem { get; }

    public bool IsRowDetailsVisible
    {
        get => _isRowDetailsVisible;
        set
        {
            _isRowDetailsVisible = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<LibraryItemDto> RemainingVersions
    {
        get => _remainingVersions;
        set
        {
            _remainingVersions = value;
            OnPropertyChanged();
        }
    }
}