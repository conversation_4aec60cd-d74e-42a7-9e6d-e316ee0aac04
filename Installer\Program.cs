﻿using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using WixSharp;
using WixSharp.CommonTasks;
using WixSharp.Controls;


var _guid = "5867d457-8d73-488a-80bc-ae807f92c600";
var _projectName = "FreeAxez Revit Plugin";
var _msiOutputDir = "Output";
var _targetDllSearchPattern = "*FreeAxez.Addin.dll";
var _installationDir = @"%AppDataFolder%\Autodesk\Revit\Addins\";

var _sourceFiles = Path.Combine(Directory.GetCurrentDirectory(), "Output", "Temp");
var _targetVersion = FileVersionInfo.GetVersionInfo(new DirectoryInfo(_sourceFiles)
    .GetFiles(_targetDllSearchPattern, SearchOption.AllDirectories).First().FullName).FileVersion;

var _msiName = $"FreeAxezRevitPlugin-{_targetVersion}";


var project = new Project
{
    Name = _projectName,
    OutDir = _msiOutputDir,
    Platform = Platform.x64,
    UI = WUI.WixUI_InstallDir,
    MajorUpgrade = MajorUpgrade.Default,
    GUID = new Guid(_guid),
    Version = new Version(_targetVersion),
    InstallScope = InstallScope.perUser,
    OutFileName = _msiName,
    ControlPanelInfo =
    {
        Manufacturer = "Anguleris Technologies",
        HelpTelephone = "+****************",
        HelpLink = "<EMAIL>",
    },
    Dirs =
    [
        new InstallDir(_installationDir, GenerateWixEntities(_sourceFiles))
    ]
};

project.RemoveDialogsBetween(NativeDialogs.WelcomeDlg, NativeDialogs.VerifyReadyDlg);
project.BuildMsi();
Directory.Delete(_sourceFiles, true);


WixEntity[] GenerateWixEntities(string releaseDir)
{
    return new Files().GetAllItems(releaseDir);
}
