﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class PowerVoiceDataFloorBox : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_ElectricalFixtures,
            FamilyNamesContains = new List<string>()
            {
                "Power_Voice_Low_Voltage"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public PowerVoiceDataFloorBox(Element element) : base(element)
        {
        }

        public static List<PowerVoiceDataFloorBox> Collect()
        {
            return FamilyCollector.Instances.Select(g => new PowerVoiceDataFloorBox(g)).ToList();
        }

        public static ISelectionFilter CreateSelectionFilter()
        {
            return new PowerVoiceDataFloorBoxSelectionFilter();
        }

        private class PowerVoiceDataFloorBoxSelectionFilter : ISelectionFilter
        {
            private readonly List<ElementId> _symbolIds;

            public PowerVoiceDataFloorBoxSelectionFilter()
            {
                _symbolIds = FamilyCollector.Symbols.Select(s => s.Id).ToList();
            }

            public bool AllowElement(Element elem)
            {
                return elem is FamilyInstance familyInstance && _symbolIds.Any(s => familyInstance.Symbol.Id.Equals(s));
            }

            public bool AllowReference(Reference reference, XYZ position)
            {
                return true;
            }
        }
    }
}
