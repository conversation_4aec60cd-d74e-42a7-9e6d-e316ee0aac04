using System.Windows;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views
{
    /// <summary>
    /// Interaction logic for DwgLayerManagerWindow.xaml
    /// </summary>
    public partial class DwgLayerManagerWindow : Window
    {
        public DwgLayerManagerWindow()
        {
            InitializeComponent();
            DataContext = new DwgLayerManagerWindowViewModel();
        }
    }
}

