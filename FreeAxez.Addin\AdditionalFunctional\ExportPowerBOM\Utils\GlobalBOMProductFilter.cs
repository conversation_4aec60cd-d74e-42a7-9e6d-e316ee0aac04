﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Utils
{
    public static class GlobalBOMProductFilter
    {
        public static Regex ByOtherRegex = new Regex(@"By Others", RegexOptions.IgnoreCase); 

        public static bool PassesFilter(Element element)
        {
            var elementType = RevitManager.Document.GetElement(element.GetTypeId()) as ElementType;
            if (elementType == null) return false;

            var modelParameterValue = elementType.get_Parameter(BuiltInParameter.ALL_MODEL_MODEL)?.AsString();
            if (modelParameterValue != null && ByOtherRegex.IsMatch(modelParameterValue))
            {
                return false;
            }

            return true;
        }
    }
}
