﻿using Autodesk.Revit.DB;

namespace FreeAxez.Addin.Infrastructure
{
    public static class ExtensionMethods
    {
        public static int GetIntegerValue(this ElementId elementId)
        {
#if revit2020 || revit2021 || revit2022 || revit2023
            return elementId.IntegerValue;
#else
            return (int)elementId.Value;
#endif
        }

        public static List<FamilyInstance> GetFamilyInstances(this FamilySymbol familySymbol)
        {
            return familySymbol
                .GetDependentElements(new ElementClassFilter(typeof(FamilyInstance)))
                .Select(familySymbol.Document.GetElement)
                .Cast<FamilyInstance>()
                .Where(i => i.Symbol.Id.Equals(familySymbol.Id))
                .ToList();
        }
    }
}
