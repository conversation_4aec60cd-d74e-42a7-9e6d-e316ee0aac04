﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using FreeAxez.Addin.Infrastructure;
using System;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllRailings.Utils
{
    public static class TagParameter
    {
        public const string TagParameterName = "Quantity";


        public static bool ParameterNotAssignedToTheRailing()
        {
            Category railingCategory = Category.GetCategory(RevitManager.Document, BuiltInCategory.OST_StairsRailing);

            BindingMap map = RevitManager.Document.ParameterBindings;
            DefinitionBindingMapIterator it = map.ForwardIterator();
            it.Reset();
            while (it.MoveNext())
            {
                if (it.Key.Name != TagParameterName)
                {
                    continue;
                }

                if (!(it.Current as ElementBinding).Categories.Contains(railingCategory))
                {
                    return true;
                }

                return false;
            }

            return true;
        }

        public static bool HasValue(Railing railing)
        {
            var tagParameterValue = Math.Round(railing.LookupParameter(TagParameterName).AsDouble(), 5);
            return tagParameterValue != 0;
        }
    }
}
