﻿using System.Diagnostics;
using Autodesk.Revit.DB;

namespace FreeAxez.Addin.Infrastructure.GeometryUtils
{
    using Autodesk.Revit.DB;
    using System.Collections.Generic;

    namespace FreeAxez.Addin.Infrastructure.GeometryUtils
    {
        public class PointInPoly
        {
            /// <summary>
            /// Проверить, находится ли точка <paramref name="point"/> внутри многоугольника <paramref name="polygon"/>.
            /// Координаты вершин <paramref name="polygon"/> и точки <paramref name="point"/> 
            /// должны быть в одной и той же плоскости/системе координат!
            /// </summary>
            public bool PolygonContains(List<UV> polygon, UV point)
            {
                int intersections = 0;

                for (int i = 0; i < polygon.Count; i++)
                {
                    UV a = polygon[i];
                    UV b = polygon[(i + 1) % polygon.Count]; 

                    if (RayIntersectsSegment(point, a, b))
                    {
                        intersections++;
                    }
                }

                return (intersections % 2) == 1;
            }

            private bool RayIntersectsSegment(UV p, UV a, UV b)
            {
                if (a.V > b.V)
                {
                    UV tmp = a;
                    a = b;
                    b = tmp;
                }

                if (p.V <= a.V || p.V > b.V)
                {
                    return false;
                }

                if (p.U > System.Math.Max(a.U, b.U))
                {
                    return false;
                }

                double xIntersect = a.U + (p.V - a.V) * (b.U - a.U) / (b.V - a.V);

                return (xIntersect >= p.U);
            }
        }
    }

}