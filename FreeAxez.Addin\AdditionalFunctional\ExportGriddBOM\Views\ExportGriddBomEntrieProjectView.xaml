﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Views.ExportGriddBomView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:viewModels="clr-namespace:FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.ViewModels"
        Title="Export Gridd BOM"
        Width="400"
        Height="700"
        WindowStartupLocation="CenterScreen">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height ="Auto"/>
            <RowDefinition Height ="*"/>
            <RowDefinition Height ="Auto"/>
        </Grid.RowDefinitions>
        <StackPanel>
            <TextBlock Text="File To Export:"
                       Style="{StaticResource TextH5}"
            />
            <Grid Margin="0 5 0 10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBox Grid.Column="0"  
                         Text="{Binding FilePath}"
                         TextWrapping="NoWrap" 
                         Tag="Select a file to export data to ..."
                         IsReadOnly="True"
                         Style="{StaticResource UiTextBox}"
                         VerticalContentAlignment="Center" 
                         Margin="0 0 10 0" />
                <Button Grid.Column="1" 
                        Content="Browse..." 
                        Style="{StaticResource ButtonOutlinedGreen}"
                        Command="{Binding BrowseCommand}" />
            </Grid>
            
            <TextBlock Grid.Row="2" Grid.Column="0" Text="Sheet:" Style="{StaticResource TextH5}"/>
            <ComboBox Grid.Row="2" Grid.Column="1" ItemsSource="{Binding Sheets}" SelectedItem="{Binding SelectedSheet}" VerticalContentAlignment="Center"/>

            <CheckBox  IsChecked="{Binding OpenFile}" 
                       Style="{StaticResource CheckBoxStyle}"
                       Margin="0 10 0 0"
                       Content="Open the file after export" 
                       VerticalAlignment="Center"/>
            <TextBlock Text="Accessories"
                       Margin="0 10 0 5"
                       Style="{StaticResource TextH5}"/>
        </StackPanel>
        <ListBox Grid.Row="1" 
                 x:Name="Accessories" 
                 ItemsSource="{Binding Accessories}" 
                 SelectionMode="Extended">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal">
                        <CheckBox IsChecked="{Binding IsChecked, Mode=TwoWay}" 
                                  Style="{StaticResource CheckBoxStyle}"
                                  Margin="0,0,5,0" Checked="CheckBox_Checked" 
                                  Unchecked="CheckBox_Checked"/>
                        <TextBlock Text="{Binding Name}"/>
                    </StackPanel>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
        <StackPanel Grid.Row="2">
            <Grid Margin="0 10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Button Grid.Column="0"
                        Style="{StaticResource ButtonOutlinedGreen}"
                        Command="{Binding CheckAllCommand}">
                    <StackPanel Orientation="Horizontal">
                        <ContentControl Template="{StaticResource CircleCheckIcon}" 
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                        <TextBlock Text="Check All"
                                   Margin="5 0"
                                   Foreground="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button Grid.Column="2"
                        Style="{StaticResource ButtonOutlinedPurple}"
                        Command="{Binding UncheckAllCommand}">
                    <StackPanel Orientation="Horizontal">
                        <ContentControl Template="{StaticResource CircleUncheckIcon}" 
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                        <TextBlock Text="Uncheck All"
                                   Foreground="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}"
                                   Margin="5 0"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </Grid>
            <Button Style="{StaticResource ButtonSimpleBlue}"
                    Command="{Binding ExportCommand}" 
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}" >
                <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource ExcelIcon}" 
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                    <TextBlock Text="Export"
                               Foreground="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}"
                               Margin="5 0"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </StackPanel>
    </Grid>
</Window>
