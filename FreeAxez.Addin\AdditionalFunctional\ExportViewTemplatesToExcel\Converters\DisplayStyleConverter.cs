﻿using Autodesk.Revit.DB;
using OfficeOpenXml;

namespace FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Converters
{
    public class DisplayStyleConverter
    {
        private static readonly Dictionary<DisplayStyle, string> DisplayStyleMap = new()
        {
            { DisplayStyle.Undefined, "Undefined" },
            { DisplayStyle.Wireframe, "Wireframe" },
            { DisplayStyle.HLR, "Hidden Line" },
            { DisplayStyle.Shading, "Shading" },
            { DisplayStyle.ShadingWithEdges, "Shading With Edges" },
            { DisplayStyle.Rendering, "Rendering" },
            { DisplayStyle.Realistic, "Realistic" },
            { DisplayStyle.FlatColors, "Flat Colors" },
            { DisplayStyle.RealisticWithEdges, "Realistic With Edges" }
        };

        public static string ConvertDisplayStyle(DisplayStyle style)
        {
            if (DisplayStyleMap.TryGetValue(style, out var displayString)) return displayString;
            return "Unknown";
        }

        public static void AddDisplayStyleValidation(ExcelWorksheet ws, string cellAddress)
        {
            var dv = ws.DataValidations.AddListValidation(cellAddress);
            foreach (var style in DisplayStyleMap.Values) dv.Formula.Values.Add(style);
            dv.ShowErrorMessage = true;
            dv.ErrorTitle = "Invalid Display Style";
            dv.Error = "Please select a valid display style.";
        }

        public static DisplayStyle ParseDisplayStyle(string displayString)
        {
            var reverseMap = new Dictionary<string, DisplayStyle>();
            foreach (var kvp in DisplayStyleMap)
                reverseMap[kvp.Value] = kvp.Key;

            if (reverseMap.TryGetValue(displayString, out var ds))
                return ds;

            return DisplayStyle.Undefined; 
        }
    }
}
