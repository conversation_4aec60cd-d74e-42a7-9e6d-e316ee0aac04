<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Pages.AdminCategoriesPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             xmlns:pages="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages"
             xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls"
             mc:Ignorable="d"
             d:DesignHeight="840"
             d:DesignWidth="1350">
    <UserControl.Resources>
        <ResourceDictionary>
            <converters:LibraryItemsToNamesConverter x:Key="LibraryItemsToNamesConverter"/>
            <converters:BooleanToYesNoConverter x:Key="BooleanToYesNoConverter"/>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        <controls:HeaderBar PageName="Categories"/>

        <!-- Loading Indicator -->
        <Grid Grid.Row="1" Grid.RowSpan="2"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
              Background="White">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <Ellipse Style="{StaticResource LoadingSpinner}" Margin="0,0,0,20"/>
                <TextBlock Text="Loading categories..."
                           Style="{StaticResource TextBase}"
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <DataGrid
            Style="{DynamicResource DataGridWithoutBorders}"
            ColumnHeaderStyle="{DynamicResource DataGridColumnHeader}"
            VirtualizingStackPanel.IsVirtualizing="True"
            VirtualizingStackPanel.VirtualizationMode="Recycling"
            EnableRowVirtualization="True"
            EnableColumnVirtualization="True"
            ItemsSource="{Binding Categories}"
            HorizontalScrollBarVisibility="Visible"
            Grid.Row="1"
            AutoGenerateColumns="False"
            CanUserDeleteRows="False"
            CanUserResizeColumns="True"
            Margin="10,0"
            CanUserAddRows="False"
            CanUserReorderColumns="False"
            HeadersVisibility="Column"
            SelectionMode="Single"
            SelectionUnit="FullRow"
            IsReadOnly="True"
            x:Name="DgTypes">
            <DataGrid.Columns>
                <DataGridTextColumn
                    Binding="{Binding CategoryName}"
                    Header="Category Name"
                    Width="250" />
                <DataGridTextColumn
                    Binding="{Binding IsFreeAxezCategory, Converter={StaticResource BooleanToYesNoConverter}}"
                    Header="FreeAxez Category"
                    Width="150" />
                <DataGridTextColumn
                    Binding="{Binding Description}"
                    Header="Description"
                    Width="250" />
                <DataGridTemplateColumn
                    Header="Family Names"
                    Width="*">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock TextWrapping="Wrap">
                                <TextBlock.Text>
                                    <MultiBinding StringFormat="{}{0}">
                                        <Binding Path="LibraryItems"
                                                 Converter="{StaticResource LibraryItemsToNamesConverter}" />
                                    </MultiBinding>
                                </TextBlock.Text>
                            </TextBlock>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Header="Actions"
                                        Width="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Margin="5 5"
                                        Command="{Binding DataContext.EditCategoryCommand,
                                        RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                        CommandParameter="{Binding}"
                                        Style="{StaticResource RoundIconButton}">
                                    <ContentControl Template="{StaticResource PencilIcon}" HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"/>
                                </Button>
                                <Button Margin="5 5"
                                        Command="{Binding DataContext.DeleteCategoryCommand,
                                        RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                        CommandParameter="{Binding}"
                                        Style="{StaticResource RoundIconButton}">
                                    <ContentControl Template="{StaticResource TrashIcon}" HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"/>
                                </Button>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        <StackPanel Orientation="Horizontal"
                    Grid.Row="2"
                    HorizontalAlignment="Right">
            <Button Content="Add Category"
                    Style="{StaticResource ButtonSimpleBlue}"
                    Margin="0 0 10 0"
                    Command="{Binding AddCategoryCommand}">
            </Button>
        </StackPanel>
    </Grid>
</UserControl>
