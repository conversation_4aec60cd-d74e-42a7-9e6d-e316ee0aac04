﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.Pallets.Utils
{
    public class PalletFamilyManager
    {
        private const string SinglePalletFamilyNameRegex = @"Staging-Pallet\d*$"; // Access_Flooring-FreeAxez-Gridd-Staging-Pallet
        private List<string> _requiredPalletFamilySymbolNames = new List<string>()
        {
            "Base Units",
            "Channel Plates",
            "Corner Plates",
            "Half Base Units",
        };

        private List<FamilySymbol> _palletFamilySymbols;
        private Dictionary<PalletType, FamilySymbol> _familySymbolByPalletType;


        public PalletFamilyManager()
        {
            var singlePalletFamilyNameRegex = new Regex(SinglePalletFamilyNameRegex);

            _palletFamilySymbols = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsElementType()
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .Cast<FamilySymbol>()
                .Where(s => singlePalletFamilyNameRegex.IsMatch(s.FamilyName.Replace("_", "-")))
                .ToList();
        }


        public bool ProjectContainsRequiredFamily()
        {
            return _palletFamilySymbols.Count > 0;
        }

        public bool ProjectContainsRequiredSymbols(out List<string> missedSymbolNames)
        {
            missedSymbolNames = _requiredPalletFamilySymbolNames
                .Where(n => !_palletFamilySymbols.Any(s => s.Name == n))
                .ToList();

            return missedSymbolNames.Count == 0;
        }

        public FamilySymbol GetFamilySymbolForPalletType(PalletType palletType)
        {
            if (_familySymbolByPalletType == null)
            {
                MatchSymbolsWithPalletTypes();
            }

            return _familySymbolByPalletType[palletType];
        }

        private void MatchSymbolsWithPalletTypes()
        {
            _familySymbolByPalletType = new Dictionary<PalletType, FamilySymbol>();

            _familySymbolByPalletType.Add(PalletType.BaseUnit, _palletFamilySymbols.Find(s => s.Name == "Base Units"));
            _familySymbolByPalletType.Add(PalletType.ChannelPlate, _palletFamilySymbols.Find(s => s.Name == "Channel Plates"));
            _familySymbolByPalletType.Add(PalletType.CornerPlate, _palletFamilySymbols.Find(s => s.Name == "Corner Plates"));
            _familySymbolByPalletType.Add(PalletType.HalfBaseUnit, _palletFamilySymbols.Find(s => s.Name == "Half Base Units"));

            _familySymbolByPalletType.Add(PalletType.HighCapacityBaseUnit, _palletFamilySymbols.Find(s => s.Name == "Base Units"));
            _familySymbolByPalletType.Add(PalletType.HighCapacityHalfBaseUnit, _palletFamilySymbols.Find(s => s.Name == "Half Base Units"));

            _familySymbolByPalletType.Add(PalletType.ReinforcedChannelPlate, _palletFamilySymbols.Find(s => s.Name == "Channel Plates"));
            _familySymbolByPalletType.Add(PalletType.ReinforcedCornerPlate, _palletFamilySymbols.Find(s => s.Name == "Corner Plates"));
        }
    }
}
