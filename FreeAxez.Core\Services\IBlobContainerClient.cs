﻿using System;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using Azure.Storage.Blobs.Models;
using Azure;

namespace FreeAxez.Core.Services
{
    public interface IBlobContainerClient
    {
        Task<Response<BlobContentInfo>> UploadFileAsync(
            string blobName, Stream stream, bool overwrite = true, CancellationToken cancellationToken = default);
        Task<Response<bool>> DeleteFileIfExistsAsync(string blobName, CancellationToken cancellationToken = default);
        Task<Response<bool>> ExistsAsync(string blobName, CancellationToken cancellationToken = default);
        string GetBlobClientAbsolutePath(string blobName);
    }
}
