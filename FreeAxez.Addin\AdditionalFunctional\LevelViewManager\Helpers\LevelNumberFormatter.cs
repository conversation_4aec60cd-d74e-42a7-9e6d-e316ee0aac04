﻿using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.LevelViewManager.Helpers
{
    public class LevelNumberFormatter
    {
        private List<Regex> _levelNameRegexes;
        private List<string> _levelNamePatterns = new List<string>()
        {
            @"LEVEL \d+",
            @"\WL\d+\W"
        };

        private Regex _sheetNumberBasedOnLevelRegex = 
            new Regex(@"\d+", RegexOptions.IgnoreCase);

        public LevelNumberFormatter()
        {
            _levelNameRegexes = new List<Regex>();
            
            foreach (var pattern in _levelNamePatterns)
            {
                _levelNameRegexes.Add(new Regex(pattern, RegexOptions.IgnoreCase));
            }
        }

        public bool ContainsLevel(string name)
        {
            return _levelNameRegexes.Any(r => r.<PERSON>(name));
        }

        public string ReplaceLevelNumber(string name, int levelNumber)
        {
            foreach (var regex in _levelNameRegexes)
            {
                if (regex.IsMatch(name))
                {
                    var levelSubstring = regex.Match(name).Value;

                    var numberSubstring = Regex.Match(levelSubstring, @"\d+").Value;
                    var newNumberSubstring = levelNumber.ToString($"D{numberSubstring.Length}");

                    var newLevelSubstring = levelSubstring.Replace(numberSubstring, newNumberSubstring);

                    var newName = name.Replace(levelSubstring, newLevelSubstring);

                    return newName;
                }
            }

            return name;
        }

        public string ReplaceLevelNumberInSheetNumber(string sheetNumber, int nextLevelNumber)
        {
            var levelNumberRegex = new Regex(@"\d+");
            var numberSubstring = levelNumberRegex.Match(sheetNumber).Value; // Example: G-I-07A-24, G-Z-L1
            var newNumberSubstring = nextLevelNumber.ToString($"D{numberSubstring.Length}");
            return levelNumberRegex.Replace(sheetNumber, newNumberSubstring, 1);
        }

        public bool IsViewDependsOnLevel(string name)
        {
            return _levelNameRegexes.Any(r => r.IsMatch(name));
        }

        public bool IsSheetDependsOnLevel(string number)
        {
            if (_sheetNumberBasedOnLevelRegex.IsMatch(number) == false)
            {
                return false;
            }

            var numberString = _sheetNumberBasedOnLevelRegex.Match(number).Value;
            return int.Parse(numberString) > 0; // Exclude G00-C-24
        }

        public string ViewNameKey(string name)
        {
            // Replace 06 to 6
            foreach (Match numberSubstring in Regex.Matches(name, @"\d+"))
            {
                var number = int.Parse(numberSubstring.Value);
                name = name.Replace(numberSubstring.Value, number.ToString());
            }

            return name.ToLower().Replace(" ", "");
        }
    }
}
