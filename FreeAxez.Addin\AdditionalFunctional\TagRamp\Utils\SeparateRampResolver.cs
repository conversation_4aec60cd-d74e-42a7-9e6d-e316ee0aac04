﻿using Autodesk.Revit.DB;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagRamp.Utils
{
    public class SeparateRampResolver
    {
        private const double OutlineTolerance = 0.1640419948;

        public List<List<FamilyInstance>> Resolve(List<FamilyInstance> familyInstances)
        {
            List<FamilyInstance> orderedFamilyInstances = familyInstances
                .OrderBy(f => (f.Location as LocationPoint).Point.X)
                .ThenBy(f => (f.Location as LocationPoint).Point.Y)
                .ToList();

            List<List<FamilyInstance>> groups = new List<List<FamilyInstance>>();

            while (orderedFamilyInstances.Count > 0)
            {
                FamilyInstance familyInstance = orderedFamilyInstances.First();
                Outline familyInstanceOutline = GetOutlineFromFamilyInstance(familyInstance);
                orderedFamilyInstances.Remove(familyInstance);

                List<FamilyInstance> currentGroup = new List<FamilyInstance>{ familyInstance };

                FindIntersectingFamilyInstances(orderedFamilyInstances, familyInstanceOutline, currentGroup);

                groups.Add(currentGroup);
            }

            return groups;
        }

        private void FindIntersectingFamilyInstances(List<FamilyInstance> orderedFamilyInstances,
                                                     Outline referenceOutline,
                                                     List<FamilyInstance> currentGroup)
        {
            List<FamilyInstance> intersectingInstances = orderedFamilyInstances
                .Where(candidate =>
                {
                    Outline candidateInstanceOutline = GetOutlineFromFamilyInstance(candidate);

                    return referenceOutline.Intersects(
                        candidateInstanceOutline, OutlineTolerance);
                })
                .ToList();

            foreach (FamilyInstance intersectingInstance in intersectingInstances)
            {
                orderedFamilyInstances.Remove(intersectingInstance);
                currentGroup.Add(intersectingInstance);

                FindIntersectingFamilyInstances(orderedFamilyInstances,
                                                GetOutlineFromFamilyInstance(intersectingInstance),
                                                currentGroup);
            }
        }

        private Outline GetOutlineFromFamilyInstance(FamilyInstance familyInstance)
        {
            Options geometryOptions = new Options
            {
                DetailLevel = ViewDetailLevel.Fine
            };

            GeometryElement familyGeometry = familyInstance.get_Geometry(geometryOptions);
            BoundingBoxXYZ familyGeometryBoundingBox = familyGeometry.GetBoundingBox();

            return new Outline(familyGeometryBoundingBox.Min, familyGeometryBoundingBox.Max);
        }
    }
}