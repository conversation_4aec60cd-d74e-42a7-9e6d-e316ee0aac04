﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.Infrastructure;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.Models
{
    /// <summary>
    /// Elements without solid geometry. 
    /// Has only vertical lines and annotation lines that only appear on plans. 
    /// There were no requests to add this to the BOM so it is not a Product.
    /// </summary>
    public class Receptacle
    {
        private static readonly Regex _powerReceptaclefamilyNameRegex =
            new Regex(@"Wall[-_]?Receptacle(_v\d+)?\d*$", RegexOptions.IgnoreCase);

        private static readonly Regex _LVReceptaclefamilyNameRegex =
            new Regex(@"Wall[-_]?Low[-_]?Voltage(_v\d+)?\d*$", RegexOptions.IgnoreCase);

        public static ISelectionFilter CreateSelectionFilter()
        {
            return new ReceptacleSelectionFilter();
        }

        private class ReceptacleSelectionFilter : ISelectionFilter
        {
            public bool AllowElement(Element elem)
            {
                if (elem is FamilyInstance familyInstance)
                {
                    return elem.Category.Id.GetIntegerValue() == (int)BuiltInCategory.OST_ElectricalFixtures
                           && (_powerReceptaclefamilyNameRegex.IsMatch(familyInstance.Symbol.FamilyName) 
                           || _LVReceptaclefamilyNameRegex.IsMatch(familyInstance.Symbol.FamilyName));
                }

                return false;
            }

            public bool AllowReference(Reference reference, XYZ position)
            {
                return false;
            }
        }
    }
}
