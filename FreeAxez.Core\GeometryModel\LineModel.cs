﻿using Newtonsoft.Json;

namespace FreeAxez.Core.GeometryModel
{
    public interface ILineModel
    {
        PointModel StartPointGetter { get; }
        PointModel EndPointGetter { get; }
    }

    public class LineModel : ILineModel
    {
        public PointModel StartPoint;
        public PointModel EndPoint;

        [JsonIgnore]
        public PointModel StartPointGetter => StartPoint;
        [JsonIgnore]
        public PointModel EndPointGetter => EndPoint;

        public LineModel()
        {
        }

        public LineModel(PointModel start, PointModel end)
        {
            StartPoint = start;
            EndPoint = end;
        }
    }
}