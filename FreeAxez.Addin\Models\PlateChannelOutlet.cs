﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class PlateChannelOutlet : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
                "Outlet_Channel_Plate",
                "Outlet_Plate_Channel"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public PlateChannelOutlet(Element element) : base(element)
        {
        }

        public static List<PlateChannelOutlet> Collect()
        {
            return FamilyCollector.Instances.Select(f => new PlateChannelOutlet(f)).ToList();
        }

        public static Family GetFamily(out string missedFamilyMessage)
        {
            var family = FamilyCollector.Families.FirstOrDefault();

            missedFamilyMessage = string.Empty;
            if (family == null)
            {
                missedFamilyMessage =
                    "No family with 'Outlet_Channel_Plate' or 'Outlet_Plate_Channel'.";
            }

            return family;
        }
    }
}
