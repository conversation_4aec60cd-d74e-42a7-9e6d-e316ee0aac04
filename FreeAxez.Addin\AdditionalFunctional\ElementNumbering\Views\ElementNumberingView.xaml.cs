﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.ElementNumbering.Views
{
    public partial class ElementNumberingView : Window
    {
        public ElementNumberingView()
        {
            InitializeComponent();
            horizontalDirection.IsChecked = Properties.Settings.Default.ElementNumberingHorizontalDirection;
            verticalDirection.IsChecked = !horizontalDirection.IsChecked;
            prefix.Text = Properties.Settings.Default.ElementNumberingPrefix;
            startNumber.Text = Properties.Settings.Default.ElementNumberingStartNumber.ToString();

            var parameterNames = GetAvailableParameterNames();
            var selectedParameterIndex = parameterNames.IndexOf(Properties.Settings.Default.ElementNumberingParameterName);
            parametersList.ItemsSource = parameterNames;
            parametersList.SelectedIndex = selectedParameterIndex == -1 ? 0 : selectedParameterIndex;
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;

            Properties.Settings.Default.ElementNumberingHorizontalDirection = (bool)horizontalDirection.IsChecked;
            Properties.Settings.Default.ElementNumberingPrefix = prefix.Text;
            Properties.Settings.Default.ElementNumberingStartNumber = int.Parse(startNumber.Text);
            Properties.Settings.Default.ElementNumberingParameterName = parametersList.SelectedItem.ToString();
            Properties.Settings.Default.Save();  
            
            this.Close();
        }

        private void startNumber_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (int.TryParse(startNumber.Text, out int result) && result > 0)
            {
                Properties.Settings.Default.ElementNumberingStartNumber = result;
                return;
            }

            startNumber.Text = Properties.Settings.Default.ElementNumberingStartNumber.ToString();
        }

        private List<string> GetAvailableParameterNames()
        {
            var output = new List<string>();

            output.AddRange(GetParameterNamesFromFamilyInstances());
            output.AddRange(GetParameterNamesFromPipes());

            output = output.Distinct().ToList();
            output.Sort();

            return output;     
        }

        private List<string> GetParameterNamesFromFamilyInstances()
        {
            var multicategoryFilter = new ElementMulticategoryFilter(new List<BuiltInCategory>()
                {
                    BuiltInCategory.OST_ElectricalEquipment,
                    BuiltInCategory.OST_ElectricalFixtures
                });

            var families = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Family))
                .Cast<Family>()
                .Where(f => f.FamilyCategoryId.GetIntegerValue() == (int)BuiltInCategory.OST_ElectricalEquipment
                         || f.FamilyCategoryId.GetIntegerValue() == (int)BuiltInCategory.OST_ElectricalFixtures)
                .ToList();

            var familyInstances = families
                .Select(f => f.GetDependentElements(new ElementClassFilter(typeof(FamilyInstance))).FirstOrDefault())
                .Where(eid => eid != null)
                .Select(eid => RevitManager.Document.GetElement(eid))
                .Cast<FamilyInstance>()
                .ToList();


            List<string> listParameters = new List<string>();

            foreach (var familyInstance in familyInstances)
            {
                var parameters = familyInstance.Parameters;
                foreach (Parameter p in parameters)
                {
                    var parameterName = p.Definition.Name;
                    if (listParameters.Contains(parameterName))
                    {
                        continue;
                    }

                    if (p.StorageType != StorageType.String)
                    {
                        continue;
                    }

                    if (p.IsReadOnly == true)
                    {
                        continue;
                    }

                    listParameters.Add(parameterName);
                }

            }

            listParameters.Sort();

            return listParameters;
        }

        private List<string> GetParameterNamesFromPipes()
        {
            var output = new List<string>();

            var pipe = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(FlexPipe))
                .FirstOrDefault();

            if (pipe == null)
            {
                return output;
            }

            foreach (Parameter p in pipe.Parameters)
            {
                var parameterName = p.Definition.Name;
                if (output.Contains(parameterName))
                {
                    continue;
                }

                if (p.StorageType != StorageType.String)
                {
                    continue;
                }

                if (p.IsReadOnly == true)
                {
                    continue;
                }

                output.Add(parameterName);
            }

            return output;
        }
    }
}
