# Area Functionality - Floor Creation from CAD

## 📋 Overview

Функциональность Area позволяет создавать полы в Revit на основе геометрии из слоя `A-AREA-PATT` в AutoCAD. Это **отдельный workflow**, который выполняется **после** размещения основных элементов FreeAxez.

## 🔧 Как это работает

### 1. Извлечение данных из AutoCAD
- AutoCAD плагин извлекает **линии** из всех целевых слоев
- Для слоя `A-AREA-PATT` дополнительно поддерживаются **полилинии**
- **Полилинии в A-AREA-PATT автоматически разбиваются** на отдельные сегменты линий
- Поддерживаются как замкнутые, так и незамкнутые полилинии
- Данные сохраняются в JSON формате как отдельные линии

### 2. Workflow последовательность
1. **Сначала**: Размещение элементов FreeAxez (BaseUnit, PlateCorner, etc.)
2. **Затем**: Обработка area данных и создание полов
3. **Результат**: Объединенный отчет по элементам и полам

### 3. Обработка геометрии Area
- Линии из слоя A-AREA-PATT группируются в замкнутые контуры
- Используется алгоритм поиска связанных компонентов
- Определяются внешние границы и отверстия
- Контуры преобразуются в `CurveLoop` для создания полов в Revit

### 4. Создание полов в Revit
Создаются два типа полов:

#### Overall Area Floors
- **Тип пола**: `OVERALL AREA` (должен существовать в проекте)
- **Описание**: Простые полы без отверстий
- **Размещение**: На уровне DWG без смещения

#### Gridd Area Floors
- **Тип пола**: `GRIDD AREA` (должен существовать в проекте)
- **Описание**: Полы с отверстиями и небольшим смещением границ
- **Размещение**: На уровне DWG с смещением `0.1` фута от границ

## 📁 Новые файлы

### Area Processing Components
- `Processing/Area/AreaProcessor.cs` - Основной процессор для обработки area данных
- `Processing/Geometry/AreaGeometryProcessor.cs` - Обработка геометрии area
- `Revit/Placement/FloorCreator.cs` - Создание полов в Revit
- `Infrastructure/FloorsConstants.cs` - Константы для полов

### Обновленные файлы
- `Workflow/GriddBuilderWorkflow.cs` - Добавлен отдельный workflow для area
- `Workflow/BuildResult.cs` - Добавлена поддержка результатов area обработки
- `AutoCAD.Plugin/LineExtractionPlugin.cs` - Добавлено извлечение слоя A-AREA-PATT

## ⚙️ Требования

### В проекте Revit должны существовать типы полов:
1. **"GRIDD AREA"** - для полов с отверстиями
2. **"OVERALL AREA"** - для сплошных полов

### В AutoCAD файле:
- Слой `A-AREA-PATT` с **линиями или полилиниями**, определяющими области для полов
- **Полилинии поддерживаются только в слое A-AREA-PATT** - автоматически разбиваются на сегменты

## 🚀 Использование

1. Запустите команду GriddBuilder в Revit
2. Выберите DWG ссылку, содержащую слой A-AREA-PATT
3. Выберите высоту базовых юнитов (40 или 70)
4. Нажмите "Build Elements"
5. **Сначала** будут размещены элементы FreeAxez
6. **Затем** будут созданы полы из area данных
7. В результатах будет показано количество размещенных элементов и созданных полов

## 📊 Результаты

В итоговом отчете отображается:
- **Элементы FreeAxez**: количество размещенных BaseUnit, PlateCorner и т.д.
- **Area полы**: количество созданных Overall Area и Gridd Area полов
- **Сообщения**: детальная информация о процессе создания полов
- **Предупреждения и ошибки**: отдельно для элементов и полов

## 🔍 Особенности реализации

### Поддержка полилиний (только A-AREA-PATT)
- **Полилинии поддерживаются только в слое A-AREA-PATT** и автоматически разбиваются на сегменты
- Обрабатываются как замкнутые, так и незамкнутые полилинии
- Каждый сегмент полилинии становится отдельной линией в JSON
- Сегменты нулевой длины автоматически пропускаются
- Логирование показывает количество сегментов, созданных из каждой полилинии
- Остальные слои (BASE, EZ_CORNER, CHANNEL) обрабатывают только линии

### Координаты
- AutoCAD координаты в дюймах автоматически конвертируются в футы для Revit
- Применяется трансформация DWG ссылки для правильного позиционирования

### Версии Revit
- Поддерживаются как старые версии Revit (2018-2021) с `CurveArray`
- Так и новые версии с `Floor.Create()` и множественными `CurveLoop`

### Обработка ошибок
- Если типы полов не найдены в проекте, выводится соответствующее сообщение
- Ошибки создания отдельных полов не останавливают весь процесс
- Подробная информация об ошибках включается в итоговый отчет

## 🎯 Архитектура

Area обработка **НЕ является** частью BaseFaElement системы:
- **Отдельный workflow**: выполняется после размещения элементов FreeAxez
- **Независимая обработка**: не влияет на размещение основных элементов
- **Собственные транзакции**: создание полов в отдельных транзакциях
- **Объединенные результаты**: итоговый отчет включает и элементы, и полы
- **Асинхронная обработка**: поддерживается на уровне workflow

### Последовательность выполнения:
1. Извлечение CAD данных (все слои включая A-AREA-PATT)
2. Создание элементов FreeAxez из BASE, EZ_CORNER, CHANNEL слоев
3. Размещение элементов FreeAxez в Revit
4. **Отдельно**: Обработка A-AREA-PATT данных и создание полов
5. Объединение результатов в итоговый отчет
