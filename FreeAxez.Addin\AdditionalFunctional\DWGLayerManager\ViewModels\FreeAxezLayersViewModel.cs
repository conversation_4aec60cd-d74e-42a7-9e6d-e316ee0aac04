using System;
using System.Windows;
using System.Windows.Input;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels
{
    public class FreeAxezLayersViewModel : WindowViewModel
    {
        public LayerManagerViewModel LayerManagerVM { get; }
        public ICommand CloseCommand { get; }

        public FreeAxezLayersViewModel(LayerManagerViewModel layerManagerViewModel)
        {
            LayerManagerVM = layerManagerViewModel ?? throw new ArgumentNullException(nameof(layerManagerViewModel));
            CloseCommand = new RelayCommand(param => Close(param as Window));
        }

        private void Close(Window window)
        {
            window?.Close();
        }
    }
}
