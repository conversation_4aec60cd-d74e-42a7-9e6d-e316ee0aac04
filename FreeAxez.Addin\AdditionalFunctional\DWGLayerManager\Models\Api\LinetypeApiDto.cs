using System;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Api;

/// <summary>
/// DTO for API communication - represents linetype data for server requests
/// </summary>
public record LinetypeDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string PatternRaw { get; set; } = string.Empty;
    public DateTime UpdatedUtc { get; set; } = DateTime.UtcNow;
}
