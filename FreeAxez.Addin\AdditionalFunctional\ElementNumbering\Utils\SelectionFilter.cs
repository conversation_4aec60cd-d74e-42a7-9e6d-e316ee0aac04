﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.NumberingElements.Utils
{
    internal class SelectionFilter : ISelectionFilter
    {
        public bool AllowElement(Element elem)
        {
            var builtInCategoryId = elem.Category?.Id.GetIntegerValue();

            if (builtInCategoryId != (int)BuiltInCategory.OST_FlexPipeCurves &&
                builtInCategoryId != (int)BuiltInCategory.OST_ElectricalFixtures &&
                builtInCategoryId != (int)BuiltInCategory.OST_ElectricalEquipment)
            {
                return false;
            }

            if (elem.LookupParameter(Properties.Settings.Default.ElementNumberingParameterName) == null)
            {
                return false;
            }

            return true;
        }
        public bool AllowReference(Reference reference, XYZ position)
        {
            return false;
        }
    }
}
