﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.TagRamp.Views.TagRampView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             xmlns:viewModels="clr-namespace:FreeAxez.Addin.AdditionalFunctional.TagRamp.ViewModels"
             SizeToContent="WidthAndHeight"
             Title="Tag All Ramps"
             WindowStartupLocation="CenterScreen"
             ResizeMode="NoResize">
    <Window.DataContext>
        <viewModels:TagRampViewModel/>
    </Window.DataContext>
    <Grid Margin="10,0,10,10">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="13*"/>
            <ColumnDefinition Width="57*"/>
        </Grid.ColumnDefinitions>
        <StackPanel Grid.ColumnSpan="2">
            <DockPanel Margin="0,10,0,0">
                <Label DockPanel.Dock="Left" Content="Leader Length"/>
                <TextBox VerticalContentAlignment="Center" Text="{Binding LengthToCenter}"/>
            </DockPanel>
            <GroupBox Margin="0,5,0,5" Header="Select Tag Type">
                <StackPanel Margin="0,5,0,2">
                    <RadioButton Content="Multicategory Tag" IsChecked="{Binding TagType}" GroupName="typeRumps"/>
                    <RadioButton Margin="0,5,0,0" Content="Generic Annotation" IsChecked="{Binding AnnotationType}" GroupName="typeRumps"/>
                </StackPanel>
            </GroupBox>
            <GroupBox Margin="0,5,0,5" Header="Select Rumps">
                <StackPanel Margin="0,5,0,2">
                    <RadioButton Content="Visible In View" IsChecked="{Binding IsVisibleInView}" GroupName="tagregRumps"/>
                    <RadioButton Margin="0,5,0,0" Content="Select Instances" IsChecked="{Binding TagSelected}" GroupName="tagregRumps"/>
                </StackPanel>
            </GroupBox>
            <StackPanel Margin="0,10,0,0" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Width="100" Height="30" Content="Create" Command="{Binding CreateCommand}" 
                        CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
                <Button Width="100" Margin="10,0,0,0" Content="Cancel" Command="{Binding CancelCommand}" 
                        CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
            </StackPanel>
        </StackPanel>
    </Grid>
</Window>