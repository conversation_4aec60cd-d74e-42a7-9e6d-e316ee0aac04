﻿<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals.Info"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800" Width="400">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="50"/>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0"
                    Margin="0 0 0 20">

            <TextBlock Style="{StaticResource TextBase}"
                       TextWrapping="Wrap"
                       Text="{Binding Message}"
                       Foreground="Black"
                       Margin="0 10 0 5"/>
        </StackPanel>
        <StackPanel Grid.Row="1" Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0 0 0 0">
            <Button Content="Ok"
                    Margin="20 0 0 0"
                    Command="{Binding ApplyCommand}"
                    Style="{StaticResource ButtonSimpleBlue}" />
        </StackPanel>
    </Grid>
</UserControl>
