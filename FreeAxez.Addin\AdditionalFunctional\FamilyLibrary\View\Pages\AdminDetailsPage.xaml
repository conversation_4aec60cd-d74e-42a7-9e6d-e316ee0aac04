<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Pages.AdminDetailsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             xmlns:pages="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages"
             xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200">
    <UserControl.Resources>
        <ResourceDictionary>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:AsyncImageConverter x:Key="AsyncImageConverter" />
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" />
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        <controls:HeaderBar PageName="Details"/>
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Loading Indicator -->
            <Grid Grid.Row="0" Grid.RowSpan="2"
                  Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                  Background="White">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <Ellipse Style="{StaticResource LoadingSpinner}" Margin="0,0,0,20"/>
                    <TextBlock Text="{Binding LoadingStatus, FallbackValue='Loading details...'}"
                               Style="{StaticResource TextBase}"
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>

            <!-- Error State -->
            <Grid Grid.Row="0" Grid.RowSpan="2"
                  Visibility="{Binding ShowErrorState, Converter={StaticResource BooleanToVisibilityConverter}}"
                  Background="White">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" MaxWidth="400">
                    <ContentControl Template="{StaticResource AlertIcon}"
                                    Width="48" Height="48"
                                    Margin="0,0,0,20"/>
                    <TextBlock Text="Error Loading Details"
                               Style="{StaticResource TextH3}"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,10"/>
                    <TextBlock Text="{Binding ErrorMessage}"
                               Style="{StaticResource TextBase}"
                               HorizontalAlignment="Center"
                               TextWrapping="Wrap"
                               TextAlignment="Center"
                               Margin="0,0,0,20"/>
                    <Button Content="Retry"
                            Command="{Binding LoadDataCommand}"
                            Style="{StaticResource ButtonOutlinedBlue}"
                            Width="100"/>
                </StackPanel>
            </Grid>

            <!-- Empty State -->
            <Grid Grid.Row="0" Grid.RowSpan="2"
                  Visibility="{Binding ShowEmptyState, Converter={StaticResource BooleanToVisibilityConverter}}"
                  Background="White">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" MaxWidth="400">
                    <ContentControl Template="{StaticResource DetailsIcon}"
                                    Width="64" Height="64"
                                    Margin="0,0,0,20"
                                    Opacity="0.3"/>
                    <TextBlock Text="No Details Found"
                               Style="{StaticResource TextH3}"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,10"/>
                    <TextBlock Text="Get started by adding your first detail file to the library."
                               Style="{StaticResource TextBase}"
                               HorizontalAlignment="Center"
                               TextWrapping="Wrap"
                               TextAlignment="Center"
                               Margin="0,0,0,20"/>
                    <Button Content="Add Details"
                            Command="{Binding AddDetailsCommand}"
                            Style="{StaticResource ButtonSimpleBlue}"
                            Width="120"/>
                </StackPanel>
            </Grid>
            <!-- Main Content -->
            <Grid Grid.Row="0" Grid.RowSpan="2"
                  Visibility="{Binding ShowContent, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="50"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <DockPanel Grid.Row="0" Margin="10 0"
                           VerticalAlignment="Center">
                    <ComboBox HorizontalAlignment="Left"
                              Style="{StaticResource ComboBoxStyleFilter}"
                              Tag="File Type"
                              Width="200"
                              ItemsSource="{Binding FileTypes}"
                              SelectedItem="{Binding SelectedFileType}"
                              />
                    <ComboBox Style="{StaticResource ComboBoxStyleFilter}"
                              Tag="Revit version"
                              Width="200"
                              Margin="10 0"
                              ItemsSource="{Binding RevitVersions}"
                              SelectedItem="{Binding SelectedRevitVersion}"
                              HorizontalAlignment="Left"
                              />
                    <Button Width="80"
                            FontWeight="Normal"
                            Height="25"
                            Style="{StaticResource ButtonOutlinedRed}"
                            Command="{Binding ResetFiltersCommand}"
                            HorizontalAlignment="Right"
                            >
                        X Clear
                    </Button>
                </DockPanel>

                <DataGrid Grid.Row="1"
                    Style="{DynamicResource DataGridWithoutBorders}"
                    Background="#F8F9FF"
                    VirtualizingStackPanel.IsVirtualizing="True"
                    VirtualizingStackPanel.VirtualizationMode="Recycling"
                    EnableRowVirtualization="True"
                    EnableColumnVirtualization="True"
                    ColumnHeaderStyle="{DynamicResource DataGridColumnHeader}"
                    ItemsSource="{Binding Details}"
                    HorizontalScrollBarVisibility="Hidden"
                    AutoGenerateColumns="False"
                    CanUserDeleteRows="False"
                    CanUserResizeColumns="True"
                    Margin="10,0"
                    CanUserAddRows="False"
                    CanUserReorderColumns="False"
                    HeadersVisibility="Column"
                    SelectionMode="Single"
                    SelectionUnit="FullRow"
                    IsReadOnly="True"
                    x:Name="DfTypes">
                <DataGrid.Columns>
                    <DataGridTemplateColumn Header="Image"
                                            Width="SizeToCells"
                                            IsReadOnly="True">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Image Width="80"
                                       Height="80"
                                       Stretch="Uniform">
                                    <Image.Source>
                                        <Binding Path="ImagePath"
                                                 Converter="{StaticResource AsyncImageConverter}">
                                            <Binding.FallbackValue>
                                                <BitmapImage UriSource="/FamiliesLibrary;component/Assets/noImage.png" />
                                            </Binding.FallbackValue>
                                        </Binding>
                                    </Image.Source>
                                </Image>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Name" Width="200" MinWidth="150" SortMemberPath="Name">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Name}"
                                           TextWrapping="Wrap"
                                           FontWeight="SemiBold" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="File Type"
                                            Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding FileType}"
                                           TextWrapping="Wrap"
                                           FontWeight="SemiBold" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Revit" Width="80" CanUserSort="False">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border Background="#1670aa"
                                        CornerRadius="5"
                                        Padding="10 0"
                                        Height="20"
                                        BorderThickness="0"
                                        VerticalAlignment="Center"
                                        HorizontalAlignment="Center">
                                    <TextBlock Text="{Binding RevitVersion}"
                                               Foreground="White"
                                               FontWeight="Bold"
                                               VerticalAlignment="Center" HorizontalAlignment="Center" />
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Description"
                                            Width="*"
                                            MinWidth="130">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Description}"
                                           TextWrapping="Wrap"
                                           FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Created By"
                                            Width="150"
                                            SortMemberPath="CreatedBy">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding CreatedBy}"
                                           TextWrapping="Wrap"
                                           FontWeight="Light"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Updated"
                                            Width="100"
                                            SortMemberPath="LastDateUpdated">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding LastDateUpdated}"
                                           TextWrapping="Wrap"
                                           FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Actions"
                                            Width="*"
                                            MinWidth="180">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Margin="2 5"
                                            Command="{Binding DataContext.EditDetailsCommand,
                                            RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource RoundIconButton}"
                                            ToolTip="Edit details">
                                        <ContentControl Template="{StaticResource PencilIcon}" HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"/>
                                    </Button>
                                    <Button Margin="2 5"
                                            Command="{Binding DataContext.DownloadToRevitCommand,
                                            RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource RoundIconButton}"
                                            ToolTip="Load into current project">
                                        <ContentControl Template="{StaticResource LoadToProjectIcon}" HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"/>
                                    </Button>
                                    <Button Margin="2 5"
                                            Command="{Binding DataContext.DownloadToPcCommand,
                                            RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource RoundIconButton}"
                                            ToolTip="Download to PC">
                                        <ContentControl Template="{StaticResource DownloadIcon}" HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"/>
                                    </Button>
                                    <Button Margin="2 5"
                                            Command="{Binding DataContext.DeleteDetailsCommand,
                                            RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource RoundIconButton}"
                                            ToolTip="Delete details">
                                        <ContentControl Template="{StaticResource TrashIcon}" HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Grid>
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Margin="0,0,20,0">
            <Button Content="Add Details"
                    Style="{StaticResource ButtonSimpleBlue}"
                    Margin="0 0 10 0"
                    Command="{Binding AddDetailsCommand}">
            </Button>
        </StackPanel>
    </Grid>
</UserControl>
