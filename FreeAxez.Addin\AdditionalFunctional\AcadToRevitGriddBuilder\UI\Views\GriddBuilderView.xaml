<Window x:Class="FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.UI.Views.GriddBuilderView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.UI.Converters"
        mc:Ignorable="d"
        Title="Build Gridd Elements"
        Width="480"
        Height="420"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        ShowInTaskbar="False">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Built-in converters -->
            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

            <!-- Custom converters -->
            <converters:IntToBoolConverter x:Key="IntToBoolConverter"/>
            <converters:InverseBooleanConverter x:Key="InverseBoolConverter"/>
            <converters:ProcessingTextConverter x:Key="ProcessingTextConverter"/>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- DWG Link Info Card -->
        <Border Grid.Row="0"
                Background="{StaticResource Gray50}"
                CornerRadius="8"
                Padding="20"
                Margin="0,0,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0"
                           Text="DWG Link:"
                           Style="{StaticResource TextH6}"
                           VerticalAlignment="Center"/>
                <TextBlock Grid.Row="0" Grid.Column="1"
                           Text="{Binding DwgLinkName}"
                           VerticalAlignment="Center"
                           TextWrapping="Wrap"
                           FontWeight="Medium"/>

                <TextBlock Grid.Row="1" Grid.Column="0"
                           Text="Level:"
                           Style="{StaticResource TextH6}"
                           VerticalAlignment="Center"
                           Margin="0,8,0,0"/>
                <TextBlock Grid.Row="1" Grid.Column="1"
                           Text="{Binding LevelName}"
                           VerticalAlignment="Center"
                           FontWeight="Medium"
                           Margin="0,8,0,0"/>
            </Grid>
        </Border>

        <!-- Height Selection Card -->
        <Border Grid.Row="1"
                Background="{StaticResource Gray50}"
                CornerRadius="8"
                Padding="20"
                Margin="0,0,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0"
                           Text="Base Unit Height:"
                           Style="{StaticResource TextH6}"
                           Margin="0,0,0,15"/>

                <UniformGrid Grid.Row="1" Columns="2">
                    <RadioButton Content="40"
                                IsChecked="{Binding BaseUnitHeight, Converter={StaticResource IntToBoolConverter}, ConverterParameter=40}"
                                IsEnabled="{Binding IsProcessing, Converter={StaticResource InverseBoolConverter}}"
                                GroupName="Height"
                                HorizontalAlignment="Center"
                                Margin="0,0,10,0"/>
                    <RadioButton Content="70"
                                IsChecked="{Binding BaseUnitHeight, Converter={StaticResource IntToBoolConverter}, ConverterParameter=70}"
                                IsEnabled="{Binding IsProcessing, Converter={StaticResource InverseBoolConverter}}"
                                GroupName="Height"
                                HorizontalAlignment="Center"
                                Margin="10,0,0,0"/>
                </UniformGrid>
            </Grid>
        </Border>

        <!-- Progress Section -->
        <Border Grid.Row="2"
                Background="{StaticResource Gray50}"
                CornerRadius="8"
                Padding="20"
                Margin="0,0,0,20"
                Visibility="{Binding IsProcessing, Converter={StaticResource BoolToVisibilityConverter}}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <ProgressBar Grid.Row="0"
                            Value="{Binding ProgressValue}"
                            Maximum="100"
                            Height="10"
                            Foreground="{StaticResource Blue500}"
                            Background="{StaticResource Gray200}"
                            Margin="0,0,0,15"/>

                <TextBlock Grid.Row="1"
                          Text="{Binding StepStatus}"
                          HorizontalAlignment="Center"
                          Style="{StaticResource TextH6}"
                          FontWeight="SemiBold"
                          Margin="0,0,0,8"/>

                <TextBlock Grid.Row="2"
                          Text="{Binding TaskStatus}"
                          HorizontalAlignment="Center"
                          Style="{StaticResource TextBase}"
                          TextWrapping="Wrap"
                          Foreground="{StaticResource Gray600}"/>
            </Grid>
        </Border>

        <!-- Buttons -->
        <UniformGrid Grid.Row="3" Columns="2">
            <Button Content="Cancel"
                   Style="{StaticResource ButtonSimpleRed}"
                   Command="{Binding CancelCommand}"
                   Margin="0,0,8,0"
                   Height="30"/>

            <Button Style="{StaticResource ButtonSimpleBlue}"
                   IsDefault="True"
                   Command="{Binding BuildCommand}"
                   Margin="8,0,0,0"
                   Height="30"
                   Content="{Binding IsProcessing, Converter={StaticResource ProcessingTextConverter}}"/>
        </UniformGrid>
    </Grid>
</Window>
