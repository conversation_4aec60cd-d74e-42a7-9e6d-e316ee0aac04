﻿using Newtonsoft.Json;
using System.IO;
using System.Net;

namespace FreeAxez.Core.Services
{
    public static class SlackService
    {
        private static readonly string _channel = "#rprt-freeaxez";
        private static readonly string _slackHook = "*****************************************************************************";
        private static readonly string _userName = "Freaxez notifications";

        public static bool SendMessage(string text)
        {
            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            var httpWebRequest = (HttpWebRequest)WebRequest.Create(_slackHook);
            httpWebRequest.ContentType = "application/json";
            httpWebRequest.Method = "POST";

            var json = new
            {
                channel = _channel,
                username = _userName,
                text = text
            };

            using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
            {
                var jsonBody = JsonConvert.SerializeObject(json);
                streamWriter.Write(jsonBody);
            }

            var httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var result = streamReader.ReadToEnd() == "ok";
                return result;
            }
        }
    }
}
