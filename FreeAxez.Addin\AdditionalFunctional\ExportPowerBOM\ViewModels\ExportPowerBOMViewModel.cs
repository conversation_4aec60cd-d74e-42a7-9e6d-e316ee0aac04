﻿using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Utils;
using FreeAxez.Addin.Infrastructure.UI;
using FreeAxez.Addin.Infrastructure;
using System;
using System.IO;
using System.Windows.Forms;
using System.Windows.Input;
using System.Windows;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Models;
using System.Collections.Generic;
using System.Linq;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Utils;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBOM.Utils;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.ViewModels
{
    internal class ExportPowerBomViewModel : WindowViewModel
    {
        private List<NodeViewModel> _accessories;

        public ExportPowerBomViewModel()
        {
            if (File.Exists(Properties.Settings.Default.exportPowerBomPath))
            {
                FilePath = Properties.Settings.Default.exportPowerBomPath;
            }

            var checkedAccessories = Properties.Settings.Default.ExportPowerBomAccessories.Split(';');
            _accessories = PowerProductCollector.GetAccessoriesNames().Select(a => 
                new NodeViewModel() { Name = a, IsChecked = checkedAccessories.Contains(a) }).ToList();

            BrowseCommand = new RelayCommand(OnBrowseCommandExecute);
            ExportCommand = new RelayCommand(OnExportCommandExecute);
            CheckAllCommand = new RelayCommand(OnCheckAllCommandExecute);
            UncheckAllCommand = new RelayCommand(OnUncheckAllCommandExecute);
            WindowLoadedCommand = new RelayCommand(OnWindowLoadedCommandExecute);
        }


        public string FilePath { get; set; }
        public List<string> Sheets
        {
            get
            {
                if (!File.Exists(FilePath))
                {
                    return new List<string>();
                }

                try
                {
                    var excelDocument = ExcelDocument.Open(FilePath);
                    var revSheetNames = excelDocument.GetSheetNames()
                        .Where(s => GriddBomExcelSchema.GetRevNumber(s) != -1)
                        .Where(s => !s.Contains("Details"))
                        .OrderByDescending(s => GriddBomExcelSchema.GetRevNumber(s))
                        .ToList();

                    if (revSheetNames.Any())
                    {
                        var lastRev = GriddBomExcelSchema.GetRevNumber(revSheetNames.First());
                        revSheetNames.Insert(0, string.Format(GriddBomExcelSchema.RevSheetNameTemplate, lastRev + 1, DateTime.Now.ToString("dd-MM-yy")) + " (New)");
                    }
                    else
                    {
                        revSheetNames.Insert(0, string.Format(GriddBomExcelSchema.RevSheetNameTemplate, 0, DateTime.Now.ToString("dd-MM-yy")) + " (New)");
                    }

                    SelectedSheet = revSheetNames.First();
                    OnPropertyChanged(nameof(SelectedSheet));
                    return revSheetNames;
                }
                catch
                {
                    return new List<string>();
                }
            }
        }

        public string SelectedSheet { get; set; }
        public List<NodeViewModel> Accessories { get => _accessories; }

        public bool OpenFile
        {
            get
            {
                return Properties.Settings.Default.exportPowerBomOpenFile;
            }
            set
            {
                Properties.Settings.Default.exportPowerBomOpenFile = value;
                Properties.Settings.Default.Save();
            }
        }

        public ICommand WindowLoadedCommand { get; set; }
        private void OnWindowLoadedCommandExecute(object p)
        {
            if (File.Exists(FilePath) && IsOpened(FilePath))
            {
                MessageWindow.ShowDialog("The file is open. Please close the file and try again.", MessageType.Notify);
            }
        }

        public ICommand BrowseCommand { get; set; }
        private void OnBrowseCommandExecute(object p)
        {
            var initialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory);
            if (!string.IsNullOrEmpty(Properties.Settings.Default.exportPowerBomPath)
                && File.Exists(Properties.Settings.Default.exportPowerBomPath))
            {
                initialDirectory = Path.GetDirectoryName(Properties.Settings.Default.exportPowerBomPath);
            }

            var openFileDialog = new OpenFileDialog
            {
                InitialDirectory = initialDirectory,
                Filter = "Excel Files|*.xls;*.xlsx;*.xlsm",
                Title = "Select an Excel file"
            };

            if (openFileDialog.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            if (IsOpened(openFileDialog.FileName))
            {
                MessageWindow.ShowDialog("The file is open. Please close the file and try again.", MessageType.Notify);
                return;
            }

            if (File.Exists(openFileDialog.FileName))
            {
                Properties.Settings.Default.exportPowerBomPath = openFileDialog.FileName;
                Properties.Settings.Default.Save();
            }

            FilePath = openFileDialog.FileName;
            OnPropertyChanged(nameof(FilePath));
            OnPropertyChanged(nameof(Sheets));
            OnPropertyChanged(nameof(SelectedSheet));
        }

        public ICommand CheckAllCommand { get; set; }
        private void OnCheckAllCommandExecute(object p)
        {
            Accessories.ForEach(a => a.IsChecked = true);
        }

        public ICommand UncheckAllCommand { get; set; }
        private void OnUncheckAllCommandExecute(object p)
        {
            Accessories.ForEach(a => a.IsChecked = false);
        }

        public ICommand ExportCommand { get; set; }
        private void OnExportCommandExecute(object p)
        {
            (p as Window).Close();

            Properties.Settings.Default.ExportPowerBomAccessories = 
                string.Join(";", _accessories.Where(a => a.IsChecked).Select(a => a.Name).ToList());
            Properties.Settings.Default.Save();
            
            if (!File.Exists(FilePath))
            {
                MessageWindow.ShowDialog("The path to the document is incorrect.", MessageType.Notify);
                return;
            }

            if (IsOpened(FilePath))
            {
                MessageWindow.ShowDialog("The file is open. Please close the file and try again.", MessageType.Notify);
                return;
            }

            var splitSheet = SelectedSheet.Split(' ');
            var revision = new RevisionViewModel()
            {
                RevisionNumber = GriddBomExcelSchema.GetRevNumber(SelectedSheet).ToString(),
                RevisionDate = splitSheet.Length > 1 ? splitSheet[1] : "Date",
                RevisionAuthor = ""
            };

            var powerBomRevision = new PowerBomRevision(revision, _accessories.Where(a => a.IsChecked).Select(a => a.Name).ToList());
            powerBomRevision.Calculate();

            var revisionViewModel = new PowerBomRevisionViewModel(powerBomRevision);
            revisionViewModel.Calculate();

            var detailsViewModel = new PowerBomDetailsViewModel(powerBomRevision);
            detailsViewModel.Calculate();

            var floorBoxCountViewModel = new PowerBomFloorBoxCountViewModel(powerBomRevision);
            floorBoxCountViewModel.Calculate();

            var powerBomToExcelExport = new PowerBomToExcelExport(FilePath, revisionViewModel, detailsViewModel, floorBoxCountViewModel);
            powerBomToExcelExport.Export();
        }
        
        private bool IsOpened(string filePath)
        {
            try
            {
                using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.None))
                {
                    return false;
                }
            }
            catch (IOException)
            {
                return true;
            }
        }
    }
}
