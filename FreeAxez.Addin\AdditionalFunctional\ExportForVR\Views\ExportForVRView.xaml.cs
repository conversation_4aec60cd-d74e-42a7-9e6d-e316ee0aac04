﻿using FreeAxez.Addin.AdditionalFunctional.ExportForVR.Models;
using FreeAxez.Addin.AdditionalFunctional.ExportForVR.ViewModels;
using System.Windows;
using System.Windows.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.ExportForVR.Views
{
    public partial class ExportForVRView : Window
    {
        public ExportForVRView()
        {
            InitializeComponent();
            DataContext = new ExportForVRViewModel();
        }

        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            foreach (LevelModel revitView in dataGrid.SelectedItems)
            {
                revitView.IsCheck = (bool)(sender as CheckBox).IsChecked;
            }
        }
    }
}
