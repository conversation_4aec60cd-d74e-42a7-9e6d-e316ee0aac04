﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using FreeAxez.Addin.AdditionalFunctional.CutUnitByVoid.Utils;
using System.Text;
using System;
using FreeAxez.Addin.Infrastructure.UI;
using FreeAxez.Addin.AdditionalFunctional.CutUnitByVoid.View;
using System.IO;
using System.Windows.Forms;

namespace FreeAxez.Addin.AdditionalFunctional.CutUnitByVoid
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    public class CutUnitByVoidCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var dialog = new CutUnitByVoidView();
            if (dialog.ShowDialog() != true)
            {
                return Result.Cancelled;
            }

            if (SelectCuttingInstance(out Element cuttingInstance) != true)
            {
                InfoDialog.ShowDialog("Cut Elements", "No cutting instance was selected.");
                return Result.Cancelled;
            }

            ProgressBarHelper progressBarHelper = new ProgressBarHelper();
            progressBarHelper.Show();

            var elementsForCutting = CutElementCollector.Collect(cuttingInstance);
            if (elementsForCutting.Count == 0)
            {
                progressBarHelper.Close();
                InfoDialog.ShowDialog("Cut Elements", "There are no elements on the view to cut.");
                return Result.Cancelled;
            }

            var processedElements = 0;
            if (!progressBarHelper.SetMaxValue(elementsForCutting.Count) |
                !progressBarHelper.SetStatus($"Processed {processedElements}/{elementsForCutting.Count}."))
            {
                return Result.Cancelled;
            }

            var cuttedElements = new List<Element>();
            var notCutElements = new List<Element>();
            using (var tg = new TransactionGroup(RevitManager.Document, "Cut Elements"))
            {
                tg.Start();

                var warningSwallower = new RollBackFailuresPreprocessor();

                foreach (var element in elementsForCutting)
                {
                    if (!progressBarHelper.UpdateProgress(++processedElements))
                    {
                        tg.RollBack();
                        return Result.Cancelled;
                    }
                    progressBarHelper.SetStatus($"Processed {processedElements}/{elementsForCutting.Count}.");

                    using (var t = new Transaction(RevitManager.Document, "Cut Element By Void"))
                    {
                        t.Start();

                        try
                        {
                            InstanceVoidCutUtils.AddInstanceVoidCut(RevitManager.Document, element, cuttingInstance);
                            element.LookupParameter("Field Cut")?.Set(1);
                        }
                        catch 
                        {
                            t.RollBack();
                            continue;
                        }

                        var option = t.GetFailureHandlingOptions();
                        option.SetClearAfterRollback(true);
                        option.SetFailuresPreprocessor(warningSwallower);
                        if (t.Commit(option) != TransactionStatus.Committed)
                        {
                            notCutElements.Add(element);
                        }
                        else
                        {
                            cuttedElements.Add(element);
                        }
                    }
                }

                tg.Assimilate();
            }

            progressBarHelper.Close();

            ShowReport(cuttedElements.Count, notCutElements.Count);

            if (notCutElements.Count > 0)
            {
                ExportUncutElements(notCutElements);
            }

            return Result.Succeeded;
        }

        private bool SelectCuttingInstance(out Element cuttingInstance)
        {
            cuttingInstance = null;

            try
            {
                var reference = RevitManager.UIDocument.Selection.PickObject(
                    Autodesk.Revit.UI.Selection.ObjectType.Element,
                    new CuttingInstanceSelectionFilter(),
                    "Select cutting instance.");

                cuttingInstance = RevitManager.Document.GetElement(reference);

                return true;
            }
            catch { }

            return false;
        }

        private void ShowReport(int cuttedElementsCount, int couldNotBeCutted)
        {
            var report = new StringBuilder();

            if (cuttedElementsCount == 1 || cuttedElementsCount == 0)
            {
                report.AppendLine($"{cuttedElementsCount} element is cut.");
            }
            else
            {
                report.AppendLine($"{cuttedElementsCount} elements are cut.");
            }

            if (couldNotBeCutted == 1)
            {
                report.AppendLine($"{couldNotBeCutted} element isn't cut.");
            }
            else if (couldNotBeCutted > 1)
            {
                report.AppendLine($"{couldNotBeCutted} elements aren't cut.");
            }

            InfoDialog.ShowDialog("Cut Element Report", report.ToString().TrimEnd(Environment.NewLine.ToCharArray()));
        }

        private void ExportUncutElements(List<Element> notCutElements)
        {
            try
            {
                var report = new StringBuilder();
                report.AppendLine(string.Format("Id,Family,Symbol,Level"));
                foreach (var element in notCutElements)
                {
                    // | Id | Family Name | Symbol Name | Level | 
                    report.AppendLine(string.Format("{0},{1},{2},{3}",
                        element.Id.GetIntegerValue(),
                        (element as FamilyInstance).Symbol.FamilyName,
                        element.Name,
                        element.get_Parameter(BuiltInParameter.FAMILY_LEVEL_PARAM).AsValueString()));
                }

                WriteToCSV(report.ToString());
            }
            catch
            {
                InfoDialog.ShowDialog("Cut Element Export", "Failed to export uncut items.");
            }
        }

        private void WriteToCSV(string report)
        {
            var dialog = new SaveFileDialog();
            dialog.Title = "Export Uncut Elements As";
            dialog.FileName = $"Uncut Elements_{RevitManager.Document.Title}_{DateTime.Now.ToString("yyMMdd_HHmm")}.csv";
            dialog.Filter = "CSV Files (*.csv)|*.csv|All Files (*.*)|*.*";
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                using (var w = new StreamWriter(dialog.FileName))
                {
                    w.Write(report);
                }
            }
        }
    }
}