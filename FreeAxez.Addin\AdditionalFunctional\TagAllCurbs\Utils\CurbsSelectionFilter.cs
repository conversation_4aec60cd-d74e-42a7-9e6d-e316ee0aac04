﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Utils
{
    public class CurbsSelectionFilter : ISelectionFilter
    {
        private List<ElementId> _curbsOnView;


        public CurbsSelectionFilter(List<FamilyInstance> curbsOnView)
        {
            _curbsOnView = curbsOnView.Select(c => c.Id).ToList();
        }


        public bool AllowElement(Element elem)
        {
            return _curbsOnView.Contains(elem.Id);
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            return false;
        }
    }
}