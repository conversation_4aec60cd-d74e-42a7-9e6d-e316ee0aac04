﻿using System.Text.RegularExpressions;
using Autodesk.Revit.DB;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Models
{
    public class ViewTemplateModel
    {
        public ViewTemplateModel(View viewTemplate)
        {
            ViewTemplate = viewTemplate;
            Name = ViewTemplate.Name;
            MappingKey = GetMappingKey();
            MappingTokens = TokenizeMappingKey(MappingKey);
        }

        public string MappingKey { get; }
        public string Name { get; }
        public View ViewTemplate { get; }

        public List<string> MappingTokens { get; }

        private string GetMappingKey()
        {
            var viewType = ViewTemplate.ViewType.ToString();
            var category = RemoveNumbers(ViewTemplate.LookupParameter("Category")?.AsString());
            var viewCategory = RemoveNumbers(ViewTemplate.LookupParameter("View Category")?.AsString());
            var viewName = RemoveNumbers(ViewTemplate.Name);

            return $"{viewType} : {category} : {viewCategory} : {viewName}";
        }

        private List<string> TokenizeMappingKey(string mappingKey)
        {
            return mappingKey.Split(':')
                .Select(token => token.Trim())
                .Where(token => !string.IsNullOrEmpty(token))
                .ToList();
        }

        private string RemoveNumbers(string value)
        {
            if (string.IsNullOrEmpty(value)) return "";

            var output = Regex.Replace(value, @"\dx\d", " ");
            output = Regex.Replace(output, @"\d", " ");
            output = Regex.Replace(output, @"[^a-zA-Zа-яА-Я]", " ");
            output = Regex.Replace(output, @"level", " ", RegexOptions.IgnoreCase);
            output = output.Trim();
            return output;
        }

        public static List<ViewTemplateModel> CollectViewTemplatesFromDocument(Document doc,
            bool includeUnusedTemplates = false)
        {
            var usedTemplateIds = new FilteredElementCollector(doc)
                .OfClass(typeof(View))
                .Cast<View>()
                .Where(v => !v.IsTemplate && v.ViewTemplateId != ElementId.InvalidElementId)
                .Select(v => v.ViewTemplateId)
                .ToHashSet();

            return new FilteredElementCollector(doc)
                .OfClass(typeof(View))
                .Cast<View>()
                .Where(v => v.IsTemplate &&
                            (includeUnusedTemplates || usedTemplateIds.Contains(v.Id)) &&
                            !string.IsNullOrEmpty(v.LookupParameter("Category")?.AsString()) &&
                            !string.IsNullOrEmpty(v.LookupParameter("View Category")?.AsString()))
                .Select(viewTemplate => new ViewTemplateModel(viewTemplate))
                .OrderBy(v => v.Name)
                .ToList();
        }
    }
}