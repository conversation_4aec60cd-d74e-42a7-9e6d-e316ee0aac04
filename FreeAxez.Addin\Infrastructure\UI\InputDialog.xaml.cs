﻿using System.Windows;
using System.Windows.Controls;

namespace FreeAxez.Addin.Infrastructure.UI
{
    /// <summary>
    /// Interaction logic for InputDialog.xaml
    /// </summary>
    public partial class InputDialog : Window
    {
        public InputDialog()
        {
            InitializeComponent();
        }


        public static string ShowDialog(string title, string label, string templateText, Window owner)
        {
            var inputDialog = new InputDialog();

            inputDialog.Title = title;
            inputDialog.label.Content = label;
            inputDialog.textBox.Text = templateText;
            inputDialog.Owner = owner;
            inputDialog.WindowStartupLocation = WindowStartupLocation.CenterOwner;

            if (inputDialog.ShowDialog() == true)
            {
                return inputDialog.textBox.Text;
            }

            return string.Empty;
        }

        public static string ShowDialog(string title, string label, string templateText)
        {
            var inputDialog = new InputDialog();

            inputDialog.Title = title;
            inputDialog.label.Content = label;
            inputDialog.textBox.Text = templateText;
            inputDialog.WindowStartupLocation = WindowStartupLocation.CenterScreen;

            if (inputDialog.ShowDialog() == true)
            {
                return inputDialog.textBox.Text;
            }

            return string.Empty;
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void textBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                button.IsEnabled = false;
            }

            button.IsEnabled = true;
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            textBox.Focus();

            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                button.IsEnabled = false;
            }
        }
    }
}
