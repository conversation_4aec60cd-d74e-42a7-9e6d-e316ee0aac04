﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Services;
using NetTopologySuite.Geometries;
using Point = NetTopologySuite.Geometries.Point;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Models
{
    public class CircuitDevice : CircuitElement
    {
        private readonly double _whipIntersectionTolerance = 0.2;
        private readonly FamilyInstance _familyInstance;
        private readonly Geometry _convexHull;
        private List<Geometry> _geometries;

        public CircuitDevice(FamilyInstance familyInstance)
        {
            _familyInstance = familyInstance;
            _convexHull = GetConvexHull();
        }

        public override ElementId LevelId
        {
            get
            {
                if (_familyInstance.LevelId != null && !_familyInstance.LevelId.Equals(ElementId.InvalidElementId)) return _familyInstance.LevelId;

                // Panelboards in walmart template are host based
                var scheduleLevelId = _familyInstance.get_Parameter(BuiltInParameter.INSTANCE_SCHEDULE_ONLY_LEVEL_PARAM)?.AsElementId();
                if (scheduleLevelId != null && !scheduleLevelId.Equals(ElementId.InvalidElementId)) return scheduleLevelId;

                return ElementId.InvalidElementId;
            }
        }
        public ElementId ElementId => _familyInstance.Id;
        public FamilyInstance FamilyInstance => _familyInstance;
        public Geometry ConvexHull => _convexHull;
        public override List<Geometry> Geometries
        {
            get
            {
                if (_geometries == null) 
                {
                    _geometries = new List<Geometry>() { _convexHull }; 
                }

                return _geometries;
            }
        }

        public bool IsConnected(CircuitWhip whipComponent)
        {
            if (!LevelId.Equals(whipComponent.LevelId)) return false;
            return whipComponent.Points.Any(p => _convexHull.Covers(p));
        }

        private Geometry GetConvexHull() 
        {
            var coordinates = new List<Point>();

            var solids = GeometryHelper.GetAllSolids(_familyInstance);
            foreach (var solid in solids) 
            {
                foreach (Edge edge in solid.Edges)
                {
                    coordinates.AddRange(edge.Tessellate().Select(p => new Point(p.X, p.Y)));
                }
            }

            GeometryFactory geometryFactory = new GeometryFactory();
            var multiPoint = geometryFactory.CreateMultiPoint(coordinates.ToArray());
            var convexHull = multiPoint.ConvexHull().Buffer(_whipIntersectionTolerance);

            return convexHull;
        }
    }
}
