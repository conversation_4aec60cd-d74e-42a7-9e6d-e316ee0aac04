﻿using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.Ramp.Enums;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.Ramp.Models
{
    public class RampComponentLocationProvider
    {
        private readonly RampComponentProvider _rampComponentProvider;

        public RampComponentLocationProvider(RampComponentProvider rampComponentProvider)
        {
            _rampComponentProvider = rampComponentProvider;
        }

        public List<RampLine> Calculate(XYZ normal,
                                        List<Curve> curves,
                                        bool isLeftSide,
                                        bool isRightSide)
        {
            List<RampLine> rampLines = new List<RampLine>();

            if (curves.Count < 2)
            {
                var leftRampComponentWidth = isLeftSide
                    ? _rampComponentProvider.GetSideWidth()
                    : _rampComponentProvider.GetMiddleWidth();

                var rightRampComponentWidth = isRightSide
                    ? _rampComponentProvider.GetSideWidth(false)
                    : _rampComponentProvider.GetMiddleWidth();

                (List<XYZ> Points, double RemainingWidth) = GetUnitPositions(curves.First(),
                                                                             isLeftSide,
                                                                             isRightSide,
                                                                             _rampComponentProvider.GetMiddleWidth(),
                                                                             leftRampComponentWidth,
                                                                             rightRampComponentWidth);

                rampLines.Add(RampLine.Create(Points,
                                              (curves.First() as Line).Direction,
                                              normal,
                                              isLeftSide,
                                              isRightSide,
                                              RampAngleType.None,
                                              RampAngleType.None,
                                              Math.PI,
                                              Math.PI,
                                              RemainingWidth));
                return rampLines;
            }

            RampAngleType angleType = RampAngleType.None;
            double angle = 0;

            for (int i = 0; i < curves.Count - 1; i++)
            {
                Curve currentCurve = curves[i];
                Curve nextCurve = curves[i + 1];

                var currentNormal = CheckNormal(normal, currentCurve);

                RampAngleType previousAngleType = angleType;
                angleType = GetAngleTypeBetweenLines(currentNormal, nextCurve);

                double previousAngle = angle;
                angle = Math.PI - (currentCurve as Line).Direction.AngleTo((nextCurve as Line).Direction);

                var checkAngle90 = Math.Round(ProjectUnitsConverter.ToRadians(90d), 2);
                var checkAngle135 = Math.Round(ProjectUnitsConverter.ToRadians(135d), 2);
                var roundPreviousAngle = Math.Round(previousAngle, 2);
                var roundAngle = Math.Round(angle, 2);

                if (roundAngle != checkAngle90 && roundAngle != checkAngle135)
                {
                    throw new NullReferenceException("Angle between lines is not correct.");
                }

                bool isLeftSideCurrent = isLeftSide && currentCurve == curves.First()
                    || roundPreviousAngle == checkAngle90 && previousAngleType == RampAngleType.Internal
                    || (previousAngleType == RampAngleType.Internal || previousAngleType == RampAngleType.External)
                    && roundPreviousAngle == checkAngle135;

                bool isRightSideCurrent = isRightSide && currentCurve == curves.Last()
                    || angleType == RampAngleType.Internal && roundAngle == checkAngle90
                    || (angleType == RampAngleType.Internal || angleType == RampAngleType.External)
                    && roundAngle == checkAngle135;

                var addLeftLocation = isLeftSide && currentCurve == curves.First();
                var addRightLocation = isRightSide && currentCurve == curves.Last()
                    || angleType == RampAngleType.Internal && roundAngle == checkAngle90;

                double leftRampComponentWidth;
                if (isLeftSide && previousAngleType == RampAngleType.None)
                {
                    leftRampComponentWidth = _rampComponentProvider.GetSideWidth();
                }
                else if (previousAngle == 0 && previousAngleType == RampAngleType.None)
                {
                    leftRampComponentWidth = _rampComponentProvider.GetMiddleWidth();
                }
                else
                {
                    leftRampComponentWidth = _rampComponentProvider.GetCornerWidth(previousAngle, previousAngleType);
                }

                (List<XYZ> Points, double RemainingWidth) = GetUnitPositions(
                                              currentCurve,
                                              isLeftSideCurrent,
                                              isRightSideCurrent,
                                              _rampComponentProvider.GetMiddleWidth(),
                                              leftRampComponentWidth,
                                              currentCurve.Intersect(nextCurve) == SetComparisonResult.Disjoint
                                                  ? _rampComponentProvider.GetMiddleWidth()
                                                  : _rampComponentProvider.GetCornerWidth(angle, angleType),
                                              addLeftLocation,
                                              addRightLocation);

                rampLines.Add(RampLine.Create(Points,
                                              (currentCurve as Line).Direction,
                                              currentNormal,
                                              isLeftSideCurrent,
                                              isRightSideCurrent,
                                              previousAngleType,
                                              angleType,
                                              previousAngle,
                                              angle,
                                              RemainingWidth));

                if (nextCurve == curves.Last())
                {
                    previousAngleType = angleType;
                    previousAngle = angle;
                    roundPreviousAngle = Math.Round(previousAngle, 2);

                    currentNormal = CheckNormal(normal, nextCurve);

                    isLeftSideCurrent = roundPreviousAngle == checkAngle90 && previousAngleType == RampAngleType.Internal
                        || (previousAngleType == RampAngleType.Internal
                        || previousAngleType == RampAngleType.External)
                        && roundPreviousAngle == checkAngle135;

                    (List<XYZ> Points, double RemainingWidth) lastCurveInfo = GetUnitPositions(nextCurve,
                                                               isLeftSideCurrent,
                                                               isRightSide,
                                                               _rampComponentProvider.GetMiddleWidth(),
                                                               currentCurve.Intersect(nextCurve) == SetComparisonResult.Disjoint
                                                                   ? _rampComponentProvider.GetMiddleWidth()
                                                                   : _rampComponentProvider.GetCornerWidth(previousAngle, previousAngleType),
                                                               isRightSide
                                                                   ? _rampComponentProvider.GetSideWidth(isLeftSideSloped: false)
                                                                   : _rampComponentProvider.GetMiddleWidth(),
                                                               addLeftLocation: false,
                                                               isRightSide);

                    rampLines.Add(RampLine.Create(lastCurveInfo.Points,
                                                  (nextCurve as Line).Direction,
                                                  currentNormal,
                                                  isLeftSideCurrent,
                                                  isRightSide,
                                                  previousAngleType,
                                                  RampAngleType.None,
                                                  previousAngle,
                                                  Math.PI,
                                                  lastCurveInfo.RemainingWidth));
                }
            }

            return rampLines;
        }

        private (List<XYZ>, double) GetUnitPositions(Curve curve,
                                                     bool isLeftSide,
                                                     bool isRightSide,
                                                     double middleRampComponentWidth,
                                                     double leftRampComponentWidth,
                                                     double rightRampComponentWidth,
                                                     bool addLeftLocation = true,
                                                     bool addRightLocation = true)
        {
            List<XYZ> points = new List<XYZ>();
            Curve boundedCurve = null;

            if (isLeftSide && !isRightSide)
            {
                var leftComponentLocation = curve.Evaluate(leftRampComponentWidth / 2, false);

                if (addLeftLocation) points.Add(leftComponentLocation);

                boundedCurve = Line.CreateBound(
                    curve.Evaluate(leftRampComponentWidth, false),
                    curve.GetEndPoint(1));
            }
            else if (isRightSide && !isLeftSide)
            {
                var rightComponentLocation = curve.Evaluate(curve.Length - rightRampComponentWidth / 2, false);

                if (addRightLocation) points.Add(rightComponentLocation);

                boundedCurve = Line.CreateBound(
                    curve.GetEndPoint(0),
                    curve.Evaluate(curve.Length - rightRampComponentWidth, false));
            }
            else if (isLeftSide && isRightSide)
            {
                var leftComponentLocation = curve.Evaluate(leftRampComponentWidth / 2, false);
                var rightComponentLocation = curve.Evaluate(curve.Length - rightRampComponentWidth / 2, false);

                if (addLeftLocation && !addRightLocation) points.Add(leftComponentLocation);
                if (addRightLocation && !addLeftLocation) points.Add(rightComponentLocation);
                if (addLeftLocation && addRightLocation)
                {
                    points.Add(leftComponentLocation);
                    points.Add(rightComponentLocation);
                }

                boundedCurve = Line.CreateBound(
                    curve.Evaluate(leftRampComponentWidth, false),
                    curve.Evaluate(curve.Length - rightRampComponentWidth, false));
            }
            var currentCurve = isLeftSide || isRightSide ? boundedCurve : curve;

            var firstPoint = curve.GetEndPoint(0);
            points.Insert(0, firstPoint);

            XYZ currentPoint = currentCurve.Evaluate(middleRampComponentWidth / 2, false);

            double remainingLength = currentCurve.Length - middleRampComponentWidth / 2;
            double endMiddleRampComponentWidth = 0;

            points.Add(currentPoint);

            while (remainingLength >= middleRampComponentWidth + middleRampComponentWidth / 2)
            {
                currentPoint += (curve as Line).Direction * middleRampComponentWidth;

                points.Add(currentPoint);

                remainingLength -= middleRampComponentWidth;

                if (remainingLength > middleRampComponentWidth / 2 + 0.1
                    && remainingLength < middleRampComponentWidth + middleRampComponentWidth / 2)
                {
                    var distance = (remainingLength - middleRampComponentWidth / 2) / 2 + middleRampComponentWidth / 2;
                    endMiddleRampComponentWidth = remainingLength - middleRampComponentWidth / 2;
                    currentPoint += (currentCurve as Line).Direction * distance;
                    points.Add(currentPoint);
                }
            }

            var sortedPoints = RampLine.OrderPointsByCurveDirection(currentCurve, points);

            return (sortedPoints, endMiddleRampComponentWidth);
        }

        private RampAngleType GetAngleTypeBetweenLines(XYZ normal, Curve nextLine)
        {
            var dotProduct = normal.DotProduct((nextLine as Line).Direction);
            var crossProduct = normal.CrossProduct(XYZ.BasisZ);

            if (Math.Abs(crossProduct.DotProduct((nextLine as Line).Direction)) == 1)
            {
                return RampAngleType.None;
            }
            else if (dotProduct < 0)
            {
                return RampAngleType.External;
            }

            return RampAngleType.Internal;
        }

        private XYZ CheckNormal(XYZ normal, Curve currentCurve)
        {
            XYZ tangentVector = currentCurve.ComputeDerivatives(0.5, true).BasisX.Normalize();
            XYZ leftSide = new XYZ(tangentVector.Y, -tangentVector.X, 0).Normalize();
            XYZ rightSide = new XYZ(-tangentVector.Y, tangentVector.X, 0).Normalize();

            XYZ currentNormal = normal.X == leftSide.X && normal.Y == leftSide.Y ? leftSide : -rightSide;

            return currentNormal;
        }
    }
}