﻿using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters
{
    public class UnknownManufacturerToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue && (stringValue == "Unknown" || !FamilyPropertiesComparer.IsManufacturerFreeAxez(stringValue)))
            {
                return Brushes.Red;
            }

            return Brushes.Black;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
