﻿using Autodesk.Revit.DB;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.ExportForClient.Utils
{
    /// <summary>
    /// NOT FINISHED wait for a list of family names to swap and family models.
    /// Class provides methods to manipulate families and their types in project.
    /// </summary>
    public class FamilySwapManager
    {
        private Document _doc;
        private Dictionary<string, (string familyName, string symbolName)> _familyNamesToSwap = new Dictionary<string, (string familyName, string symbolName)>()
        {
            { "Familyname_FamilySymbolName", ("familyName", "symbolName") },
            { "FreeAxez-Base_Unit_Gridd-70", ("FreeAxez-Base_Unit_Half", "Gridd-70") },
        };


        public FamilySwapManager(Document doc)
        {
            _doc = doc;
        }


        /// <summary>
        /// NOT FINISHED wait for a list of family names to swap and family models.
        /// Method replace families with high detail level to families with low detail level.
        /// </summary>
        public void ReplaceFamilies()
        {
            var elementIdsToReplace = new FilteredElementCollector(_doc)
                .OfClass(typeof(FamilyInstance))
                .Cast<FamilyInstance>()
                .Where(i => i.Symbol.Name == "Gridd-70")
                .Select(i => i.Id)
                .ToList();

            var requiredSymbol = GetSymbol(_doc, GetFamily(_doc, "FreeAxez-Base_Unit_Half"), "Gridd-70");

            using (var t = new Transaction(_doc, "Replace family symbols"))
            {
                var failOptions = t.GetFailureHandlingOptions();
                failOptions.SetFailuresPreprocessor(new WarningSwallower());
                t.SetFailureHandlingOptions(failOptions);

                t.Start();

                Element.ChangeTypeId(_doc, elementIdsToReplace, requiredSymbol.Id);

                t.Commit();
            }
        }

        private string GetKey(FamilyInstance instance)
        {
            var symbolName = instance.Symbol.Name;
            var familyName = instance.Symbol.Family.Name;
            return $"{familyName}_{symbolName}";
        }

        private Family GetFamily(Document exportDoc, string familyName)
        {
            return new FilteredElementCollector(exportDoc).OfClass(typeof(Family)).Cast<Family>().Where(f => f.Name == familyName).FirstOrDefault();
        }

        private FamilySymbol GetSymbol(Document exportDoc, Family family, string symbolName)
        {
            return family.GetFamilySymbolIds().Select(id => exportDoc.GetElement(id)).Cast<FamilySymbol>().Where(f => f.Name == symbolName).FirstOrDefault();
        }
    }
}
