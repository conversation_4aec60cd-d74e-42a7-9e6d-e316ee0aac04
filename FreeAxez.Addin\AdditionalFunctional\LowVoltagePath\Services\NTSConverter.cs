using System;
using System.Linq;
using Autodesk.Revit.DB;
using NetTopologySuite.Geometries;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services
{
    /// <summary>
    /// A static service class to convert geometry between Revit API and NetTopologySuite formats.
    /// </summary>
    public static class NTSConverter
    {
        private const int CoordinateAccuracy = 4;

        /// <summary>
        /// Converts a Revit XYZ to a NetTopologySuite Point.
        /// The Z coordinate is ignored.
        /// </summary>
        /// <param name="xyz">The Revit XYZ object.</param>
        /// <returns>A new NetTopologySuite Point, or null if the input is null.</returns>
        public static NetTopologySuite.Geometries.Point XYZToPoint(XYZ xyz)
        {
            if (xyz == null)
            {
                return null;
            }
            return new NetTopologySuite.Geometries.Point(Math.Round(xyz.X, CoordinateAccuracy), Math.Round(xyz.Y, CoordinateAccuracy));
        }

        /// <summary>
        /// Converts a NetTopologySuite Point to a Revit XYZ.
        /// </summary>
        /// <param name="point">The NetTopologySuite Point object.</param>
        /// <param name="z">The optional Z coordinate for the new XYZ object. Defaults to 0.</param>
        /// <returns>A new Revit XYZ object, or null if the input is null.</returns>
        public static XYZ PointToXYZ(NetTopologySuite.Geometries.Point point, double z = 0)
        {
            if (point == null)
            {
                return null;
            }
            return new XYZ(Math.Round(point.X, CoordinateAccuracy), Math.Round(point.Y, CoordinateAccuracy), z);
        }

        /// <summary>
        /// Converts a Revit Curve to a NetTopologySuite LineString by tessellating the curve.
        /// The Z coordinates of the curve points are ignored.
        /// </summary>
        /// <param name="curve">The Revit Curve object.</param>
        /// <returns>A new NetTopologySuite LineString, or null if the input is null.</returns>
        public static LineString CurveToLineString(Curve curve)
        {
            if (curve == null)
            {
                return null;
            }

            var points = curve.Tessellate().Select(xyz => new Coordinate(Math.Round(xyz.X, CoordinateAccuracy), Math.Round(xyz.Y, CoordinateAccuracy))).ToArray();
            return new LineString(points);
        }

        public static LineString CurveLoopToLineString(List<Curve> curveLoop)
        {
            if (curveLoop == null || curveLoop.Count == 0)
            {
                return null;
            }

            var points = curveLoop
                .Select(c => c.Tessellate())
                .SelectMany(x => x).Select(xyz => new Coordinate(Math.Round(xyz.X, CoordinateAccuracy), Math.Round(xyz.Y, CoordinateAccuracy)))
                .ToArray();

            return new LineString(points);
        }

        /// <summary>
        /// Converts a NetTopologySuite LineString to a Revit Curve.
        /// If the LineString has two points, a Line is created.
        /// If it has more than two points, a start-end points line is created.
        /// </summary>
        /// <param name="lineString">The NetTopologySuite LineString object.</param>
        /// <param name="z">The optional Z coordinate for all points of the new Curve. Defaults to 0.</param>
        /// <returns>A new Revit Curve, or null if the input is null or has fewer than two points.</returns>
        public static Curve LineStringToCurve(LineString lineString, double z = 0)
        {
            if (lineString == null || lineString.Coordinates.Length < 2)
            {
                return null;
            }

            var points = lineString.Coordinates.Select(c => new XYZ(Math.Round(c.X, CoordinateAccuracy), Math.Round(c.Y, CoordinateAccuracy), z)).ToList();

            return Line.CreateBound(points[0], points[1]);
        }

        public static CurveLoop LineStringToCurveLoop(LineString lineString, double z = 0)
        {
            if (lineString == null || lineString.Coordinates.Length < 2)
            {
                return null;
            }
            var points = lineString.Coordinates.Select(c => new XYZ(Math.Round(c.X, CoordinateAccuracy), Math.Round(c.Y, CoordinateAccuracy), z)).ToList();
            var curveLoop = new CurveLoop();
            for (int i = 0; i < points.Count - 1; i++)
            {
                curveLoop.Append(Line.CreateBound(points[i], points[i + 1]));
            }
            return curveLoop;
        }
    }
}
