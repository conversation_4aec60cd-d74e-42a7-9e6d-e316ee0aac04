using Autodesk.Revit.DB;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Details;

public class CommonFailuresPreprocessor : IFailuresPreprocessor
{
    public FailureProcessingResult PreprocessFailures(FailuresAccessor failuresAccessor)
    {
        var failureMessages = failuresAccessor.GetFailureMessages();
        var hasErrors = false;

        foreach (var failureMessage in failureMessages)
        {
            var severity = failureMessage.GetSeverity();
            if (severity == FailureSeverity.Warning)
                HandleWarning(failuresAccessor, failureMessage);
            else if (severity == FailureSeverity.Error) hasErrors = HandleError(failuresAccessor, failureMessage);
        }

        return hasErrors ? FailureProcessingResult.ProceedWithCommit : FailureProcessingResult.Continue;
    }

    public static void SetFailuresPreprocessor(Transaction transaction)
    {
        var options = transaction.GetFailureHandlingOptions();
        options.SetFailuresPreprocessor(new CommonFailuresPreprocessor());
        transaction.SetFailureHandlingOptions(options);
    }

    private void HandleWarning(FailuresAccessor failuresAccessor, FailureMessageAccessor failureMessage)
    {
        failuresAccessor.DeleteWarning(failureMessage);
    }

    private bool HandleError(FailuresAccessor failuresAccessor, FailureMessageAccessor failureMessage)
    {
        var definitionId = failureMessage.GetFailureDefinitionId();
        var description = failureMessage.GetDescriptionText();
        var elementIds = failureMessage.GetFailingElementIds();
        var resolution = GetNextResolution(failuresAccessor, failureMessage);

        if (resolution == FailureResolutionType.Invalid)
        {
            failuresAccessor.DeleteWarning(failureMessage);
            return false;
        }

        failureMessage.SetCurrentResolutionType(resolution);
        failuresAccessor.ResolveFailure(failureMessage);
        return true;
    }

    private FailureResolutionType GetNextResolution(
        FailuresAccessor failuresAccessor, FailureMessageAccessor failureMessageAccessor)
    {
        var suitableResolutions = new List<FailureResolutionType>
        {
            FailureResolutionType.Default,
            FailureResolutionType.MoveElements,
            FailureResolutionType.SetValue,
            FailureResolutionType.UnlockConstraints,
            FailureResolutionType.DetachElements,
            FailureResolutionType.SkipElements,
            FailureResolutionType.Others,
            FailureResolutionType.DeleteElements
        };

        var usedResolutions = failuresAccessor
            .GetAttemptedResolutionTypes(failureMessageAccessor)
            .ToList();

        foreach (var resolution in suitableResolutions)
            if (!usedResolutions.Contains(resolution) && failureMessageAccessor.HasResolutionOfType(resolution))
                return resolution;

        return FailureResolutionType.Invalid;
    }
}