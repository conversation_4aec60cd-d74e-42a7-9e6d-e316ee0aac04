﻿using System.Windows.Input;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;

public class InfoVm : ModalDialogVm
{
    public InfoVm(string message)
    {
        Message = message;
        ApplyCommand = new RelayCommand(Ok);
    }

    public string Message { get; }

    public ICommand ApplyCommand { get; }


    private void Ok(object parameter)
    {
        CloseModal(false);
    }
}