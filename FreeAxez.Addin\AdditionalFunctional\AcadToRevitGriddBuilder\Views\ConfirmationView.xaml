<Window x:Class="FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Views.ConfirmationView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:viewModels="clr-namespace:FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.ViewModels"
        mc:Ignorable="d"
        WindowStartupLocation="CenterOwner"
        Title="{Binding Title}" SizeToContent="WidthAndHeight"
        ResizeMode="NoResize">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Window.DataContext>
        <viewModels:ConfirmationViewModel/>
    </Window.DataContext>
    <Grid>
        <StackPanel Margin="20" MinWidth="300">
            <TextBlock Style="{StaticResource TextBase}"
                       Margin="0 10 0 20"
                       TextWrapping="Wrap"
                       HorizontalAlignment="Center"
                       Text="{Binding Message}"/>
            
            <StackPanel Orientation="Horizontal" 
                        HorizontalAlignment="Center"
                        Margin="0 10 0 0">
                <Button Content="Да" 
                        Margin="0 0 10 0"
                        MinWidth="80"
                        Style="{StaticResource ButtonOutlinedRed}"
                        Command="{Binding YesCommand}" />
                <Button Content="Нет" 
                        MinWidth="80"
                        Style="{StaticResource ButtonOutlined}"
                        Command="{Binding NoCommand}" />
            </StackPanel>
        </StackPanel>
    </Grid>
</Window>
