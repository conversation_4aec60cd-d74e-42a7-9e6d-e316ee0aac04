<Window x:Class="FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views.SelectLinetypeWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Select Linetype"
        Height="400"
        Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </Window.Resources>

    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
            <TextBlock Text="Existing Linetypes"
                       FontWeight="Bold"/>
            <ProgressBar Width="20" Height="20"
                         Margin="10,0,0,0"
                         IsIndeterminate="True"
                         Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </StackPanel>

        <!-- Linetypes list -->
        <DataGrid Grid.Row="1"
                  Style="{DynamicResource DataGridWithoutBorders}"
                  ColumnHeaderStyle="{DynamicResource DataGridColumnHeader}"
                  ItemsSource="{Binding LoadedLinetypes}"
                  SelectedItem="{Binding SelectedLinetype}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  CanUserReorderColumns="False"
                  CanUserResizeRows="False"
                  HeadersVisibility="Column"
                  SelectionMode="Single"
                  Margin="0,0,0,15">

            <DataGrid.Columns>
                <DataGridTextColumn Header="Linetype"
                                    Binding="{Binding Name}"
                                    Width="200"
                                    IsReadOnly="True"/>
                <DataGridTextColumn Header="Description"
                                    Binding="{Binding Description}"
                                    Width="*"
                                    IsReadOnly="True"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Buttons -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Left side buttons -->
            <StackPanel Grid.Column="0"
                        Orientation="Horizontal"
                        HorizontalAlignment="Left">
                <Button Style="{StaticResource ButtonOutlinedRed}"
                        Command="{Binding DeleteLinetypeCommand}">
                    <StackPanel Orientation="Horizontal">
                        <ContentControl Template="{StaticResource TrashIcon}" Margin="0,0,8,0"/>
                        <TextBlock Text="Delete" VerticalAlignment="Center"
                                   Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>
            </StackPanel>

            <!-- Right side buttons -->
            <Grid Grid.Column="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Button Grid.Column="0"
                        Content="OK"
                        Command="{Binding OkCommand}"
                        Style="{StaticResource ButtonSimpleBlue}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Height="30"/>
                <Button Grid.Column="2"
                        Content="Cancel"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource ButtonSimpleRed}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Height="30"/>
                <Button Grid.Column="4"
                        Content="Load..."
                        Style="{StaticResource ButtonOutlinedBlue}"
                        Command="{Binding LoadCommand}"
                        Height="30"/>
            </Grid>
        </Grid>
    </Grid>
</Window>
