﻿using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media.Imaging;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters;

public class AsyncImageConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var uri = value as string;
        if (string.IsNullOrEmpty(uri))
            return null;

        var image = new BitmapImage();
        image.BeginInit();
        image.UriSource = new Uri(uri, UriKind.RelativeOrAbsolute);
        image.CacheOption = BitmapCacheOption.OnLoad;
        image.EndInit();
        image.DownloadCompleted += (s, e) => { };
        return image;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}