﻿using System.Windows;
using System.Windows.Controls;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.ViewModel;

namespace FreeAxez.Addin.AdditionalFunctional.UserAccountControl.View
{
    /// <summary>
    /// Interaction logic for LoginView.xaml
    /// </summary>
    public partial class LoginView : Window
    {
        public LoginView()
        {
            InitializeComponent();
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (this.DataContext is LoginVm viewModel)
            {
                viewModel.UserPassword = PasswordBox.Password;
                PlaceholderText.Visibility = PasswordBox.Password.Length > 0
                    ? Visibility.Collapsed
                    : Visibility.Visible;
            }
        }
    }
}
