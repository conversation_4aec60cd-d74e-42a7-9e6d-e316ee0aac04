﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.Views.TransferParameterValueView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.Views"
        xmlns:vm="clr-namespace:FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.ViewModels"
        mc:Ignorable="d" 
        MinHeight="450" MinWidth="800"
        Height="450" Width="800"
        WindowStartupLocation="CenterScreen"
        Title="Duplicate parameter values">
    <Grid Margin="3,10,3,3">
        <Grid.Resources>
            <Style TargetType="Button">
                <Setter Property="Padding" Value="3"/>
                <Setter Property="Margin" Value="5"/>
            </Style>
        </Grid.Resources>
        <Grid.ColumnDefinitions>
            <ColumnDefinition/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition/>
            <ColumnDefinition Width="100"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="25"/>
            <RowDefinition Height="25"/>
            <RowDefinition/>
            <RowDefinition Height="40"/>
        </Grid.RowDefinitions>
        
        <DockPanel Margin="0,0,4,0" Grid.Column="0" Grid.Row="0" Grid.ColumnSpan="4">
            <Label DockPanel.Dock="Left" Content="Parameters set:" />
            <ComboBox ItemsSource="{Binding ParameterSets}" SelectedItem="{Binding SelectedParameterSet}">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Name}"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
        </DockPanel>

        <Label Grid.Column="0" Grid.Row="1" DockPanel.Dock="Top" Content="Available parameters:"/>
        <ListBox Margin="3" Grid.Column="0" Grid.Row="2" ItemsSource="{Binding AvailableParameterNames}" SelectedItem="{Binding SelectedAvailableParameterName}"/>
        
        <Grid Grid.Column="1" Grid.Row="2" VerticalAlignment="Top">
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
            </Grid.RowDefinitions>
            <Button Grid.Row="0" Content="Add" Command="{Binding AddParameterCommand}"/>
            <Button Grid.Row="1" Content="Remove" Command="{Binding RemoveParameterCommand}"/>
        </Grid>

        <Label Grid.Column="2" Grid.Row="1" DockPanel.Dock="Top" Content="Duplicate parameters:"/>
        <ListBox Margin="3" Grid.Column="2" Grid.Row="2" ItemsSource="{Binding DuplicateParameterNames}" SelectedItem="{Binding SelectedDuplicateParameterName}"/>

        <Grid Grid.Column="3" Grid.Row="2" VerticalAlignment="Top">
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
            </Grid.RowDefinitions>
            <Button Grid.Row="0" Content="New" Command="{Binding NewCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
            <Button Grid.Row="1" Content="Rename" Command="{Binding RenameCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}" IsEnabled="{Binding RenameCommandAvailable}"/>
            <Button Grid.Row="2" Content="Duplicate" Command="{Binding DuplicateSetCommand}"/>
            <Button Grid.Row="3" Content="Delete"  Command="{Binding DeleteCommand}" IsEnabled="{Binding DeleteCommandAvailable}"/>
        </Grid>

        <DockPanel Grid.Column="0" Grid.Row="4" Grid.ColumnSpan="4" LastChildFill="False">
            <Button DockPanel.Dock="Right" Width="100" Content="Cancel" Command="{Binding CancelCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
            <Button DockPanel.Dock="Right" Width="100" Content="Run" Command="{Binding DuplicateCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
        </DockPanel>
    </Grid>
</Window>
