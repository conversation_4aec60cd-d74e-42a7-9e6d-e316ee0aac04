﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils
{
    public class PanelCollector
    {
        private readonly string _panelFamilyNameSuffix = "panelboard";
        private readonly LevelHelper _levelHelper;


        public PanelCollector(LevelHelper levelHelper)
        {
            _levelHelper = levelHelper;
        }


        public List<FamilyInstance> GetPanelFamilyInstances()
        {
            return GetPanelFamilySymbol()
              .GetDependentElements(new ElementClassFilter(typeof(FamilyInstance)))
              .Select(RevitManager.Document.GetElement)
              .Cast<FamilyInstance>()
              .Where(_levelHelper.BelongsToTheSelectedLevel)
              .ToList();
        }

        public FamilySymbol GetPanelFamilySymbol()
        {
            var familySymbol = new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_ElectricalEquipment)
                .WhereElementIsElementType()
                .Where(t => t is FamilySymbol)
                .Cast<FamilySymbol>()
                .Where(s => s.FamilyName.ToLower().Contains(_panelFamilyNameSuffix))
                .FirstOrDefault();

            if (familySymbol is null) throw new InvalidOperationException(
                $"The panel family with the category Electrical Equipment and the family name suffix \"{_panelFamilyNameSuffix}\" was not found in the project.");

            return familySymbol;
        }
    }
}
