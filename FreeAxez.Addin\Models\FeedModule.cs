﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class FeedModule : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_ElectricalFixtures,
            FamilyNamesContains = new List<string>()
            {
                "FeedModule"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public FeedModule(Element element) : base(element)
        {
        }

        public static List<FeedModule> Collect()
        {
            return FamilyCollector.Instances.Select(g => new FeedModule(g)).ToList();
        }
    }
}
