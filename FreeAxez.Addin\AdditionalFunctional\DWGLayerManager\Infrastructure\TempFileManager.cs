using System;
using System.IO;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Infrastructure;

public class TempFileManager : IDisposable
{
    public TempFileManager()
    {
        CreateTempDirectory();
    }

    public string TempDirectory { get; private set; }
    public string JsonFilePath { get; private set; }

    public void Dispose()
    {
        CleanupTempFiles();
    }

    private void CreateTempDirectory()
    {
        var tempPath = Path.GetTempPath();
        var uniqueFolder = $"DWGLayerManager_{Guid.NewGuid():N}";
        TempDirectory = Path.Combine(tempPath, uniqueFolder);

        Directory.CreateDirectory(TempDirectory);
        JsonFilePath = Path.Combine(TempDirectory, "layers.json");
    }

    private void CleanupTempFiles()
    {
        try
        {
            if (Directory.Exists(TempDirectory))
            {
                Directory.Delete(TempDirectory, true);
            }
        }
        catch (Exception)
        {
            // Ignore temp file cleanup errors - they don't affect functionality
            // The OS will eventually clean up temp files
        }
    }
}