﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent;

[Transaction(TransactionMode.Manual)]
public class TransferProjectContent : BaseExternalCommand
{
    public override Result Execute()
    {
        if (RevitManager.Application.Documents.Size < 2)
        {
            MessageWindow.ShowDialog(
                "For the plug-in to work, the source and target documents must be open.", 
                MessageType.Notify);

            return Result.Cancelled;
        }

        var window = new TransferProjectContentView();
        RevitManager.SetRevitAsWindowOwner(window);
        window.ShowDialog();
        window.Activate();

        return Result.Succeeded;
    }
}