using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Details;

/// <summary>
///     Utility class for copying detail views between documents
/// </summary>
public static class DetailsCopyUtility
{
    /// <summary>
    ///     Copies selected drafting views with progress reporting and cancellation support
    /// </summary>
    public static List<ViewDrafting> CopySelectedDraftingViewsWithProgress(
        Document sourceDoc,
        Document targetDoc,
        List<ElementId> selectedViewIds,
        ref bool copySuccess)
    {
        var copiedViews = new List<ViewDrafting>();
        copySuccess = true;

        if (selectedViewIds == null || selectedViewIds.Count == 0) return copiedViews;

        var templateDrafting = GetOrCreateTemplateDrafting(targetDoc, out var createdTemp);

        var currentIndex = 0;

        foreach (var viewId in selectedViewIds)
            using (var transaction = new Transaction(targetDoc, "Copy Detail View"))
            {
                try
                {
                    transaction.Start();
                    CommonFailuresPreprocessor.SetFailuresPreprocessor(transaction);

                    var sourceView = sourceDoc.GetElement(viewId) as ViewDrafting;
                    if (sourceView == null) continue;

                    LogHelper.Information(
                        $"Copying view {currentIndex + 1} of {selectedViewIds.Count}: {sourceView.Name}");

                    ViewDrafting targetDraftingView;
                    var existingView = GetExistingDraftingView(targetDoc, sourceView.Name);

                    if (existingView != null)
                    {
                        targetDraftingView = existingView;
                        ClearViewContent(targetDraftingView);
                        LogHelper.Information($"Updating existing view: {sourceView.Name}");
                    }
                    else
                    {
                        var targetDraftingViewId = templateDrafting.Duplicate(ViewDuplicateOption.Duplicate);
                        targetDraftingView = targetDoc.GetElement(targetDraftingViewId) as ViewDrafting;
                        targetDraftingView.Name = sourceView.Name;
                        targetDraftingView.Discipline = sourceView.Discipline;
                        LogHelper.Information($"Creating new view: {sourceView.Name}");
                    }

                    targetDraftingView.Scale = sourceView.Scale;
                    AnnotationManager.CopyViewContent(sourceView, targetDraftingView, CancellationToken.None);

                    transaction.Commit();

                    copiedViews.Add(targetDraftingView);
                    currentIndex++;

                    LogHelper.Information($"Successfully copied view: {sourceView.Name}");
                }
                catch (Exception ex)
                {
                    transaction.RollBack();
                    LogHelper.Error($"Error copying view {viewId}: {ex.Message}");
                    copySuccess = false;
                }
            }

        if (createdTemp)
        {
            using var tx = new Transaction(targetDoc, "Delete temp drafting view");
            tx.Start();
            targetDoc.Delete(templateDrafting.Id);
            tx.Commit();
        }

        return copiedViews;
    }

    /// <summary>
    ///     Gets existing drafting view by name in the target document
    /// </summary>
    private static ViewDrafting GetExistingDraftingView(Document doc, string viewName)
    {
        return new FilteredElementCollector(doc)
            .OfClass(typeof(ViewDrafting))
            .Cast<ViewDrafting>()
            .FirstOrDefault(v => v.Name.Equals(viewName, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    ///     Clears all content from a drafting view (excluding title blocks)
    /// </summary>
    private static void ClearViewContent(ViewDrafting view)
    {
        try
        {
            var doc = view.Document;

            // Get all elements in the view except title blocks (same logic as IsDraftingViewNonEmpty)
            var elementsToDelete = AnnotationManager.GetAnnotations(view);

            if (elementsToDelete.Count > 0)
            {
                doc.Delete(elementsToDelete);
                LogHelper.Information($"Cleared {elementsToDelete.Count} elements from view {view.Name}");
            }
            else
            {
                LogHelper.Information($"View {view.Name} was already empty");
            }
        }
        catch (Exception ex)
        {
            LogHelper.Warning($"Failed to clear view content for {view.Name}: {ex.Message}");
        }
    }

    private static ViewDrafting GetOrCreateTemplateDrafting(
        Document doc,
        out bool created)
    {
        created = false;

        var template = new FilteredElementCollector(doc)
            .OfClass(typeof(ViewDrafting))
            .Cast<ViewDrafting>()
            .FirstOrDefault(v => v.CanViewBeDuplicated(ViewDuplicateOption.Duplicate));

        if (template != null) return template;

        var draftingType = new FilteredElementCollector(doc)
            .OfClass(typeof(ViewFamilyType))
            .Cast<ViewFamilyType>()
            .FirstOrDefault(t => t.ViewFamily == ViewFamily.Drafting);

        using (var tx = new Transaction(doc, "Create temp drafting view"))
        {
            tx.Start();
            var tempViewDrafting = ViewDrafting.Create(doc, draftingType.Id);
            tx.Commit();
            created = true;
            return tempViewDrafting;
        }
    }
}