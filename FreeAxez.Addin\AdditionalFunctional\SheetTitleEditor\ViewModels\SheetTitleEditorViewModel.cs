using FreeAxez.Addin.AdditionalFunctional.SheetTitleEditor.Models;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.SheetTitleEditor.ViewModels
{
    public class SheetTitleEditorViewModel : WindowViewModel
    {
        private ObservableCollection<SheetTitleModel> _allItems;
        private ICollectionView _filteredItems;
        private string _searchText = string.Empty;
        private bool _hasChanges = false;

        public ICollectionView FilteredItems
        {
            get => _filteredItems;
            set => Set(ref _filteredItems, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (Set(ref _searchText, value))
                {
                    ApplyFilter();
                }
            }
        }

        public int FilteredCount => FilteredItems?.Cast<SheetTitleModel>().Count() ?? 0;
        public int SelectedCount => FilteredItems?.Cast<SheetTitleModel>().Count(i => i.IsSelected) ?? 0;

        public ICommand SelectAllCommand { get; }
        public ICommand ClearSelectionCommand { get; }
        public ICommand CopyViewNameToTitleCommand { get; }
        public ICommand ApplyCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand ClearFiltersCommand { get; }

        public SheetTitleEditorViewModel(List<SheetTitleModel> sheetTitleModels)
        {
            _allItems = new ObservableCollection<SheetTitleModel>(sheetTitleModels);

            // Initialize CollectionView for filtering and sorting
            FilteredItems = CollectionViewSource.GetDefaultView(_allItems);

            // Set up sorting (first by BrowserPath, then by SheetNumber, then by SheetName)
            FilteredItems.SortDescriptions.Add(new SortDescription("BrowserPath", ListSortDirection.Ascending));
            FilteredItems.SortDescriptions.Add(new SortDescription("SheetNumber", ListSortDirection.Ascending));

            // Set up filtering
            FilteredItems.Filter = FilterItem;

            // Initialize commands
            SelectAllCommand = new RelayCommand(SelectAll);
            ClearSelectionCommand = new RelayCommand(ClearSelection);
            CopyViewNameToTitleCommand = new RelayCommand(CopyViewNameToTitle, CanCopyViewNameToTitle);
            ApplyCommand = new RelayCommand(Apply);
            CancelCommand = new RelayCommand(Cancel);
            ClearFiltersCommand = new RelayCommand(ClearFilters);
        }

        // Public method to update selected count (called from code-behind)
        public void UpdateSelectedCount()
        {
            OnPropertyChanged(nameof(SelectedCount));
            (CopyViewNameToTitleCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }

        // Filter predicate for CollectionView
        private bool FilterItem(object item)
        {
            if (string.IsNullOrWhiteSpace(SearchText))
                return true;

            var sheetTitleModel = item as SheetTitleModel;
            return sheetTitleModel != null && (
                   sheetTitleModel.SheetNumber.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                   sheetTitleModel.SheetName.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                   sheetTitleModel.BrowserPath.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                   sheetTitleModel.ViewName.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                   sheetTitleModel.SheetTitle.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0);
        }

        private void ApplyFilter()
        {
            // Reset selection for items that will be filtered out
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                foreach (var item in _allItems)
                {
                    if (!FilterItem(item))
                    {
                        item.IsSelected = false;
                    }
                }
            }

            // Refresh the view to apply the filter
            FilteredItems.Refresh();

            // Notify UI about count changes
            OnPropertyChanged(nameof(FilteredCount));
            OnPropertyChanged(nameof(SelectedCount));
        }

        private void SelectAll(object p)
        {
            foreach (var item in FilteredItems.Cast<SheetTitleModel>())
            {
                item.IsSelected = true;
            }

            OnPropertyChanged(nameof(SelectedCount));
            (CopyViewNameToTitleCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }

        private void ClearSelection(object p)
        {
            foreach (var item in FilteredItems.Cast<SheetTitleModel>())
            {
                item.IsSelected = false;
            }

            OnPropertyChanged(nameof(SelectedCount));
            (CopyViewNameToTitleCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }

        private void ClearFilters(object p)
        {
            _searchText = string.Empty;

            foreach (var item in _allItems)
            {
                item.IsSelected = false;
            }

            ApplyFilter();

            OnPropertyChanged(nameof(SearchText));
        }

        private bool CanCopyViewNameToTitle(object p)
        {
            return FilteredItems.Cast<SheetTitleModel>().Any(i => i.IsSelected);
        }

        private void CopyViewNameToTitle(object p)
        {
            var selectedItems = FilteredItems.Cast<SheetTitleModel>().Where(i => i.IsSelected).ToList();

            foreach (var item in selectedItems)
            {
                item.SheetTitle = item.ViewName;
            }

            (ApplyCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }

        private void Apply(object p)
        {
            var window = p as Window;
            window.DialogResult = true;
            window.Close();
        }

        private void Cancel(object p)
        {
            var hasChanges = _allItems.Any(i => i.IsTitleChanged);

            if (hasChanges)
            {
                var result = MessageWindow.ShowDialog(
                    "Do you really want to cancel all unapplied changes?", 
                    MessageType.Warning);

                if (result != true)
                {
                    return;
                }
            }

            var window = p as Window;
            window.DialogResult = false;
            window.Close();
        }
    }
}