﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.PanelScheduleCreation.Views;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.PanelScheduleCreation
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    internal class PanelScheduleCreationCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var panelScheduleCreationView = new PanelScheduleCreationView();
            panelScheduleCreationView.ShowDialog();

            return Result.Succeeded;
        }
    }
}
