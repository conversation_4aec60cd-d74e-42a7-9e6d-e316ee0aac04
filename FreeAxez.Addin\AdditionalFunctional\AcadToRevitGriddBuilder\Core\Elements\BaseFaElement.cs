﻿using System.Reflection;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using NetTopologySuite.Geometries;
using LineSegment = NetTopologySuite.Geometries.LineSegment;
using Point = FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data.Point;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Core.Elements;

public abstract class BaseFaElement
{
    protected BaseFaElement(int id, List<LineSegmentData> segments)
    {
        Id = id;
        CalculateCenter(segments);
        CalculateRotationAngle(segments);
        IsRotated = Math.Abs(RotationAngle) > 0.1;
        Lines = segments.Select(d => d.Data).ToList();
    }

    protected BaseFaElement()
    {
        Id = 0;
        Lines = new List<JsonLineData>();
    }

    public int Id { get; private set; }
    public Point Center { get; set; }
    public List<JsonLineData> Lines { get; private set; } = new();
    public double RotationAngle { get; set; }
    public bool IsRotated { get; set; }
    public int LineCount => Lines.Count;

    public abstract ElementTypeConfiguration Configuration { get; }

    public static List<T> CreateFromComponents<T>(
        List<List<LineSegmentData>> components,
        int idCounter,
        HashSet<LineSegmentData> usedSegments,
        Transform dwgTransform) where T : BaseFaElement, new()
    {
        var elements = new List<T>();
        var tempInstance = new T();

        foreach (var component in components)
        {
            if (tempInstance.IsValidComponentForThisType(component, usedSegments))
            {
                var transformedSegments = ApplyTransformToSegments(component, dwgTransform);
                var element = (T)Activator.CreateInstance(typeof(T),
                    BindingFlags.NonPublic | BindingFlags.Instance,
                    null,
                    new object[] { idCounter++, transformedSegments },
                    null);
                elements.Add(element);

                MarkSegmentsAsUsed(component, usedSegments, tempInstance);
            }
        }

        return elements;
    }






    protected abstract void CalculateCenter(List<LineSegmentData> segments);
    protected abstract void CalculateRotationAngle(List<LineSegmentData> segments);

    protected abstract bool
        IsValidComponentForThisType(List<LineSegmentData> component, HashSet<LineSegmentData> usedSegments);
    protected virtual void MarkSegmentsAsUsed(List<LineSegmentData> component, HashSet<LineSegmentData> usedSegments)
    {
        foreach (var segment in component)
            usedSegments.Add(segment);
    }

    public static List<LineSegmentData> ApplyTransformToSegments(List<LineSegmentData> segments, Transform dwgTransform)
    {
        if (dwgTransform == null || dwgTransform.IsIdentity)
            return segments;

        return segments.Select(segment =>
        {
            var transformedData = TransformLineData(segment.Data, dwgTransform);
            var p0 = new Coordinate(transformedData.startPoint.x, transformedData.startPoint.y);
            var p1 = new Coordinate(transformedData.endPoint.x, transformedData.endPoint.y);

            return new LineSegmentData
            {
                Index = segment.Index,
                Segment = new LineSegment(p0, p1),
                Data = transformedData,
                IsPrimaryEdge = segment.IsPrimaryEdge
            };
        }).ToList();
    }

    private static void MarkSegmentsAsUsed(List<LineSegmentData> component, HashSet<LineSegmentData> usedSegments, BaseFaElement tempInstance)
    {
        tempInstance.MarkSegmentsAsUsed(component, usedSegments);
    }

    public static JsonLineData TransformLineData(JsonLineData lineData, Transform dwgTransform)
    {
        var startPoint = new XYZ(lineData.startPoint.x / 12.0, lineData.startPoint.y / 12.0, 0.0);
        var endPoint = new XYZ(lineData.endPoint.x / 12.0, lineData.endPoint.y / 12.0, 0.0);

        var transformedStart = dwgTransform.OfPoint(startPoint);
        var transformedEnd = dwgTransform.OfPoint(endPoint);

        return new JsonLineData
        {
            startPoint = new JsonPoint3D
            {
                x = transformedStart.X * 12.0,
                y = transformedStart.Y * 12.0
            },
            endPoint = new JsonPoint3D
            {
                x = transformedEnd.X * 12.0,
                y = transformedEnd.Y * 12.0
            },
            length = lineData.length,
            angle = lineData.angle,
            layer = lineData.layer
        };
    }

    public virtual void SetElementSpecificParameters(FamilyInstance instance)
    {
    }

    public static List<LineSegmentData> ConvertLinesToSegmentsUniversal(List<JsonLineData> lines)
    {
        return lines.Select((l, i) =>
        {
            var p0 = new Coordinate(l.startPoint.x, l.startPoint.y);
            var p1 = new Coordinate(l.endPoint.x, l.endPoint.y);
            return new LineSegmentData
            {
                Index = i,
                Segment = new LineSegment(p0, p1),
                Data = l,
                IsPrimaryEdge = false
            };
        }).ToList();
    }

    protected static double NormalizeAngle(double angle)
    {
        while (angle < 0) angle += 360;
        while (angle >= 360) angle -= 360;
        return angle;
    }

    protected static double SnapToCardinal(double angle)
    {
        foreach (var cardinal in new[] { 0, 90, 180, 270 })
            if (Math.Abs(angle - cardinal) < 5)
                return cardinal;
        return angle;
    }
}