﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.ExportForVR.Views;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ExportForVR
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class ExportForVRCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            // Need to save all changes, because when exporting it will be copied via File.Copy()
            RevitManager.Document.Save();

            var exportView = new ExportForVRView();
            exportView.ShowDialog();

            return Result.Succeeded;
        }
    }
}
