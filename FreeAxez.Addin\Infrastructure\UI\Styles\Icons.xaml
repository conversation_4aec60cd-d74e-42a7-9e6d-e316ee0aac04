﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="ColorsPalette.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <ControlTemplate x:Key="WarningIcon">
        <Grid Width="30"
              Height="30"
              Margin="0 0 0 5"
              VerticalAlignment="Center">
            <Ellipse Width="30"
                     Height="30"
                     Fill="{StaticResource Yellow100}" />
            <Viewbox Width="24"
                     Height="24"
                     HorizontalAlignment="Center"
                     VerticalAlignment="Center">
                <Canvas Width="512"
                        Height="512">
                    <Path Data="M85.57,446.25H426.43a32,32,0,0,0,28.17-47.17L284.18,82.58c-12.09-22.44-44.27-22.44-56.36,0L57.4,399.08A32,32,0,0,0,85.57,446.25Z"
                          Stroke="{StaticResource Yellow500}"
                          StrokeLineJoin="Round"
                          StrokeThickness="32" />
                    <Path Data="M250.26,195.39l5.74,122,5.73-121.95a5.74,5.74,0,0,0-5.79-6h0A5.74,5.74,0,0,0,250.26,195.39Z"
                          Stroke="{StaticResource Yellow500}"
                          StrokeLineJoin="Round"
                          StrokeThickness="32" />
                    <Ellipse Width="40"
                             Height="40"
                             Canvas.Left="236"
                             Canvas.Top="357.25"
                             Fill="{StaticResource Yellow500}" />
                </Canvas>
            </Viewbox>
        </Grid>
    </ControlTemplate>

    <ControlTemplate x:Key="NotifyIcon">
        <Grid Width="30"
              Height="30"
              Margin="0 0 0 5"
              VerticalAlignment="Center">
            <Ellipse Width="30"
                     Height="30"
                     Fill="{StaticResource Purple100}" />
            <Viewbox Width="24"
                     Height="24"
                     HorizontalAlignment="Center"
                     VerticalAlignment="Center">
                <Canvas Width="512"
                        Height="512">
                    <Path Data="M85.57,446.25H426.43a32,32,0,0,0,28.17-47.17L284.18,82.58c-12.09-22.44-44.27-22.44-56.36,0L57.4,399.08A32,32,0,0,0,85.57,446.25Z"
                          Stroke="{StaticResource Purple500}"
                          StrokeLineJoin="Round"
                          StrokeThickness="32" />
                    <Path Data="M250.26,195.39l5.74,122,5.73-121.95a5.74,5.74,0,0,0-5.79-6h0A5.74,5.74,0,0,0,250.26,195.39Z"
                          Stroke="{StaticResource Purple500}"
                          StrokeLineJoin="Round"
                          StrokeThickness="32" />
                    <Ellipse Width="40"
                             Height="40"
                             Canvas.Left="236"
                             Canvas.Top="357.25"
                             Fill="{StaticResource Purple500}" />
                </Canvas>
            </Viewbox>
        </Grid>
    </ControlTemplate>

    <ControlTemplate x:Key="InfoIcon">
        <Grid Width="30"
              Height="30"
              Margin="0 0 0 5"
              VerticalAlignment="Center">
            <Ellipse Width="30"
                     Height="30"
                     Fill="{StaticResource Blue100}" />
            <Viewbox Width="24"
                     Height="24">
                <Canvas Width="512"
                        Height="512">
                    <Path Data="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336h24V272H216c-13.3 0-24-10.7-24-24s10.7-24 24-24h48c13.3 0 24 10.7 24 24v88h8c13.3 0 24 10.7 24 24s-10.7 24-24 24H216c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-208a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"
                          Fill="{StaticResource Blue500}" />
                </Canvas>
            </Viewbox>
        </Grid>
    </ControlTemplate>

    <ControlTemplate x:Key="SuccessIcon">
        <Grid Width="30"
              Height="30"
              Margin="0 0 0 5"
              VerticalAlignment="Center">
            <Ellipse Width="30"
                     Height="30"
                     Fill="{StaticResource Green100}" />
            <Viewbox Width="20"
                     Height="20"
                     HorizontalAlignment="Center"
                     VerticalAlignment="Center">
                <Canvas Width="512"
                        Height="512">
                    <Path Fill="{StaticResource Green500}"
                          Data="M437.016,74.984c-99.979-99.979-262.075-99.979-362.033,0.002c-99.978,99.978-99.978,262.073,0.004,362.031
             c99.954,99.978,262.05,99.978,362.029-0.002C536.995,337.059,536.995,174.964,437.016,74.984z M406.848,406.844
             c-83.318,83.318-218.396,83.318-301.691,0.004c-83.318-83.299-83.318-218.377-0.002-301.693
             c83.297-83.317,218.375-83.317,301.691,0S490.162,323.549,406.848,406.844z" />
                    <Path Fill="{StaticResource Green500}"
                          Data="M368.911,155.586L234.663,289.834l-70.248-70.248c-8.331-8.331-21.839-8.331-30.17,0s-8.331,21.839,0,30.17
             l85.333,85.333c8.331,8.331,21.839,8.331,30.17,0l149.333-149.333c8.331-8.331,8.331-21.839,0-30.17
             S377.242,147.255,368.911,155.586z" />
                </Canvas>
            </Viewbox>
        </Grid>
    </ControlTemplate>

    <ControlTemplate x:Key="ErrorIcon">
        <Grid Width="30"
              Height="30"
              Margin="0 0 0 5"
              HorizontalAlignment="Center"
              VerticalAlignment="Center">
            <Ellipse Width="30"
                     Height="30"
                     Fill="{StaticResource Red100}" />
            <Viewbox Width="24"
                     Height="24">
                <Canvas Width="512"
                        Height="512">
                    <Path Data="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-384c-13.3 0-24 10.7-24 24V264c0 13.3 10.7 24 24 24s24-10.7 24-24V152c0-13.3-10.7-24-24-24zM288 352a32 32 0 1 1 -64 0 32 32 0 1 1 64 0z"
                          Fill="{StaticResource Red500}" />
                </Canvas>
            </Viewbox>
        </Grid>
    </ControlTemplate>

    <ControlTemplate x:Key="FamilyIcon">
        <Viewbox Width="20"
                 Height="20">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M248.8 191.5L86.5 120.5L248.1 54.9L425.5 120.3L248.8 191.5ZM256 243.7V456.3L427 384.1V174.7L256 243.7ZM66.9 72.4C47.2 80.1 34 99.6 34 120.6V384.6C34 405.1 46.5 424.1 65.4 432.4L234.1 504.6C247.9 510.6 263.6 510.7 277.5 504.8L446.8 432.4C466.1 424.8 479 405.2 479 384.1V120.6C479 99.5 465.6 79.7 445.2 72.2L273.6 7.0C260.6 2.5 246.1 2.5 233.1 7.1L66.9 72.4Z"
                      Fill="White" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="AlertIcon">
        <Viewbox Width="20"
                 Height="20">
            <Canvas Width="512"
                    Height="512">
                <Ellipse Width="444"
                         Height="444"
                         Canvas.Left="34"
                         Canvas.Top="34"
                         Fill="White"
                         Stroke="White"
                         StrokeThickness="0" />
                <Rectangle Width="68"
                           Height="68"
                           Canvas.Left="222"
                           Canvas.Top="342"
                           RadiusX="17"
                           RadiusY="17"
                           Fill="{StaticResource Blue500}" />
                <Rectangle Width="68"
                           Height="188"
                           Canvas.Left="222"
                           Canvas.Top="102"
                           Fill="{StaticResource Blue500}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="RefreshIcon">
        <Viewbox Width="20"
                 Height="20">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M125.7 175.5c17.6-28.9 44.3-51.2 77.4-63.3c33.1-12.1 69.8-11.1 102.3 2.8c22.1 9.5 41.9 23.3 58.4 40.6L334.6 184.6c-11.4 10.5-2.5 28.5 14.6 29.7l85.7 6.2c8.9 0.6 16.6-6.6 17.2-16.5l6.2-85.7c1.2-17.1-19.2-26-29.7-14.6l-27.8 30.1C360.9 99.6 315.1 80 265.3 75.6c-49.8-4.4-99.5 8.8-139.7 37.3c-40.2 28.5-69.1 70.3-81.5 117.6c-12.4 47.3-7.2 97.7 14.6 141.7l63.1-26.6c-14.7-29.7-17.6-63.3-8.1-94.1zM386.3 336.5c-17.6 28.9-44.3 51.2-77.4 63.3c-33.1 12.1-69.8 11.1-102.3-2.8c-22.1-9.5-41.9-23.3-58.4-40.6l29.2-29c11.4-10.5 2.5-28.5-14.6-29.7l-85.7-6.2c-8.9-0.6-16.6 6.6-17.2 16.5l-6.2 85.7c-1.2 17.1 19.2 26 29.7 14.6l27.8-30.1c39.9 34.2 85.7 53.8 135.5 58.2c49.8 4.4 99.5-8.8 139.7-37.3c40.2-28.5 69.1-70.3 81.5-117.6c12.4-47.3 7.2-97.7-14.6-141.7l-63.1 26.6c14.7 29.7 17.6 63.3 8.1 94.1z"
                      Fill="White" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="HistoryIcon">
        <Viewbox Width="20"
                 Height="20">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm0 448c-110.5 0-200-89.5-200-200S145.5 56 256 56s200 89.5 200 200-89.5 200-200 200zm61.8-104.4l-84.9-61.7c-3.1-2.3-4.9-5.9-4.9-9.7V116c0-6.6 5.4-12 12-12h32c6.6 0 12 5.4 12 12v141.7l66.8 48.6c5.4 3.9 6.5 11.4 2.6 16.8L334.6 349c-3.9 5.3-11.4 6.5-16.8 2.6z"
                      Fill="White" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="TabletIcon">
        <Viewbox Width="20"
                 Height="20">
            <Canvas Width="512"
                    Height="512">
                <Rectangle Width="358"
                           Height="461"
                           Canvas.Left="77"
                           Canvas.Top="25.5"
                           RadiusX="25.5"
                           RadiusY="25.5"
                           Stroke="White"
                           StrokeThickness="51" />
                <Rectangle Width="307"
                           Height="85"
                           Canvas.Left="102"
                           Canvas.Top="376"
                           Fill="White" />
                <Ellipse Width="34"
                         Height="34"
                         Canvas.Left="239"
                         Canvas.Top="427"
                         Fill="{StaticResource Blue500}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <!--Button Icons-->
    <ControlTemplate x:Key="PencilIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M362.7 19.3L314.3 67.7 444.3 197.7l48.4-48.4c25-25 25-65.5 0-90.5L453.3 19.3c-25-25-65.5-25-90.5 0zm-71 71L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4L1 481.2C-1.5 489.7 .8 498.8 7 505s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L421.7 220.3 291.7 90.3z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="ExcelIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M48 448V64c0-8.8 7.2-16 16-16H224v80c0 17.7 14.3 32 32 32h80V448c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16zM64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V154.5c0-17-6.7-33.3-18.7-45.3L274.7 18.7C262.7 6.7 246.5 0 229.5 0H64zm90.9 233.3c-8.1-10.5-23.2-12.3-33.7-4.2s-12.3 23.2-4.2 33.7L161.6 320l-44.5 57.3c-8.1 10.5-6.3 25.5 4.2 33.7s25.5 6.3 33.7-4.2L192 359.1l37.1 47.6c8.1 10.5 23.2 12.3 33.7 4.2s12.3-23.2 4.2-33.7L222.4 320l44.5-57.3c8.1-10.5 6.3-25.5-4.2-33.7s-25.5-6.3-33.7 4.2L192 280.9l-37.1-47.6z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="TrashIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M167.2 49.7L160 64H64C46.3 64 32 78.3 32 96S46.3 128 64 128H448c17.7 0 32-14.3 32-32s-14.3-32-32-32H352l-7.2-14.3C339.4 38.8 328.3 32 316.2 32H195.8c-12.1 0-23.2 6.8-28.6 17.7zM448 160H64L85.2 499c1.6 25.3 22.6 45 47.9 45H378.9c25.3 0 46.3-19.7 47.9-45L448 160z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="CloudUploadIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M115.2 384C51.6 384 0 332.4 0 268.8c0-50.2 32.2-93 76.9-108.7c-.1-2.2-.2-4.3-.2-6.5c0-70.7 57.3-128 128-128c47.4 0 88.8 25.8 110.9 64.2C327.9 81.6 342.6 77.6 358.4 77.6c42.4 0 76.8 34.4 76.8 76.8c0 9.8-1.8 19-5.1 27.7C476.8 190.7 512 232.1 512 281.6c0 56.6-45.9 102.4-102.4 102.4H115.2zm63.2-173.6c-7.5 7.5-7.5 19.7 0 27.1s19.7 7.5 27.1 0l31.2-31.2V313.6c0 10.6 8.6 19.2 19.2 19.2s19.2-8.6 19.2-19.2V206.3l31.2 31.2c7.5 7.5 19.7 7.5 27.1 0s7.5-19.7 0-27.1l-64-64c-7.5-7.5-19.7-7.5-27.1 0l-64 64z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="LoadToProjectIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M256 32c-17.7 0-32 14.3-32 32V306.7l-73.4-73.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l128 128c12.5 12.5 32.8 12.5 45.3 0l128-128c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L288 306.7V64c0-17.7-14.3-32-32-32zM64 64c-35.3 0-64 28.7-64 64V448c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V128c0-35.3-28.7-64-64-64H64zm32 64H416V448H96V128z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="CopyFileIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M384 336H192c-8.8 0-16-7.2-16-16V64c0-8.8 7.2-16 16-16l140.1 0L400 115.9V320c0 8.8-7.2 16-16 16zM192 384H384c35.3 0 64-28.7 64-64V115.9c0-12.7-5.1-24.9-14.1-33.9L366.1 14.1c-9-9-21.2-14.1-33.9-14.1H192c-35.3 0-64 28.7-64 64V320c0 35.3 28.7 64 64 64zM64 128c-35.3 0-64 28.7-64 64V448c0 35.3 28.7 64 64 64H256c35.3 0 64-28.7 64-64V416H272v32c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16V192c0-8.8 7.2-16 16-16H96V128H64z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="CircleCheckIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416zm0 464A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-111 111-47-47c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0L369 209z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="CircleUncheckIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416zm0 464A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM175 175c-9.4 9.4-9.4 24.6 0 33.9l47 47-47 47c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l47-47 47 47c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-47-47 47-47c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-47 47-47-47c-9.4-9.4-24.6-9.4-33.9 0z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>



    <ControlTemplate x:Key="DownloadIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M288 32c0-17.7-14.3-32-32-32s-32 14.3-32 32V274.7l-73.4-73.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l128 128c12.5 12.5 32.8 12.5 45.3 0l128-128c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L288 274.7V32zM64 352c-35.3 0-64 28.7-64 64v32c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V416c0-35.3-28.7-64-64-64H346.5l-45.3 45.3c-25 25-65.5 25-90.5 0L165.5 352H64zm368 56a24 24 0 1 1 0 48 24 24 0 1 1 0-48z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="CodeForkIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M112 136a24 24 0 1 0 0-48 24 24 0 1 0 0 48zm80-24c0 32.8-19.7 61-48 73.3V224c0 17.7 14.3 32 32 32H336c17.7 0 32-14.3 32-32V185.3C339.7 173 320 144.8 320 112c0-44.2 35.8-80 80-80s80 35.8 80 80c0 32.8-19.7 61-48 73.3V224c0 53-43 96-96 96H288v70.7c28.3 12.3 48 40.5 48 73.3c0 44.2-35.8 80-80 80s-80-35.8-80-80c0-32.8 19.7-61 48-73.3V320H176c-53 0-96-43-96-96V185.3C51.7 173 32 144.8 32 112C32 67.8 67.8 32 112 32s80 35.8 80 80zm208 24a24 24 0 1 0 0-48 24 24 0 1 0 0 48zM280 464a24 24 0 1 0 -48 0 24 24 0 1 0 48 0z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="CircleInfoIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336h24V272H216c-13.3 0-24-10.7-24-24s10.7-24 24-24h48c13.3 0 24 10.7 24 24v88h8c13.3 0 24 10.7 24 24s-10.7 24-24 24H216c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-208a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="TriangleInfoIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M256 32c14.2 0 27.3 7.5 34.5 19.8l216 368c7.3 12.4 7.3 27.7 .2 40.1S486.3 480 472 480H40c-14.3 0-27.6-7.7-34.7-20.1s-7-27.8 .2-40.1l216-368C228.7 39.5 241.8 32 256 32zm0 128c-13.3 0-24 10.7-24 24V296c0 13.3 10.7 24 24 24s24-10.7 24-24V184c0-13.3-10.7-24-24-24zm32 224a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="SettingsIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2-17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336a80 80 0 1 0 0-160 80 80 0 1 0 0 160z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="DocumentIcon">
        <Viewbox Width="20"
                 Height="20">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M51 85C51 66.3 66.3 51 85 51H427C445.7 51 461 66.3 461 85V427C461 445.7 445.7 461 427 461H85C66.3 461 51 445.7 51 427V85ZM85 85V427H427V85H85ZM136 171H376V136H136V171ZM136 239H376V204H136V239ZM136 307H307V272H136V307ZM136 375H256V340H136V375Z"
                      Fill="White" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="DetailsIcon">
        <Viewbox Width="20"
                 Height="20">
            <Canvas Width="512"
                    Height="512">
                <!-- Connections -->
                <Line X1="96" Y1="96" X2="416" Y2="96" Stroke="White" StrokeThickness="32" 
                      StrokeStartLineCap="Round" StrokeEndLineCap="Round"/>
                <Line X1="96" Y1="96" X2="256" Y2="416" Stroke="White" StrokeThickness="32" 
                      StrokeStartLineCap="Round" StrokeEndLineCap="Round"/>
                <Line X1="416" Y1="96" X2="256" Y2="416" Stroke="White" StrokeThickness="32" 
                      StrokeStartLineCap="Round" StrokeEndLineCap="Round"/>

                <!-- Central building -->
                <Rectangle Width="192" Height="224" Canvas.Left="160" Canvas.Top="160" Stroke="White" StrokeThickness="32" StrokeLineJoin="Round" Fill="Transparent"/>
                <!-- Roof -->
                <Polyline Points="160,160 256,48 352,160" Stroke="White" StrokeThickness="32" StrokeLineJoin="Round" 
                          StrokeStartLineCap="Round" StrokeEndLineCap="Round" Fill="Transparent"/>
                <!-- Door -->
                <Rectangle Width="64" Height="96" Canvas.Left="224" Canvas.Top="288" Fill="White"/>
                <!-- Window -->
                <Rectangle Width="64" Height="64" Canvas.Left="192" Canvas.Top="192" Fill="White"/>

                <!-- Nodes -->
                <Ellipse Width="96" Height="96" Canvas.Left="48"  Canvas.Top="48"  Fill="White"/>
                <Ellipse Width="96" Height="96" Canvas.Left="368" Canvas.Top="48"  Fill="White"/>
                <Ellipse Width="96" Height="96" Canvas.Left="208" Canvas.Top="368" Fill="White"/>
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="RevitIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M64 64C28.7 64 0 92.7 0 128V384c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V128c0-35.3-28.7-64-64-64H64zm88 64v64H64V128h88zM64 224h88v64H64V224zm0 128h88v64H64V352zm224 64V224c0-17.7 14.3-32 32-32h96c17.7 0 32 14.3 32 32V416H288z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>


    <ControlTemplate x:Key="MergeIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M112 136a24 24 0 1 0 0-48 24 24 0 1 0 0 48zm80-24c0 32.8-19.7 61-48 73.3V224c0 17.7 14.3 32 32 32H336c17.7 0 32-14.3 32-32V185.3C339.7 173 320 144.8 320 112c0-44.2 35.8-80 80-80s80 35.8 80 80c0 32.8-19.7 61-48 73.3V224c0 53-43 96-96 96H288v70.7c28.3 12.3 48 40.5 48 73.3c0 44.2-35.8 80-80 80s-80-35.8-80-80c0-32.8 19.7-61 48-73.3V320H176c-53 0-96-43-96-96V185.3C51.7 173 32 144.8 32 112C32 67.8 67.8 32 112 32s80 35.8 80 80zm208 24a24 24 0 1 0 0-48 24 24 0 1 0 0 48zM280 464a24 24 0 1 0 -48 0 24 24 0 1 0 48 0z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="AddIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M256 80c0-17.7-14.3-32-32-32s-32 14.3-32 32V224H48c-17.7 0-32 14.3-32 32s14.3 32 32 32H192V432c0 17.7 14.3 32 32 32s32-14.3 32-32V288H400c17.7 0 32-14.3 32-32s-14.3-32-32-32H256V80z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <ControlTemplate x:Key="DeleteIcon">
        <Viewbox Width="16"
                 Height="16">
            <Canvas Width="512"
                    Height="512">
                <Path Data="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416zm0 464A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM175 175c-9.4 9.4-9.4 24.6 0 33.9l47 47-47 47c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l47-47 47 47c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-47-47 47-47c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-47 47-47-47c-9.4-9.4-24.6-9.4-33.9 0z"
                      Fill="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>
</ResourceDictionary>