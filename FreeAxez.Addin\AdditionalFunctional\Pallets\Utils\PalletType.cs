﻿namespace FreeAxez.Addin.AdditionalFunctional.Pallets.Utils
{
    /// <summary>
    /// Specifies the pallet type. 
    /// 
    /// TODO: According to the task, only 4 types of pallets BaseUnit, ChannelPlate, CornerPlate, HalfBaseUnit are placed. 
    /// The customer did not clarify how to deal with all types and did not comment on the fact that different types of 
    /// pallets can accommodate a different number of units. In order to be safe, we specify the type of pallet in the instance comment. 
    /// 
    /// Thus, if there are Reinforced Channel Plate units on the plan, an instance with the Channel Plate type will be placed, 
    /// but the comment will say ReinforcedChannelPlate.
    /// </summary>
    public enum PalletType
    {
        Undefined,
        BaseUnit,
        ChannelPlate,
        CornerPlate,
        HalfBaseUnit,
        ReinforcedCornerPlate,
        ReinforcedChannelPlate,
        HighCapacityBaseUnit,
        HighCapacityHalfBaseUnit,
    }
}
