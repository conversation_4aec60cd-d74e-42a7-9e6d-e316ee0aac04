﻿using System;
using System.Globalization;
using System.Windows.Data;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Views
{
    public class IsNullOrEmptyConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return String.IsNullOrEmpty(value?.ToString());
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
