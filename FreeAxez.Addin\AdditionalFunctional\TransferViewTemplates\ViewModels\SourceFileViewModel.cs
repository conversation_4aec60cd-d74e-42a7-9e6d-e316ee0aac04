﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Windows.Data;
using System.Windows.Input;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Models;
using FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using Microsoft.Win32;
using Settings = FreeAxez.Addin.Properties.Settings;
using TransferViewTemplatesRevitManager =
    FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Utils.TransferViewTemplatesRevitManager;

namespace FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.ViewModels;

public class SourceFileViewModel : BaseViewModel
{
    private ObservableCollection<RevitViewTemplate> _allRevitViewTemplates;
    private bool _deleteCurrentViewTemplate;
    private bool _includeLinkedDocuments;

    private bool _isAnyTemplateChecked;
    private ObservableCollection<RevitViewTemplate> _revitViewTemplates;
    private bool _saveCurrentViewTemplateAsOld;

    private string _searchText = string.Empty;
    private string _selectedDiscipline;
    private string _selectedDocumentName;
    private string _selectedViewType;
    private string _sourceFilePath;


    public SourceFileViewModel()
    {
        RevitViewTemplates = new ObservableCollection<RevitViewTemplate>();

        DeleteCurrentViewTemplate = Settings.Default.TransferViewTemplatesDeleteTemplates;
        SaveCurrentViewTemplateAsOld = !DeleteCurrentViewTemplate;

        SourceFilePath = Settings.Default.TransferViewTemplatesSourceFilePath;
        ResetFiltersCommand = new RelayCommand(_ => ResetFilters());
        LoadRevitViewTemplates();
    }

    public ObservableCollection<string> Disciplines { get; private set; }
    public ObservableCollection<string> ViewTypes { get; private set; }
    public ObservableCollection<string> DocumentNames { get; private set; }

    public ICommand ResetFiltersCommand { get; private set; }

    public bool IncludeLinkedDocuments
    {
        get => _includeLinkedDocuments;
        set
        {
            _includeLinkedDocuments = value;
            OnPropertyChanged();
            LoadRevitViewTemplates();
        }
    }

    public bool IsAnyTemplateChecked
    {
        get => _isAnyTemplateChecked;
        set
        {
            _isAnyTemplateChecked = value;
            OnPropertyChanged();
        }
    }

    public string SearchText
    {
        get => _searchText;
        set
        {
            _searchText = value;
            OnPropertyChanged();
            Filter();
        }
    }

    public string SelectedDiscipline
    {
        get => _selectedDiscipline;
        set
        {
            _selectedDiscipline = value;
            OnPropertyChanged();
            Filter();
        }
    }

    public string SelectedViewType
    {
        get => _selectedViewType;
        set
        {
            _selectedViewType = value;
            OnPropertyChanged();
            Filter();
        }
    }

    public string SelectedDocumentName
    {
        get => _selectedDocumentName;
        set
        {
            _selectedDocumentName = value;
            OnPropertyChanged();
            Filter();
        }
    }

    public bool SaveCurrentViewTemplateAsOld
    {
        get => _saveCurrentViewTemplateAsOld;
        set
        {
            _saveCurrentViewTemplateAsOld = value;
            OnPropertyChanged();
        }
    }

    public bool DeleteCurrentViewTemplate
    {
        get => _deleteCurrentViewTemplate;
        set
        {
            _deleteCurrentViewTemplate = value;
            OnPropertyChanged();
        }
    }

    public string SourceFilePath
    {
        get => _sourceFilePath;
        set
        {
            _sourceFilePath = value;
            OnPropertyChanged();

            LoadRevitViewTemplates();
        }
    }

    public ObservableCollection<RevitViewTemplate> RevitViewTemplates
    {
        get => _revitViewTemplates;
        set
        {
            _revitViewTemplates = value;
            OnPropertyChanged();
        }
    }

    private void UpdateIsAnyTemplateChecked()
    {
        IsAnyTemplateChecked = RevitViewTemplates.Any(template => template.IsChecked);
    }

    private void Item_PropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == "IsChecked") UpdateIsAnyTemplateChecked();
    }

    private void Filter()
    {
        var view = CollectionViewSource.GetDefaultView(RevitViewTemplates);
        view.Filter = item =>
        {
            var revitViewTemplate = item as RevitViewTemplate;
            if (revitViewTemplate == null) return false;

            return RevitViewTemplateFilter.FilterRevitViewTemplate(revitViewTemplate, SearchText, SelectedDiscipline,
                SelectedViewType, SelectedDocumentName);
        };

        view.Refresh();
    }

    private void LoadFilterLookupValues()
    {
        var disciplines = _revitViewTemplates
            .Select(t => t.Discipline.ToString())
            .Where(d => !string.IsNullOrEmpty(d))
            .Distinct()
            .OrderBy(x => x)
            .ToList();

        var viewTypes = _revitViewTemplates
            .Select(t => t.ViewType.ToString())
            .Distinct()
            .OrderBy(x => x)
            .ToList();

        var documentNames = _revitViewTemplates
            .Where(t => t.RevitDocument != null)
            .Select(t => t.RevitDocument.Title)
            .Distinct()
            .OrderBy(x => x)
            .ToList();

        disciplines.Insert(0, "");
        viewTypes.Insert(0, "");
        documentNames.Insert(0, "");

        Disciplines = new ObservableCollection<string>(disciplines);
        ViewTypes = new ObservableCollection<string>(viewTypes);
        DocumentNames = new ObservableCollection<string>(documentNames);

        OnPropertyChanged(nameof(Disciplines));
        OnPropertyChanged(nameof(ViewTypes));
        OnPropertyChanged(nameof(DocumentNames));
    }

    public void ResetFilters()
    {
        SelectedDiscipline = "";
        SelectedViewType = "";
        SelectedDocumentName = "";
        SearchText = "";

        Filter();
    }

    public void OnSelectSourceFileCommandExecute(object p)
    {
        var openFileDialog = new OpenFileDialog();
        openFileDialog.Filter = "Revit Files (*.rvt;*.rte)|*.rvt;*.rte|All Files (*.*)|*.*";

        if (openFileDialog.ShowDialog() == true)
            try
            {
                SourceFilePath = openFileDialog.FileName;
                Settings.Default.TransferViewTemplatesSourceFilePath = SourceFilePath;
            }
            catch
            {
                InfoDialog.ShowDialog("Warning", "The source Revit file must be made in the same version as current.");
                SourceFilePath = null;
            }
    }

    public void LoadRevitViewTemplates()
    {
        if (string.IsNullOrWhiteSpace(SourceFilePath))
            return;

        if (!File.Exists(SourceFilePath))
        {
            Settings.Default.TransferViewTemplatesSourceFilePath = null;
            Settings.Default.Save();
            return;
        }

        var selectedExtension = Path.GetExtension(SourceFilePath);

        if (selectedExtension == ".rvt" || selectedExtension == ".rte")
        {
            TransferViewTemplatesRevitManager.SourceDocument =
                TransferViewTemplatesRevitManager.Application.OpenDocumentFile(SourceFilePath);

            _allRevitViewTemplates = new ObservableCollection<RevitViewTemplate>(
                new FilteredElementCollector(TransferViewTemplatesRevitManager.SourceDocument)
                    .OfClass(typeof(View))
                    .Cast<View>()
                    .Where(view => view.IsTemplate)
                    .Select(v => new RevitViewTemplate
                    {
                        View = v,
                        Name = v.Title,
                        Id = v.Id,
                        Discipline = v.Discipline,
                        ViewType = v.ViewType,
                        RevitDocument = TransferViewTemplatesRevitManager.SourceDocument
                    })
                    .OrderBy(v => v.ViewType)
                    .ToList());

            var nestedTemplates = new ObservableCollection<RevitViewTemplate>();

            if (IncludeLinkedDocuments)
            {
                var linkInstances = new FilteredElementCollector(TransferViewTemplatesRevitManager.SourceDocument)
                    .OfClass(typeof(RevitLinkInstance))
                    .Cast<RevitLinkInstance>()
                    .ToList();


                foreach (var linkInstance in linkInstances)
                {
                    var linkDocument = linkInstance.GetLinkDocument();
                    if (linkDocument != null)
                    {
                        var linkViewTemplates = new FilteredElementCollector(linkDocument)
                            .OfClass(typeof(View))
                            .Cast<View>()
                            .Where(view => view.IsTemplate)
                            .Select(v => new RevitViewTemplate
                            {
                                View = v,
                                Name = v.Title,
                                Id = v.Id,
                                Discipline = v.Discipline,
                                ViewType = v.ViewType,
                                RevitDocument = linkDocument
                            });

                        foreach (var template in linkViewTemplates) _allRevitViewTemplates.Add(template);
                    }
                }
            }

            foreach (var item in _allRevitViewTemplates) item.PropertyChanged += Item_PropertyChanged;

            RevitViewTemplates = _allRevitViewTemplates;
            LoadFilterLookupValues();
        }
        else
        {
            InfoDialog.ShowDialog("Warning", "Please select a valid .rvt file.");
        }
    }
}