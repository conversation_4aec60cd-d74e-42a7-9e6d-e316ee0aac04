﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    internal class BaseUnitHeavyDuty : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
                "Base_Unit-Heavy_Duty"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public BaseUnitHeavyDuty(Element element) : base(element)
        {
        }

        public static List<BaseUnitHeavyDuty> Collect()
        {

            return FamilyCollector.Instances.Select(g => new BaseUnitHeavyDuty(g)).ToList();
        }
    }
}
