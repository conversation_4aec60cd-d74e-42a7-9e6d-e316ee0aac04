﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Models
{
    public class RevitViewTemplate : BaseViewModel
    {
        private bool _isChecked;
        public bool IsChecked
        {
            get => _isChecked;
            set
            {
                _isChecked = value;
                OnPropertyChanged();
            }
        }

        public string Name { get; set; }
        public ViewDiscipline Discipline { get; set; }
        public ViewType ViewType { get; set; }
        public ElementId Id { get; set; }
        public Element View { get; set; }
        public Document RevitDocument { get; set; }
    }
}