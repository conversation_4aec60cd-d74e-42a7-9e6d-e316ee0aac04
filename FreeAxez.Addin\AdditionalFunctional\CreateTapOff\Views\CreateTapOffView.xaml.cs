﻿using FreeAxez.Addin.AdditionalFunctional.CreateTapOff.ViewModels;
using System.Windows;
using System.Windows.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.CreateTapOff.Views
{
    public partial class CreateTapOffView : Window
    {
        public CreateTapOffView()
        {
            InitializeComponent();
        }

        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            foreach (LevelViewModel viewModel in dataGrid.SelectedItems)
            {
                viewModel.IsCheck = (bool)(sender as CheckBox).IsChecked;
            }
        }
    }
}
