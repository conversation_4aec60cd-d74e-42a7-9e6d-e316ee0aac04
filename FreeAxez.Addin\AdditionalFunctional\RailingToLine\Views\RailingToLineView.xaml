﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.RailingToLine.Views.RailingToLineView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.RailingToLine.Views"
        xmlns:vm="clr-namespace:FreeAxez.Addin.AdditionalFunctional.RailingToLine.ViewModels"
        SizeToContent="WidthAndHeight"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterScreen"
        Title="Railings To Lines">
    <Window.DataContext>
        <vm:RailingToLineViewModel/>
    </Window.DataContext>
    <Grid Margin="5">
        <StackPanel>
            <Label Content="Line Style:" />
            <ComboBox ItemsSource="{Binding LineStyles}" SelectedItem="{Binding SelectedLineStyle}" Width="350">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Name}"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
            <Button Margin="0,20,0,0" Padding="5" Content="Convert To Lines" Height="30" Command="{Binding ConvertCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
        </StackPanel>
    </Grid>
</Window>
