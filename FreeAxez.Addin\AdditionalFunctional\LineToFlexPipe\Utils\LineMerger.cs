﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Exceptions;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.LineToFlexPipe.Utils
{
    public static class LineMerger
    {
        private const double PointDistanceTolerance = 0.01;

        public static List<List<Curve>> GetMergedCurves(List<CurveElement> curves)
        {
            var curveGroups = GroupCurves(curves);
            var mergedCurveGroups = MergeGroupsCurves(curveGroups);
            return mergedCurveGroups;
        }

        private static List<List<CurveElement>> GroupCurves(List<CurveElement> allCurves)
        {
            var output = new List<List<CurveElement>>();
            var usedLines = new List<CurveElement>();

            foreach (CurveElement currentCurve in allCurves)
            {
                if (usedLines.Contains(currentCurve)) continue;

                var group = new List<CurveElement>() { currentCurve };
                usedLines.Add(currentCurve);

                var linesFromSide = CollectAttachedLinesRecursively(
                    currentCurve.GeometryCurve.GetEndPoint(0), new List<CurveElement>(), ref usedLines, allCurves);
                group.AddRange(linesFromSide);

                linesFromSide = CollectAttachedLinesRecursively(
                    currentCurve.GeometryCurve.GetEndPoint(1), new List<CurveElement>(), ref usedLines, allCurves);
                group.AddRange(linesFromSide);

                output.Add(group);
            }

            return output;
        }

        private static List<List<Curve>> MergeGroupsCurves(List<List<CurveElement>> curveElementGroups)
        {
            var output = new List<List<Curve>>();

            foreach (var curveElementGroup in curveElementGroups)
            {
                var curveGroups = curveElementGroup.Select(q => q.GeometryCurve).ToList();
                var alignedCurveGroups = AlignCurvesDirection(curveGroups);
                output.Add(alignedCurveGroups);
            }

            return output;
        }

        private static List<CurveElement> CollectAttachedLinesRecursively(
            XYZ attachmentPoint, 
            List<CurveElement> connectedLines,
            ref List<CurveElement> usedLines,
            List<CurveElement> allCurves)
        {
            foreach (CurveElement curve in allCurves)
            {
                if (usedLines.Contains(curve) || curve.GeometryCurve.Distance(attachmentPoint) > PointDistanceTolerance) continue;

                connectedLines.Add(curve);
                usedLines.Add(curve);

                var nextAttachmentPoint = curve.GeometryCurve.GetEndPoint(0);
                if (nextAttachmentPoint.DistanceTo(attachmentPoint) < PointDistanceTolerance)
                {
                    nextAttachmentPoint = curve.GeometryCurve.GetEndPoint(1);
                }

                return CollectAttachedLinesRecursively(nextAttachmentPoint, connectedLines, ref usedLines, allCurves);
            }

            return connectedLines;
        }

        private static List<Curve> AlignCurvesDirection(List<Curve> curves)
        {
            var output = new List<Curve>();

            var startPoint = GetStartPoint(curves);
            var usedCurves = new List<Curve>();
            while (output.Count < curves.Count)
            {
                var nextCurve = curves.First(c => !usedCurves.Contains(c) 
                                               && c.Distance(startPoint) < PointDistanceTolerance);
                usedCurves.Add(nextCurve);

                var nextPoint = nextCurve.GetEndPoint(0);
                if (nextPoint.DistanceTo(startPoint) < PointDistanceTolerance)
                {
                    nextPoint = nextCurve.GetEndPoint(1);
                }

                var newCurve = Line.CreateBound(startPoint, nextPoint);
                output.Add(newCurve);

                startPoint = nextPoint;
            }

            return output;
        }

        private static XYZ GetStartPoint(List<Curve> curves)
        {
            var points = curves.Select(q => q.GetEndPoint(0)).ToList();
            points.AddRange(curves.Select(q => q.GetEndPoint(1)).ToList());

            var startPoint = points.FirstOrDefault(
                p1 => points.Count(p2 => p2.DistanceTo(p1) < PointDistanceTolerance) == 1);

            if (startPoint == null)
            {
                throw new FreeAxezWorkflowException(
                    $"Cannot find start point for curve loop that contains {curves.Count} curves.");
            }

            return startPoint;
        }
    }
}
