﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.CreateTapOff.Views;
using FreeAxez.Addin.Infrastructure;
using System.Windows.Interop;

namespace FreeAxez.Addin.AdditionalFunctional.CreateTapOff
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class CreateTapOffCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var createTapOffView = new CreateTapOffView();
            var handler = new WindowInteropHelper(createTapOffView);
            handler.Owner = RevitManager.UIApplication.MainWindowHandle;
            createTapOffView.ShowDialog();

            return Result.Succeeded;
        }
    }
}
