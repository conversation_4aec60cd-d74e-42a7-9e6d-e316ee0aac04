﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.Ramp.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB.Structure;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.Ramp.Models
{
    public class RampComponentCreator
    {
        private const double Corner45WidthSlope12 = 0.**********;
        private const double Corner45WidthSlope20 = 0.**********;
        private readonly RampComponentProvider _rampComponentProvider;

        public RampComponentCreator(RampComponentProvider rampComponentProvider)
        {
            _rampComponentProvider = rampComponentProvider;
        }

        public void Create(List<RampLine> rampLines)
        {
            foreach (var rampLine in rampLines)
            {
                if (rampLine.IsLeftSideSlope && rampLine.AngleTypeLeft == RampAngleType.None)
                {
                    var leftSideSymbol = _rampComponentProvider.GetSideSymbol(isLeftSideSloped: true);

                    rampLine.Locations.Remove(rampLine.Locations.First());

                    var leftSideInstance = RevitManager.Document
                        .Create
                        .NewFamilyInstance(rampLine.Locations.First(),
                                           leftSideSymbol,
                                           RevitManager.Document.ActiveView.GenLevel,
                                           StructuralType.NonStructural);

                    Rotate(leftSideInstance, rampLine.RampComponentDirection, rampLine.Locations.First(), 180d);

                    rampLine.Locations.Remove(rampLine.Locations.First());
                }

                if (rampLine.IsLeftSideSlope == false
                    && rampLine.AngleTypeLeft == RampAngleType.External
                    && Math.Round(rampLine.AngleLeft, 2) == Math.Round(ProjectUnitsConverter.ToRadians(90d), 2))
                {
                    var cornerSymbol = _rampComponentProvider.GetCornerSymbol(
                        Math.Round(rampLine.AngleLeft, 2), rampLine.AngleTypeLeft);

                    var cornerInstance = RevitManager.Document
                        .Create
                        .NewFamilyInstance(rampLine.Locations.First(),
                                           cornerSymbol,
                                           RevitManager.Document.ActiveView.GenLevel,
                                           StructuralType.NonStructural);

                    Rotate(cornerInstance, rampLine.RampComponentDirection, rampLine.Locations.First(), 180d);

                    var direction = -rampLine.Direction * _rampComponentProvider.GetCornerWidth(
                        Math.Round(rampLine.AngleLeft, 2), rampLine.AngleTypeLeft) / 2;

                    cornerInstance.Location.Move(direction);

                    rampLine.Locations.Remove(rampLine.Locations.First());
                }

                if (rampLine.IsLeftSideSlope && (rampLine.AngleTypeLeft == RampAngleType.Internal
                    || rampLine.AngleTypeLeft == RampAngleType.External)
                    && Math.Round(rampLine.AngleLeft, 2) == Math.Round(ProjectUnitsConverter.ToRadians(135d), 2))
                {
                    var cornerSymbol = _rampComponentProvider.GetCornerSymbol(
                        Math.Round(rampLine.AngleLeft, 2), rampLine.AngleTypeLeft);

                    var cornerInstance = RevitManager.Document
                        .Create
                        .NewFamilyInstance(rampLine.Locations.First(),
                                           cornerSymbol,
                                           RevitManager.Document.ActiveView.GenLevel,
                                           StructuralType.NonStructural);

                    if (rampLine.AngleTypeLeft == RampAngleType.Internal)
                    {
                        Rotate(cornerInstance, rampLine.RampComponentDirection, rampLine.Locations.First(), 225);
                    }
                    else
                    {
                        Rotate(cornerInstance, rampLine.RampComponentDirection, rampLine.Locations.First(), 180d);

                        cornerInstance.Location.Rotate(Line.CreateBound(
                            rampLine.Locations.First(), rampLine.Locations.First() + XYZ.BasisZ),
                            ProjectUnitsConverter.ToRadians(180d));

                        var direction = rampLine.RampComponentDirection * cornerSymbol.LookupParameter("C Length").AsDouble();

                        double smallWidth = _rampComponentProvider.RampSlope == 12 ? Corner45WidthSlope12 : Corner45WidthSlope20;

                        cornerInstance.Location.Move(direction);
                        cornerInstance.Location.Move(-rampLine.Direction *
                            (cornerSymbol.LookupParameter("C Length/2").AsDouble() - smallWidth));
                    }

                    rampLine.Locations.Remove(rampLine.Locations.First());
                }

                if (rampLine.IsRightSideSlope && rampLine.AngleTypeRight == RampAngleType.None)
                {
                    var rightSideSymbol = _rampComponentProvider.GetSideSymbol(isLeftSideSloped: false);

                    var rightSideInstance = RevitManager.Document
                        .Create
                        .NewFamilyInstance(rampLine.Locations.Last(),
                                           rightSideSymbol,
                                           RevitManager.Document.ActiveView.GenLevel,
                                           StructuralType.NonStructural);

                    Rotate(rightSideInstance, rampLine.RampComponentDirection, rampLine.Locations.Last(), 180d);

                    rampLine.Locations.Remove(rampLine.Locations.Last());
                }

                if (rampLine.IsRightSideSlope && rampLine.AngleTypeRight == RampAngleType.Internal
                    && Math.Round(rampLine.AngleRight, 2) == Math.Round(ProjectUnitsConverter.ToRadians(90d), 2))
                {
                    var cornerSymbol = _rampComponentProvider.GetCornerSymbol(
                        Math.Round(rampLine.AngleRight, 2), rampLine.AngleTypeRight);

                    var cornerInstance = RevitManager.Document
                        .Create.
                        NewFamilyInstance(rampLine.Locations.Last(),
                                          cornerSymbol,
                                          RevitManager.Document.ActiveView.GenLevel,
                                          StructuralType.NonStructural);

                    Rotate(cornerInstance, rampLine.RampComponentDirection, rampLine.Locations.Last(), 180d);

                    rampLine.Locations.Remove(rampLine.Locations.Last());
                }

                if (rampLine.AngleTypeLeft == RampAngleType.None && !rampLine.IsLeftSideSlope
                    || rampLine.AngleTypeLeft == RampAngleType.Internal
                    && Math.Round(rampLine.AngleLeft, 2) == Math.Round(ProjectUnitsConverter.ToRadians(90d), 2))
                {
                    rampLine.Locations.Remove(rampLine.Locations.First());
                }

                CreateMiddleRampInstances(rampLine);
            }
        }

        private void CreateMiddleRampInstances(RampLine rampLine)
        {
            foreach (var location in rampLine.Locations)
            {
                var familyInstance = RevitManager.Document
                    .Create
                    .NewFamilyInstance(location,
                                       _rampComponentProvider.GetMiddleSymbol(),
                                       RevitManager.Document.ActiveView.GenLevel,
                                       StructuralType.NonStructural);

                Rotate(familyInstance, rampLine.RampComponentDirection, location, 180d);

                if (rampLine.EndMiddleRampComponentWidth > 0 && location == rampLine.Locations.Last())
                {
                    try
                    {
                        familyInstance.LookupParameter("Desired Width Left")
                            .Set(rampLine.EndMiddleRampComponentWidth / 2);

                        familyInstance.LookupParameter("Desired Width Right")
                            .Set(rampLine.EndMiddleRampComponentWidth / 2);
                    }
                    catch (Exception)
                    {
                        throw;
                    }
                }
            }
        }

        private void Rotate(FamilyInstance familyInstance,
                            XYZ direction,
                            XYZ familyInstanceLocation,
                            double additionalAngle = 0d)
        {
            var rotationAngle = direction.X < 0
                    ? direction.AngleTo(XYZ.BasisY)
                    : -direction.AngleTo(XYZ.BasisY);

            familyInstance.Location.Rotate(Line.CreateBound(
                familyInstanceLocation, familyInstanceLocation + XYZ.BasisZ),
                rotationAngle + ProjectUnitsConverter.ToRadians(additionalAngle));
        }
    }
}