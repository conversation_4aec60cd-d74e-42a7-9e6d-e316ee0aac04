﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace FreeAxez.Addin.AdditionalFunctional.ScopeInstructions
{
    public class ScopeInstructionService
    {
        private const string BaseUrl = "https://api-freeaxez.bimsmith.com";
        //private const string BaseUrl = "https://api-freeaxez-uat.bimsmith.com";
        //private const string BaseUrl = "https://localhost:44376";

        private readonly HttpClient _httpClient;

        public ScopeInstructionService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task<IList<ScopeInstruction>> GetAllAsync()
        {
            var requestUri = new Uri($"{BaseUrl}/api/scopeInstructions");

            HttpResponseMessage httpResponseMessage = await _httpClient.GetAsync(requestUri, HttpCompletionOption.ResponseHeadersRead);

            string contentString = await httpResponseMessage.Content.ReadAsStringAsync();

            if (!httpResponseMessage.IsSuccessStatusCode)
            {
                throw new Exception(contentString);
            }

            return JsonConvert.DeserializeObject<IList<ScopeInstruction>>(contentString);
        }
    }
}
