﻿using System;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.Ramp.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;

namespace FreeAxez.Addin.AdditionalFunctional.Ramp
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class RampCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            try
            {
                var rampView = new RampView();

                rampView.ShowDialog();
            }
            catch (Autodesk.Revit.Exceptions.OperationCanceledException)
            {
                InfoDialog.ShowDialog("Error", "No line was selected.");
            }
            catch (NullReferenceException nullReferenceException)
            {
                InfoDialog.ShowDialog("Error", nullReferenceException.Message);
            }
            catch (Exception exception)
            {
                InfoDialog.ShowDialog("Error", exception.Message);

                return Result.Failed;
            }

             return Result.Succeeded;
        }
    }
}