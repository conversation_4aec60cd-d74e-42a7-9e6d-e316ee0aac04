using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Details;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals
{
    public partial class DetailSelectionDialog : UserControl
    {
        public DetailSelectionDialogVm ViewModel => DataContext as DetailSelectionDialogVm;

        public DetailSelectionDialog()
        {
            InitializeComponent();
            LogHelper.Information("DetailSelectionDialog UserControl created");
        }

        private void DataGridRow_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is DataGridRow row && row.DataContext is DetailViewModel viewModel)
            {
                if (e.OriginalSource is DependencyObject source && IsChildOfPreviewBorder(source))
                {
                    return;
                }

                if (e.OriginalSource is CheckBox)
                {
                    return;
                }

                if (viewModel.IsEnabled)
                {
                    viewModel.IsSelected = !viewModel.IsSelected;
                    e.Handled = true;
                }
            }
        }

        private bool IsChildOfPreviewBorder(DependencyObject element)
        {
            while (element != null)
            {
                if (element is Border border && border.Name == "PreviewBorder")
                {
                    return true;
                }
                element = VisualTreeHelper.GetParent(element);
            }
            return false;
        }

        private void PreviewImage_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && border.Tag is DetailViewModel viewModel)
            {
                ViewModel?.CmdShowPreview.Execute(viewModel);
                e.Handled = true;
            }
        }
    }
}
