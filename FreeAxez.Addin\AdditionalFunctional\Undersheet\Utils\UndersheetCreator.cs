﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.Undersheet.Utils
{
    internal class UndersheetCreator
    {
        private readonly Line _startPosition;
        private readonly List<FamilyInstance> _units;
        private readonly FamilySymbol _undersheetSymbol;
        private readonly double _pointTolerance = 0.02; // 8mm
        private readonly double _minimalGap = 1 / 12.0 * 5; // 5"

        public const string LengthParameterName = "Length";


        public UndersheetCreator(Line startPosition, List<FamilyInstance> units, FamilySymbol undersheetSymbol)
        {
            _startPosition = startPosition;
            _units = units;
            _undersheetSymbol = undersheetSymbol;
        }


        public List<FamilyInstance> Create()
        {
            var createdUndersheetFamilies = new List<FamilyInstance>();

            var gridd = new Gridd(_startPosition, _units);
            gridd.CreateGridLines();

            using (var t = new Transaction(RevitManager.Document, "Place Undersheet"))
            {
                t.Start();

                if (!_undersheetSymbol.IsActive) _undersheetSymbol.Activate();

                var locationLines = GetLocationLines(gridd, _units.Select(u => u.Id).ToList());

                foreach (var position in locationLines)
                {
                    var undersheet = RevitManager.Document.Create.NewFamilyInstance(
                        position.Origin, _undersheetSymbol, RevitManager.Document.ActiveView);

                    undersheet.LookupParameter(LengthParameterName)?.Set(position.ApproximateLength);

                    createdUndersheetFamilies.Add(undersheet);
                }

                // Calculate location points
                RevitManager.Document.Regenerate();

                if (!gridd.HorizontalOrientation)
                {
                    foreach (var undersheet in createdUndersheetFamilies)
                    {
                        var origin = undersheet.Location as LocationPoint;
                        var axis = Line.CreateBound(origin.Point, origin.Point.Add(new XYZ(0, 0, 1)));

                        undersheet.Location.Rotate(axis, Math.PI / 2);
                    }
                }

                t.Commit();
            }

            return createdUndersheetFamilies;
        }

        private List<Line> GetLocationLines(Gridd grid, List<ElementId> unitIds)
        {
            var mergedLocationLines = new List<Line>();

            var option = new Options() { View = RevitManager.Document.ActiveView };
            foreach (var gridLine in grid.Lines)
            {
                var lineOutline = GetOutlineForGridLine(gridLine);
                var closestUnits = new FilteredElementCollector(RevitManager.Document, unitIds)
                    .WherePasses(new BoundingBoxIntersectsFilter(lineOutline, _pointTolerance))
                    .ToList();

                if (closestUnits.Count == 0)
                {
                    continue;
                }

                var intersectedUnitOutlines = new List<Outline>();
                foreach (var unit in closestUnits)
                {
                    var unitOutlines = GetOutlinesFromGeometry(option, unit);
                    foreach (var outline in unitOutlines)
                    {
                        if (lineOutline.Intersects(outline, _pointTolerance))
                        {
                            intersectedUnitOutlines.Add(outline);
                        }
                    }
                }

                var locationLines = GetLocationLinesAboveEachOutline(grid, gridLine, intersectedUnitOutlines);
                var mergedLines = MergeLines(locationLines);
                mergedLocationLines.AddRange(mergedLines);
            }

            return mergedLocationLines;
        }

        private Outline GetOutlineForGridLine(Line gridLine)
        {
            var start = gridLine.GetEndPoint(0);
            var end = gridLine.GetEndPoint(1);

            return new Outline(
                new XYZ(start.X, start.Y, start.Z),
                new XYZ(end.X, end.Y, end.Z + 1));
        }

        private IEnumerable<Outline> GetOutlinesFromGeometry(Options option, Element unit)
        {
            var solids = new List<Solid>();

            foreach (var geometryObject in unit.get_Geometry(option))
            {
                if (geometryObject is Solid solid)
                {
                    solids.Add(solid);
                }
                else if (geometryObject is GeometryInstance geometryInstance)
                {
                    foreach (var geometryObject1 in geometryInstance.GetInstanceGeometry())
                    {
                        if (geometryObject1 is Solid solid1)
                        {
                            solids.Add(solid1);
                        }
                    }
                }
            }

            return solids
                .Where(s => s.SurfaceArea > 0)
                .Select(s => new Outline(s.GetBoundingBox().Min.Add(s.GetBoundingBox().Transform.Origin),
                                         s.GetBoundingBox().Max.Add(s.GetBoundingBox().Transform.Origin)));
        }

        private List<Line> GetLocationLinesAboveEachOutline(Gridd grid, Line gridLine, List<Outline> unitOutlines)
        {
            var locationLines = new List<Line>();

            foreach (var outline in unitOutlines)
            {
                var x = gridLine.GetEndPoint(0).X;
                var y = gridLine.GetEndPoint(0).Y;
                var z = gridLine.GetEndPoint(0).Z;

                XYZ startPoint;
                XYZ endPoint;

                if (grid.HorizontalOrientation)
                {
                    startPoint = new XYZ(outline.MinimumPoint.X, y, z);
                    endPoint = new XYZ(outline.MaximumPoint.X, y, z);
                }
                else
                {
                    startPoint = new XYZ(x, outline.MinimumPoint.Y, z);
                    endPoint = new XYZ(x, outline.MaximumPoint.Y, z);
                }

                if (startPoint.IsAlmostEqualTo(endPoint))
                {
                    continue;
                }

                locationLines.Add(Line.CreateBound(startPoint, endPoint));
            }

            return locationLines;
        }

        private List<Line> MergeLines(List<Line> locationLines)
        {
            for (int i = 0; i < locationLines.Count; i++)
            {
                for (int j = i + 1; j < locationLines.Count; j++)
                {
                    if (ShouldMerge(locationLines[i], locationLines[j]))
                    {
                        var mergedLines = MergeTwoLines(locationLines, i, j);
                        return MergeLines(mergedLines);
                    }
                }
            }
            return locationLines;
        }

        private bool ShouldMerge(Line line1, Line line2)
        {
            return line1.Intersect(line2) != SetComparisonResult.Disjoint
                || line1.Distance(line2.GetEndPoint(0)) <= _minimalGap
                || line1.Distance(line2.GetEndPoint(1)) <= _minimalGap;
        }

        private List<Line> MergeTwoLines(List<Line> lines, int index1, int index2)
        {
            var mergedLines = new List<Line>(lines);
            var line1 = mergedLines[index1];
            var line2 = mergedLines[index2];

            mergedLines.RemoveAt(index2); 
            mergedLines.RemoveAt(index1);

            var points = new List<XYZ>
            {
                line1.GetEndPoint(0),
                line1.GetEndPoint(1),
                line2.GetEndPoint(0),
                line2.GetEndPoint(1)
            }
            .OrderBy(p => p.X)
            .ThenBy(p => p.Y)
            .ToList();

            mergedLines.Add(Line.CreateBound(points.First(), points.Last()));

            return mergedLines;
        }
    }
}
