﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllFrames.Utils
{
    public class FramesSelectionFilter : ISelectionFilter
    {
        private List<ElementId> _framesOnView;

        public FramesSelectionFilter(List<FamilyInstance> framesOnView)
        {
            _framesOnView = framesOnView.Select(c => c.Id).ToList();   
        }

        public bool AllowElement(Element elem)
        {
            return _framesOnView.Contains(elem.Id);
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            return false;
        }
    }
}