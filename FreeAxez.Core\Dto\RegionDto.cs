﻿using System;

namespace FreeAxez.Core.Dto
{
    public class RegionDto 
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public int RevitRegionId { get; set; }
        public Guid ProjectId { get; set; }
        public int? LevelId { get; set; }
        public string LayoutJson { get; set; } //information about region lines
        public string SpecialAreasJson { get; set; }
        public Guid OptionId { get; set; }
        public int OrderIndex { get; set; }
        public bool IsCompleted { get; set; }
        public int BaseUnitsCount { get; set; }
        public int HalfBaseUnitsCount { get; set; }
        public int QuarterBaseUnitsCount { get; set; }
        public int ChannelsCount { get; set; }
        public int HalfChannelsCount { get; set; }
        public int CornersCount { get; set; }
        public int CutPieces { get; set; }
        public string ConstructionImage { get; set; }

        public double CutBorderArea { get; set; }
        public double BorderArea { get; set; }
        public double StandardUnitsArea { get; set; }
        public double NonBorderPerc { get; set; }

        public string BuildDataUrl { get; set; }
        public string ImageUrl { get; set; }
        public double MinX { get; set; }
        public double MinY { get; set; }
        public double MaxX { get; set; }
        public double MaxY { get; set; }
        public int StartItem { get; set; }
        public int StartPointNum { get; set; }
        public string GriddType { get; set; }
        public double? Price { get; set; }
        public double? TimeToInstall { get; set; }
        public int TotalBaseUnitsCount { get; set; }

        public RegionDto()
        {
            ChannelsCount = 0;
            BaseUnitsCount = 0;
            HalfBaseUnitsCount = 0;
            QuarterBaseUnitsCount = 0;
            ChannelsCount = 0;
            HalfChannelsCount = 0;
            CornersCount = 0;
            CutPieces = 0;
            NonBorderPerc = 0;
        }
    }
}