﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Views.TaskManagerView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
        xmlns:viewModels="clr-namespace:FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels"
        xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Converters"
        xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Controls"
        xmlns:views="clr-namespace:FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Views"
        mc:Ignorable="d" 
        Title="Smartsheet Task Manager"
        Height="700" Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Closed="Window_Closed">

    <Window.Resources>
        <ResourceDictionary>
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:DateTimeToDateConverter x:Key="DateTimeToDateConverter"/>
            <converters:FullNameToShortNameConverter x:Key="FullNameToShortNameConverter"/>

            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/AdditionalFunctional/SmartsheetTaskManager/Styles/Styles.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/AdditionalFunctional/SmartsheetTaskManager/Styles/LoadingSpinnerStyle.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/AdditionalFunctional/SmartsheetTaskManager/Styles/PlaceHolderTextBoxStyle.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="20"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="25"/>
            <RowDefinition Height="295"/>
            <RowDefinition Height="30"/>
            <RowDefinition Height="210"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="40"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="310"/>
            <ColumnDefinition Width="475"/>
        </Grid.ColumnDefinitions>

        <StackPanel Orientation="Horizontal"
                    Grid.Row="0" Grid.Column="0"
                    VerticalAlignment="Center"
                    Margin="10 3 5 0">
            <TextBlock Text="Project:" FontWeight="Bold"
                       Style="{StaticResource TextBlock}"
                       Margin="0 0 5 0"/>
            <TextBlock Text="{Binding ProjectName}"
                       Style="{StaticResource TextBlock}"
                       ToolTip="{Binding ProjectName}"/>
        </StackPanel>

        <StackPanel Orientation="Horizontal"
                    Grid.Row="1" Grid.Column="0"
                    VerticalAlignment="Center"
                    Margin="10 2.5 0 0">
            <TextBlock Text="Scope:" FontWeight="Bold"
                       Style="{StaticResource TextBlock}"
                       Margin="0 0 5 0"/>
            <TextBlock Text="{Binding Scope}"
                       Style="{StaticResource TextBlock}"
                       ToolTip="{Binding Scope}"/>
            <TextBlock Style="{StaticResource TextBlock}" Visibility="{Binding HasVideoInstruction, Converter={StaticResource BooleanToVisibilityConverter}}" Margin="5 0 0 0">
                <Hyperlink NavigateUri="{Binding ScopeUrl}"
                           RequestNavigate="Hyperlink_RequestNavigate">
                    Instruction
                </Hyperlink>
            </TextBlock>
        </StackPanel>

        <StackPanel Orientation="Horizontal"
                    Grid.Row="2" Grid.Column="0"
                    VerticalAlignment="Center"
                    Margin="10 2.5 0 5">
            <TextBlock Text="Step:" FontWeight="Bold"
                   Style="{StaticResource TextBlock}"
                   Margin="0 0 5 0"/>
            <TextBlock Text="{Binding Step}"
                       Style="{StaticResource TextBlock}"
                       ToolTip="{Binding Step}"/>
        </StackPanel>

        <ListView ItemsSource="{Binding RowColumnDtos}"
                  Grid.Row="3" Grid.Column="0"
                  Grid.RowSpan="3"
                  Margin="10 5 5 5"
                  Style="{StaticResource ListView}"
                  ItemContainerStyle="{StaticResource ListViewItemFocusable}">
            <ListView.View>
                <GridView>
                    <GridViewColumn>
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Title}" Margin="0 2.5 0 2.5"/>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="140">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding CellValue}" Margin="0 2.5 0 2.5"/>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>

        <TextBlock Text="Conversations:"
                   Grid.Row="0" Grid.Column="1"
                   Margin="5 5 10 2.5"
                   VerticalAlignment="Bottom"
                   Style="{StaticResource TextBlock}"/>

        <ListView ItemsSource="{Binding RowComments}"
                  Grid.Row="1" Grid.Column="1" Grid.RowSpan="3"
                  Margin="5 0 10 5"
                  Style="{StaticResource ListView}"
                  ItemContainerStyle="{StaticResource ListViewItemFocusable}">
            <ListView.View>
                <GridView>
                    <GridViewColumn Width="445">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" Margin="0 5 0 0">
                                    <Grid>
                                        <Ellipse Width="40" Height="40"
                                                 Fill="{Binding NameColor}" Stroke="Black"
                                                 StrokeThickness="2"
                                                 Opacity="0.6"
                                                 HorizontalAlignment="Center"
                                                 VerticalAlignment="Top">
                                            <Ellipse.Clip>
                                                <EllipseGeometry RadiusX="15" RadiusY="15" Center="20,20"/>
                                            </Ellipse.Clip>
                                        </Ellipse>
                                        <TextBlock Text="{Binding CreatedBy, Converter={StaticResource FullNameToShortNameConverter}}"
                                                   Margin="0 11 0 0"
                                                   HorizontalAlignment="Center" VerticalAlignment="Top"
                                                   Foreground="White" FontWeight="Bold"/>
                                    </Grid>
                                    <StackPanel Orientation="Vertical" Margin="0 5 0 0">
                                        <TextBlock Text="{Binding CreatedBy}"
                                                   FontWeight="Bold"/>
                                        <TextBlock Text="{Binding Text}" TextWrapping="Wrap" Width="420"
                                                   Margin="0 3 0 3"/>
                                        <TextBlock Text="{Binding CreatedAt,
                                                        Converter={StaticResource DateTimeToDateConverter}}"
                                                   Foreground="Gray"/>
                                    </StackPanel>
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>

        <TextBlock Text="Attachments:"
                   Grid.Row="4" Grid.Column="1"
                   Margin="5 5 10 5"
                   VerticalAlignment="Bottom"
                   Style="{StaticResource TextBlock}"/>

        <ListView ItemsSource="{Binding RowAttachmentDtos}"
                  Grid.Row="5" Grid.Column="1"
                  Margin="5 0 10 5"
                  Style="{StaticResource ListView}"
                  ItemContainerStyle="{StaticResource ListViewItemNotFocusable}">
            <ListView.View>
                <GridView>
                    <GridViewColumn Width="400">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding Name}" Margin="0 0 5 5" Padding="0 3 0 0"/>
                                    <Button Content="&#8677;"
                                            BorderThickness="0"
                                            FontSize="18"
                                            Command="{Binding DataContext.DownloadFileCommand,
                                                              RelativeSource={RelativeSource AncestorType={x:Type ListView}}}"
                                            CommandParameter="{Binding RelativeSource={RelativeSource Mode=Self},
                                                                   Path=DataContext}">
                                        <Button.Template>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="5" Padding="3 0 0 0">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                      VerticalAlignment="Center"
                                                                      RenderTransformOrigin="0.5, 0.5">
                                                        <ContentPresenter.RenderTransform>
                                                            <RotateTransform Angle="90"/>
                                                        </ContentPresenter.RenderTransform>
                                                    </ContentPresenter>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#FFE9F1FB"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Button.Template>
                                        <Button.Style>
                                            <Style TargetType="Button">
                                                <Setter Property="Background" Value="Transparent" />
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>

        <TextBlock Text="{Binding ErrorResponse}"
                   Grid.Column="0"
                   Grid.Row="6" Grid.ColumnSpan="2"
                   Margin="10 0 0 0" Foreground="Red"
                   Style="{StaticResource TextBlock}"/>

        <controls:LoadingSpinner IsExecuting="{Binding IsExecuting}"
                                 Grid.RowSpan="8"
                                 Grid.ColumnSpan="2"/>

        <StackPanel Orientation="Horizontal"
                    Grid.Row="7" Grid.Column="1"
                    HorizontalAlignment="Right"
                    Margin="0 0 10 5">
            <Button Content="{Binding ChangeStatusButtonName}"
                    Command="{Binding ChangeStatusCommand}"
                    Style="{StaticResource CommonButtonStyle}"
                    ToolTip="Complete task"
                    Width="80" Height="25"/>
            <Button Content="Cancel"
                    Margin="10 0 0 0"
                    Command="{Binding CancelCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor,
                                AncestorType={x:Type Window}}}"
                    Style="{StaticResource CommonButtonStyle}"
                    Width="80" Height="25"/>
        </StackPanel>
    </Grid>
</Window>
