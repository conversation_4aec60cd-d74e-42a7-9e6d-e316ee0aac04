﻿using System.IO;
using System.Threading;

namespace FreeAxez.Addin.Infrastructure;

public static class FileOperationHelper
{
    public static bool TryDeleteFileWithRetries(string filePath, int maxRetries, int delayInMilliseconds)
    {
        for (var attempt = 0; attempt < maxRetries; attempt++)
            if (File.Exists(filePath))
                try
                {
                    File.Delete(filePath);
                    return true;
                }
                catch (IOException)
                {
                    Thread.Sleep(delayInMilliseconds);
                }
            else
                return true;

        return false;
    }

    public static string CreateTempFilePath(string originalFilePath)
    {
        string directory = Path.GetDirectoryName(originalFilePath);
        string filename = Path.GetFileNameWithoutExtension(originalFilePath);
        string extension = Path.GetExtension(originalFilePath);
        string newFilename = $"{filename}_temp{extension}";
        return Path.Combine(directory, newFilename);
    }
}