using System;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Api;

/// <summary>
/// DTO for API communication - represents layer data for server requests
/// </summary>
public record LayerDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public uint Color24bit { get; set; } = 0xFFFFFF;
    public short Lineweight01mm { get; set; } = 25;
    public byte TransparencyPct { get; set; } = 0;
    public Guid LinetypeId { get; set; }
    public DateTime UpdatedUtc { get; set; } = DateTime.UtcNow;
    public string LinetypeName { get; set; } = string.Empty;
    public string LinetypeDescription { get; set; } = string.Empty;
}
