﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Enums;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Models;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System.Windows;
using System.Windows.Input;
using System.Windows.Interop;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.ViewModels
{
    public class ElectricalCircuitOptionsViewModel : WindowViewModel
    {
        private static ElectricalCircuitWindow _reportWindow;

        public ElectricalCircuitOptionsViewModel()
        {
            Levels = GetLevelModels();
            CreateCommand = new RelayCommand(OnCreateCommandExecute);
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
        }

        public List<LevelViewModel> Levels { get; set; }

        public ICommand CreateCommand { get; set; }
        private void OnCreateCommandExecute(object p)
        {
            (p as Window).Close();

            var selectedLevels = Levels.Where(l => l.IsCheck).Select(l => l.Level as Level).ToList();
            if (!Levels.Any(level => level.IsCheck))
            {
                MessageWindow.ShowDialog("No level has been selected.", MessageType.Notify);
                return;
            }

            var levelHelper = new LevelHelper(selectedLevels);
            
            var createdPanelInstances = new List<FamilyInstance>();
            using (var t = new Transaction(RevitManager.Document, "Create Panels"))
            {
                t.Start();
                CommonFailuresPreprocessor.SetFailuresPreprocessor(t);

                try
                {
                    var panelCreator = new PanelCreator(levelHelper);
                    createdPanelInstances = panelCreator.CreatePanelIfNotExistOnLevel();
                }
                catch (Exception ex)
                {
                    MessageWindow.ShowDialog(ex.Message, MessageType.Error);
                    LogHelper.Error(ex.Message + ex.StackTrace);
                    return;
                }

                t.Commit();
            }

            var circuitCollector = new CircuitCollector(levelHelper);
            var circuits = circuitCollector.Collect();
            if (circuits.Count == 0)
            {
                MessageWindow.ShowDialog("No elements were found to create an electric circuit.", MessageType.Notify);
                return;
            }

            // Process attached tracks to update level parameters
            var fakeTrackCircuits = CircuitCollector.CollectFakeCircuitsForUnusedTracks(circuits, levelHelper);

            using (var t = new Transaction(RevitManager.Document, "Create Circuits"))
            {
                t.Start();
                CommonFailuresPreprocessor.SetFailuresPreprocessor(t);

                CircuitAssembly.RemoveBoxesFromInvalidElectricalSystems(circuits);

                var panelScheduleCreator = new PanelScheduleCreator();
                foreach (var circuit in circuits)
                {
                    circuit.CreateCircuit();
                    panelScheduleCreator.CreateIfNotExist(circuit);
                }

                // The motivation for creating a separate class that sets all parameters for all electrical circuits
                // is that the floor box refers to many circuits at the same time,
                // and the parameters must be set from all circuits in which this box is involved.
                var parameterSetter = new ParameterSetter(circuits);
                var attachedTracksParameterSetter = new ParameterSetter(fakeTrackCircuits);

                parameterSetter.ClearParameters();
                attachedTracksParameterSetter.ClearParameters();

                RevitManager.Document.Regenerate();

                parameterSetter.SetParameters();
                attachedTracksParameterSetter.SetParameters();

                t.Commit();
            }

            var fbSystems = circuits.Where(c => c.CircuitAssemblyType == CircuitAssemblyType.FloorBox).ToList();
            var trackSystems = circuits.Where(c => c.CircuitAssemblyType == CircuitAssemblyType.Track).ToList();

            var shortReport =
                "Floor Box Systems: Electrical Circuit Creation\n" +
                $"Circuits Assigned - {fbSystems.Count(c => c.CircuitCreationResult == CreationResult.Created)};\n" +
                $"Unchanged - {fbSystems.Count(c => c.CircuitCreationResult == CreationResult.Unchanged)};\n" +
                $"Failed Electrical Circuit Creation - {fbSystems.Count(c => c.CircuitCreationResult == CreationResult.Failed && c.CircuitModelStatus == CircuitModelStatus.Valid)};\n" +
                $"Failed Systems (Geometry Issues) - {fbSystems.Count(c => c.CircuitCreationResult == CreationResult.Failed && c.CircuitModelStatus != CircuitModelStatus.Valid)};\n" +
                $"Panel Created - {createdPanelInstances.Count}.\n" +
                $"\n" +
                $"Floor Box Systems: Geometry Analysis\n" +
                $"Valid - {fbSystems.Count(c => c.CircuitModelStatus == CircuitModelStatus.Valid)};\n" +
                $"Missing Panel - {fbSystems.Count(c => c.CircuitModelStatus == CircuitModelStatus.NoPanel)};\n" +
                $"Missing Track - {fbSystems.Count(c => c.CircuitModelStatus == CircuitModelStatus.NoTrack)};\n" +
                $"Missing Whip - {fbSystems.Count(c => c.CircuitModelStatus == CircuitModelStatus.NoWhip)};\n" +
                $"Missing Floor Box - {fbSystems.Count(c => c.CircuitModelStatus == CircuitModelStatus.NoBox)};\n" +
                $"Isolated Whip - {fbSystems.Count(c => c.CircuitModelStatus == CircuitModelStatus.SingleWhip)};\n" +
                $"Isolated Floor Box - {fbSystems.Count(c => c.CircuitModelStatus == CircuitModelStatus.SingleBox)}.\n" +
                $"\n" +
                $"Track Systems: Geometry Analysis\n" +
                $"Valid - {trackSystems.Count(c => c.CircuitModelStatus == CircuitModelStatus.Valid)};\n" + 
                $"Missing Feed Module (Unpowered) - {trackSystems.Count(c => c.CircuitModelStatus == CircuitModelStatus.NoFeedModule)};\n" +
                $"Isolated Feed Module - {trackSystems.Count(c => c.CircuitModelStatus == CircuitModelStatus.SingleFeedModule)}.";

            MessageWindow.ShowDialog(shortReport, MessageType.Success);

            // Show report window
            if (_reportWindow != null && _reportWindow.IsVisible) _reportWindow.Close();

            var electricalCircuitViewModel = new ElectricalCircuitViewModel(circuits);
            _reportWindow = new ElectricalCircuitWindow(electricalCircuitViewModel);
            var handler = new WindowInteropHelper(_reportWindow);
            handler.Owner = RevitManager.UIApplication.MainWindowHandle;
            _reportWindow.Show();
        }

        public ICommand CancelCommand { get; set; }
        private void OnCancelCommandExecute(object p)
        {
            (p as Window).Close();
        }

        private List<LevelViewModel> GetLevelModels()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Level))
                .OrderBy(l => l.get_Parameter(BuiltInParameter.LEVEL_ELEV).AsDouble())
                .Select(l => new LevelViewModel() { Name = l.Name, Level = l })
                .ToList();
        }
    }
}
