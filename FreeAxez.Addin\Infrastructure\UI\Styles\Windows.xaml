﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="ColorsPalette.xaml" />
        <ResourceDictionary Source="Buttons.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <ControlTemplate x:Key="WindowControl"
                     TargetType="Window">
        <Grid SnapsToDevicePixels="True"
              UseLayoutRounding="True">
            <!-- Drop Shadow -->
            <Border CornerRadius="8"
                    Margin="10">
                <Border.Effect>
                    <DropShadowEffect Color="Black"
                                      Opacity="0.15"
                                      ShadowDepth="8"
                                      BlurRadius="20"
                                      Direction="270"/>
                </Border.Effect>
            </Border>

            <!-- Main Window Border -->
            <Border BorderThickness="1"
                    CornerRadius="8"
                    Background="White"
                    BorderBrush="{StaticResource Gray300}"
                    Margin="10">
                <Grid Margin="15 0 15 15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Grid Grid.Row="0"
                          Height="30">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="30" />
                        </Grid.ColumnDefinitions>
                        <Image Grid.Column="0"
                               Height="20"
                               Source="/FreeAxez.Addin;component/Infrastructure/UI/Images/FA_Logo_Small.png"
                               Stretch="Uniform"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center" />
                        <TextBlock Text="{Binding Title, RelativeSource={RelativeSource AncestorType=Window}}"
                                   Grid.Column="1"
                                   FontSize="13"
                                   FontWeight="SemiBold"
                                   VerticalAlignment="Center"
                                   Margin="8 2 0 0"
                                   Foreground="{StaticResource Gray800}"
                                   TextTrimming="CharacterEllipsis" />
                        <Button Grid.Column="2"
                                Margin="0 0 -20 0"
                                Command="{Binding CancelCommand}"
                                CommandParameter="{Binding RelativeSource={RelativeSource AncestorType={x:Type Window}}}"
                                Style="{StaticResource ButtonCloseSecond}"
                                VerticalAlignment="Center"
                                WindowChrome.IsHitTestVisibleInChrome="True">
                        </Button>
                    </Grid>
                    <Grid Grid.Row="1">
                        <ContentPresenter Margin="0 10 0 0"/>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </ControlTemplate>

    <Style TargetType="Window"
           x:Key="ThemeWindow">
        <Setter Property="Template"
                Value="{StaticResource WindowControl}" />
        <Setter Property="WindowStyle"
                Value="None" />
        <Setter Property="AllowsTransparency"
                Value="True" />
        <Setter Property="ShowInTaskbar"
                Value="True" />
        <Setter Property="WindowChrome.WindowChrome">
            <Setter.Value>
                <WindowChrome NonClientFrameEdges="None"
                              GlassFrameThickness="0"
                              ResizeBorderThickness="7"
                              CaptionHeight="32"
                              CornerRadius="0" />
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="PageStyle"
           TargetType="{x:Type UserControl}">
        <Setter Property="RenderTransform">
            <Setter.Value>
                <TranslateTransform X="0"
                                    Y="50" />
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="Visibility"
                     Value="Collapsed">
                <Setter Property="Opacity"
                        Value="0" />
            </Trigger>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                         Duration="0:0:1"
                                         From="0"
                                         To="1" />
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                                         Duration="0:0:0.8"
                                         From="50"
                                         To="0"
                                         DecelerationRatio="0.6" />
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="DialogStyle"
           TargetType="{x:Type Window}">
        <Setter Property="RenderTransform">
            <Setter.Value>
                <TranslateTransform X="0"
                                    Y="50" />
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="Visibility"
                     Value="Collapsed">
                <Setter Property="Opacity"
                        Value="0" />
            </Trigger>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                         Duration="0:0:1"
                                         From="0"
                                         To="1" />
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                                         Duration="0:0:0.8"
                                         From="50"
                                         To="0"
                                         DecelerationRatio="0.6" />
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>