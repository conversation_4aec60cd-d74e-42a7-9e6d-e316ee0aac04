﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;
using FreeAxez.Addin.Infrastructure;
using Microsoft.Win32;
using Settings = FreeAxez.Addin.Properties.Settings;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals
{
    public class AdminFamilyAddVm : AdminFamilyBaseVm
    {
        public AdminFamilyAddVm()
        {
            ChooseFileCommand = new RelayCommand(ExecuteChooseFile);
            FileDropCommand = new RelayCommand(DragOver);
            ApplyCommand = new RelayCommand(ExecuteApply, CanExecuteApply);
        }

        public ICommand ChooseFileCommand { get; }
        public ICommand FileDropCommand { get; }
        public ICommand ApplyCommand { get; }

        protected override void ExecuteChooseFile(object parameter)
        {
            var openFileDialog = new OpenFileDialog
            {
                Multiselect = true,
                Filter = "Revit Families (*.rfa)|*.rfa"
            };

            if (openFileDialog.ShowDialog() == true)
                ProcessFiles(openFileDialog.FileNames);
        }

        private void DragOver(object? parameter)
        {
            if (parameter is DragEventArgs e && e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                var files = ((string[])e.Data.GetData(DataFormats.FileDrop))
                    .Where(f => Path.GetExtension(f).Equals(".rfa", StringComparison.OrdinalIgnoreCase));
                ProcessFiles(files);
            }
        }

        private void ProcessFiles(IEnumerable<string> files)
        {
            var tempFolderPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());

            try
            {
                Directory.CreateDirectory(tempFolderPath);

                foreach (var file in files)
                {
                    var fileName = Path.GetFileName(file);
                    if (FamiliesToUpload.Any(f => f.Name == fileName))
                        continue;

                    var tempFilePath = Path.Combine(tempFolderPath, fileName);
                    try
                    {
                        File.Copy(file, tempFilePath);
                    }
                    catch (IOException ex)
                    {
                        FileErrors.Add((fileName, $"Cannot access file: {ex.Message}"));
                        continue;
                    }

                    var (success, errorMessage) = ProcessSingleFile(tempFilePath, fileName);
                    if (!success)
                        FileErrors.Add((fileName, errorMessage));
                }

                if (FileErrors.Any())
                    ShowIncompatibleFilesMessage();
            }
            finally
            {
                CleanupTempFolder(tempFolderPath);
            }
        }

        private (bool Success, string ErrorMessage) ProcessSingleFile(string tempFilePath, string fileName)
        {
            Document familyDoc = null;
            byte[] revitFileBytes = null;

            try
            {
                // Read file bytes before opening in Revit to avoid file lock
                revitFileBytes = File.ReadAllBytes(tempFilePath);

                // Open Revit document
                var path = ModelPathUtils.ConvertUserVisiblePathToModelPath(tempFilePath);
                familyDoc = RevitManager.Application.OpenDocumentFile(path, new OpenOptions());

                if (!familyDoc.IsFamilyDocument)
                    return (false, "Not a valid Revit family document");

                var familyManager = familyDoc.FamilyManager;
                familyDoc.Save(new SaveOptions());
                var version = GetParameterValueOrDefault(familyManager, "Version");
                var productName = GetParameterValueOrDefault(familyManager, "Product Name");
                var manufacturer = GetParameterValueOrDefault(familyManager, "Manufacturer");
                var description = GetParameterValueOrDefault(familyManager, "Description");

                // Close document before generating thumbnail
                familyDoc.Close(false);
                familyDoc = null;

                var familyPreview = GetThumbnail(tempFilePath);
                var fileInfo = new FileInfo(tempFilePath);

                var libraryItemDto = new LibraryItemDto
                {
                    Name = fileName,
                    Version = version,
                    ProductName = productName,
                    Description = description,
                    RevitVersion = RevitManager.RevitVersion,
                    CreatedBy = Settings.Default.UserEmail
                };

                var fileDetail = new LibraryItemDetailsNew
                {
                    DataContext = new LibraryItemDetailsNewVm(libraryItemDto, this)
                    {
                        FileSize = $"{fileInfo.Length / 1.049e+6:0.0} MB",
                        UploadProgress = 100,
                        FamilyPreview = familyPreview,
                        Manufacturer = manufacturer,
                        RevitFileBytes = revitFileBytes
                    }
                };

                UploadFileInfo.Add(fileDetail);
                FamiliesToUpload.Add(libraryItemDto);
                return (true, null);
            }
            catch (Autodesk.Revit.Exceptions.FileAccessException ex)
            {
                LogHelper.Error($"File access error for {fileName}: {ex.Message}");
                return (false, "File created in a later version of Revit or is corrupted");
            }
            catch (IOException ex)
            {
                LogHelper.Error($"IO error for {fileName}: {ex.Message}");
                return (false, $"Cannot access file: {ex.Message}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error processing file {fileName}: {ex.Message}");
                return (false, $"Processing error: {ex.Message}");
            }
            finally
            {
                familyDoc?.Close(false); // Ensure document is closed
            }
        }

        private void CleanupTempFolder(string tempFolderPath)
        {
            if (string.IsNullOrEmpty(tempFolderPath) || !Directory.Exists(tempFolderPath))
                return;

            try
            {
                Directory.Delete(tempFolderPath, true);
            }
            catch (Exception ex)
            {
                LogHelper.Warning($"Failed to delete temp folder {tempFolderPath}: {ex.Message}");
            }
        }

        private bool CanExecuteApply(object parameter)
        {
            if (UploadFileInfo?.Any() != true)
                return false;

            return UploadFileInfo.All(fileDetail =>
            {
                if (fileDetail.DataContext is not LibraryItemDetailsNewVm vm)
                    return false;

                var selectedCategory = vm.Categories.FirstOrDefault(c => c.Id == vm.SelectedCategoryId);
                if (selectedCategory == null || vm.SelectedCategoryId == Guid.Empty)
                    return false;

                return selectedCategory.IsFreeAxezCategory
                    ? vm.ProductName != "Unknown" &&
                      vm.Version != "Unknown" &&
                      vm.Manufacturer != "Unknown" &&
                      FamilyPropertiesComparer.IsManufacturerFreeAxez(vm.Manufacturer)
                    : true;
            });
        }

        protected override async void ExecuteApply(object parameter)
        {
            IsUploading = true;
            try
            {
                foreach (var fileDetail in UploadFileInfo)
                {
                    if (fileDetail.DataContext is not LibraryItemDetailsNewVm vm)
                        continue;

                    try
                    {
                        var (familyFilePath, familyFileError) = await ApiService.Instance.UploadFamilyFile(vm);
                        var (familyImagePath, familyImageError) = await ApiService.Instance.UploadFamilyImage(vm.FamilyPreview);

                        if (familyFileError != null || familyImageError != null)
                        {
                            if (familyFileError != null) LoadingErrors.Add(familyFileError);
                            if (familyImageError != null) LoadingErrors.Add(familyImageError);
                            continue;
                        }

                        vm.LibraryItem.FamilyFilePath = familyFilePath;
                        vm.LibraryItem.FamilyImagePath = familyImagePath;

                        var response = await ApiService.Instance.AddFamilyAsync(vm.LibraryItem);
                        if (!response.IsSuccessStatusCode)
                            LoadingErrors.Add(await response.Content.ReadAsStringAsync());
                    }
                    catch (Exception ex)
                    {
                        LoadingErrors.Add($"Error uploading {vm.LibraryItem.Name}: {ex.Message}");
                        LogHelper.Error($"Error uploading {vm.LibraryItem.Name}: {ex.Message}");
                    }
                }

                if (LoadingErrors.Any())
                    ShowLoadingErrorsMessage();
                else
                    ShowSuccessMessage();
            }
            finally
            {
                IsUploading = false;
                CloseModal(true);
            }
        }
    }
}