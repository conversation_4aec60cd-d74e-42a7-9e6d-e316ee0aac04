﻿using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Navigation;

public class UserNavigationVm : BaseViewModel
{
    private readonly UnmatchedFamiliesPageVm _unmatchedFamiliesPageVm = new();
    private readonly FamiliesPageVm _userFamiliesPageVm = new();
    private readonly FamilyUpdatesPageVm _userFamilyUpdatesBasePageVm = new();
    private readonly HistoryPageVm _userHistoryPageVm = new();
    private readonly DetailsPageVm _userDetailsPageVm = new();
    private object _currentView;

    public UserNavigationVm()
    {
        FamiliesPageCommand = new RelayCommand(FamiliesPage);
        FamilyUpdatesPageCommand = new RelayCommand(FamilyUpdatesPage);
        UnmatchedFamiliesPageCommand = new RelayCommand(UnmatchedFamiliesPage);
        HistoryPageCommand = new RelayCommand(HistoryPage);
        DetailsPageCommand = new RelayCommand(DetailsPage);

        CurrentView = _userFamiliesPageVm;
    }

    public ICommand FamiliesPageCommand { get; }
    public ICommand FamilyUpdatesPageCommand { get; }
    public ICommand UnmatchedFamiliesPageCommand { get; }
    public ICommand HistoryPageCommand { get; }
    public ICommand DetailsPageCommand { get; }

    public object CurrentView
    {
        get => _currentView;
        set
        {
            _currentView = value;
            OnPropertyChanged();
        }
    }

    private void FamiliesPage(object obj)
    {
        CurrentView = _userFamiliesPageVm;
    }

    private void FamilyUpdatesPage(object obj)
    {
        CurrentView = _userFamilyUpdatesBasePageVm;
    }

    private void UnmatchedFamiliesPage(object obj)
    {
        CurrentView = _unmatchedFamiliesPageVm;
    }

    private void HistoryPage(object obj)
    {
        CurrentView = _userHistoryPageVm;
    }

    private void DetailsPage(object obj)
    {
        CurrentView = _userDetailsPageVm;
    }
}