<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals.AdminDeleteDetails"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             Width="400"
             Height="200">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0" Margin="0 20 0 0">
            <TextBlock Style="{StaticResource TextLarge}"
                       Foreground="Black"
                       Text="Confirm Delete"
                       FontWeight="Bold"
                       Margin="0 0 0 20"/>
            <TextBlock Style="{StaticResource TextBase}"
                       Foreground="Black"
                       TextWrapping="Wrap"
                       Margin="0 0 0 10">
                <Run Text="Are you sure you want to delete details: "/>
                <Run Text="{Binding DetailsName, Mode=OneWay}" FontWeight="Bold"/>
                <Run Text="?"/>
            </TextBlock>
            <TextBlock Style="{StaticResource TextBase}"
                       Foreground="Red"
                       Text="{Binding Error}"
                       TextWrapping="Wrap"
                       Margin="0 10 0 0"/>
        </StackPanel>
        <StackPanel Grid.Row="1"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    Margin="0 20 0 0">
            <Button Style="{StaticResource ButtonOutlinedBlue}"
                    Width="120"
                    Margin="0 0 20 0"
                    Command="{Binding CancelCommand}">
                Cancel
            </Button>
            <Button Style="{StaticResource ButtonSimpleRed}"
                    Width="120"
                    Command="{Binding ApplyCommand}">
                Delete
            </Button>
        </StackPanel>
    </Grid>
</UserControl>
