using Autodesk.Revit.DB;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data
{
    public class DwgLinkInfo
    {
        public ImportInstance ImportInstance { get; set; }
        public ElementId ElementId { get; set; }
        public Level Level { get; set; }
        public string DwgFilePath { get; set; }
        public string LinkName { get; set; }
        public Transform Transform { get; set; }

        public bool IsValid => ImportInstance != null && 
                              Level != null && 
                              !string.IsNullOrEmpty(DwgFilePath);

        public string GetDisplayName()
        {
            return !string.IsNullOrEmpty(LinkName) ? LinkName : "Unknown DWG Link";
        }
    }
}
