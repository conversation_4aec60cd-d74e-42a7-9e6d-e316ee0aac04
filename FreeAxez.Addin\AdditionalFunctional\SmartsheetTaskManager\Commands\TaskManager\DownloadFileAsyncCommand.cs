﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.DataTransferObjects;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Commands.TaskManager
{
    public class DownloadFileAsyncCommand : AsyncCommandBase
    {
        private readonly TaskManagerViewModel _taskManagerViewModel;

        public DownloadFileAsyncCommand(TaskManagerViewModel taskManagerViewModel)
        {
            _taskManagerViewModel = taskManagerViewModel;
        }

        public async override Task ExecuteAsync(object obj)
        {
            if (obj is AttachmentDto attachmentDto)
            {
                string downloadFolder = Environment.GetFolderPath
                    (Environment.SpecialFolder.UserProfile) + "\\Downloads";
                string filePath = Path.Combine(downloadFolder, attachmentDto.Name);

                await DownloadFileAsync(attachmentDto.FilePath, filePath);

                OpenFile(filePath);
            }
        }

        private async Task DownloadFileAsync(string fileUrl, string savePath)
        {
            try
            {
                using (HttpClient httpClient = new HttpClient())
                {
                    byte[] fileBytes = await httpClient.GetByteArrayAsync(fileUrl);

                    File.WriteAllBytes(savePath, fileBytes);
                }
            }
            catch (Exception exception)
            {
                _taskManagerViewModel.ErrorResponse = exception.Message;
            }
        }

        private void OpenFile(string filePath)
        {
            try
            {
                var startInfo = new ProcessStartInfo()
                {
                    FileName = filePath,
                    UseShellExecute = true
                };

                Process.Start(startInfo);
            }
            catch (Exception exception)
            {
                _taskManagerViewModel.ErrorResponse = exception.Message;
            }
        }
    }
}
