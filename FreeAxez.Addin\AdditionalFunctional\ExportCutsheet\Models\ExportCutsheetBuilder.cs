﻿using FreeAxez.Addin.AdditionalFunctional.ExportCutsheet.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace FreeAxez.Addin.AdditionalFunctional.ExportCutsheet.Models
{
    public class ExportCutsheetBuilder
    {
        private readonly string _missedProductFileName = "MissedProducts.csv";
        private readonly string _subFolderName = "Cutsheets";
        private readonly string _folderPath;
        private readonly bool _isOpenDirectory;
        private readonly CancellationToken _cancellationToken;

        public ExportCutsheetBuilder(string folderPath, bool isOpenDirectory, CancellationToken cancellationToken)
        {
            _folderPath = folderPath ?? throw new NullReferenceException("No folder was selected.");
            _isOpenDirectory = isOpenDirectory;
            _cancellationToken = cancellationToken;
        }

        public async Task BuildAsync()
        {
            var pdfFolderPath = Path.Combine(_folderPath, _subFolderName);
            var missedProductsPath = Path.Combine(_folderPath, _missedProductFileName);
            PrepareFolder(pdfFolderPath, missedProductsPath);

            var powerElementCollector = new PowerElementCollector();
            var powerElements = powerElementCollector.GetPowerElements();

            var productDocumentationProvider = new ProductDocumentationProvider();
            (var productDocumentationUrls, var missedProducts) = 
                await productDocumentationProvider.GetProductDocumentationUrlsAsync(powerElements);

            var productDocumentationDownloader = 
                new ProductDocumentationDownloader(productDocumentationUrls, pdfFolderPath, _cancellationToken);
            await productDocumentationDownloader.DownloadAsync();

            if (missedProducts.Count > 0) WriteLinesToFile(missedProductsPath, missedProducts);

            if (_isOpenDirectory) 
            {
                try
                {
                    var startInfo = new ProcessStartInfo()
                    {
                        FileName = _folderPath,
                        UseShellExecute = true
                    };

                    Process.Start(startInfo);
                }
                catch (Exception ex)
                {
                    MessageWindow.ShowDialog("Error Opening Directory", "Failed to open the directory: " + ex.Message, MessageType.Error);
                }
            }
        }

        private void PrepareFolder(string pdfFolderPath, string missedProductsFilePath)
        {
            if (Directory.Exists(pdfFolderPath))
            {
                foreach (string file in Directory.GetFiles(pdfFolderPath))
                {
                    DeleteFile(file);
                }
            }
            else
            {
                Directory.CreateDirectory(pdfFolderPath);
            }

            DeleteFile(missedProductsFilePath);
        }

        private void DeleteFile(string filePath)
        {
            try
            {
                File.Delete(filePath);
            }
            catch (Exception ex)
            {
                LogHelper.Warning($"Failed to delete {filePath} file {ex.Message}.");
            }
        }

        private void WriteLinesToFile(string filePath, List<string> lines)
        {
            try
            {
                File.WriteAllLines(filePath, lines);
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Failed to write to file {filePath}: {ex.Message}");
            }
        }
    }
}
