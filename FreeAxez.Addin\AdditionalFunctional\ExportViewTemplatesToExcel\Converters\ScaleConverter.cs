﻿using OfficeOpenXml;

namespace FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Converters
{
    public class ScaleConverter
    {
        private static readonly Dictionary<int, string> ScaleMap = new()
        {
            { 1, "1/64\"=1'-0\"" },
            { 2, "1/32\"=1'-0\"" },
            { 3, "12\"=1'-0\"" },
            { 4, "6\"=1'-0\"" },
            { 6, "3\"=1'-0\"" },
            { 8, "1 1/2\"=1'-0\"" },
            { 12, "1\"=1'-0\"" },
            { 16, "3/4\"=1'-0\"" },
            { 24, "1/2\"=1'-0\"" },
            { 32, "3/8\"=1'-0\"" },
            { 48, "1/4\"=1'-0\"" },
            { 64, "3/16\"=1'-0\"" },
            { 96, "1/8\"=1'-0\"" },
            { 120, "1\"=10'-0\"" },
            { 128, "3/32\"=1'-0\"" },
            { 192, "1/16\"=10'-0\"" },
            { 240, "1\"=20'-0\"" },
            { 384, "1/32\"=10'-0\"" },
            { 480, "1\"=30'-0\"" },
            //{ 640, "1\"=40'-0\"" },
            //{ 800, "1\"=50'-0\"" },
            //{ 960, "1\"=60'-0\"" },
            //{ 1280, "1\"=80'-0\"" },
            //{ 1600, "1\"=100'-0\"" },
            //{ 2560, "1\"=160'-0\"" },
            //{ 3200, "1\"=200'-0\"" },
            //{ 6400, "1\"=400'-0\"" }
        };

        public static string ConvertScale(int revitScale)
        {
            if (ScaleMap.TryGetValue(revitScale, out var scaleRepresentation)) return scaleRepresentation;
            return $"Unknown Scale ({revitScale})";
        }

        public static void AddScaleValidation(ExcelWorksheet ws, string cellAddress)
        {
            var dv = ws.DataValidations.AddListValidation(cellAddress);
            foreach (var scale in ScaleMap.Values) dv.Formula.Values.Add(scale);
            dv.ShowErrorMessage = true;
            dv.ErrorTitle = "Invalid Scale";
            dv.Error = "Please select a valid scale.";
        }

        public static int ParseScale(string scaleString)
        {
            var reverseMap = new Dictionary<string, int>();
            foreach (var kvp in ScaleMap)
                reverseMap[kvp.Value] = kvp.Key;

            if (reverseMap.TryGetValue(scaleString, out var revitScale))
                return revitScale;

            return -1;
        }
    }
}
