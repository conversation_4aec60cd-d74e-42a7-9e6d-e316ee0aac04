﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Utils
{
    public class SeparateCurbResolver
    {
        private const double OutlineTolerance = 0.1648419948;
        public List<List<FamilyInstance>> Resolve(List<FamilyInstance> familyInstances)
        {
            List<FamilyInstance> orderedFamilyInstances = familyInstances
                .OrderBy(f => (f.Location as LocationPoint).Point.X)
                .ThenBy(f => (f.Location as LocationPoint).Point.Y)
                .ToList();

            List<FamilyInstance> cornerInstances = orderedFamilyInstances
                .Where(f => CurbFamily.IsCorner(f))
                .ToList();

            List<FamilyInstance> nonCornerInstances = orderedFamilyInstances
                .Where(f => !CurbFamily.IsCorner(f))
                .ToList();

            List<List<FamilyInstance>> groups = new List<List<FamilyInstance>>();

            while (nonCornerInstances.Count > 0)
            {
                var familyInstance = nonCornerInstances.First();
                var familyInstanceOutline = GetOutlineFromFamilyInstance(familyInstance);
                nonCornerInstances.Remove(familyInstance);

                List<FamilyInstance> currentGroup = new List<FamilyInstance> { familyInstance };

                FindIntersectionFamilyInstances(nonCornerInstances, familyInstanceOutline, currentGroup);

                groups.Add(currentGroup.Count == 1
                        ? new List<FamilyInstance> { currentGroup[0] }
                        : new List<FamilyInstance> { currentGroup[1] });
            }

            foreach (var cornerInstance in cornerInstances)
            {
                groups.Add(new List<FamilyInstance> { cornerInstance });
            }

            return groups;
        }

        private Outline GetOutlineFromFamilyInstance(FamilyInstance familyInstance)
        {
            var geometryOptions = new Options
            {
                DetailLevel = ViewDetailLevel.Fine
            };

            var familyGeometry = familyInstance.get_Geometry(geometryOptions);
            var familyGeometryBoundingBox = familyGeometry.GetBoundingBox();

            return new Outline(familyGeometryBoundingBox.Min, familyGeometryBoundingBox.Max);
        }

        private void FindIntersectionFamilyInstances(List<FamilyInstance> orderedFamilyInstances,
                                                     Outline referenceOutline,
                                                     List<FamilyInstance> currentGroup)
        {
            List<FamilyInstance> intersectiongInstances = orderedFamilyInstances
                .Where(candidate =>
                {
                    var candidateInstanceOutline = GetOutlineFromFamilyInstance(candidate);

                    return referenceOutline.Intersects(candidateInstanceOutline, OutlineTolerance);
                })
                .ToList();
            foreach (var intersectiongInstance in intersectiongInstances)
            {
                orderedFamilyInstances.Remove(intersectiongInstance);
                currentGroup.Add(intersectiongInstance);

                FindIntersectionFamilyInstances(orderedFamilyInstances, GetOutlineFromFamilyInstance(intersectiongInstance),
                                                                       currentGroup);
            }
        }
    }
}
