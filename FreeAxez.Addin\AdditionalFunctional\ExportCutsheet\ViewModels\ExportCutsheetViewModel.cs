﻿using FreeAxez.Addin.AdditionalFunctional.ExportCutsheet.Models;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System.IO;
using System.Threading;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.ExportCutsheet.ViewModels
{
    public class ExportCutsheetViewModel : BaseViewModel
    {
        private string _folderPath;
        private bool _isOpenDirectory;
        private bool _isDownloading;
        private readonly CancellationTokenSource _cancellationTokenSource;

        public ExportCutsheetViewModel()
        {
            IsDownloading = false;
            _cancellationTokenSource = new CancellationTokenSource();

            if (Directory.Exists(Properties.Settings.Default.ExportCutsheetFolderPath))
            {
                _folderPath = Properties.Settings.Default.ExportCutsheetFolderPath;
            }

            _isOpenDirectory = Properties.Settings.Default.ExportCutsheetIsOpenDirectory;

            ExportCutsheetCommand = new RelayCommand(
                OnExportCutsheetExecuteAsync, _ => Directory.Exists(FolderPath) && !IsDownloading);

            BrowseFolderCommand = new RelayCommand(OnBrowseFolderExecute);
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
            WindowClosingCommand = new RelayCommand(
                _ => _cancellationTokenSource.Cancel());
        }

        public string FolderPath
        {
            get => _folderPath;
            set
            {
                if (_folderPath != value)
                {
                    _folderPath = value;
                    OnPropertyChanged(nameof(FolderPath));
                }
            }
        }

        public bool IsOpenDirectory
        {
            get => _isOpenDirectory;
            set
            {
                if (_isOpenDirectory != value)
                {
                    _isOpenDirectory = value;
                    OnPropertyChanged(nameof(IsOpenDirectory));
                }
            }
        }

        public bool IsDownloading
        {
            get => _isDownloading;
            set
            {
                if (_isDownloading != value)
                {
                    _isDownloading = value;
                    OnPropertyChanged(nameof(IsDownloading));
                }
            }
        }

        public ICommand BrowseFolderCommand { get; }

        public ICommand ExportCutsheetCommand { get; }

        public ICommand CancelCommand { get; }

        public ICommand WindowClosingCommand { get; }

        private void OnBrowseFolderExecute(object @object)
        {
            using (var folderDialog = new FolderBrowserDialog())
            {
                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    FolderPath = folderDialog.SelectedPath;
                }
            }
        }

        private async void OnExportCutsheetExecuteAsync(object @object)
        {
            if (!Directory.Exists(_folderPath))
            {
                InfoDialog.ShowDialog("Warning", "Invalid folder, please choose another folder.");
                return;
            }

            SaveSettings();

            var exportCutsheetBuilder = new ExportCutsheetBuilder(_folderPath,
                                                                  _isOpenDirectory,
                                                                  _cancellationTokenSource.Token);

            IsDownloading = true;
            await exportCutsheetBuilder.BuildAsync();
            IsDownloading = false;

            if (!_isOpenDirectory && !_cancellationTokenSource.IsCancellationRequested)
            {
                InfoDialog.ShowDialog("Success", "PDF files downloaded successfully!");
            }

            (@object as Window).Close();
        }

        private void OnCancelCommandExecute(object @object)
        {
            _cancellationTokenSource?.Cancel();

            SaveSettings();

            Window window = @object as Window;

            if (window.IsActive)
            {
                window.Close();
            }
        }

        private void SaveSettings()
        {
            Properties.Settings.Default.ExportCutsheetFolderPath = _folderPath;
            Properties.Settings.Default.ExportCutsheetIsOpenDirectory = _isOpenDirectory;

            Properties.Settings.Default.Save();
        }
    }
}
