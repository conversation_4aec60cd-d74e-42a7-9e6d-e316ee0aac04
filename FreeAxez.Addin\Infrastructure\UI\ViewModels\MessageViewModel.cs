﻿using System.Windows.Input;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using System.Windows;

namespace FreeAxez.Addin.Infrastructure.UI.ViewModels;

public class MessageViewModel : WindowViewModel
{
    private MessageType _currentMessageType;

    public MessageViewModel()
    {
    }

    public MessageViewModel(string title, string message, MessageType messageType)
    {
        Title = title;
        Message = message;
        SetVisibility(messageType);

        _currentMessageType = messageType;

        OkDialogCommand = new RelayCommand(ExecuteOkCommand);
        CancelDialogCommand = new RelayCommand(ExecuteCancelCommand);
    }

    public MessageViewModel(string message, MessageType messageType)
        : this(GetTitleByMessageType(messageType), message, messageType)
    {
        Message = message;
        SetVisibility(messageType);

        _currentMessageType = messageType;

        OkDialogCommand = new RelayCommand(ExecuteOkCommand);
        CancelDialogCommand = new RelayCommand(ExecuteCancelCommand);
    }

    public string Title { get; set; }
    public string Message { get; set; }
    public MessageType DefaultMessageType { get; set; }
    public Visibility InfoVisibility { get; set; } = Visibility.Collapsed;
    public Visibility WarningVisibility { get; set; } = Visibility.Collapsed;
    public Visibility ErrorVisibility { get; set; } = Visibility.Collapsed;
    public Visibility SuccessVisibility { get; set; } = Visibility.Collapsed;
    public Visibility NotifyVisibility { get; set; } = Visibility.Collapsed;

    public ICommand OkDialogCommand { get; }
    public ICommand CancelDialogCommand { get; }

    public MessageType CurrentMessageType
    {
        get => _currentMessageType;
        set
        {
            _currentMessageType = value;
            OnPropertyChanged(nameof(CurrentMessageType));
        }
    }

    private void SetVisibility(MessageType messageType)
    {
        switch (messageType)
        {
            case MessageType.Info:
                InfoVisibility = Visibility.Visible;
                break;
            case MessageType.Warning:
                WarningVisibility = Visibility.Visible;
                break;
            case MessageType.Error:
                ErrorVisibility = Visibility.Visible;
                break;
            case MessageType.Success:
                SuccessVisibility = Visibility.Visible;
                break;
            case MessageType.Notify:
                NotifyVisibility = Visibility.Visible;
                break;
        }

        OnPropertyChanged(nameof(InfoVisibility));
        OnPropertyChanged(nameof(WarningVisibility));
        OnPropertyChanged(nameof(ErrorVisibility));
        OnPropertyChanged(nameof(SuccessVisibility));
        OnPropertyChanged(nameof(NotifyVisibility));
    }

    private static string GetTitleByMessageType(MessageType messageType)
    {
        switch (messageType)
        {
            case MessageType.Info:
                return "Information";
            case MessageType.Warning:
                return "Warning";
            case MessageType.Error:
                return "Error";
            case MessageType.Success:
                return "Success";
            case MessageType.Notify:
                return "Notify";
            default:
                return "Message";
        }
    }

    private void ExecuteOkCommand(object sender)
    {
        if (sender is Window window)
        {
            window.DialogResult = true;
            window.Close();
        }
    }

    private void ExecuteCancelCommand(object sender)
    {
        if (sender is Window window)
        {
            window.DialogResult = false;
            window.Close();
        }
    }
}