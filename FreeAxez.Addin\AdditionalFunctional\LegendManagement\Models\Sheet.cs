﻿using Microsoft.Extensions.Azure;

namespace FreeAxez.Addin.AdditionalFunctional.LegendManagement.Models
{
    public class Sheet
    {
        public Sheet(long id, string number)
        {
            Id = id;
            SheetNumber = number;
        }

        public long Id { get; }
        public string SheetNumber { get; }

        public override bool Equals(object obj)
        {
            if (obj is null)
            {
                return false;
            }

            if (obj is Sheet sheet)
            {
                return sheet.SheetNumber.Equals(SheetNumber, System.StringComparison.OrdinalIgnoreCase);
            }

            return false;
        }


        public override int GetHashCode()
        {
            return SheetNumber.GetHashCode();
        }
    }
}
