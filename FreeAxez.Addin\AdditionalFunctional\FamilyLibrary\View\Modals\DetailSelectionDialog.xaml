<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals.DetailSelectionDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:viewModel="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance Type={x:Type viewModel:DetailSelectionDialogVm}}"
             Width="1000" Height="700">
    <UserControl.Resources>
        <ResourceDictionary>
            <Style x:Key="DataGridRowStyle" TargetType="DataGridRow">
                <EventSetter Event="PreviewMouseLeftButtonDown" Handler="DataGridRow_PreviewMouseLeftButtonDown"/>
            </Style>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Search Box -->
        <DockPanel Grid.Row="0" Margin="0,0,0,15">
            <TextBox Tag="Search by name"
                     Style="{StaticResource Search}"
                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                     VerticalContentAlignment="Center"/>
        </DockPanel>

        <DataGrid x:Name="DetailsDataGrid"
                  Grid.Row="1"
                  ItemsSource="{Binding DetailViews}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  CanUserReorderColumns="False"
                  CanUserResizeRows="False"
                  CanUserSortColumns="True"
                  HeadersVisibility="Column"
                  SelectionMode="Single"
                  SelectionUnit="FullRow"
                  Style="{StaticResource DataGridWithBorders}"
                  ColumnHeaderStyle="{StaticResource DataGridColumnHeader}"
                  RowStyle="{StaticResource DataGridRowStyle}">
            <DataGrid.Columns>
                <DataGridCheckBoxColumn Header="Select"
                                        Binding="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                        Width="60"
                                        IsReadOnly="False">
                    <DataGridCheckBoxColumn.ElementStyle>
                        <Style TargetType="CheckBox">
                            <Setter Property="VerticalAlignment" Value="Center"/>
                            <Setter Property="HorizontalAlignment" Value="Center"/>
                            <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                        </Style>
                    </DataGridCheckBoxColumn.ElementStyle>
                    <DataGridCheckBoxColumn.CellStyle>
                        <Style TargetType="DataGridCell">
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderBrush" Value="Transparent"/>
                            <Setter Property="IsHitTestVisible" Value="True"/>
                            <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding DataContext.IsEnabled, RelativeSource={RelativeSource AncestorType=DataGridRow}}" Value="False">
                                    <Setter Property="IsEnabled" Value="False"/>
                                    <Setter Property="Opacity" Value="0.5"/>
                                    <Setter Property="ToolTip" Value="This view is disabled"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGridCheckBoxColumn.CellStyle>
                </DataGridCheckBoxColumn>
                <DataGridTemplateColumn Header="Preview" Width="200">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Border Name="PreviewBorder"
                                    Background="Transparent"
                                    Cursor="Hand"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch"
                                    MouseLeftButtonDown="PreviewImage_MouseLeftButtonDown"
                                    Tag="{Binding}">
                                <Grid>
                                    <Image Source="{Binding PreviewImage}"
                                           Width="160"
                                           Height="120"
                                           Stretch="Uniform"/>
                                    <Path HorizontalAlignment="Right"
                                          VerticalAlignment="Bottom"
                                          Data="M3 4C3 3.44772 3.44772 3 4 3H8C8.55228 3 9 3.44772 9 4C9 4.55228 8.55228 5 8 5H6.41421L9.70711 8.29289C10.0976 8.68342 10.0976 9.31658 9.70711 9.70711C9.31658 10.0976 8.68342 10.0976 8.29289 9.70711L5 6.41421V8C5 8.55228 4.55228 9 4 9C3.44772 9 3 8.55228 3 8V4ZM16 3H20C20.5523 3 21 3.44772 21 4V8C21 8.55228 20.5523 9 20 9C19.4477 9 19 8.55228 19 8V6.41421L15.7071 9.70711C15.3166 10.0976 14.6834 10.0976 14.2929 9.70711C13.9024 9.31658 13.9024 8.68342 14.2929 8.29289L17.5858 5H16C15.4477 5 15 4.55228 15 4C15 3.44772 15.4477 3 16 3ZM9.70711 14.2929C10.0976 14.6834 10.0976 15.3166 9.70711 15.7071L6.41421 19H8C8.55228 19 9 19.4477 9 20C9 20.5523 8.55228 21 8 21H4C3.44772 21 3 20.5523 3 20V16C3 15.4477 3.44772 15 4 15C4.55228 15 5 15.4477 5 16V17.5858L8.29289 14.2929C8.68342 13.9024 9.31658 13.9024 9.70711 14.2929ZM14.2929 14.2929C14.6834 13.9024 15.3166 13.9024 15.7071 14.2929L19 17.5858V16C19 15.4477 19.4477 15 20 15C20.5523 15 21 15.4477 21 16V20C21 20.5523 20.5523 21 20 21H16C15.4477 21 15 20.5523 15 20C15 19.4477 15.4477 19 16 19H17.5858L14.2929 15.7071C13.9024 15.3166 13.9024 14.6834 14.2929 14.2929Z"
                                          Stroke="Gray"
                                          StrokeThickness="1"
                                          Width="20"
                                          Height="20"
                                          Stretch="Uniform"/>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="Type"
                                    Binding="{Binding TypeDisplayName}"
                                    Width="100"/>
                <DataGridTextColumn Header="Name"
                                    Binding="{Binding Name}"
                                    Width="*"/>
            </DataGrid.Columns>
        </DataGrid>

        <Grid Grid.Row="2" Margin="0,15,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Style="{StaticResource ButtonOutlinedGreen}"
                    Command="{Binding CmdSelectAll}">
                <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource CircleCheckIcon}"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                    <TextBlock Text="Check All"
                               Margin="5,0,0,0"
                               Foreground="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Button>

            <Button Grid.Column="2"
                    Style="{StaticResource ButtonOutlinedPurple}"
                    Command="{Binding CmdSelectNone}">
                <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource CircleUncheckIcon}"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                    <TextBlock Text="Uncheck All"
                               Foreground="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}"
                               Margin="5,0,0,0"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Button>

            <Button Grid.Column="4"
                    Content="Import Selected"
                    Command="{Binding CmdImport}"
                    Style="{StaticResource ButtonSimpleBlue}"/>

            <Button Grid.Column="6"
                    Content="Cancel"
                    Command="{Binding CancelCommand}"
                    Style="{StaticResource ButtonSimpleLight}"/>
        </Grid>
    </Grid>
</UserControl>
