using NetTopologySuite.Geometries;
using NetTopologySuite.Operation.Linemerge;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services;

public class GeometryAnalyzerService
{
    private const double OUTLET_CONNECTION_TOLERANCE = 2.0; // 2 feet
    private const double LINE_CONNECTION_TOLERANCE = 1.0 / 12.0; // 1 inch

    public GeometryTreeModel AnalyzeGeometry(List<LineString> normalizedLines, List<OutletModel> outlets)
    {
        var root = IdentifyRoot(normalizedLines, outlets);
        var paths = BuildPaths(root, normalizedLines, outlets);

        return new GeometryTreeModel
        {
            Root = root,
            Leaves = outlets,
            Paths = paths
        };
    }

    private Point IdentifyRoot(List<LineString> lines, List<OutletModel> outlets)
    {
        var allEndpoints = lines.SelectMany(l => new[] { l.StartPoint, l.EndPoint }).ToList();
        var endpointGroups = allEndpoints.GroupBy(p => p, new PointEqualityComparer(LINE_CONNECTION_TOLERANCE));

        var freeEndpoints = endpointGroups.Where(g => g.Count() == 1).Select(g => g.Key);

        var rootPoint = freeEndpoints.FirstOrDefault(p =>
            !outlets.Any(o => o.Location.IsWithinDistance(p, OUTLET_CONNECTION_TOLERANCE)));

        return rootPoint ?? Point.Empty;
    }

    private Dictionary<OutletModel, LineString> BuildPaths(Point root, List<LineString> lines, List<OutletModel> outlets)
    {
        var paths = new Dictionary<OutletModel, LineString>();
        var lineGraph = new Dictionary<Point, List<LineString>>(new PointEqualityComparer(LINE_CONNECTION_TOLERANCE));

        foreach (var line in lines)
        {
            if (!lineGraph.ContainsKey(line.StartPoint)) lineGraph[line.StartPoint] = new List<LineString>();
            lineGraph[line.StartPoint].Add(line);
            if (!lineGraph.ContainsKey(line.EndPoint)) lineGraph[line.EndPoint] = new List<LineString>();
            lineGraph[line.EndPoint].Add(line);
        }

        foreach (var outlet in outlets)
        {
            var connectionPoint = FindConnectionPoint(outlet.Location, lines);
            if (connectionPoint == null) continue;

            var pathSegments = FindPath(connectionPoint, root, lineGraph);
            if (pathSegments.Any())
            {
                var lineMerger = new LineMerger();
                lineMerger.Add(pathSegments);
                var merged = lineMerger.GetMergedLineStrings();
                if (merged.Count > 0)
                {
                    paths[outlet] = (LineString)merged.First();
                }
            }
        }

        return paths;
    }

    private Point FindConnectionPoint(Point outletLocation, List<LineString> lines)
    {
        return lines.SelectMany(l => new[] { l.StartPoint, l.EndPoint })
                    .Where(p => p.IsWithinDistance(outletLocation, OUTLET_CONNECTION_TOLERANCE))
                    .OrderBy(p => p.Distance(outletLocation))
                    .FirstOrDefault();
    }

    private List<LineString> FindPath(Point start, Point end, Dictionary<Point, List<LineString>> graph)
    {
        var queue = new Queue<List<LineString>>();
        var visitedPoints = new HashSet<Point>(new PointEqualityComparer(LINE_CONNECTION_TOLERANCE));

        queue.Enqueue(new List<LineString>());
        visitedPoints.Add(start);

        var paths = new Dictionary<Point, List<LineString>>(new PointEqualityComparer(LINE_CONNECTION_TOLERANCE))
        {
            [start] = new List<LineString>()
        };

        var q = new Queue<Point>();
        q.Enqueue(start);

        while (q.Count > 0)
        {
            var currentPoint = q.Dequeue();

            if (currentPoint.IsWithinDistance(end, LINE_CONNECTION_TOLERANCE))
            {
                return paths[currentPoint];
            }

            if (!graph.ContainsKey(currentPoint)) continue;

            foreach (var line in graph[currentPoint])
            {
                var otherEnd = line.StartPoint.Equals(currentPoint) ? line.EndPoint : line.StartPoint;
                if (!visitedPoints.Contains(otherEnd))
                {
                    visitedPoints.Add(otherEnd);
                    var newPath = new List<LineString>(paths[currentPoint]) { line };
                    paths[otherEnd] = newPath;
                    q.Enqueue(otherEnd);
                }
            }
        }

        return new List<LineString>(); // No path found
    }
}
