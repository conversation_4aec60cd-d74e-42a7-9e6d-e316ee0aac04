﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="ColorsPalette.xaml" />
    </ResourceDictionary.MergedDictionaries>
    <Style x:Key="ColumnHeaderGripperStyle"
           TargetType="{x:Type Thumb}">
        <Setter Property="Width"
                Value="2" />
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="Cursor"
                Value="SizeWE" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border Background="{TemplateBinding Background}"
                            Padding="{TemplateBinding Padding}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- DataGridSelectAllButton Style -->
    <Style x:Key="{ComponentResourceKey ResourceId=DataGridSelectAllButtonStyle, TypeInTargetAssembly={x:Type DataGrid}}"
           TargetType="{x:Type Button}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Grid>
                        <Rectangle x:Name="Border"
                                   Fill="{DynamicResource {x:Static SystemColors.ControlBrushKey}}"
                                   SnapsToDevicePixels="True" />
                        <Polygon x:Name="Arrow"
                                 Fill="Black"
                                 HorizontalAlignment="Right"
                                 Margin="8,8,3,3"
                                 Opacity="0.15"
                                 Points="0,10 10,10 10,0"
                                 Stretch="Uniform"
                                 VerticalAlignment="Bottom" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter Property="Stroke"
                                    TargetName="Border"
                                    Value="{DynamicResource {x:Static SystemColors.ControlDarkBrushKey}}" />
                        </Trigger>
                        <Trigger Property="IsPressed"
                                 Value="True">
                            <Setter Property="Fill"
                                    TargetName="Border"
                                    Value="{DynamicResource {x:Static SystemColors.ControlDarkBrushKey}}" />
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                 Value="False">
                            <Setter Property="Visibility"
                                    TargetName="Arrow"
                                    Value="Collapsed" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- DataGridCell Style -->
    <Style x:Key="DataGridCellStyle"
           TargetType="DataGridCell">
        <Setter Property="Padding"
                Value="5" />
        <Setter Property="VerticalContentAlignment"
                Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridCell">
                    <Border Background="Transparent">
                        <ContentPresenter VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Margin="{TemplateBinding Padding}">
                        </ContentPresenter>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType={x:Type DataGridRow}}, Path=IsSelected}"
                         Value="True">
                <Setter Property="Foreground"
                        Value="Black" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <!-- DataGridRow Style -->
    <Style x:Key="DataGridRowStyle"
           TargetType="DataGridRow">
        <Setter Property="MinHeight"
                Value="25" />
        <Style.Triggers>
            <Trigger Property="IsSelected"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Gray100}" />
                <Setter Property="Foreground"
                        Value="White" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- DataGrid Style -->
    <Style x:Key="DataGridWithBorders"
           TargetType="{x:Type DataGrid}">
        <Style.Resources>
            <Style TargetType="DataGridCell"
                   BasedOn="{StaticResource DataGridCellStyle}" />
            <Style TargetType="DataGridRow"
                   BasedOn="{StaticResource DataGridRowStyle}" />
        </Style.Resources>
        <Setter Property="Background"
                Value="{DynamicResource {x:Static SystemColors.ControlBrushKey}}" />
        <Setter Property="FontSize"
                Value="12"></Setter>
        <Setter Property="Foreground"
                Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />
        <Setter Property="BorderBrush"
                Value="{StaticResource Gray200}" />
        <Setter Property="BorderThickness"
                Value="1" />
        <Setter Property="HorizontalGridLinesBrush"
                Value="{StaticResource Gray200}" />
        <Setter Property="VerticalGridLinesBrush"
                Value="{StaticResource Gray200}" />
        <Setter Property="GridLinesVisibility"
                Value="All" />
        <Setter Property="RowDetailsVisibilityMode"
                Value="VisibleWhenSelected" />
        <Setter Property="ScrollViewer.CanContentScroll"
                Value="true" />
        <Setter Property="ScrollViewer.PanningMode"
                Value="Both" />
        <Setter Property="Stylus.IsFlicksEnabled"
                Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGrid}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="0"
                            Padding="{TemplateBinding Padding}"
                            SnapsToDevicePixels="True">
                        <Grid>
                            <TextBlock x:Name="EmptyContentTextBlock"
                                       Text="No items found. Please add an item."
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Visibility="Collapsed" />
                            <ScrollViewer x:Name="DG_ScrollViewer"
                                          Focusable="false">
                                <ScrollViewer.Template>
                                    <ControlTemplate TargetType="{x:Type ScrollViewer}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="*" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>
                                            <Button Command="{x:Static DataGrid.SelectAllCommand}"
                                                    Focusable="false"
                                                    Style="{DynamicResource {ComponentResourceKey ResourceId=DataGridSelectAllButtonStyle, TypeInTargetAssembly={x:Type DataGrid}}}"
                                                    Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.All}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"
                                                    Width="{Binding CellsPanelHorizontalOffset, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" />
                                            <DataGridColumnHeadersPresenter x:Name="PART_ColumnHeadersPresenter"
                                                                            Grid.Row="0"
                                                                            Grid.Column="1"
                                                                            Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.Column}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" />
                                            <ScrollContentPresenter x:Name="PART_ScrollContentPresenter"
                                                                    CanContentScroll="{TemplateBinding CanContentScroll}"
                                                                    Grid.ColumnSpan="2"
                                                                    Grid.Column="0"
                                                                    Grid.Row="1" />
                                            <ScrollBar x:Name="PART_VerticalScrollBar"
                                                       Grid.Column="2"
                                                       Maximum="{TemplateBinding ScrollableHeight}"
                                                       Orientation="Vertical"
                                                       Grid.Row="1"
                                                       Value="{Binding VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                                       ViewportSize="{TemplateBinding ViewportHeight}"
                                                       Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}" />
                                            <Grid Grid.Column="1"
                                                  Grid.Row="2">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="{Binding NonFrozenColumnsViewportHorizontalOffset, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" />
                                                    <ColumnDefinition Width="*" />
                                                </Grid.ColumnDefinitions>
                                                <ScrollBar x:Name="PART_HorizontalScrollBar"
                                                           Grid.Column="1"
                                                           Maximum="{TemplateBinding ScrollableWidth}"
                                                           Orientation="Horizontal"
                                                           Value="{Binding HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                                           ViewportSize="{TemplateBinding ViewportWidth}"
                                                           Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}" />
                                            </Grid>
                                        </Grid>
                                    </ControlTemplate>
                                </ScrollViewer.Template>
                                <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                            </ScrollViewer>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Items.Count, RelativeSource={RelativeSource TemplatedParent}}"
                                     Value="0">
                            <Setter TargetName="EmptyContentTextBlock"
                                    Property="Visibility"
                                    Value="Visible" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsGrouping"
                               Value="true" />
                    <Condition Property="VirtualizingPanel.IsVirtualizingWhenGrouping"
                               Value="false" />
                </MultiTrigger.Conditions>
                <Setter Property="ScrollViewer.CanContentScroll"
                        Value="false" />
            </MultiTrigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="DataGridWithoutBorders"
           TargetType="{x:Type DataGrid}">
        <Style.Resources>
            <Style TargetType="DataGridCell"
                   BasedOn="{StaticResource DataGridCellStyle}" />
            <Style TargetType="DataGridRow"
                   BasedOn="{StaticResource DataGridRowStyle}" />
        </Style.Resources>
        <Setter Property="Background"
                Value="{DynamicResource {x:Static SystemColors.ControlBrushKey}}" />
        <Setter Property="FontSize"
                Value="12"></Setter>
        <Setter Property="Foreground"
                Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />
        <Setter Property="BorderBrush"
                Value="Transparent" />
        <Setter Property="BorderThickness"
                Value="0" />
        <Setter Property="HorizontalGridLinesBrush"
                Value="#ECF0F1" />
        <Setter Property="GridLinesVisibility"
                Value="Horizontal" />
        <Setter Property="RowDetailsVisibilityMode"
                Value="VisibleWhenSelected" />
        <Setter Property="ScrollViewer.CanContentScroll"
                Value="true" />
        <Setter Property="ScrollViewer.PanningMode"
                Value="Both" />
        <Setter Property="Stylus.IsFlicksEnabled"
                Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGrid}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}"
                            SnapsToDevicePixels="True">
                        <Grid>
                            <TextBlock x:Name="EmptyContentTextBlock"
                                       Text="No items found. Please add an item."
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Visibility="Collapsed" />
                            <ScrollViewer x:Name="DG_ScrollViewer"
                                          Focusable="false">
                                <ScrollViewer.Template>
                                    <ControlTemplate TargetType="{x:Type ScrollViewer}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="*" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>
                                            <Button Command="{x:Static DataGrid.SelectAllCommand}"
                                                    Focusable="false"
                                                    Style="{DynamicResource {ComponentResourceKey ResourceId=DataGridSelectAllButtonStyle, TypeInTargetAssembly={x:Type DataGrid}}}"
                                                    Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.All}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"
                                                    Width="{Binding CellsPanelHorizontalOffset, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" />
                                            <DataGridColumnHeadersPresenter x:Name="PART_ColumnHeadersPresenter"
                                                                            Grid.Row="0"
                                                                            Grid.Column="1"
                                                                            Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.Column}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" />
                                            <ScrollContentPresenter x:Name="PART_ScrollContentPresenter"
                                                                    CanContentScroll="{TemplateBinding CanContentScroll}"
                                                                    Grid.ColumnSpan="2"
                                                                    Grid.Column="0"
                                                                    Grid.Row="1" />
                                            <ScrollBar x:Name="PART_VerticalScrollBar"
                                                       Grid.Column="2"
                                                       Maximum="{TemplateBinding ScrollableHeight}"
                                                       Orientation="Vertical"
                                                       Grid.Row="1"
                                                       Value="{Binding VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                                       ViewportSize="{TemplateBinding ViewportHeight}"
                                                       Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}" />
                                            <Grid Grid.Column="1"
                                                  Grid.Row="2">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="{Binding NonFrozenColumnsViewportHorizontalOffset, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" />
                                                    <ColumnDefinition Width="*" />
                                                </Grid.ColumnDefinitions>
                                                <ScrollBar x:Name="PART_HorizontalScrollBar"
                                                           Grid.Column="1"
                                                           Maximum="{TemplateBinding ScrollableWidth}"
                                                           Orientation="Horizontal"
                                                           Value="{Binding HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                                           ViewportSize="{TemplateBinding ViewportWidth}"
                                                           Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}" />
                                            </Grid>
                                        </Grid>
                                    </ControlTemplate>
                                </ScrollViewer.Template>
                                <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                            </ScrollViewer>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Items.Count, RelativeSource={RelativeSource TemplatedParent}}"
                                     Value="0">
                            <Setter TargetName="EmptyContentTextBlock"
                                    Property="Visibility"
                                    Value="Visible" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsGrouping"
                               Value="true" />
                    <Condition Property="VirtualizingPanel.IsVirtualizingWhenGrouping"
                               Value="false" />
                </MultiTrigger.Conditions>
                <Setter Property="ScrollViewer.CanContentScroll"
                        Value="false" />
            </MultiTrigger>
        </Style.Triggers>
    </Style>
    <!-- DataGridColumnHeader Style -->
    <Style x:Key="DataGridColumnHeader"
           TargetType="{x:Type DataGridColumnHeader}">
        <Setter Property="VerticalContentAlignment"
                Value="Center" />
        <Setter Property="Background"
                Value=" White" />
        <Setter Property="FontSize"
                Value="13" />
        <Setter Property="Foreground"
                Value="Black" />
        <Setter Property="Padding"
                Value="6" />
        <Setter Property="FontWeight"
                Value="SemiBold" />
        <Setter Property="MinHeight"
                Value="40" />
        <Setter Property="BorderThickness"
                Value="0.5 0 0.5 0" />
        <Setter Property="BorderBrush"
                Value="#ECF0F1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGridColumnHeader}">
                    <Grid>
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Padding="{TemplateBinding Padding}">
                            <Grid Margin="{TemplateBinding Margin}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  RecognizesAccessKey="True"
                                                  SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                                <StackPanel Grid.Column="1"
                                            Margin="10 0 0 0"
                                            Orientation="Horizontal"
                                            x:Name="SortArrowsPanel"
                                            HorizontalAlignment="Center">
                                    <Viewbox Width="12"
                                             Height="12"
                                             HorizontalAlignment="Center"
                                             VerticalAlignment="Center">
                                        <Canvas Width="24"
                                                Height="24">
                                            <Path x:Name="SortUpIcon"
                                                  Fill="Black"
                                                  Data="m18.787 9.473s-4.505-4.502-6.259-6.255c-.147-.146-.339-.22-.53-.22-.192 0-.384.074-.531.22-1.753 1.753-6.256 6.252-6.256 6.252-.147.147-.219.339-.217.532.001.19.075.38.221.525.292.293.766.295 1.056.004l4.977-4.976v14.692c0 .414.336.75.75.75.413 0 .75-.336.75-.75v-14.692l4.978 4.978c.289.29.762.287 1.055-.006.145-.145.219-.335.221-.525.002-.192-.07-.384-.215-.529z" />
                                        </Canvas>
                                    </Viewbox>
                                    <Viewbox Width="12"
                                             Height="12"
                                             HorizontalAlignment="Center"
                                             VerticalAlignment="Center">
                                        <Canvas Width="24"
                                                Height="24">
                                            <Path x:Name="SortDownIcon"
                                                  Fill="Black"
                                                  Data="m5.214 14.522s4.505 4.502 6.259 6.255c.146.147.338.22.53.22s.384-.073.53-.22c1.754-1.752 6.249-6.244 6.249-6.244.144-.144.216-.334.217-.523 0-.193-.074-.386-.221-.534-.293-.293-.766-.294-1.057-.004l-4.968 4.968v-14.692c0-.414-.336-.75-.75-.75s-.75.336-.75.75v14.692l-4.979-4.978c-.289-.289-.761-.287-1.054.006-.148.148-.222.341-.221.534 0 .189.071.377.215.52z" />
                                        </Canvas>
                                    </Viewbox>
                                </StackPanel>
                            </Grid>
                        </Border>
                        <Thumb x:Name="PART_LeftHeaderGripper"
                               HorizontalAlignment="Left"
                               Style="{StaticResource ColumnHeaderGripperStyle}" />
                        <Thumb x:Name="PART_RightHeaderGripper"
                               HorizontalAlignment="Right"
                               Style="{StaticResource ColumnHeaderGripperStyle}" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="SortDirection"
                                 Value="Ascending">
                            <Setter TargetName="SortUpIcon"
                                    Property="Fill"
                                    Value="Black" />
                            <Setter TargetName="SortDownIcon"
                                    Property="Fill"
                                    Value="{StaticResource Gray300}" />
                        </Trigger>
                        <Trigger Property="SortDirection"
                                 Value="Descending">
                            <Setter TargetName="SortUpIcon"
                                    Property="Fill"
                                    Value="{StaticResource Gray300}" />
                            <Setter TargetName="SortDownIcon"
                                    Property="Fill"
                                    Value="Black" />
                        </Trigger>
                        <Trigger Property="SortDirection"
                                 Value="{x:Null}">
                            <Setter TargetName="SortUpIcon"
                                    Property="Fill"
                                    Value="{StaticResource Gray300}" />
                            <Setter TargetName="SortDownIcon"
                                    Property="Fill"
                                    Value="{StaticResource Gray300}" />
                        </Trigger>
                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource Self}, Path=CanUserSort}"
                                     Value="False">
                            <Setter TargetName="SortArrowsPanel"
                                    Property="Visibility"
                                    Value="Collapsed" />
                            <Setter TargetName="SortUpIcon"
                                    Property="Visibility"
                                    Value="Collapsed" />
                            <Setter TargetName="SortDownIcon"
                                    Property="Visibility"
                                    Value="Collapsed" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style x:Key="DataGridColumnHeaderWithBorders"
           TargetType="{x:Type DataGridColumnHeader}"
           BasedOn="{StaticResource DataGridColumnHeader}">
        <Setter Property="BorderThickness"
                Value="0 1 1 1"/>
        <Setter Property="MinHeight"
                Value="30" />
        <Setter Property="BorderBrush"
                Value="{StaticResource Gray200}"/>
    </Style>
</ResourceDictionary>