<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Pages.UserFamiliesPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             xmlns:pages="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages"
             xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls"
             mc:Ignorable="d"
             d:DesignHeight="900" d:DesignWidth="900">
    <UserControl.Resources>
        <ResourceDictionary>
            <converters:RemoveQuotesConverter x:Key="RemoveQuotesConverter" />
            <converters:AsyncImageConverter x:Key="AsyncImageConverter" />
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" />
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <controls:HeaderBar PageName="Families"/>
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Loading Indicator -->
            <Grid Grid.Row="0" Grid.RowSpan="2"
                  Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                  Background="White">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <Ellipse Style="{StaticResource LoadingSpinner}" Margin="0,0,0,20"/>
                    <TextBlock Text="Loading families..."
                               Style="{StaticResource TextBase}"
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
            <DockPanel Margin="10 0"
                                    VerticalAlignment="Center">
                <ComboBox HorizontalAlignment="Left"
                          Style="{StaticResource ComboBoxStyleFilter}"
                          Tag="Categories"
                          Width="200"
                          ItemsSource="{Binding Categories}"
                          SelectedItem="{Binding SelectedCategory}"
                          DisplayMemberPath="CategoryName"/>
                <ComboBox Style="{StaticResource ComboBoxStyleFilter}"
                          Tag="Revit version" Width="200" Margin="10 0"
                          ItemsSource="{Binding RevitVersions}"
                          SelectedItem="{Binding SelectedRevitVersion}"
                          HorizontalAlignment="Left"/>
                <Button Width="80"
                        FontWeight="Normal"
                        Height="25"
                        Style="{StaticResource ButtonOutlinedRed}"
                        Command="{Binding ResetFiltersCommand}"
                        HorizontalAlignment="Right">
                    X Clear All
                </Button>
            </DockPanel>
            <DataGrid
             Style="{DynamicResource DataGridWithoutBorders}"
             Background="#F8F9FF"
             ColumnHeaderStyle="{DynamicResource DataGridColumnHeader}"
             ItemsSource="{Binding Families}"
             HorizontalScrollBarVisibility="Hidden"
             Grid.Row="1"
             AutoGenerateColumns="False"
             CanUserDeleteRows="False"
             CanUserResizeColumns="True"
             Margin="10,0"
             CanUserAddRows="False"
             CanUserReorderColumns="False"
             HeadersVisibility="Column"
             SelectionMode="Single"
             SelectionUnit="FullRow"
             IsReadOnly="True"
             x:Name="DfTypes">
                <DataGrid.Columns>
                    <DataGridTemplateColumn Header="Image"
                                            Width="SizeToCells"
                                            IsReadOnly="True">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Image Width="80"
                                       Height="80"
                                       Stretch="Uniform">
                                    <Image.Source>
                                        <Binding Path="LibraryItem.FamilyImagePath"
                                                 Converter="{StaticResource AsyncImageConverter}">
                                            <Binding.FallbackValue>
                                                <BitmapImage UriSource="/FamiliesLibrary;component/Assets/noImage.png" />
                                            </Binding.FallbackValue>
                                        </Binding>
                                    </Image.Source>
                                </Image>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Name"
                                            Width="*"
                                            MinWidth="100"
                                            SortMemberPath="ProductName">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                 Text="{Binding LibraryItem.ProductName, Converter={StaticResource RemoveQuotesConverter}}"
                                 TextWrapping="Wrap"
                                 FontWeight="SemiBold"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="File Name"
                                            Width="*"
                                            MinWidth="120"
                                            SortMemberPath="Name">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding LibraryItem.Name}"
                                           TextWrapping="Wrap"
                                           FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Version"
                                            Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding LibraryItem.Version, Converter={StaticResource RemoveQuotesConverter}}"
                                           TextWrapping="Wrap"
                                           FontWeight="SemiBold"
                                           ToolTip="{Binding LibraryItem.DateCreated}"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Revit"
                                            Width="80"
                                            CanUserSort="False">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border Background="#1670aa"
                                        CornerRadius="5"
                                        Padding="10 0"
                                        Height="20"
                                        BorderThickness="0"
                                        VerticalAlignment="Center"
                                        HorizontalAlignment="Center">
                                    <TextBlock Text="{Binding LibraryItem.RevitVersion}"
                                            Foreground="White"
                                            FontWeight="Bold"
                                            VerticalAlignment="Center"
                                            HorizontalAlignment="Center" />
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Version Notes"
                                            Width="*"
                                            MinWidth="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding LibraryItem.ChangesDescription}"
                                           TextWrapping="Wrap"
                                           FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Category"
                                            Width="120"
                                            SortMemberPath="Category.CategoryName">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding LibraryItem.Category.CategoryName}"
                                           TextWrapping="Wrap"
                                           FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Created By"
                                            Width="120"
                                            SortMemberPath="CreatedBy">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding LibraryItem.CreatedBy}"
                                           TextWrapping="Wrap"
                                           FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Updated"
                                            Width="100"
                                            SortMemberPath="LastDateUpdated">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding LibraryItem.LastDateUpdated}"
                                           TextWrapping="Wrap"
                                           FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Actions"
                                            Width="*">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Margin="2 5"
                                            Command="{Binding DataContext.DownloadToRevitCommand,
                                            RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                            CommandParameter="{Binding}"
                                            IsEnabled="{Binding DataContext.IsLoading,
                                            Converter={StaticResource InverseBooleanConverter}, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                            Style="{StaticResource RoundIconButton}">
                                        <StackPanel>
                                            <ContentControl Template="{StaticResource LoadToProjectIcon}" HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                    <Button Margin="2 5"
                                            Command="{Binding DataContext.DownloadToPcCommand,
                                            RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource RoundIconButton}">
                                        <ContentControl Template="{StaticResource DownloadIcon}" HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"/>
                                    </Button>
                                    <Button Margin="2 5"
                                         Command="{Binding DataContext.ToggleRowDetailsCommand,
                                            RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"
                                         CommandParameter="{Binding}"
                                         Style="{StaticResource RoundIconButton}"
                                         ToolTip="Open previous versions">
                                        <ContentControl Template="{StaticResource CodeForkIcon}" HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
                <DataGrid.RowDetailsTemplate>
                    <DataTemplate>
                        <Grid>
                            <DataGrid x:Name="DetailsDataGrid"
                                   ItemsSource="{Binding RemainingVersions}"
                                   Style="{DynamicResource DataGridWithoutBorders}"
                                   ColumnHeaderStyle="{DynamicResource DataGridColumnHeader}"
                                   AutoGenerateColumns="False"
                                   CanUserDeleteRows="False"
                                   HorizontalScrollBarVisibility="Auto"
                                   CanUserResizeColumns="True"
                                   Margin="10,0"
                                   CanUserAddRows="False"
                                   CanUserReorderColumns="False"
                                   HeadersVisibility="Column"
                                   SelectionMode="Single"
                                   SelectionUnit="FullRow"
                                   IsReadOnly="True">
                                <DataGrid.Columns>
                                    <DataGridTemplateColumn Header="Image"
                                                            Width="SizeToCells"
                                                            IsReadOnly="True">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Image Width="80"
                                                       Height="80"
                                                       Stretch="Uniform">
                                                    <Image.Source>
                                                        <Binding Path="FamilyImagePath"
                                                                 Converter="{StaticResource AsyncImageConverter}">
                                                            <Binding.FallbackValue>
                                                                <BitmapImage UriSource="/FamiliesLibrary;component/Assets/noImage.png" />
                                                            </Binding.FallbackValue>
                                                        </Binding>
                                                    </Image.Source>
                                                </Image>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>

                                    <DataGridTemplateColumn Header="Name"
                                                            Width="140"
                                                            SortMemberPath="ProductName">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding ProductName, Converter={StaticResource RemoveQuotesConverter}}"
                                                           TextWrapping="Wrap"
                                                           FontWeight="SemiBold" />
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>

                                    <DataGridTemplateColumn Header="File Name"
                                                            Width="200"
                                                            SortMemberPath="Name">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Name}"
                                                           TextWrapping="Wrap"
                                                           FontWeight="Light" />
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>

                                    <DataGridTemplateColumn Header="Version" Width="100">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Version,
                                                    Converter={StaticResource RemoveQuotesConverter}}"
                                                           TextWrapping="Wrap"
                                                           FontWeight="SemiBold" />
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>

                                    <DataGridTemplateColumn Header="Revit"
                                                            Width="80"
                                                            CanUserSort="False">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Border Background="#1670aa"
                                                        CornerRadius="5"
                                                        Padding="10 0"
                                                        Height="20"
                                                        BorderThickness="0"
                                                        VerticalAlignment="Center"
                                                        HorizontalAlignment="Center">
                                                    <TextBlock Text="{Binding RevitVersion}"
                                                               Foreground="White"
                                                               FontWeight="Bold"
                                                               VerticalAlignment="Center"
                                                               HorizontalAlignment="Center" />
                                                </Border>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    <DataGridTemplateColumn Header="Version Notes"
                                                            Width="130">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding ChangesDescription}"
                                                           TextWrapping="Wrap"
                                                           FontWeight="Light" />
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    <DataGridTemplateColumn Header="Created By"
                                                            Width="120"
                                                            SortMemberPath="CreatedBy">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding CreatedBy}"
                                                           TextWrapping="Wrap"
                                                           FontWeight="Light" />
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    <DataGridTemplateColumn Header="Created"
                                                            Width="100"
                                                            SortMemberPath="DateCreated">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding DateCreated}"
                                                           TextWrapping="Wrap"
                                                           FontWeight="Light" />
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    <DataGridTemplateColumn Header="Updated"
                                                            Width="100"
                                                            SortMemberPath="LastDateUpdated">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding LastDateUpdated}"
                                                           TextWrapping="Wrap"
                                                           FontWeight="Light" />
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    <DataGridTemplateColumn Header="Actions"
                                                            Width="*"
                                                            MinWidth="100">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Margin="2 5"
                                                            Command="{Binding DataContext.DownloadToRevitCommand,
                                                            RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                                            CommandParameter="{Binding}"
                                                            IsEnabled="{Binding DataContext.IsLoading, Converter={StaticResource InverseBooleanConverter}, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                                            Style="{StaticResource RoundIconButton}">
                                                        <ContentControl Template="{StaticResource LoadToProjectIcon}" HorizontalAlignment="Center"
                                                                        VerticalAlignment="Center"/>
                                                    </Button>
                                                    <Button Margin="2 5"
                                                            Command="{Binding DataContext.DownloadToPcCommand,
                                                            RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                                            CommandParameter="{Binding}"
                                                            Style="{StaticResource RoundIconButton}">
                                                        <ContentControl Template="{StaticResource DownloadIcon}" HorizontalAlignment="Center"
                                                                        VerticalAlignment="Center"/>
                                                    </Button>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                            <TextBlock x:Name="NoVersionsTextBlock"
                                       Text="No previous versions available."
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Visibility="Collapsed"/>
                        </Grid>
                        <DataTemplate.Triggers>
                            <DataTrigger Binding="{Binding RemainingVersions.Count}"
                                         Value="0">
                                <Setter TargetName="NoVersionsTextBlock"
                                        Property="Visibility"
                                        Value="Visible"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding RemainingVersions.Count}"
                                         Value="0" >
                                <Setter TargetName="DetailsDataGrid"
                                        Property="Visibility"
                                        Value="Collapsed"/>
                            </DataTrigger>
                        </DataTemplate.Triggers>
                    </DataTemplate>
                </DataGrid.RowDetailsTemplate>
                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Setter Property="DetailsVisibility"
                                Value="Collapsed"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsRowDetailsVisible}"
                                         Value="True">
                                <Setter Property="DetailsVisibility"
                                        Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>
        </Grid>
    </Grid>
</UserControl>
