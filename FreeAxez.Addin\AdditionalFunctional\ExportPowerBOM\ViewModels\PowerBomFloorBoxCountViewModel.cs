﻿using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Models;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Spreadsheets;
using NPOI.SS.UserModel;
using System.Collections.Generic;
using System.Linq;
using CellType = NPOI.SS.UserModel.CellType;
using Color = SixLabors.ImageSharp.Color;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.ViewModels
{
    public class PowerBomFloorBoxCountViewModel : ISheetViewModel
    {
        private readonly Color _headerColor = Color.LightGray;
        private readonly HashSet<Border> _borders = new HashSet<Border>()
        {
            Border.Left,
            Border.Top,
            Border.Right,
            Border.Bottom,
        };


        private PowerBomRevision _powerBomRevision;


        public PowerBomFloorBoxCountViewModel(PowerBomRevision powerBomRevision)
        {
            _powerBomRevision = powerBomRevision;
        }


        public string ProjectName { get; private set; }
        public string RevisionDate { get; private set; }
        public string PlanReferenced { get; private set; }
        public string SheetName { get; private set; }
        public List<OrderRow> Report { get; private set; }


        public void Calculate()
        {
            ProjectName = _powerBomRevision.ProjectName;
            RevisionDate = _powerBomRevision.RevisionDate;
            PlanReferenced = $"Power Plan Rev {_powerBomRevision.RevisionNumber}";
            SheetName = $"Floor Box Count";
            Report = CreateReport();
        }

        private List<OrderRow> CreateReport()
        {
            var rows = new List<OrderRow>();

            CreateHeaderRow(ref rows);
            foreach (var group in _powerBomRevision.FloorBoxCountGroups)
            {
                CreateRows(group, ref rows);
                CreateTotalRow(group, ref rows);
            }

            return rows;
        }

        private void CreateHeaderRow(ref List<OrderRow> rows)
        {
            var row = new OrderRow();
            var headers = GetHeaders();
            foreach (var header in headers)
            {
                var cell = new OrderCell()
                {
                    CellStyle = new OrderCellStyle()
                    {
                        Foreground = _headerColor,
                        FontBold = true,
                        Borders = _borders,
                        Alignment = HorizontalAlignment.Center
                    },
                    Value = header
                };
                row.Cells.Add(cell);
            }
            rows.Add(row);
        }

        private void CreateRows(ProductGroup group, ref List<OrderRow> rows)
        {
            // Sub groups rows
            var subGroups = GroupProducts(group.ProductViewModels);

            foreach (var subGroup in subGroups)
            {
                var product = subGroup.First();

                var row = new OrderRow();

                // Component Number
                var cell = new OrderCell()
                {
                    Value = subGroup.First().ComponentNumber,
                    CellStyle = new OrderCellStyle()
                    {
                        Borders = _borders,
                        Alignment = HorizontalAlignment.Right
                    }
                };
                row.Cells.Add(cell);

                // FB Type
                cell = new OrderCell()
                {
                    Value = subGroup.First().FloorBoxType,
                    CellStyle = new OrderCellStyle()
                    {
                        Borders = _borders,
                        Alignment = HorizontalAlignment.Right
                    }
                };
                row.Cells.Add(cell);

                // Count
                cell = new OrderCell()
                {
                    Value = subGroup.Count.ToString(),
                    CellType = CellType.Numeric,
                    CellStyle = new OrderCellStyle()
                    {
                        Borders = _borders,
                        Alignment = HorizontalAlignment.Right
                    }
                };
                row.Cells.Add(cell);

                rows.Add(row);
            }
        }

        private void CreateTotalRow(ProductGroup group, ref List<OrderRow> rows)
        {
            var row = new OrderRow();

            var subGroups = GroupProducts(group.ProductViewModels);

            // Empty cell
            var cell = new OrderCell()
            {
                CellType = CellType.Numeric,
                CellStyle = new OrderCellStyle()
                {
                    Foreground = _headerColor,
                    Borders = _borders,
                }
            };
            row.Cells.Add(cell);

            // Total
            cell = new OrderCell()
            {
                Value = "Total",
                CellStyle = new OrderCellStyle()
                {
                    Foreground = _headerColor,
                    FontBold = true,
                    Borders = _borders,
                    Alignment = HorizontalAlignment.Right,
                }
            };
            row.Cells.Add(cell);

            // Count
            cell = new OrderCell()
            {
                Value = $@"SUM(<<{row.Cells.Count + 1}:{rows.Count - (subGroups.Count - 1)}>>" +
                        $@":<<{row.Cells.Count + 1}:{rows.Count}>>)",

                CellType = CellType.Formula,
                CellStyle = new OrderCellStyle()
                {
                    Foreground = _headerColor,
                    FontBold = true,
                    Borders = _borders,
                    Alignment = HorizontalAlignment.Right
                }
            };
            row.Cells.Add(cell);

            rows.Add(row);
        }

        private List<string> GetHeaders()
        {
            return new List<string>() { "Component Number", "FB Type", "Count" };
        }

        private List<List<ProductViewModel>> GroupProducts(List<ProductViewModel> products)
        {
            return products
                .GroupBy(p => p.ComponentNumber + p.FloorBoxType)
                .OrderBy(g => g.First().ComponentNumber)
                .Select(g => g.OrderBy(p => p.FloorBoxType).ToList())
                .ToList();
        }
    }
}
