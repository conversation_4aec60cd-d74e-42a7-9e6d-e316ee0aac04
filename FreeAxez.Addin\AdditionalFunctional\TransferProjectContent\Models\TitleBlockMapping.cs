﻿using Autodesk.Revit.DB;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Models
{
    public class TitleBlockMapping
    {
        public TitleBlockMapping(FamilySymbol source, FamilySymbol target)
        {
            Source = source;
            Target = target;
            SourceMappingKey = $"{source.Family.Name}:{source.Name}";
        }

        public FamilySymbol Source { get; }
        public FamilySymbol Target { get; }
        public string SourceMappingKey { get; }
    }
}