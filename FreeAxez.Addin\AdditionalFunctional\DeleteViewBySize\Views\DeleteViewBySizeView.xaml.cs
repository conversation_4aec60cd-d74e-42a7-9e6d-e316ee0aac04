﻿using FreeAxez.Addin.AdditionalFunctional.DeleteViewBySize.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace FreeAxez.Addin.AdditionalFunctional.DeleteViewBySize.Views
{
    /// <summary>
    /// Interaction logic for DeleteViewBySizeView.xaml
    /// </summary>
    public partial class DeleteViewBySizeView : Window
    {
        public DeleteViewBySizeView()
        {
            InitializeComponent();
        }


        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            foreach (ViewSize revitView in dataGrid.SelectedItems)
            {
                revitView.IsCheck = (bool)(sender as CheckBox).IsChecked;
            }
        }
    }
}
