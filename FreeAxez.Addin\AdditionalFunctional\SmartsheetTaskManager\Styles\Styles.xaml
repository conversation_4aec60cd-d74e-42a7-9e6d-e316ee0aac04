﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <Style x:Key="TextBlock" TargetType="{x:Type TextBlock}">
        <Style.Triggers>
            <DataTrigger Binding="{Binding IsExecuting}" Value="True">
                <Setter Property="Visibility" Value="Hidden"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding IsExecuting}" Value="False">
                <Setter Property="Visibility" Value="Visible"/>
            </DataTrigger>
            <Trigger Property="FrameworkElement.Visibility" Value="Visible">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="Opacity" To="1" Duration="0:0:1"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="Opacity" To="0" Duration="0:0:1"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.ExitActions>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="CustomScrollBar" TargetType="{x:Type ScrollBar}">
        <Setter Property="Stylus.IsFlicksEnabled" Value="True"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="5">
                        <Track x:Name="PART_Track">
                            <Track.Style>
                                <Style TargetType="{x:Type Track}">
                                    <Style.Triggers>
                                        <Trigger Property="Orientation" Value="Horizontal">
                                            <Setter Property="IsDirectionReversed" Value="False"/>
                                        </Trigger>
                                        <Trigger Property="Orientation" Value="Vertical">
                                            <Setter Property="IsDirectionReversed" Value="True"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Track.Style>
                            <Track.Thumb>
                                <Thumb>
                                    <Thumb.Template>
                                        <ControlTemplate TargetType="{x:Type Thumb}">
                                            <Rectangle x:Name="PART_Thumb"
                                                       Fill="{TemplateBinding Background}"
                                                       Stroke="{TemplateBinding BorderBrush}"
                                                       StrokeThickness="{TemplateBinding BorderThickness}"
                                                       RadiusX="5" RadiusY="5"/>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter TargetName="PART_Thumb"
                                                            Property="Fill"
                                                            Value="LightGray"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Thumb.Template>
                                </Thumb>
                            </Track.Thumb>
                        </Track>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="Orientation" Value="Horizontal">
                <Setter Property="Height" Value="10" />
                <Setter Property="MinHeight" Value="10" />
            </Trigger>
            <Trigger Property="Orientation" Value="Vertical">
                <Setter Property="Width" Value="10" />
                <Setter Property="MinWidth" Value="10" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="ListView" TargetType="{x:Type ListView}">
        <Style.Triggers>
            <DataTrigger Binding="{Binding IsExecuting}" Value="True">
                <Setter Property="Visibility" Value="Collapsed"/>
            </DataTrigger>
            <DataTrigger Binding="{Binding IsExecuting}" Value="False">
                <Setter Property="Visibility" Value="Visible"/>
            </DataTrigger>
            <Trigger Property="FrameworkElement.Visibility" Value="Visible">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="Opacity" To="1" Duration="0:0:1"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="Opacity" To="0" Duration="0:0:1"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.ExitActions>
            </Trigger>
        </Style.Triggers>
        <Setter Property="BorderBrush" Value="Gray"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListView}">
                    <Border CornerRadius="3"
                            BorderThickness="1"
                            BorderBrush="Gray">
                        <ScrollViewer>
                            <ScrollViewer.Resources>
                                <Style TargetType="{x:Type ScrollBar}" BasedOn="{StaticResource CustomScrollBar}"/>
                            </ScrollViewer.Resources>
                            <ItemsPresenter/>
                        </ScrollViewer>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ListViewItemFocusable" TargetType="{x:Type ListViewItem}">
        <Setter Property="Foreground" Value="Black"/>
        <Setter Property="Focusable" Value="True"/>
        <Setter Property="ToolTip" Value="{Binding CellValue}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListViewItem">
                    <Border Background="{TemplateBinding Background}">
                        <GridViewRowPresenter Content="{TemplateBinding Content}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#FFE9F1FB"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="#FFE9F1FB"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="ListViewItemNotFocusable" TargetType="{x:Type ListViewItem}">
        <Setter Property="Foreground" Value="Black"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListViewItem">
                    <Border Background="{TemplateBinding Background}">
                        <GridViewRowPresenter Content="{TemplateBinding Content}" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
                <Setter Property="Opacity" Value="5"/>
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="CommonButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="Background" Value="#3576BA"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border Background="{TemplateBinding Background}"
                        CornerRadius="3">
                        <ContentPresenter HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#7FB9E5"/>
                <Setter Property="Cursor" Value="Hand"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="#FF7796B9"/>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>