using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services
{
    /// <summary>
    /// Service for validating low voltage path generation prerequisites
    /// </summary>
    public class LowVoltageValidationService
    {
        private readonly Document _document;
        private readonly View _activeView;

        public LowVoltageValidationService(Document document, View activeView)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _activeView = activeView ?? throw new ArgumentNullException(nameof(activeView));
        }

        /// <summary>
        /// Validates if the current view is a floor plan
        /// </summary>
        public ValidationResult ValidateViewType()
        {
            if (_activeView.ViewType != ViewType.FloorPlan)
            {
                return ValidationResult.Failure("This command can only be executed on a floor plan view.");
            }

            return ValidationResult.Success(0, 0);
        }

        /// <summary>
        /// Validates outlets and lines for the operation
        /// </summary>
        public ValidationResult ValidateData(List<CurveElement> lines, List<FamilyInstance> outlets, LowVoltagePathSettings settings)
        {
            var result = new ValidationResult { IsValid = true };

            // Validate lines
            if (lines.Count == 0)
            {
                result.AddError("No LV or MC lines found in the active view.");
            }
            else
            {
                result.ValidLinesCount = lines.Count;
            }

            // Validate outlets
            if (outlets.Count == 0)
            {
                result.AddError("No electrical fixtures with low voltage parameters found in the current view.");
            }
            else
            {
                result.ValidOutletsCount = outlets.Count;
                
                // Check for outlets that already have railings
                var outletsWithRailings = CountOutletsWithExistingRailings(outlets);
                result.OutletsWithExistingRailingsCount = outletsWithRailings;
                
                if (outletsWithRailings > 0)
                {
                    result.AddWarning($"{outletsWithRailings} outlet(s) already have connected railings and will be skipped.");
                }

                // Check if all outlets would be skipped
                if (outletsWithRailings >= outlets.Count)
                {
                    result.AddError("All outlets already have connected railings. No new railings will be created.");
                }
            }

            // Validate settings
            if (settings?.SelectedRailingType == null)
            {
                result.AddError("No railing type selected.");
            }

            // Validate selected elements scope
            if (settings?.ScopeType == ScopeType.SelectedElements)
            {
                var selectedIds = RevitManager.UIDocument.Selection.GetElementIds();
                if (selectedIds.Count == 0)
                {
                    result.AddError("Selected elements scope chosen but no elements are selected.");
                }
            }

            return result;
        }

        /// <summary>
        /// Validates railing types availability
        /// </summary>
        public ValidationResult ValidateRailingTypes()
        {
            var railingTypes = new FilteredElementCollector(_document)
                .OfClass(typeof(RailingType))
                .Cast<RailingType>()
                .ToList();

            if (railingTypes.Count == 0)
            {
                return ValidationResult.Failure("No railing types found in the project. Please load railing families first.");
            }

            return ValidationResult.Success(0, 0);
        }

        /// <summary>
        /// Validates annotation families availability
        /// </summary>
        public ValidationResult ValidateAnnotationFamilies()
        {
            var annotationSymbols = new FilteredElementCollector(_document)
                .OfClass(typeof(FamilySymbol))
                .OfCategory(BuiltInCategory.OST_GenericAnnotation)
                .Cast<FamilySymbol>()
                .Where(s => s.FamilyName == Constants.LowVoltageConstants.AnnotationFamilyName)
                .ToList();

            if (annotationSymbols.Count == 0)
            {
                var result = ValidationResult.Success(0, 0);
                result.AddWarning($"Annotation family '{Constants.LowVoltageConstants.AnnotationFamilyName}' not found. Annotations will not be created.");
                return result;
            }

            return ValidationResult.Success(0, 0);
        }

        /// <summary>
        /// Performs comprehensive validation before execution
        /// </summary>
        public ValidationResult ValidateAll(List<CurveElement> lines, List<FamilyInstance> outlets, LowVoltagePathSettings settings)
        {
            var results = new List<ValidationResult>
            {
                ValidateViewType(),
                ValidateData(lines, outlets, settings),
                ValidateRailingTypes(),
                ValidateAnnotationFamilies()
            };

            // Combine all validation results
            var combinedResult = new ValidationResult { IsValid = true };

            foreach (var result in results)
            {
                if (!result.IsValid)
                {
                    combinedResult.IsValid = false;
                }

                combinedResult.ErrorMessages.AddRange(result.ErrorMessages);
                combinedResult.WarningMessages.AddRange(result.WarningMessages);

                // Use the data counts from the main data validation
                if (result.ValidOutletsCount > 0 || result.ValidLinesCount > 0)
                {
                    combinedResult.ValidOutletsCount = result.ValidOutletsCount;
                    combinedResult.ValidLinesCount = result.ValidLinesCount;
                    combinedResult.OutletsWithExistingRailingsCount = result.OutletsWithExistingRailingsCount;
                }
            }

            return combinedResult;
        }

        /// <summary>
        /// Counts outlets that already have railings connected to them
        /// </summary>
        private int CountOutletsWithExistingRailings(List<FamilyInstance> outlets)
        {
            // TODO: Implement logic to check if outlets already have railings
            // This would involve checking proximity to existing railings
            // For now, return 0 as placeholder
            return 0;
        }
    }
}
