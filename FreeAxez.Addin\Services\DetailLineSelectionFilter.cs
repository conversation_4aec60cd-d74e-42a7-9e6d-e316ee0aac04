﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.Services;

public class DetailLineSelectionFilter : ISelectionFilter
{
    public bool AllowElement(Element elem)
    {
        return elem is CurveElement curveElement &&
               curveElement.Category.Id.GetIntegerValue() == (int)BuiltInCategory.OST_Lines;
    }

    public bool AllowReference(Reference reference, XYZ position)
    {
        return true;
    }
}