﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.CutUnitByVoid.Utils
{
    public class CuttingInstanceSelectionFilter : ISelectionFilter
    {
        public bool AllowElement(Element elem)
        {
            if (elem.Category?.Id.GetIntegerValue() != (int)BuiltInCategory.OST_GenericModel)  
            {
                return false;
            }

            return elem.get_Geometry(new Options() { IncludeNonVisibleObjects = true })
                .Where(x => x is Solid)
                .Cast<Solid>()
                .Any(x => x.Volume < 0);
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            throw new NotImplementedException();
        }
    }
}