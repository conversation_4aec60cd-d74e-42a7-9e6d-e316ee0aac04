﻿using Autodesk.Revit.DB;
using System.Collections.Generic;
using System.Linq;
using FreeAxez.Addin.Models;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Utils;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBOM.Utils;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Models
{
    public class ProductGroup
    {
        private readonly List<Product> _products;


        public ProductGroup(List<Product> products)
        {
            _products = products;

            ProductViewModels = products.Select(p => new ProductViewModel(p)).ToList();
            ProductGroupName = GetProductGroupName(_products.First());
            FloorBoxType = GetFloorBoxType(_products.First());
        }


        public List<Product> Products => _products;
        public List<ProductViewModel> ProductViewModels { get; }
        public string ProductGroupName { get; }
        public string FloorBoxType { get; }


        public static List<ProductGroup> GroupProducts(List<Product> products)
        {
            // GetFloorBoxType() is designed to create separate groups for boxes with different FB Type values
            return products
                .GroupBy(p => GetProductGroupName(p) + GetFloorBoxType(p)) 
                .Select(g => new ProductGroup(g.ToList()))
                .ToList();
        }

        public static List<ProductGroup> GroupFloorBoxes(List<Product> products)
        {
            return products
                .Where(p => p is FloorBox)
                .GroupBy(fb => (fb as FloorBox).ComponentNumber)
                .Select(g => new ProductGroup(g.OrderBy(fb => (fb as FloorBox).FloorBoxType).ToList()))
                .ToList();
        }

        private static string GetProductGroupName(Product product)
        {
            if (PowerProductCollector.IsAccessory(product))
            {
                return "Accessory Category";
            }

            switch (product)
            {
                case FloorBox floorBox:
                    var symbolName = (floorBox.Element as FamilyInstance).Symbol.Name;
                    return $"{floorBox.ProductName} {symbolName}";
                default:
                    return product.ProductName;
            }
        }

        private static string GetFloorBoxType(Product product)
        {
            return product is FloorBox floorBox ? floorBox.FloorBoxType : "";
        }
    }
}
