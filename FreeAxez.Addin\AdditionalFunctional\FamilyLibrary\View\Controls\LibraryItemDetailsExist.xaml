<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls.LibraryItemDetailsExist"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls"
             mc:Ignorable="d">
    <UserControl.DataContext>
        <controls:LibraryItemDetailsExistVm></controls:LibraryItemDetailsExistVm>
    </UserControl.DataContext>
    <UserControl.Resources>
        <ResourceDictionary>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid Margin="0 0 0 5">
        <Border CornerRadius="5" 
                BorderBrush= "LightGray" 
                BorderThickness="1">
            <Grid Margin="5 5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="30"/>
                </Grid.ColumnDefinitions>
                <Grid Grid.Column="0" 
                      Margin="5 0" VerticalAlignment="Top" Height="150">
                    <Image x:Name="FamilyImage"
                           Width="150"
                           Height="150"
                           Source="{Binding FamilyPreview}"
                           Stretch="Uniform" />
                    <Button x:Name="ChooseImage"
                            VerticalAlignment="Bottom"
                            HorizontalAlignment="Right"
                            Command="{Binding ChooseImageCommand}"
                            Style="{StaticResource RoundIconButton}">
                        <ContentControl Template="{StaticResource PencilIcon}" HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Button>
                </Grid>
                <Grid Grid.Column="1" Margin="5 0">
                    <Grid.RowDefinitions>
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition Height="40"/>
                        <RowDefinition />
                    </Grid.RowDefinitions>
                    <TextBlock
                        TextTrimming="CharacterEllipsis"
                        MaxHeight="100"
                        Text="{Binding FileName, StringFormat='File Name: {0}'}" Style="{StaticResource TextBase}" />
                    <TextBlock Grid.Row="1"
                               Text="{Binding ProductName, StringFormat='Name: {0}'}"
                               Style="{StaticResource TextBase}" />
                    <TextBlock Grid.Row="2"
                               Text="{Binding Version, StringFormat='Version: {0}'}"
                               Style="{StaticResource TextBase}" />
                    <TextBlock Grid.Row="3"
                               Text="{Binding RevitVersion, StringFormat='Revit: {0}'}"
                               Style="{StaticResource TextBase}" Foreground="{StaticResource Blue500}" />
                    <TextBlock Grid.Row="4"
                               Text="{Binding Description, StringFormat='Description: {0}'}"
                               Style="{StaticResource TextBase}"
                               TextTrimming="CharacterEllipsis"
                               MaxHeight="100" TextWrapping="Wrap" />
                    <TextBlock Grid.Row="5"
                               Text="{Binding CreatedBy, StringFormat='Created By: {0}'}"
                               Style="{StaticResource TextBase}"
                               TextTrimming="CharacterEllipsis"
                               MaxHeight="100" TextWrapping="Wrap" />
                    <TextBlock Grid.Row="6"
                               Text="{Binding DateCreated, StringFormat='Date Created: {0}'}"
                               Style="{StaticResource TextBase}"
                               TextTrimming="CharacterEllipsis"
                               MaxHeight="100" TextWrapping="Wrap" />
                    <TextBlock Grid.Row="7"
                               Text="{Binding LastDateUpdated, StringFormat='Last Date Updated: {0}'}"
                               Style="{StaticResource TextBase}"
                               TextTrimming="CharacterEllipsis"
                               MaxHeight="100" TextWrapping="Wrap" />
                    <ComboBox Grid.Row="8" 
                              Tag="Select Family Category"
                              Width="250"
                              HorizontalAlignment="Left"
                              ItemsSource="{Binding Categories}"
                              SelectedValuePath="Id"
                              SelectedValue="{Binding SelectedCategoryId}"
                              Style="{StaticResource ComboBoxSmStyleFa}">
                        <ComboBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding CategoryName}" />
                                    <TextBlock Text=" (FA)" 
                                               Foreground="Green"
                                               Margin="5,0,0,0"
                                               Visibility="{Binding IsFreeAxezCategory, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                </StackPanel>
                            </DataTemplate>
                        </ComboBox.ItemTemplate>
                    </ComboBox>
                    <StackPanel Grid.Row="9">
                        <TextBlock Text="Version Notes"
                                   Margin="0 5"
                                   Style="{StaticResource TextH6}"/>
                        <TextBox Text="{Binding VersionNotes, 
                            UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                 Style="{StaticResource UiTextBox}"
                                 Height="100" TextWrapping="Wrap"
                                 Tag="Enter family version notes"
                                 HorizontalAlignment="Left"
                                 Width="400"
                                 Margin="0 0 5 0"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
