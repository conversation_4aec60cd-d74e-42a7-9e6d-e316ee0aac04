﻿using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages;

public class UnmatchedFamiliesPageVm : BasePageVm
{
    private ObservableCollection<UnmatchedFamilySymbol> _unmatchedFamilies;

    public UnmatchedFamiliesPageVm()
    {
        LoadFamiliesCommand = new AsyncRelayCommand(async () => await LoadFamilies());
        Task.Run(LoadFamilies);
    }

    public ICommand LoadFamiliesCommand { get; }

    public ObservableCollection<UnmatchedFamilySymbol> UnmatchedFamilies
    {
        get => _unmatchedFamilies;
        set
        {
            _unmatchedFamilies = value;
            OnPropertyChanged();
        }
    }

    private async Task LoadFamilies()
    {
        try
        {
            var result = await DataLoader.LoadUnmatchedFamiliesPageData(RevitManager.Document);
            UnmatchedFamilies = result.UnmatchedFamilies;
        }
        catch (Exception ex)
        {
            LogHelper.Error($"An error occurred: {ex.Message}");
        }
    }
}