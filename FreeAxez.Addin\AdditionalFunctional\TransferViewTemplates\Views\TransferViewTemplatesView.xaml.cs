﻿using System.Windows;
using FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Models;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Views
{
    public partial class TransferViewTemplatesView : Window
    {
        public TransferViewTemplatesView()
        {
            InitializeComponent();

            var viewModel = new TransferViewTemplatesViewModel();

        }

        private void DataGridRow_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            var row = sender as DataGridRow;
            if (row != null && !IsCheckboxClicked(e))
            {
                var template = row.Item as RevitViewTemplate;
                if (template != null)
                {
                    template.IsChecked = !template.IsChecked;
                }
            }
        }

        private bool IsCheckboxClicked(MouseButtonEventArgs e)
        {
            var source = e.OriginalSource as DependencyObject;
            while (source != null && !(source is CheckBox) && !(source is DataGridCell))
            {
                source = VisualTreeHelper.GetParent(source);
            }
            return source is CheckBox;
        }
    }
}