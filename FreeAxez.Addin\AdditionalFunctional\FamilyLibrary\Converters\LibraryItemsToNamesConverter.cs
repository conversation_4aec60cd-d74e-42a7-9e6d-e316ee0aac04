using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows.Data;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters;

public class LibraryItemsToNamesConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is IList<LibraryItemDto> items)
        {
            if (items.Count == 0) return "There are no families in this category";

            return string.Join(", ", items.Select(i => i.Name));
        }

        return "Invalid data";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
