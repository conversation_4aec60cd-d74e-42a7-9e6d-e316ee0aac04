﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Controls"
                    xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Converters">

    <Style TargetType="{x:Type controls:PlaceholderTextBox}" BasedOn="{StaticResource {x:Type TextBox}}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type controls:PlaceholderTextBox}">
                    <ControlTemplate.Resources>
                        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
                        <converters:AddLeftPaddingConverter x:Key="AddLeftPaddingConverter"/>
                    </ControlTemplate.Resources>
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid>
                            <TextBlock Padding="{TemplateBinding Padding,
                                                                 Converter={StaticResource AddLeftPaddingConverter},
                                                                 ConverterParameter=3}"
                                       VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                       HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                       Panel.ZIndex="1"
                                       IsHitTestVisible="False"
                                       Opacity="0.5"
                                       Text="{TemplateBinding Placeholder}"
                                       Visibility="{TemplateBinding IsEmpty,
                                            Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            <ScrollViewer x:Name="PART_ContentHost"/>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>