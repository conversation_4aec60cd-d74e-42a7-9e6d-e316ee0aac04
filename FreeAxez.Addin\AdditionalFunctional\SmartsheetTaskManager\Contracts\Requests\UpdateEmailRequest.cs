﻿using System.Collections.Generic;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Contracts.Requests;

public class UpdateEmailRequest
{
    public IList<long> ColumnIds { get; set; } = null!;
    public IList<long> RowIds { get; set; } = null!;
    public string Message { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string SendTo { get; set; } = string.Empty;
}
