﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class Grommet : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_ElectricalFixtures,
            FamilyNamesContains = new List<string>()
            {
                "Grommet"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public Grommet(Element element) : base(element)
        {
        }

        public static List<Grommet> Collect()
        {
            return FamilyCollector.Instances.Where(i => i.SuperComponent == null).Select(g => new Grommet(g)).ToList();
        }

        public static List<FamilyInstance> CollectInstances()
        {
            return FamilyCollector.Instances.Where(i => i.SuperComponent == null).ToList();
        }

        public static ISelectionFilter CreateSelectionFilter()
        {
            return new GrommetSelectionFilter();
        }

        private class GrommetSelectionFilter : ISelectionFilter
        {
            private List<ElementId> _symbolIds;

            public GrommetSelectionFilter()
            {
                _symbolIds = FamilyCollector.Symbols.Select(s => s.Id).ToList();
            }

            public bool AllowElement(Element elem)
            {
                if (elem is FamilyInstance familyInstance)
                {
                    var isValidSymbol = _symbolIds.Any(s => familyInstance.Symbol.Id.Equals(s));
                    bool isNotSuperComponent = familyInstance.SuperComponent is null;

                    return isValidSymbol && isNotSuperComponent;
                }

                return false;
            }

            public bool AllowReference(Reference reference, XYZ position)
            {
                return true;
            }
        }
    }
}
