﻿using System.Collections.Generic;
using FreeAxez.Core.Utilities;

namespace FreeAxez.Core.GeometryModel
{
    // TODO Rename Vector2?
    public class PointModel
    {
        public double X;
        public double Y;

        public PointModel()
        {
        }

        public PointModel(double x, double y)
        {
            X = x;
            Y = y;
        }

        public PointModel(PointModel orig)
        {
            X = orig.X;
            Y = orig.Y;
        }

        public override bool Equals(object obj)
        {
            return this == (PointModel)obj;
        }

        public override int GetHashCode()
        {
            return 0;
        }

        public static PointModel operator +(PointModel p1, PointModel p2)
        {
            return new PointModel(p1.X + p2.X, p1.Y + p2.Y);
        }

        public static PointModel operator -(PointModel p1, PointModel p2)
        {
            return new PointModel(p1.X - p2.X, p1.Y - p2.Y);
        }

        public static PointModel operator -(PointModel p)
        {
            return new PointModel(-p.X, -p.Y);
        }

        public static PointModel operator *(PointModel p, double n)
        {
            return new PointModel(p.X * n, p.Y * n);
        }

        public static bool operator ==(PointModel p, PointModel p2)
        {
            if (p is null || p2 is null)
                return p is null && p2 is null;

            return MathUtilities.Approximately(p.X, p2.X) && MathUtilities.Approximately(p.Y, p2.Y);
        }

        public static bool operator !=(PointModel p, PointModel p2)
        {
            return !(p == p2);
        }
    }
}