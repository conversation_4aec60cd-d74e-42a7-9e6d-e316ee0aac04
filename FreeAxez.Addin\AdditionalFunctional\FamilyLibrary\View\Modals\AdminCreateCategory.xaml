<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals.AdminCreateCategory"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             mc:Ignorable="d"
             MinWidth="300">
    <UserControl.Resources>
        <ResourceDictionary>
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0">
            <TextBlock Style="{StaticResource TextBase}" 
                       Text="Category Name" 
                       Foreground="Black"
                       Margin="0 10 0 5" />
            <TextBox Style="{StaticResource UiTextBox}" 
                     Tag="Enter category name" 
                     Name="NameTextBox"
                     VerticalContentAlignment="Center"
                     Margin="0,0,0,5" 
                     Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}" 
                     Height="40" />
            <TextBlock Text="{Binding Error}" 
                       Foreground="Red"
                       Visibility="{Binding Error, Converter={StaticResource StringToVisibilityConverter}}" />
            <TextBlock Style="{StaticResource TextBase}" 
                       Text="Category Description" 
                       Foreground="Black"
                       Margin="0 10 0 5" />
            <TextBox Style="{StaticResource UiTextBox}" 
                     Tag="Enter category description"
                     Name="DescriptionTextBox"
                     Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}" 
                     Height="80" />
            <CheckBox Content="Is FreeAxez Category"
                      IsChecked="{Binding IsFreeAxezCategory, UpdateSourceTrigger=PropertyChanged}" 
                      Margin="0 10 0 5"/>
        </StackPanel>
        <Grid Grid.Row="1" Margin="0 15 0 0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Button Style="{StaticResource ButtonOutlinedRed}" 
                    Content="Cancel"
                    Command="{Binding CancelCommand}"/>
            <Button Grid.Column="2" 
                    Content="Add Category" 
                    Command="{Binding ApplyCommand}" 
                    IsEnabled="{Binding CanApply}"
                    Style="{StaticResource ButtonSimpleBlue}" />
        </Grid>
    </Grid>
</UserControl>