﻿using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.AdditionalFunctional.TagRamp.Abstractions;
using FreeAxez.Addin.AdditionalFunctional.TagRamp.Utils;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.TagRamp.Models
{
    public class RampComponentSelector : IRampSelector
    {
        public List<FamilyInstance> Select()
        {
            if (RevitManager.Document.ActiveView.ViewType == ViewType.ThreeD)
            {
                throw new NullReferenceException("Select plan view.");
            }

            var selectedRampComponents = RevitManager.UIDocument.Selection
                .PickObjects(ObjectType.Element, new RampComponentSelectionFilter())
                .Select(r => RevitManager.Document.GetElement(r.ElementId))
                .Cast<FamilyInstance>()
                .Where(f => f.Symbol.FamilyName.Split(new char[] { '-', '_' })
                    .Any(item => item == "Ramp"))
                .ToList();

            if (selectedRampComponents.Count < 2)
            {
                throw new NullReferenceException("Select 2 or more ramp instances");
            }

            return selectedRampComponents;
        }
    }
}