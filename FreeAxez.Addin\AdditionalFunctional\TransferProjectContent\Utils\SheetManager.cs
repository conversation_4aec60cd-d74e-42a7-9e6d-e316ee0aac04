﻿using System.Text.RegularExpressions;
using System.Windows.Forms;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Enums;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Interfaces;
using FreeAxez.Addin.Infrastructure;
using View = Autodesk.Revit.DB.View;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils
{
    public static class SheetManager
    {
        public static List<ViewSheet> CollectSheetsBySheetSize(Document doc, bool containNumbers)
        {
            var containsNumbersRegex = new Regex(@"\d");

            return new FilteredElementCollector(doc)
                .OfClass(typeof(ViewSheet))
                .Cast<ViewSheet>()
                .Where(sheet =>
                {
                    var sheetSizeParam = sheet.LookupParameter("Sheet Size");
                    var sheetSizeValue = sheetSizeParam?.AsString();

                    if (string.IsNullOrEmpty(sheetSizeValue))
                    {
                        return false;
                    }

                    return containNumbers
                        ? containsNumbersRegex.IsMatch(sheetSizeValue)
                        : !containsNumbersRegex.IsMatch(sheetSizeValue);
                })
                .ToList();
        }

        public static void DeleteSheetsFromDocument(Document doc, List<ViewSheet> sheetsToDelete,
            IProgressReporter progressReporter, CancellationToken cancellationToken)
        {
            var totalSheets = sheetsToDelete.Count;
            var processedSheets = 0;

            foreach (var sheet in sheetsToDelete)
            {
                cancellationToken.ThrowIfCancellationRequested();

                progressReporter.ReportStatus($"Deleting sheet: {sheet.SheetNumber}");

                try
                {
                    using (var trans = new Transaction(doc, $"Delete Sheet {sheet.SheetNumber}"))
                    {
                        trans.Start();
                        doc.Delete(sheet.Id);
                        trans.Commit();
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Error deleting sheet {sheet.SheetNumber}: {ex.Message}");
                }

                processedSheets++;
                var progress = (double)processedSheets / totalSheets * 100;
                progressReporter.ReportProgress(progress);

                Application.DoEvents();
            }
        }

        public static void CopySheetsFromSourceToTarget(Document sourceDoc, Document targetDoc,
            List<ViewSheet> sourceSheets, ProjectMapper mapper, IProgressReporter progressReporter,
            CancellationToken cancellationToken)
        {
            var totalSheets = sourceSheets.Count;
            var processedSheets = 0;

            foreach (var sourceSheet in sourceSheets)
            {
                cancellationToken.ThrowIfCancellationRequested();

                progressReporter.ReportStatus($"Copying sheet: {sourceSheet.SheetNumber}");

                ViewSheet targetSheet = null;
                try
                {
                    using (var trans = new Transaction(targetDoc, $"Copy Sheet {sourceSheet.SheetNumber}"))
                    {
                        CommonFailuresPreprocessor.SetFailuresPreprocessor(trans);
                        trans.Start();

                        var sourceTitleBlockInstance = GetTitleBlockInstanceFromSheet(sourceDoc, sourceSheet);
                        if (sourceTitleBlockInstance != null)
                        {
                            var titleBlockMapping =
                                mapper.MappedTitleBlocks.FirstOrDefault(m => m.Source.Id == sourceTitleBlockInstance.Symbol.Id);

                            if (titleBlockMapping != null && titleBlockMapping.Target != null)
                            {
                                targetSheet = ViewSheet.Create(targetDoc, titleBlockMapping.Target.Id);

                                CopySheetParameters(sourceSheet, targetSheet);

                                var targetTitleBlockInstance = GetTitleBlockInstanceFromSheet(targetDoc, targetSheet);
                                if (targetTitleBlockInstance != null)
                                {
                                    SetGridHeight(sourceTitleBlockInstance, targetTitleBlockInstance);
                                    SetNotForConstruction(sourceTitleBlockInstance, targetTitleBlockInstance);
                                }
                            }
                            else
                            {
                                LogHelper.Warning($"No matching title block found for {sourceTitleBlockInstance.Symbol.Name}");
                                continue; 
                            }
                        }
                        else
                        {
                            LogHelper.Warning($"No title block found on source sheet {sourceSheet.Name}");
                            continue;
                        }

                        AnnotationManager.CopyAnnotationsBetweenViews(sourceSheet, targetSheet, cancellationToken);
                        trans.Commit();
                    }

                    if (targetSheet != null)
                    {
                        CopyViewsOnSheet(sourceDoc, sourceSheet, targetDoc, targetSheet, mapper, progressReporter,
                            cancellationToken);
                    }
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Error copying sheet {sourceSheet.SheetNumber}: {ex.Message}");
                }

                processedSheets++;
                var progress = (double)processedSheets / totalSheets * 100;
                progressReporter.ReportProgress(progress);

                Application.DoEvents();
            }
            CloseOpenedSchedules(targetDoc);

            progressReporter.ReportStatus("Completed copying sheets.");
        }

        private static FamilyInstance? GetTitleBlockInstanceFromSheet(Document doc, ViewSheet sheet)
        {
            return new FilteredElementCollector(doc, sheet.Id)
                .OfCategory(BuiltInCategory.OST_TitleBlocks)
                .OfClass(typeof(FamilyInstance))
                .Cast<FamilyInstance>()
                .FirstOrDefault();
        }

        public static void CloseOpenedSchedules(Document targetDoc)
        {
            var uiApp = new Autodesk.Revit.UI.UIApplication(targetDoc.Application);
            var uiDoc = uiApp.ActiveUIDocument;

            var scheduleViews = uiDoc.GetOpenUIViews()
                .Where(uiView => targetDoc.GetElement(uiView.ViewId) is ViewSchedule)
                .ToList();

            foreach (var uiView in scheduleViews)
            {
                uiView.Close();
            }
        }


        private static FamilySymbol? GetTitleBlockFromSheet(Document doc, ViewSheet sheet)
        {
            var titleBlockInstance = new FilteredElementCollector(doc, sheet.Id)
                .OfCategory(BuiltInCategory.OST_TitleBlocks)
                .OfClass(typeof(FamilyInstance))
                .Cast<FamilyInstance>()
                .FirstOrDefault();

            return titleBlockInstance?.Symbol;
        }

        private static void CopyViewsOnSheet(
            Document sourceDoc,
            ViewSheet sourceSheet,
            Document targetDoc,
            ViewSheet targetSheet,
            ProjectMapper mapper,
            IProgressReporter progressReporter,
            CancellationToken cancellationToken)
        {
            var sourceViewPorts = new FilteredElementCollector(sourceDoc, sourceSheet.Id)
                .OfClass(typeof(Viewport))
                .Cast<Viewport>()
                .ToList();

            var sourceScheduleInstances = new FilteredElementCollector(sourceDoc, sourceSheet.Id)
                .OfClass(typeof(ScheduleSheetInstance))
                .Cast<ScheduleSheetInstance>()
                .Where(e => !e.IsTitleblockRevisionSchedule)
                .ToList();

            foreach (var sourceViewPort in sourceViewPorts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var sourceViewId = sourceViewPort.ViewId;
                var sourceBoxCenter = sourceViewPort.GetBoxCenter();

                var sourceView = sourceDoc.GetElement(sourceViewId) as View;

                if (sourceView == null)
                {
                    LogHelper.Warning($"Unable to find source view with ID {sourceViewId.GetIntegerValue()}");
                    continue;
                }

                if (sourceView is ViewPlan sourceViewPlan)
                {
                    var targetViewPlan = FindTargetViewByName(targetDoc, sourceViewPlan.Name);
                    if (targetViewPlan != null)
                    {
                        AddViewportToSheet(targetDoc, targetSheet, targetViewPlan, sourceBoxCenter);
                    }
                }
                else if (sourceView.ViewType == ViewType.Legend)
                {
                    ProcessLegendView(targetDoc, targetSheet, mapper, sourceViewPort, sourceView, sourceBoxCenter);
                }
                else if (sourceView.ViewType == ViewType.DraftingView)
                {
                    ProcessDraftingView(targetDoc, targetSheet, mapper, sourceViewPort, sourceView, sourceBoxCenter);
                }

                Application.DoEvents();
            }

            foreach (var sourceScheduleInstance in sourceScheduleInstances)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var scheduleId = sourceScheduleInstance.ScheduleId;
                var schedulePoint = sourceScheduleInstance.Point;

                var sourceSchedule = sourceDoc.GetElement(scheduleId) as ViewSchedule;
                if (sourceSchedule == null)
                {
                    LogHelper.Warning($"Unable to find source schedule with ID {scheduleId.GetIntegerValue()}");
                    continue;
                }

                HandleScheduleViews(targetDoc, targetSheet, mapper, sourceScheduleInstance, sourceSchedule,
                    schedulePoint);
                Application.DoEvents();
            }
        }


        private static void CopyLegendView(Document targetDoc, ViewSheet targetSheet, Viewport sourceViewPort,
            View targetLegend, XYZ sourceBoxCenter)
        {
            using (var trans = new Transaction(targetDoc, "Copy Legend View"))
            {
                CommonFailuresPreprocessor.SetFailuresPreprocessor(trans);
                trans.Start();

                var sourceView = sourceViewPort.Document.GetElement(sourceViewPort.ViewId) as View;
                var sourceOutline = sourceView?.Outline;

                var targetOutline = targetLegend.Outline;

                if (sourceOutline != null && targetOutline != null)
                {
                    var offsetY = sourceOutline.Min.V - targetOutline.Min.V;

                    var adjustedCenter = new XYZ(sourceBoxCenter.X, sourceBoxCenter.Y + offsetY, sourceBoxCenter.Z);

                    var targetLegendVp = Viewport.Create(targetDoc, targetSheet.Id, targetLegend.Id, adjustedCenter);

                    var viewportTypeId = new FilteredElementCollector(targetDoc)
                        .OfClass(typeof(ElementType))
                        .Cast<ElementType>()
                        .FirstOrDefault(et => et.FamilyName.Equals("Viewport", StringComparison.OrdinalIgnoreCase) &&
                                              et.Name.Equals("None", StringComparison.OrdinalIgnoreCase))?.Id;

                    if (viewportTypeId != null)
                    {
                        targetLegendVp.ChangeTypeId(viewportTypeId);
                    }

                    if (targetLegendVp != null)
                    {
#if !revit2020 && !revit2021
                        targetLegendVp.LabelLineLength = sourceViewPort.LabelLineLength;
                        targetLegendVp.LabelOffset = sourceViewPort.LabelOffset;
#endif
                    }
                }

                trans.Commit();
            }
        }

        private static void CopyDraftingView(Document targetDoc, ViewSheet targetSheet, Viewport sourceViewPort,
            View targetDraftingView, XYZ sourceBoxCenter)
        {
            using (var trans = new Transaction(targetDoc, "Copy Drafting View"))
            {
                CommonFailuresPreprocessor.SetFailuresPreprocessor(trans);
                trans.Start();

                var targetDraftingVp =
                    Viewport.Create(targetDoc, targetSheet.Id, targetDraftingView.Id, sourceBoxCenter);
                if (targetDraftingVp != null)
                {
#if !revit2020 && !revit2021
                    targetDraftingVp.LabelLineLength = sourceViewPort.LabelLineLength;
                    targetDraftingVp.LabelOffset = sourceViewPort.LabelOffset;
#endif
                }

                trans.Commit();
            }
        }

        private static void CopySheetParameters(ViewSheet sourceSheet, ViewSheet targetSheet)
        {
            targetSheet.Name = sourceSheet.Name;
            targetSheet.SheetNumber = sourceSheet.SheetNumber;

            var parameterNames = new List<string>
            {
                "Sheet Title",
                "Sheet Title (Short)",
                "Sheet Size",
                "Sheet Category",
                "Sheet Sorting",
                "Approved By",
                "Designed By",
                "Drawn By",
                "Sheet Issue Date"
            };

            foreach (var paramName in parameterNames)
            {
                var sourceParam = sourceSheet.LookupParameter(paramName);
                var targetParam = targetSheet.LookupParameter(paramName);

                if (sourceParam != null && targetParam != null && !targetParam.IsReadOnly)
                {
                    targetParam.Set(sourceParam.AsString());
                }
            }
        }

        private static void SetGridHeight(FamilyInstance sourceTitleBlockInstance, FamilyInstance targetTitleBlockInstance)
        {
            var sourceGridHeightParam = sourceTitleBlockInstance.LookupParameter("Gridd Height");
            var targetGridHeightParam = targetTitleBlockInstance.LookupParameter("Gridd Height");

            if (sourceGridHeightParam != null && targetGridHeightParam != null && !targetGridHeightParam.IsReadOnly)
            {
                if (sourceGridHeightParam.StorageType == StorageType.ElementId)
                {
                    var sourceNestedFamilyTypeId = sourceGridHeightParam.AsElementId();
                    var sourceNestedFamilyType = sourceTitleBlockInstance.Document.GetElement(sourceNestedFamilyTypeId) as FamilySymbol;

                    if (sourceNestedFamilyType != null)
                    {
                        var sourceNestedTypeName = sourceNestedFamilyType.Name;

                        var targetNestedFamilyType = new FilteredElementCollector(targetTitleBlockInstance.Document)
                            .OfClass(typeof(FamilySymbol))
                            .Cast<FamilySymbol>()
                            .FirstOrDefault(fs => fs.Name == sourceNestedTypeName);

                        if (targetNestedFamilyType != null)
                        {
                            targetGridHeightParam.Set(targetNestedFamilyType.Id);
                        }
                    }
                }
            }
        }

        private static void SetNotForConstruction(FamilyInstance sourceTitleBlockInstance, FamilyInstance targetTitleBlockInstance)
        {
            var sourceNotForConstructionParam = sourceTitleBlockInstance.LookupParameter("Not for Construction");
            var targetNotForConstructionParam = targetTitleBlockInstance.LookupParameter("Not for Construction");

            if (sourceNotForConstructionParam != null && targetNotForConstructionParam != null && !targetNotForConstructionParam.IsReadOnly)
            {
                targetNotForConstructionParam.Set(sourceNotForConstructionParam.AsInteger());
            }
        }

        private static View? FindTargetViewByName(Document doc, string viewName)
        {
            return new FilteredElementCollector(doc)
                .OfClass(typeof(View))
                .Cast<View>()
                .FirstOrDefault(view => view.Name.Equals(viewName, StringComparison.OrdinalIgnoreCase));
        }

        private static void AddViewportToSheet(
            Document targetDoc,
            ViewSheet targetSheet,
            View targetView,
            XYZ boxCenter)
        {
            using (var trans = new Transaction(targetDoc, "Add ViewPlan to Sheet"))
            {
                CommonFailuresPreprocessor.SetFailuresPreprocessor(trans);
                trans.Start();

                var viewport = Viewport.Create(targetDoc, targetSheet.Id, targetView.Id, boxCenter);
                if (viewport == null)
                {
                    throw new InvalidOperationException($"Failed to create viewport for view {targetView.Name}.");
                }

                trans.Commit();
            }
        }

        private static void ProcessLegendView(
            Document targetDoc,
            ViewSheet targetSheet,
            ProjectMapper mapper,
            Viewport sourceViewPort,
            View sourceView,
            XYZ sourceBoxCenter)
        {
            View targetLegend = null;

            if (OptionsManager.ViewHandlingOption == ViewHandlingOption.UseSource)
            {
                var targetLegendName = $"{sourceView.Name}_Old";
                targetLegend = FindTargetViewByName(targetDoc, targetLegendName);
            }
            else
            {
                var legendMapping = mapper.MappedLegends.FirstOrDefault(m => m.Source.Id == sourceView.Id);
                if (legendMapping != null && legendMapping.Target != null)
                {
                    targetLegend = legendMapping.Target;
                }
                else if (legendMapping?.Target == null &&
                         OptionsManager.ViewHandlingOption == ViewHandlingOption.Combine)
                {
                    var targetLegendName = $"{sourceView.Name}_Old";
                    targetLegend = FindTargetViewByName(targetDoc, targetLegendName);
                }
            }

            if (targetLegend != null)
            {
                CopyLegendView(targetDoc, targetSheet, sourceViewPort, targetLegend, sourceBoxCenter);
            }
            else
            {
                LogHelper.Warning($"No matching legend found for {sourceView.Name}");
            }
        }

        private static void ProcessDraftingView(
            Document targetDoc,
            ViewSheet targetSheet,
            ProjectMapper mapper,
            Viewport sourceViewPort,
            View sourceView,
            XYZ sourceBoxCenter)
        {
            View targetDraftingView = null;

            if (OptionsManager.ViewHandlingOption == ViewHandlingOption.UseSource)
            {
                var targetDraftingViewName = $"{sourceView.Name}_Old";
                targetDraftingView = FindTargetViewByName(targetDoc, targetDraftingViewName);
            }
            else
            {
                var draftingViewMapping = mapper.MappedDraftingViews.FirstOrDefault(m => m.Source.Id == sourceView.Id);
                if (draftingViewMapping != null && draftingViewMapping.Target != null)
                {
                    targetDraftingView = draftingViewMapping.Target;
                }
                else if (draftingViewMapping?.Target == null &&
                         OptionsManager.ViewHandlingOption == ViewHandlingOption.Combine)
                {
                    var targetDraftingViewName = $"{sourceView.Name}_Old";
                    targetDraftingView = FindTargetViewByName(targetDoc, targetDraftingViewName);
                }
            }

            if (targetDraftingView != null)
            {
                CopyDraftingView(targetDoc, targetSheet, sourceViewPort, targetDraftingView, sourceBoxCenter);
            }
            else
            {
                LogHelper.Warning($"No matching drafting view found for {sourceView.Name}");
            }
        }

        private static void HandleScheduleViews(
            Document targetDoc,
            ViewSheet targetSheet,
            ProjectMapper mapper,
            ScheduleSheetInstance sourceScheduleInstance,
            ViewSchedule sourceSchedule,
            XYZ schedulePoint)
        {
            ViewSchedule targetSchedule = null;

            switch (OptionsManager.ScheduleHandlingOption)
            {
                case ScheduleHandlingOption.UseSource:
                    var targetScheduleName = $"{sourceSchedule.Name}_Old";
                    targetSchedule = FindTargetViewByName(targetDoc, targetScheduleName) as ViewSchedule;
                    break;

                case ScheduleHandlingOption.UseTarget:
                    var scheduleMapping = mapper.MappedSchedules.FirstOrDefault(m => m.Source.Id == sourceSchedule.Id);
                    if (scheduleMapping != null && scheduleMapping.Target != null)
                    {
                        targetSchedule = scheduleMapping.Target as ViewSchedule;
                    }

                    break;

                case ScheduleHandlingOption.Combine:
                    scheduleMapping = mapper.MappedSchedules.FirstOrDefault(m => m.Source.Id == sourceSchedule.Id);

                    if (scheduleMapping != null && scheduleMapping.Target != null)
                    {
                        targetSchedule = scheduleMapping.Target as ViewSchedule;
                    }
                    else
                    {
                        targetScheduleName = $"{sourceSchedule.Name}_Old";
                        targetSchedule = FindTargetViewByName(targetDoc, targetScheduleName) as ViewSchedule;
                    }

                    break;
            }

            if (targetSchedule != null)
            {
                using (var trans = new Transaction(targetDoc, "Place Schedule on Sheet"))
                {
                    trans.Start();

                    try
                    {
                        var scheduleInstance = ScheduleSheetInstance.Create(
                            targetDoc,
                            targetSheet.Id,
                            targetSchedule.Id,
                            schedulePoint);

                        if (scheduleInstance != null)
                        {
                            LogHelper.Information(
                                $"Schedule '{targetSchedule.Name}' placed on sheet '{targetSheet.Name}' at {schedulePoint}.");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"Failed to place schedule '{targetSchedule?.Name}' on sheet: {ex.Message}");
                    }

                    trans.Commit();
                }
            }
            else
            {
                LogHelper.Warning(
                    $"No matching schedule found for '{sourceSchedule.Name}' to place on sheet '{targetSheet.Name}'.");
            }
        }
    }
}