﻿using AspNetCore.Reporting;
using FreeAxez.Core.Models;
using System;
using System.Collections.Generic;
using System.Text;

namespace FreeAxez.Reporting
{
    public static class ReportService
    {
        public static byte[] RenderPergolaCard(BuildDoneModel model, string reportType)
        {
            string templateName = "OptionReport.rdlc";
            RenderType format = GetRenderType(reportType);
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            parameters.Add("OptionLink", model.OptionLink ?? "not selected");
            parameters.Add("BlobLink", model.BlobLink ?? "not selected");
            parameters.Add("OptionName", model.OptionName ?? "not selected");
            parameters.Add("TotalAreaStandardUnits", model.TotalAreaStandardUnits ?? "not selected");
            parameters.Add("TotalArea", model.TotalArea ?? "not selected");
            parameters.Add("HighTrafficArea", model.HighTrafficArea ?? "not selected");
            parameters.Add("TotalAreaBorderComponent", model.TotalAreaBorderComponent ?? "not selected");
            parameters.Add("HalfAndQuarterBaseBorderComponent", model.HalfAndQuarterBaseBorderComponent ?? "not selected");
            parameters.Add("RelativeCost", model.RelativeCost ?? "not selected");
            parameters.Add("RelativeTime", model.RelativeTime ?? "not selected");
            parameters.Add("NumberOfCutPieces", model.NumberOfCutPieces ?? "not selected");
            
            parameters.Add("SubTotalFullBaseUnits", model.SubTotalFullBaseUnits ?? "not selected");
            parameters.Add("TotalFullBaseUnits", model.FullBaseUnits ?? "not selected");
            parameters.Add("HalfBase", model.HalfBase ?? "not selected");
            parameters.Add("QuarterBase", model.QuarterBase ?? "not selected");
            parameters.Add("Corner", model.Corner ?? "not selected");
            parameters.Add("Channel", model.Channel ?? "not selected");
            parameters.Add("HalfChannel", model.HalfChannel ?? "not selected");


            string dataSourceName = "DataSet1";
            object dataSourceModel = model.DataSet;
            var result = Render(templateName, parameters, format, dataSourceName, dataSourceModel);
            return result.MainStream;
        }

        private static ReportResult Render(
            string reportSourceFileName,
            Dictionary<string, string> parameters,
            RenderType format,
            string dataSourceName,
            object dataSourceModel
            )
        {
            string rdlcFilePath = string.Format(@"{0}ReportTemplates\{1}", AppDomain.CurrentDomain.BaseDirectory, reportSourceFileName);
            Encoding.GetEncoding("utf-8");
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            LocalReport report = new LocalReport(rdlcFilePath);
            report.AddDataSource(dataSourceName, dataSourceModel);
            var result = report.Execute(format, 1, parameters);
            return result;
        }

        private static RenderType GetRenderType(string reportType)
        {
            RenderType renderType;
            switch (reportType.ToLower())
            {
                default:
                case "pdf":
                    renderType = RenderType.Pdf;
                    break;
                case "word":
                    renderType = RenderType.Word;
                    break;
                case "excel":
                    renderType = RenderType.Excel;
                    break;
            }

            return renderType;
        }
    }
}
