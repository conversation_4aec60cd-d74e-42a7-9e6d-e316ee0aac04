﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.LineToRailing.Utils
{
    public class LevelManager
    {
        private List<Level> _levels;


        public LevelManager()
        {
            _levels = new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_Levels)
                .WhereElementIsNotElementType()
                .Cast<Level>()
                .ToList();
        }


        public Level GetNearestLevel(double pointZ)
        {
            var distances = _levels.Select(l => Math.Abs(l.ProjectElevation - pointZ)).ToList();

            return _levels[distances.IndexOf(distances.Min())];
        }
    }
}
