﻿using System;
using System.Net;
using System.Threading.Tasks;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;

public class AdminFamilyDeleteVm : ModalDialogVm
{
    private readonly LibraryItemDto _selectedFamily;

    public AdminFamilyDeleteVm(LibraryItemDto selectedFamily)
    {
        _selectedFamily = selectedFamily;
        ApplyCommand = new RelayCommand(ExecuteApply);
    }
    public ICommand ApplyCommand { get; }

    public string Name
    {
        get => _selectedFamily.Name;
        set
        {
            _selectedFamily.Name = value;
            OnPropertyChanged();
        }
    }

    private async void ExecuteApply(object parameter)
    {
        try
        {
            var response = await ApiService.Instance.HardDeleteFamilyAsync(_selectedFamily.Id);

            if (response.IsSuccessStatusCode)
            {
                CloseModal(true);
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Error = $"Failed to delete family: {errorContent}";
                LogHelper.Error($"Failed to delete family: {errorContent}");
            }
        }
        catch (Exception ex)
        {
            Error = $"Error: {ex.Message}";
            LogHelper.Error($"An error occurred: {ex.Message}");
        }
    }
}