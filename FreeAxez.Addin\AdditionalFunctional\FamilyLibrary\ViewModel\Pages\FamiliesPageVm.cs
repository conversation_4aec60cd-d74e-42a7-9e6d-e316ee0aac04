﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows.Input;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Dialog;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages;

public class FamiliesPageVm : BasePageVm
{
    private readonly DialogManager _dialogManager;
    private ObservableCollection<LibraryCategoryDto> _categories;
    private ObservableCollection<LibraryItemVm> _families;
    private Dictionary<Guid, List<LibraryItemDto>> _groupedLibraryFamilies = new();
    private bool _isLoading;
    private ObservableCollection<string> _revitVersions;
    private LibraryCategoryDto _selectedCategory;
    private string _selectedRevitVersion;

    public FamiliesPageVm()
    {
        _dialogManager = new DialogManager();
        ToggleRowDetailsCommand = new RelayCommand(ToggleRowDetails);
        ResetFiltersCommand = new RelayCommand(ResetFilters);
        LoadDataCommand = new AsyncRelayCommand(async () => await LoadData());
        AddFamilyCommand = new RelayCommand(param => AddFamily());
        EditFamilyCommand = new RelayCommand(param => EditFamily((LibraryItemVm)param));
        DeleteFamilyCommand = new AsyncRelayCommand(async param => await ConfirmAndDeleteFamily(param));
        DownloadToPcCommand = new RelayCommand(ExecuteDownloadToPc);
        DownloadToRevitCommand = new RelayCommand(ExecuteDownloadToRevit);
        Task.Run(LoadData);
    }

    public ICommand ToggleRowDetailsCommand { get; private set; }
    public ICommand ResetFiltersCommand { get; }
    public ICommand LoadDataCommand { get; }
    public ICommand AddFamilyCommand { get; }
    public ICommand EditFamilyCommand { get; }
    public ICommand DeleteFamilyCommand { get; }
    public ICommand DownloadToPcCommand { get; }
    public ICommand DownloadToRevitCommand { get; }

    public bool IsLoading
    {
        get => _isLoading;
        set
        {
            _isLoading = value;

            OnPropertyChanged();
        }
    }

    public ObservableCollection<LibraryItemVm> Families
    {
        get => _families;
        set
        {
            _families = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<LibraryCategoryDto> Categories
    {
        get => _categories;
        set
        {
            _categories = value;
            OnPropertyChanged();
        }
    }

    public LibraryCategoryDto SelectedCategory
    {
        get => _selectedCategory;
        set
        {
            _selectedCategory = value;

            OnPropertyChanged();
            Filter();
        }
    }

    public string SelectedRevitVersion
    {
        get => _selectedRevitVersion;
        set
        {
            _selectedRevitVersion = value;

            OnPropertyChanged();
            Filter();
        }
    }

    public ObservableCollection<string> RevitVersions
    {
        get => _revitVersions;
        set
        {
            _revitVersions = value;
            OnPropertyChanged();
        }
    }

    public async Task LoadData()
    {
        try
        {
            IsLoading = true;
            var result = await DataLoader.LoadFamiliesPageData();
            Categories = result.Categories;
            Families = result.Families;
            RevitVersions = result.RevitVersions;
            _groupedLibraryFamilies = result.GroupedLibraryFamilies;
        }
        catch (Exception ex)
        {
            LogHelper.Error($"Failed to load page data due to {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    protected override void Filter()
    {
        var view = CollectionViewSource.GetDefaultView(Families);
        view.Filter = item =>
        {
            var viewModel = item as LibraryItemVm;
            if (viewModel == null) return false;

            return ItemFilter.FilterLibraryItem(viewModel, SearchText, SelectedCategory, SelectedRevitVersion);
        };

        view.Refresh();
    }

    private void ResetFilters(object parameter)
    {
        SelectedCategory = null;
        SelectedRevitVersion = null;
        SearchText = string.Empty;
        Filter();
    }

    private async void OnDialogClose(bool? result)
    {
        if (result == true) await LoadData();
    }

    private void AddFamily()
    {
        var handler = FamilyLibraryCore.FamilyAddHandler;
        var externalEvent = FamilyLibraryCore.FamilyAddEvent;

        if (handler != null && externalEvent != null)
        {
            handler.SetData(this);
            externalEvent.Raise();
        }
        else
        {
            LogHelper.Error("FamilyAddHandler or ExternalEvent is null");
            FamilyLibraryCore.ShowMessage("Add Family", "Family add handler not initialized.", MessageType.Error);
        }
    }

    private void EditFamily(LibraryItemVm libraryItemVm)
    {
        var handler = FamilyLibraryCore.FamilyEditHandler;
        var externalEvent = FamilyLibraryCore.FamilyEditEvent;

        if (handler != null && externalEvent != null)
        {
            handler.SetData(libraryItemVm, this);
            externalEvent.Raise();
        }
        else
        {
            LogHelper.Error("FamilyEditHandler or ExternalEvent is null");
            FamilyLibraryCore.ShowMessage("Edit Family", "Family edit handler not initialized.", MessageType.Error);
        }
    }

    private Task ConfirmAndDeleteFamily(object parameter)
    {
        var libraryItemDto = parameter is LibraryItemVm vm ? vm.LibraryItem : parameter as LibraryItemDto;
        _dialogManager.ShowDeleteFamilyDialog(libraryItemDto, OnDialogClose);
        return Task.CompletedTask;
    }

    private void ExecuteDownloadToPc(object parameter)
    {
        if (parameter is LibraryItemVm libraryItemVm)
            SaveFamilyFile(libraryItemVm.LibraryItem.FamilyFilePath, libraryItemVm.LibraryItem.Name);
        else if (parameter is LibraryItemDto libraryItemDto)
            SaveFamilyFile(libraryItemDto.FamilyFilePath, libraryItemDto.Name);
    }

    private void ExecuteDownloadToRevit(object parameter)
    {
        try
        {
            IsLoading = true;
            var libraryItemDto = parameter is LibraryItemVm vm ? vm.LibraryItem : parameter as LibraryItemDto;

            if (libraryItemDto != null)
            {
                LogHelper.Information($"Requesting family download to Revit: {libraryItemDto.Name}");

                // Set matching name before processing
                SetMatchingName(libraryItemDto, _groupedLibraryFamilies);

                // Use FamilyDownloadToRevitHandler for complete workflow
                var handler = FamilyLibraryCore.FamilyDownloadToRevitHandler;
                var externalEvent = FamilyLibraryCore.FamilyDownloadToRevitEvent;

                if (handler != null && externalEvent != null)
                {
                    handler.SetData(libraryItemDto, this); // Pass ViewModel to handler
                    externalEvent.Raise();
                    LogHelper.Information($"FamilyDownloadToRevitHandler executed for: {libraryItemDto.Name}");
                }
                else
                {
                    LogHelper.Error("FamilyDownloadToRevitHandler or ExternalEvent is null");
                    FamilyLibraryCore.ShowMessage("Download to Revit",
                        "Family download handler not initialized.", MessageType.Error);
                    IsLoading = false; // Reset loading state if handler is not available
                }
            }
            else
            {
                IsLoading = false; // Reset loading state if no item
            }
        }
        catch (Exception ex)
        {
            LogHelper.Error($"Error requesting family download: {ex.Message}");
            FamilyLibraryCore.ShowMessage("Download to Revit",
                $"Error requesting family download: {ex.Message}", MessageType.Error);
            IsLoading = false; // Reset loading state on error
        }
    }

    private async void ToggleRowDetails(object param)
    {
        var itemViewModel = param as LibraryItemVm;
        if (itemViewModel == null) return;

        itemViewModel.IsRowDetailsVisible = !itemViewModel.IsRowDetailsVisible;

        if (itemViewModel.IsRowDetailsVisible && itemViewModel.RemainingVersions == null)
        {
            var oldVersions = await ApiService.Instance.GetOldVersionsAsync(itemViewModel.LibraryItem.OriginalItemId);
            if (oldVersions != null)
                itemViewModel.RemainingVersions = new ObservableCollection<LibraryItemDto>(oldVersions);
        }

        var view = CollectionViewSource.GetDefaultView(Families);
        view?.Refresh();
    }

    private void SetMatchingName(LibraryItemDto libraryItemDto, Dictionary<Guid, List<LibraryItemDto>> groupedLibraryFamilies)
    {
        if (groupedLibraryFamilies.TryGetValue(libraryItemDto.OriginalItemId, out var familyGroup))
        {
            var familyNamesWithoutExtension = familyGroup.Select(f => Path.GetFileNameWithoutExtension(f.Name)).ToList();

            var doc = RevitManager.Document;
            var specialityEquipmentCategory = doc.Settings.Categories.get_Item(BuiltInCategory.OST_SpecialityEquipment);
            var categoryId = specialityEquipmentCategory.Id;

            var matchedFamily = new FilteredElementCollector(doc)
                .OfClass(typeof(Family))
                .Cast<Family>()
                .Where(f => f.FamilyCategoryId == categoryId)
                .SelectMany(f => f.GetFamilySymbolIds().Select(fid => doc.GetElement(fid) as FamilySymbol))
                .FirstOrDefault(fs => fs != null && familyNamesWithoutExtension.Contains(fs.Family.Name));

            if (matchedFamily != null)
                libraryItemDto.MatchingName = matchedFamily.Family.Name + ".rfa";
        }
    }

    private void SaveFamilyFile(string fileUrl, string fileName)
    {
        try
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                FileName = fileName,
                Filter = "Revit Family Files (*.rfa)|*.rfa"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                using (var client = new System.Net.WebClient())
                {
                    client.DownloadFile(fileUrl, saveFileDialog.FileName);
                }

                FamilyLibraryCore.ShowMessage("Download to PC",
                    $"File '{fileName}' has been saved successfully.", MessageType.Success);
            }
        }
        catch (Exception ex)
        {
            LogHelper.Error($"Error saving family file: {ex.Message}");
            FamilyLibraryCore.ShowMessage("Download to PC",
                $"Error saving file: {ex.Message}", MessageType.Error);
        }
    }
}