using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.ViewScale.ViewModels
{
    public class ViewScaleViewModel : WindowViewModel
    {
        private ObservableCollection<ViewScaleModel> _allItems;
        private List<ViewScaleModel> _selectedItems = new List<ViewScaleModel>();
        private ICollectionView _filteredItems;
        private string _searchText = string.Empty;
        private bool _viewFilter = true;
        private bool _viewTemplateFilter = true;
        private bool _isScaleUpdating = false;


        public ViewScaleViewModel() : this(new List<ViewScaleModel>
        {
            new ViewScaleModel("Test View 1", 0, "Template 1", false, 100, true),
            new ViewScaleModel("Test View 2", 1, "TEMPLATE", true, 240, false),
            new ViewScaleModel("Test View 3", 2, "Template 3", false, 300, false),
            new ViewScaleModel("Test View 3", 3, "", false, 300, false)
        })
        {
            
        }

        public ViewScaleViewModel(List<ViewScaleModel> viewScaleModels)
        {
            _allItems = new ObservableCollection<ViewScaleModel>(viewScaleModels);

            // Initialize CollectionView for filtering and sorting
            FilteredItems = CollectionViewSource.GetDefaultView(_allItems);
            FilteredItems.SortDescriptions.Add(new SortDescription(nameof(ViewScaleModel.IsViewTemplate), ListSortDirection.Descending));
            FilteredItems.SortDescriptions.Add(new SortDescription(nameof(ViewScaleModel.Title), ListSortDirection.Ascending));
            FilteredItems.Filter = FilterItem;

            // Initialize commands
            ClearFiltersCommand = new RelayCommand(OnClearFiltersCommandExecute);
            ScaleUpdatedCommand = new RelayCommand(OnScaleUpdatedCommandExecute);
            SelectionChangedCommand = new RelayCommand(OnSelectionChangedCommandExecute);
            ApplyCommand = new RelayCommand(OnApplyCommandExecute);
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
        }


        #region Public Properties

        public ICollectionView FilteredItems
        {
            get => _filteredItems;
            set => Set(ref _filteredItems, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (Set(ref _searchText, value))
                {
                    ApplyFilter();
                }
            }
        }

        public bool ViewFilter
        {
            get => _viewFilter;
            set
            {
                if (Set(ref _viewFilter, value))
                {
                    ApplyFilter();
                }
            }
        }

        public bool ViewTemplateFilter
        {
            get => _viewTemplateFilter;
            set
            {
                if (Set(ref _viewTemplateFilter, value))
                {
                    ApplyFilter();
                }
            }
        }

        public int FilteredCount => FilteredItems?.Cast<ViewScaleModel>().Count() ?? 0;

        public int SelectedCount => _selectedItems.Count;

        #endregion 

        #region Commands

        public ICommand SelectionChangedCommand { get; }
        private void OnSelectionChangedCommandExecute(object p)
        {
            var args = p as SelectionChangedEventArgs;
            if (args == null)
                return;

            if (!(args.OriginalSource is ListView))
                return;

            foreach (ViewScaleModel item in args.RemovedItems ?? new object[0])
            {
                _selectedItems.Remove(item);
                OnPropertyChanged(nameof(SelectedCount));
            }

            foreach (ViewScaleModel item in args.AddedItems ?? new object[0])
            {
                _selectedItems.Add(item);
                OnPropertyChanged(nameof(SelectedCount));
            }
        }

        public ICommand ScaleUpdatedCommand { get; }
        private void OnScaleUpdatedCommandExecute(object p)
        {
            // Prevent recursive calls
            if (_isScaleUpdating)
                return;

            var changedItem = p as ViewScaleModel;
            if (changedItem == null)
                return;

            var scaleValue = changedItem.Scale; // Get the scale value from the model

            _isScaleUpdating = true;

            if (_selectedItems.Contains(changedItem)
                && _selectedItems.Count > 1)
            {
                var relatedItems = _selectedItems
                    .Where(i => i.IsScaleNotDependOnViewTemplate)
                    .ToList();

                foreach (ViewScaleModel item in relatedItems)
                {
                    item.Scale = scaleValue;

                    if (item.IsViewTemplate)
                        UpdateTemplateBasedItems(scaleValue, _allItems.ToList(), item);
                }
            }
            else
            {
                if (changedItem.IsViewTemplate)
                    UpdateTemplateBasedItems(scaleValue, _allItems.ToList(), changedItem);
            }

            _isScaleUpdating = false;
        }

        public ICommand ClearFiltersCommand { get; }
        private void OnClearFiltersCommandExecute(object p)
        {
            SearchText = string.Empty;
            ViewFilter = true;
            ViewTemplateFilter = true;

            OnPropertyChanged(nameof(SearchText));
            OnPropertyChanged(nameof(ViewFilter));
            OnPropertyChanged(nameof(ViewTemplateFilter));
        }

        public ICommand ApplyCommand { get; }
        private void OnApplyCommandExecute(object p)
        {
            var window = p as Window;
            window.DialogResult = true;
            window.Close();
        }

        public ICommand CancelCommand { get; }
        private void OnCancelCommandExecute(object p)
        {
            var window = p as Window;
            window.DialogResult = false;
            window.Close();
        }

        #endregion

        #region Private Methods

        private bool FilterItem(object item)
        {
            var viewScaleModel = item as ViewScaleModel;
            if (viewScaleModel == null)
                return false;

            //Apply view type filter
            if (ViewTemplateFilter == false && viewScaleModel.IsViewTemplate)
                return false;
            if (ViewFilter == false && viewScaleModel.IsViewTemplate == false)
                return false;

            return viewScaleModel != null && (
                   viewScaleModel.Title.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                   viewScaleModel.ViewTemplate.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                   viewScaleModel.ScaleString.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                   viewScaleModel.Scale.ToString().IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0);
        }

        private void ApplyFilter()
        {
            FilteredItems.Refresh();
            OnPropertyChanged(nameof(FilteredCount));
            OnPropertyChanged(nameof(SelectedCount));
        }

        private void UpdateTemplateBasedItems(int scaleValue, List<ViewScaleModel> allItmes, ViewScaleModel templateItem)
        {
            allItmes
                .Where(i => i.IsViewTemplate == false
                         && i.ViewTemplate == templateItem.Title
                         && i.IsScaleNotDependOnViewTemplate == false)
                .ToList()
                .ForEach(i => i.Scale = scaleValue);
        }

        #endregion
    }
}
