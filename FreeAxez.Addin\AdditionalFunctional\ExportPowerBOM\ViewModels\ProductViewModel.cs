﻿using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Utils;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBOM.Utils;
using FreeAxez.Addin.Models;
using FreeAxez.Addin.Models.Base;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.ViewModels
{
    public class ProductViewModel
    {
        private readonly Dictionary<string, string> _floorBoxPhaseKeyByPhase = new Dictionary<string, string>()
        {
            { "L1", "1" },
            { "L2", "2" },
            { "L3", "3" },
            { "L1+L2", "4" },
            { "L1+L3", "5" },
            { "L2+L3", "6" },
            { "L1+L2+L3", "7" },
        };

        private readonly Product _product;


        public ProductViewModel(Product product)
        {
            _product = product;
        }


        public string Description
        {
            get
            {
                if (PowerProductCollector.IsAccessory(_product)) return _product.ProductName;
                return _product.Description;
            }
        }

        public Product Product => _product;

        public string Model
        {
            get
            {
                // Replace length symbols
                // GP-707-FM-XXX
                // GP-708-FAS7001-082-X-XX => GP-708-FAS7001-082-X-
                var lengthPattern = @"-[Xx]+\s*$";
                if (Regex.IsMatch(_product.Model, lengthPattern))
                {
                    return _product.Model.Replace(Regex.Match(_product.Model, lengthPattern).Value, "-");
                }
                else
                {
                    return _product.Model;
                }
            }
        }

        public string ModelWithPhase
        {
            get
            {
                if (_product is FloorBox floorBox)
                {
                    // GP-708-FAS7001-082-X- => GP-708-FAS7001-082-5-
                    var phasePlaceholder = @"-[Xx]-";
                    if (Regex.IsMatch(Model, phasePlaceholder))
                    {
                        var value = Regex.Match(Model, phasePlaceholder).Value;
                        var phaseKey = _floorBoxPhaseKeyByPhase.ContainsKey(floorBox.Phase) ? _floorBoxPhaseKeyByPhase[floorBox.Phase] : "X";
                        return Model.Replace(value, $"-{phaseKey}-");
                    }
                }

                return Model;
            }
        }

        public string Length
        {
            get
            {
                if (_product.Length == -1) return "Error";
                else if (_product.Length == 0) return "";
                else return _product.Length.ToString("0");
            }
        }

        public string Phase => (_product as FloorBox)?.Phase;
        public string LevelName => _product.Level?.Name;
        public string FloorBoxType => (_product as FloorBox)?.FloorBoxType;
        public string ComponentNumber => (_product as FloorBox)?.ComponentNumber;
    }
}
