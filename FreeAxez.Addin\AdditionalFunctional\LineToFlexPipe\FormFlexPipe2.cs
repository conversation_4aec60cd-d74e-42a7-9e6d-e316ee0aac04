﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.Infrastructure;
using System.Windows.Forms;

namespace FreeAxez.Addin.AdditionalFunctional.LineToFlexPipe
{
    public partial class FormFlexPipe2 : System.Windows.Forms.Form
    {
        public string serializePrefix = "FlexPipe";
        Document doc = null;
        Units units;
        public FormFlexPipe2(Document _doc)
        {
            InitializeComponent();
            doc = _doc;

            units = new Units(UnitSystem.Imperial);
            FormatOptions foInches = new FormatOptions
            {
                UseDefault = false,
#if revit2018 || revit2019 || revit2020 || revit2021
                DisplayUnits = DisplayUnitType.DUT_FRACTIONAL_INCHES,
#endif
                Accuracy = 1.0 / 16.0
            };

#if revit2018 || revit2019 || revit2020 || revit2021
            units.SetFormatOptions(UnitType.UT_Length, foInches);
#else
            foInches.SetUnitTypeId(UnitTypeId.FractionalInches);
            units.SetFormatOptions(SpecTypeId.Length, foInches);
#endif

            List<FlexPipeType> FreeAxezTypes = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(FlexPipeType))
                .Cast<FlexPipeType>()
                .ToList();

            List<CommonUtils.NameIDObject> NameIdObjectList = FreeAxezTypes
                .Select(t => new CommonUtils.NameIDObject(t.FamilyName + " - " + t.Name, t.Id.GetIntegerValue()))
                .ToList();

            lstPipeTypes.DataSource = NameIdObjectList;
            lstPipeTypes.DisplayMember = "Name";
            lstPipeTypes.ValueMember = "IdValue";

            if (FreeAxezTypes.Any())
            {
                chkSetPipeTypeByLength.Enabled = true;
                chkSetPipeTypeByLength.Checked = true;
            }
            else
            {
                chkSetPipeTypeByLength.Enabled = false;
                chkSetPipeTypeByLength.Checked = false;
            }

            SetListEnabled();

            CommonUtils.checkLengthTextBox(doc, units, txtLevelOffset, AllowedValues.All);
            CommonUtils.checkLengthTextBox(doc, units, txtDiam, AllowedValues.Positive);
            CommonUtils.checkLengthTextBox(doc, units, whipDropTextBox, AllowedValues.NonNegative);
            CommonUtils.checkLengthTextBox(doc, units, whipRiseTextBox, AllowedValues.NonNegative);
            CommonUtils.checkLengthTextBox(doc, units, whipOverheadTextBox, AllowedValues.NonNegative);
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            if (getPipeDiam() <= 0)
            {
                Autodesk.Revit.UI.TaskDialog.Show("Error", "Enter a valid pipe diameter");
                return;
            }
            DialogResult = DialogResult.OK;
            Close();
        }

        public FlexPipeType getPipeType()
        {
            if (chkSetPipeTypeByLength.Checked)
            {
                return null;
            }
            else
            {
                int i = ((CommonUtils.NameIDObject)lstPipeTypes.SelectedItem).IdValue;
                ElementId id = new ElementId(i);
                FlexPipeType ret = doc.GetElement(id) as FlexPipeType;
                return ret;
            }
        }

        public double getPipeDiam()
        {
            return CommonUtils.GetInternalUnitsFromTextBox(doc, units, txtDiam);
        }

        public double getLevelOffset()
        {
            return CommonUtils.GetInternalUnitsFromTextBox(doc, units, txtLevelOffset);
        }

        public double GetWhipDropValue()
        {
            return CommonUtils.GetInternalUnitsFromTextBox(doc, units, whipDropTextBox);
        }

        public double GetWhipRiseValue()
        {
            return CommonUtils.GetInternalUnitsFromTextBox(doc, units, whipRiseTextBox);
        }

        public double GetWhipOverheadValue()
        {
            return CommonUtils.GetInternalUnitsFromTextBox(doc, units, whipOverheadTextBox);
        }

        public double GetSpliceValue()
        {
            if (double.TryParse(spliceTextBox.Text, out double parsedResult))
            {
                return parsedResult;
            }

            return 0;
        }

        public bool GetFirstPigtailValue()
        {
            return firstPigtailCheckBox.Checked;
        }

        public bool GetSecondPigtailValue()
        {
            return secondPigtailCheckBox.Checked;
        }

        public bool GetRounded()
        {
            return chkRound.Checked;
        }

        public bool GetDeleteLines()
        {
            return chkDeleteLines.Checked;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void txtDiam_Leave(object sender, EventArgs e)
        {
            CommonUtils.checkLengthTextBox(doc, units, (TextBox)sender, AllowedValues.Positive);
        }

        private void txtLevelOffset_Leave(object sender, EventArgs e)
        {
            CommonUtils.checkLengthTextBox(doc, units, (TextBox)sender, AllowedValues.All);
        }

        private void WhipDropTextBoxLeave(object sender, EventArgs e)
        {
            CommonUtils.checkLengthTextBox(doc, units, (TextBox)sender, AllowedValues.NonNegative);
        }

        private void WhipRiseTextBoxLeave(object sender, EventArgs e)
        {
            CommonUtils.checkLengthTextBox(doc, units, (TextBox)sender, AllowedValues.NonNegative);
        }

        private void WhipOverheadTextBoxLeave(object sender, EventArgs e)
        {
            CommonUtils.checkLengthTextBox(doc, units, (TextBox)sender, AllowedValues.NonNegative);
        }

        private void chkSetPipeTypeByLength_CheckedChanged(object sender, EventArgs e)
        {
            SetListEnabled();
        }

        private void SetListEnabled()
        {
            if (chkSetPipeTypeByLength.Checked)
            {
                lstPipeTypes.Enabled = false;
            }
            else
            {
                lstPipeTypes.Enabled = true;
            }
        }

        private void spliceTextBox_Leave(object sender, EventArgs e)
        {
            if (!double.TryParse((sender as TextBox).Text, out double value))
            {
                (sender as TextBox).Text = "0";
            }
            else
            {
                (sender as TextBox).Text = value.ToString();
            }
        }
    }
}
