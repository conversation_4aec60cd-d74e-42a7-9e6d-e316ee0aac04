﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.LevelViewManager.Helpers;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Services;
using System.Text;

namespace FreeAxez.Addin.AdditionalFunctional.LevelViewManager
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    public class LevelViewManagerCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            if (SelectLevelWindow.ShowDialog("Level View Manager", out List<Level> selectedLevels) != true || selectedLevels.Count == 0)
            {
                return Result.Cancelled;
            }

            var progressReporter = new ProgressBarHelper();
            progressReporter.Show();

            var report = new StringBuilder();

            using (var tg = new TransactionGroup(RevitManager.Document, "Create Views For Levels"))
            {
                tg.Start();

                var levelNumberFormatter = new LevelNumberFormatter();

                //progressReporter.ReportStepStatus("Create New Level");
                //progressReporter.ReportStatus("In progress...");
                //progressReporter.ReportProgress(50);
                //var levelHepler = new LevelHelper();
                //var sourceLevel = levelHepler.GetLastLevel();
                //var targetLevel = levelHepler.CreateNewLevel();

                foreach (var targetLevel in selectedLevels)
                {
                    if (LevelHelper.LevelNameContainsNumber(targetLevel.Name) == false)
                    {
                        continue;
                    }

                    progressReporter.ReportStepStatus("Create Ghost DWG");
                    progressReporter.ReportStatus("In progress...");
                    progressReporter.ReportProgress(50);
                    var dwgHelper = new DwgHelper();
                    dwgHelper.LinkGhostDwg(targetLevel);

                    progressReporter.ReportStepStatus("Create Views");
                    var viewDuplicator = new ViewGenerator(targetLevel, progressReporter, levelNumberFormatter);
                    var targetViewPlanes = viewDuplicator.DuplicateViews();

                    progressReporter.ReportStepStatus("Create Schedules");
                    var targetSchedules = viewDuplicator.DuplicateSchedules();

                    progressReporter.ReportStepStatus("Create Sheets");
                    var targetSheets = viewDuplicator.DuplicateSheets(targetViewPlanes, targetSchedules);


                    #region Write Report
                    report.AppendLine($"--- CREATED VIEWS FOR {targetLevel.Name} ---");
                    report.AppendLine();

                    report.AppendLine($"> CREATED VIEW PLANES {targetViewPlanes.Count}:");
                    targetViewPlanes.ForEach(v => report.AppendLine(v.Name));
                    report.AppendLine();

                    report.AppendLine($"> CREATED SCHEDULES {targetSchedules.Count}:");
                    targetSchedules.ForEach(v => report.AppendLine(v.Name));
                    report.AppendLine();

                    report.AppendLine($"> CREATED SHEETS {targetSheets.Count}:");
                    targetSheets.ForEach(v => report.AppendLine($"{(v as ViewSheet).SheetNumber} - {v.Name}"));
                    report.AppendLine();
                    #endregion
                }

                progressReporter.Close();

                if (progressReporter.CancellationTokenSource.IsCancellationRequested)
                {
                    tg.RollBack();
                }
                else
                {
                    tg.Assimilate();
                    var reportString = report.ToString();
                    if (string.IsNullOrEmpty(reportString))
                    {
                        reportString = "Some selected levels did not have a number in the name and were skipped.";
                    }

                    MessageWindow.ShowDialog(reportString, Infrastructure.UI.Enums.MessageType.Success);
                }
            }

            return Result.Succeeded;
        }
    }
}
