﻿using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Spreadsheets;
using System.Collections.Generic;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.ViewModels
{
    internal interface ISheetViewModel
    {
        string ProjectName { get; }
        string RevisionDate { get; }
        string PlanReferenced { get; }
        string SheetName { get; }
        List<OrderRow> Report { get; }
    }
}
