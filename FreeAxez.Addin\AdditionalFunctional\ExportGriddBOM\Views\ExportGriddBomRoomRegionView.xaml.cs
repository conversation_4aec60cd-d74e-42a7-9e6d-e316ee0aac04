﻿using System.Windows;
using System.Windows.Controls;
using FreeAxez.Addin.Infrastructure.UI;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Views
{
    /// <summary>
    ///     Interaction logic for ExportGriddBomRoomRegionView.xaml
    /// </summary>
    public partial class ExportGriddBomRoomRegionView : Window
    {
        public ExportGriddBomRoomRegionView()
        {
            InitializeComponent();
        }

        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            foreach (NodeViewModel node in Accessories.SelectedItems)
            {
                node.IsChecked = (bool)(sender as CheckBox).IsChecked;
            }
        }
    }
}