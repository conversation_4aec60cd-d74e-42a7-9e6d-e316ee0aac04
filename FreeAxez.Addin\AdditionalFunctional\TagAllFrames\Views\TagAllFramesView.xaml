﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.TagAllFrames.Views.TagAllFramesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vb="clr-namespace:FreeAxez.Addin.AdditionalFunctional.TagAllFrames.ViewModels"
             SizeToContent="WidthAndHeight"
             WindowStartupLocation="CenterScreen"
             ResizeMode="NoResize"
             Title="Tag Frames">
    <Window.DataContext>
        <vb:TagAllFramesViewModel/>
    </Window.DataContext>
    <Grid Margin="10,0,10,10">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="13*"/>
            <ColumnDefinition Width="57*"/>
        </Grid.ColumnDefinitions>
        <StackPanel Grid.ColumnSpan="2">
            <DockPanel Margin="0,10,0,0">
                <Label DockPanel.Dock="Left" Content="Leader Length"/>
                <TextBox VerticalContentAlignment="Center" Text="{Binding LengthToCenter}"/>
            </DockPanel>
            <GroupBox Margin="0,5,0,5" Header="Select Frames">
                <StackPanel Margin="0,5,0,2">
                    <RadioButton Content="Visible In View" IsChecked="{Binding TagAll}" GroupName="tagregFrames"/>
                    <RadioButton Margin="0,5,0,0" Content="Select Instances" IsChecked="{Binding TagSelected}" GroupName="tagregFrames"/>
                </StackPanel>
            </GroupBox>
            <StackPanel Margin="0,10,0,0" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Width="100" Height="30" Content="Create" Command="{Binding CreateCommand}" 
                        CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
                <Button Width="100" Margin="10,0,0,0" Content="Cancel" Command="{Binding CancelCommand}" 
                        CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
            </StackPanel>
        </StackPanel>
    </Grid>
</Window>
