﻿using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Utils;
using FreeAxez.Addin.Infrastructure;
using Newtonsoft.Json;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;

public class ApiService
{
    //private const string BaseApiUrl = "https://api-freeaxez-uat.bimsmith.com/api";
    //private static readonly string BaseApiUrl = "http://localhost:5000/api";
    private const string BaseApiUrl = "https://api-freeaxez.bimsmith.com/api";

    private readonly HttpClient _httpClient;
    private readonly string _libraryApiUrl = $"{BaseApiUrl}/familylibrary";

    private ApiService()
    {
        _httpClient = new HttpClient();

        var (_, token) = UserAuthManager.GetCredentials();
        if (!string.IsNullOrEmpty(token))
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
    }

    public static ApiService Instance { get; } = new();

    public async Task<List<LibraryItemDto>> GetLatestFamiliesAsync()
    {
        var response = await _httpClient.GetAsync($"{_libraryApiUrl}/library-items/latest");
        if (response.IsSuccessStatusCode)
        {
            var jsonString = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<List<LibraryItemDto>>(jsonString);
        }

        return null;
    }

    public async Task<List<LibraryItemDto>> GetAllFamiliesAsync()
    {
        var response = await _httpClient.GetAsync($"{_libraryApiUrl}/library-items");
        if (response.IsSuccessStatusCode)
        {
            var jsonString = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<List<LibraryItemDto>>(jsonString);
        }

        return null;
    }

    public async Task<List<LibraryItemDto>> GetOldVersionsAsync(Guid originalItemId)
    {
        var response = await _httpClient.GetAsync($"{_libraryApiUrl}/library-items/{originalItemId}/oldversions");
        if (response.IsSuccessStatusCode)
        {
            var jsonString = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<List<LibraryItemDto>>(jsonString);
        }

        return null;
    }

    public async Task<(string FilePath, string Error)> UploadFamilyFile(LibraryItemDetailsNewVm vm)
    {
        var familyCode = $"{CleanFileName(vm.ProductName)}_{CleanFileName(vm.Version)}";

        using (var multipartContent = new MultipartFormDataContent())
        {
            var fileContent = new ByteArrayContent(vm.RevitFileBytes);
            fileContent.Headers.ContentDisposition = new ContentDispositionHeaderValue("form-data")
            {
                Name = "\"file\"",
                FileName = $"\"{familyCode}\""
            };
            multipartContent.Add(fileContent, "file", familyCode);

            var response = await _httpClient.PostAsync($"{BaseApiUrl}/upload/familyFile", multipartContent);
            if (response.IsSuccessStatusCode)
            {
                var jsonString = await response.Content.ReadAsStringAsync();
                var uploadResponse = JsonConvert.DeserializeObject<UploadResponse>(jsonString);
                return (uploadResponse?.FilePath, null);
            }

            var errorResponse = await response.Content.ReadAsStringAsync();
            return (null, $"Failed to upload {vm.FileName} to blob: {errorResponse}");
        }
    }

    public async Task<(string FilePath, string Error)> UploadFamilyImage(BitmapSource familyPreview)
    {
        var imageBytes = ImageService.BitmapSourceToByteArray(familyPreview);

        using (var multipartContent = new MultipartFormDataContent())
        {
            var fileContent = new ByteArrayContent(imageBytes);
            fileContent.Headers.ContentDisposition = new ContentDispositionHeaderValue("form-data")
            {
                Name = "\"file\"",
                FileName = "\"image\""
            };
            multipartContent.Add(fileContent, "file", "image");

            var response = await _httpClient.PostAsync($"{BaseApiUrl}/upload/familyImage", multipartContent);
            if (response.IsSuccessStatusCode)
            {
                var jsonString = await response.Content.ReadAsStringAsync();
                var uploadResponse = JsonConvert.DeserializeObject<UploadResponse>(jsonString);
                return (uploadResponse?.FilePath, null);
            }

            var errorResponse = await response.Content.ReadAsStringAsync();
            return (null, $"Failed to upload image to blob: {errorResponse}");
        }
    }

    public async Task<HttpResponseMessage> AddFamilyAsync(LibraryItemDto family)
    {
        var jsonString = JsonConvert.SerializeObject(family);
        var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
        var response = await _httpClient.PostAsync($"{_libraryApiUrl}/library-items", content);
        return response;
    }

    public async Task<HttpResponseMessage> AddFamilyNewVersionAsync(LibraryItemDto family)
    {
        var jsonString = JsonConvert.SerializeObject(family);
        var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
        var response = await _httpClient.PostAsync($"{_libraryApiUrl}/library-items/addversion", content);
        return response;
    }

    public async Task<HttpResponseMessage> UpdateFamilyAsync(LibraryItemDto family)
    {
        var jsonString = JsonConvert.SerializeObject(family);
        var content = new StringContent(jsonString, Encoding.UTF8, "application/json");

        var response = await _httpClient.PutAsync($"{_libraryApiUrl}/library-items", content);
        return response;
    }

    public async Task<HttpResponseMessage> HardDeleteFamilyAsync(Guid familyId)
    {
        var response = await _httpClient.DeleteAsync($"{_libraryApiUrl}/library-items/{familyId}/hard");
        return response;
    }


    public async Task<List<LibraryCategoryDto>> GetAllCategoriesAsync()
    {
        var response = await _httpClient.GetAsync($"{_libraryApiUrl}/categories");
        if (response.IsSuccessStatusCode)
        {
            var jsonString = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<List<LibraryCategoryDto>>(jsonString);
        }

        return null;
    }

    public async Task<HttpResponseMessage> AddCategoryAsync(LibraryCategoryDto category)
    {
        var jsonString = JsonConvert.SerializeObject(category);
        var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
        var response = await _httpClient.PostAsync($"{_libraryApiUrl}/categories", content);
        return response;
    }

    public async Task<HttpResponseMessage> UpdateCategoryAsync(LibraryCategoryDto category)
    {
        var jsonString = JsonConvert.SerializeObject(category);
        var content = new StringContent(jsonString, Encoding.UTF8, "application/json");

        var response = await _httpClient.PutAsync($"{_libraryApiUrl}/categories", content);
        return response;
    }

    public async Task<HttpResponseMessage> DeleteCategoryAsync(Guid categoryId)
    {
        var response = await _httpClient.DeleteAsync($"{_libraryApiUrl}/categories/{categoryId}");
        return response;
    }

    public async Task<List<LibraryItemHistoryEntryDto>> GetAllHistoryAsync()
    {
        var response = await _httpClient.GetAsync($"{_libraryApiUrl}/library-items/history");
        if (response.IsSuccessStatusCode)
        {
            var jsonString = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<List<LibraryItemHistoryEntryDto>>(jsonString);
        }

        return null;
    }

    // Details API methods
    public async Task<List<LibraryItemDetailsDto>> GetAllDetailsAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_libraryApiUrl}/details");

            if (response.IsSuccessStatusCode)
            {
                var jsonString = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<LibraryItemDetailsDto>>(jsonString) ??
                       new List<LibraryItemDetailsDto>();
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            throw new HttpRequestException($"Failed to load details: {response.StatusCode} - {errorContent}");
        }
        catch (HttpRequestException)
        {
            throw; // Re-throw HTTP exceptions as-is
        }
        catch (Exception ex)
        {
            throw new Exception($"Error loading details: {ex.Message}", ex);
        }
    }


    public async Task<List<LibraryItemDetailsHistoryEntryDto>> GetAllDetailsHistoryAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_libraryApiUrl}/details/history");

            if (response.IsSuccessStatusCode)
            {
                var jsonString = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<LibraryItemDetailsHistoryEntryDto>>(jsonString) ??
                       new List<LibraryItemDetailsHistoryEntryDto>();
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            throw new HttpRequestException($"Failed to load details history: {response.StatusCode} - {errorContent}");
        }
        catch (HttpRequestException)
        {
            throw; // Re-throw HTTP exceptions as-is
        }
        catch (Exception ex)
        {
            throw new Exception($"Error loading details history: {ex.Message}", ex);
        }
    }

    public async Task<(string FilePath, string Error)> UploadDetailsFile(byte[] fileBytes, string fileName,
        string fileExtension)
    {
        var detailsCode = CleanFileName(fileName);

        using (var multipartContent = new MultipartFormDataContent())
        {
            var fileContent = new ByteArrayContent(fileBytes);
            fileContent.Headers.ContentDisposition = new ContentDispositionHeaderValue("form-data")
            {
                Name = "\"file\"",
                FileName = $"\"{detailsCode}\""
            };
            multipartContent.Add(fileContent, "file", detailsCode);

            var response = await _httpClient.PostAsync($"{BaseApiUrl}/upload/detailsFile", multipartContent);
            if (response.IsSuccessStatusCode)
            {
                var jsonString = await response.Content.ReadAsStringAsync();
                var uploadResponse = JsonConvert.DeserializeObject<UploadResponse>(jsonString);
                return (uploadResponse?.FilePath, null);
            }

            var errorResponse = await response.Content.ReadAsStringAsync();
            return (null, $"Failed to upload details file to blob: {errorResponse}");
        }
    }

    public async Task<(string FilePath, string Error)> UploadDetailsImage(BitmapSource imagePreview)
    {
        if (imagePreview == null)
            return (null, null);

        var imageBytes = ImageService.BitmapSourceToByteArray(imagePreview);

        using (var multipartContent = new MultipartFormDataContent())
        {
            var fileContent = new ByteArrayContent(imageBytes);
            fileContent.Headers.ContentDisposition = new ContentDispositionHeaderValue("form-data")
            {
                Name = "\"file\"",
                FileName = "\"image\""
            };
            multipartContent.Add(fileContent, "file", "image");

            var response = await _httpClient.PostAsync($"{BaseApiUrl}/upload/detailsImage", multipartContent);
            if (response.IsSuccessStatusCode)
            {
                var jsonString = await response.Content.ReadAsStringAsync();
                var uploadResponse = JsonConvert.DeserializeObject<UploadResponse>(jsonString);
                return (uploadResponse?.FilePath, null);
            }

            var errorResponse = await response.Content.ReadAsStringAsync();
            return (null, $"Failed to upload details image to blob: {errorResponse}");
        }
    }

    public async Task<HttpResponseMessage> AddDetailsAsync(LibraryItemDetailsDto details)
    {
        var jsonString = JsonConvert.SerializeObject(details);
        var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
        var response = await _httpClient.PostAsync($"{_libraryApiUrl}/details", content);
        return response;
    }

    public async Task<HttpResponseMessage> UpdateDetailsAsync(LibraryItemDetailsDto details)
    {
        var jsonString = JsonConvert.SerializeObject(details);
        var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
        var response = await _httpClient.PutAsync($"{_libraryApiUrl}/details", content);
        return response;
    }

    public async Task<HttpResponseMessage> DeleteDetailsAsync(Guid detailsId, string userId)
    {
        var response = await _httpClient.DeleteAsync($"{_libraryApiUrl}/details/{detailsId}?userId={userId}");
        return response;
    }

    public async Task<BitmapSource> LoadImageFromUrlAsync(string url)
    {
        if (string.IsNullOrEmpty(url))
            return null;

        if (url.StartsWith("pack://"))
            try
            {
                var image = new BitmapImage(new Uri(url));
                return image;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Failed to load image from resource: {ex.Message}");
                return null;
            }

        if (url.StartsWith("http://") || url.StartsWith("https://"))
            try
            {
                using (var client = new HttpClient())
                {
                    var response = await client.GetAsync(url);
                    if (response.IsSuccessStatusCode)
                        using (var stream = await response.Content.ReadAsStreamAsync())
                        {
                            var image = new BitmapImage();
                            image.BeginInit();
                            image.CacheOption = BitmapCacheOption.OnLoad;
                            image.StreamSource = stream;
                            image.EndInit();
                            image.Freeze();
                            return image;
                        }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Failed to load image from URL {url}: {ex.Message}");
            }

        return null;
    }

    private string CleanFileName(string name)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        foreach (var c in invalidChars) name = name.Replace(c, '_');
        return name;
    }
}