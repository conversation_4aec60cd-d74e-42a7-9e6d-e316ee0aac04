﻿using System.Collections.Generic;

namespace FreeAxez.Core.GeometryModel
{
    public class LayoutModel
    {
        public List<PlacingPointModel> BaseUnitPoints { get; set; }
        public List<PlacingPointModel> BaseUnitBottomHalfPoints { get; set; }
        public List<PlacingPointModel> BaseUnitTopHalfPoints { get; set; }
        public List<PlacingPointModel> BaseUnitRightHalfPoints { get; set; }
        public List<PlacingPointModel> BaseUnitLeftHalfPoints { get; set; }
        public List<PlacingPointModel> BaseUnitTopLeftPoints { get; set; }
        public List<PlacingPointModel> BaseUnitTopRightPoints { get; set; }
        public List<PlacingPointModel> BaseUnitBottomLeftPoints { get; set; }
        public List<PlacingPointModel> BaseUnitBottomRightPoints { get; set; }

        public List<PlacingPointModel> ChannelHorizontalPoints { get; set; }
        public List<PlacingPointModel> ChannelHorizontalLeftPoints { get; set; }
        public List<PlacingPointModel> ChannelHorizontalRightPoints { get; set; }

        public List<PlacingPointModel> VerticalChannelPoints { get; set; }
        public List<PlacingPointModel> VerticalChannelTopPoints { get; set; }
        public List<PlacingPointModel> VerticalChannelBottomPoints { get; set; }

        public List<PlacingPointModel> CornerPoints { get; set; }

        public List<PlacingPointModel> FullBorders { get; set; }
        public List<PlacingPointModel> AngledBordersEndCover { get; set; }
        public List<PlacingPointModel> AngledBordersNoEndCover { get; set; }
        public List<PlacingPointModel> Trapezoids { get; set; }
        public List<PlacingPointModel> LTrapezoids { get; set; }
        public List<PlacingPointModel> RadialBorders { get; set; }
        public List<PlacingPointModel> ConcaveBorders { get; set; }
        public List<PlacingPointModel> ConvexBorders { get; set; }
        public List<PlacingPointModel> LBorders { get; set; }

        public string GriddType { get; set; }

        public List<BorderCompositionModel> DiagonalBorders { get; set; }

        public LayoutModel()
        {
            Trapezoids = new List<PlacingPointModel>();
            LTrapezoids = new List<PlacingPointModel>();
        }
    }
}