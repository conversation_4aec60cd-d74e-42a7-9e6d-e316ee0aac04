﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.LineToRailing.Views;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.LineToRailing
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    internal class LineToRailingCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var lineToRailingView = new LineToRailingView();
            lineToRailingView.ShowDialog();

            return Result.Succeeded;
        }
    }
}
