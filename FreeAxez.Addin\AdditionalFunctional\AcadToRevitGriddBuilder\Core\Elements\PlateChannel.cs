using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using Point = FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data.Point;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Core.Elements;

public class PlateChannel : BaseFaElement
{
    private static readonly PlateChannelConfiguration _config = new();

    private PlateChannel(int id, List<LineSegmentData> segments) : base(id, segments)
    {
    }

    public PlateChannel() : base()
    {
    }

    public override ElementTypeConfiguration Configuration => _config;

    protected override void CalculateCenter(List<LineSegmentData> segments)
    {
        var shortSides = segments
            .Where(d => _config.IsValidShortLength(d.Segment.Length))
            .ToList();

        if (shortSides.Count != 2)
        {
            var allPoints = segments.SelectMany(s => new[] { s.Segment.P0, s.Segment.P1 }).ToList();
            var centerX = allPoints.Average(p => p.X);
            var centerY = allPoints.Average(p => p.Y);
            Center = new Point(centerX, centerY, 0);
            return;
        }

        var side1 = shortSides[0];
        var side2 = shortSides[1];
        var isHorizontal = IsHorizontalOrientation(segments);

        var targetSide = isHorizontal
            ? (side1.Segment.MidPoint.X < side2.Segment.MidPoint.X ? side1 : side2)
            : (side1.Segment.MidPoint.Y < side2.Segment.MidPoint.Y ? side1 : side2);

        var midPoint = targetSide.Segment.MidPoint;
        Center = new Point(midPoint.X, midPoint.Y, 0);
    }

    protected override void CalculateRotationAngle(List<LineSegmentData> segments)
    {
        var longSides = segments
            .Where(d => _config.IsValidLongLength(d.Segment.Length))
            .ToList();

        if (longSides.Count != 2)
        {
            RotationAngle = 0;
            return;
        }

        var side = longSides[0];
        var dx = side.Segment.P1.X - side.Segment.P0.X;
        var dy = side.Segment.P1.Y - side.Segment.P0.Y;

        var angleRad = Math.Atan2(dy, dx);
        var angleDeg = angleRad * 180.0 / Math.PI;
        if (angleDeg < 0)
            angleDeg += 360;

        bool isHorizontal = Math.Abs(dx) >= Math.Abs(dy);
        bool isReversed = isHorizontal ? dx < 0 : dy < 0;

        if (isReversed)
            angleDeg = (angleDeg + 180) % 360;

        RotationAngle = Math.Round(angleDeg, 2);
    }

    private bool IsHorizontalOrientation(List<LineSegmentData> segments)
    {
        var longSides = segments
            .Where(d => _config.IsValidLongLength(d.Segment.Length))
            .ToList();

        if (longSides.Count != 2)
            return true;

        var side = longSides[0];
        var dx = side.Segment.P1.X - side.Segment.P0.X;
        var dy = side.Segment.P1.Y - side.Segment.P0.Y;

        return Math.Abs(dx) >= Math.Abs(dy);
    }

    protected override bool IsValidComponentForThisType(List<LineSegmentData> component, HashSet<LineSegmentData> usedSegments)
    {
        if (component.All(d => usedSegments.Contains(d)))
            return false;

        var availableSegments = component.Where(d => !usedSegments.Contains(d)).ToList();
        if (availableSegments.Count != 4) return false;

        var shortSides = availableSegments
            .Where(d => _config.IsValidShortLength(d.Segment.Length)).ToList();
        var longSides = availableSegments
            .Where(d => _config.IsValidLongLength(d.Segment.Length)).ToList();

        return shortSides.Count == 2 && longSides.Count == 2;
    }


}
