﻿namespace FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Models;

public class PageViewTemplateRow
{
    public PageViewTemplateRow(string viewTemplateName, string viewType,
        Dictionary<string, ViewTemplateParameter> parameters)
    {
        ViewTemplateName = viewTemplateName;
        ViewType = viewType;
        Parameters = parameters ?? new Dictionary<string, ViewTemplateParameter>();
    }

    public string ViewTemplateName { get; set; }
    public string ViewType { get; set; }
    public Dictionary<string, ViewTemplateParameter> Parameters { get; }
}