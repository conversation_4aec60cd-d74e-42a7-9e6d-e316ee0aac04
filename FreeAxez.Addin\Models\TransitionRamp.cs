﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class TransitionRamp : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
                "Ramp"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public TransitionRamp(Element element) : base(element)
        {
        }


        public static List<TransitionRamp> Collect()
        {
            return FamilyCollector.Instances.Select(g => new TransitionRamp(g)).ToList();
        }
    }
}
