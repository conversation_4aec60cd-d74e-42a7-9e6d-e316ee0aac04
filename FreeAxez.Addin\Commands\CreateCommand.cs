﻿using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.Exceptions;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastucture;
using FreeAxez.Addin.Services;
using FreeAxez.Addin.Utils;
using FreeAxez.Core.Dto;
using FreeAxez.Core.Services;

namespace FreeAxez.Addin.Commands
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class CreateCommand : BaseExternalCommand
    {
        private readonly FreeAxezWebApiService _webApiService;
        private readonly SelectionService _selectionService;
        private readonly ProjectGeometryGatherer _geometryGatherer;
        private readonly string _fileVersion;
        public CreateCommand()
        {
            _webApiService = new FreeAxezWebApiService();
            _selectionService = new SelectionService();
            _geometryGatherer = new ProjectGeometryGatherer();
            _fileVersion = typeof(StartupApp).Assembly.GetName().Version.ToString();
        }

        public override Result Execute()
        {
            if (!WebView2Helper.CheckWebView2Installed())
            {
                return Result.Cancelled;
            }

            if (!_webApiService.CheckCurrentVersion(_fileVersion))
            {
                RevitDialogHelper.ShowNotification("You use not actual version of Revit Plugin. " +
                                                   "Please, go to: https://freeaxez.bimsmith.com/download             and get actual version");

                ApplicationExecutor.RunUpdatePage();
                return Result.Failed;
            }

            LogHelper.Information("Create command.");
            if (String.IsNullOrEmpty(RevitManager.Document.Title) || String.IsNullOrEmpty(RevitManager.Document.PathName))
            {
                RevitDialogHelper.ShowNotification("Please save the project before interaction.");
                return Result.Succeeded;
            }

            char[] charsToValidate = new char[] {'+', '=', '(', ')', ';'};
            if (RevitManager.Document.Title.IndexOfAny(charsToValidate) >= 0)
            {
                RevitDialogHelper.ShowNotification("Project name contains unsupported characters. \n" +
                                                   "Unsupported characters: (, ), ;, +, =.\n" +
                                                   "Please rename the project before interaction.");
                return Result.Succeeded;
            }

            var projectIdGP = GlobalParameterHelper.GetProjectId(RevitManager.Document);

            if (projectIdGP == null && !projectIdGP.HasValue)
            {
                if (!RevitDialogHelper.ShowConfirmation("New project will be created for this document.", "Confirm", "Do nothing"))
                {
                    LogHelper.Information("Create command canceled: project exists.");
                    return Result.Succeeded;
                }
            }

            LogHelper.Information("Creating new.");
            var regions = _selectionService.SelectRegions(RevitManager.UIApplication);

            var isHighTrafficBeingSelected = RevitDialogHelper.ShowConfirmation("Would you like to select High Traffic Area?", "Yes", "No");

            List<FilledRegion> highTrafficRegions = null;

            if (isHighTrafficBeingSelected)
            {
                highTrafficRegions = _selectionService.SelectHighTraffic(RevitManager.UIApplication);

                if (highTrafficRegions == null || !highTrafficRegions.Any())
                {
                    RevitDialogHelper.ShowNotification("High Traffic Area selection cancelled.");
                }
                else
                {
                }
            }

            var areTransitionLinesBeingSelected = RevitDialogHelper.ShowConfirmation("Would you like to select Transition Lines?", "Yes", "No");
            var transitionLinePoints = new List<XYZ>();
            ProjectDto project = null;

            if (areTransitionLinesBeingSelected)
            {
                var transitionLineCurves = _selectionService.SelectTransitionLines();

                // Check if transition lines correctly align with the edges of regions
                if (transitionLineCurves == null || !transitionLineCurves.Any())
                {
                    RevitDialogHelper.ShowNotification("Transition Lines selection cancelled.");
                    // Continue processing with existing regions and high traffic areas
                    project = _geometryGatherer.MergeRegionsGeometry(regions, highTrafficRegions, transitionLinePoints, RevitManager.Document);
                }
                else if (!GeometryManager.AreDetailLinesOnRegionEdges(transitionLineCurves, regions))
                {
                    TaskDialog.Show("Error", "The transition lines must lie on the edges of the selected regions. Please ensure that all transition lines align with the region boundaries before proceeding and try again.");
                    return Result.Failed;
                }
                else
                {
                    var region = regions.First();
                    var filledRegionType = RevitManager.Document.GetElement(region.GetTypeId()) as FilledRegionType;

                    // Gather points from transition lines
                    transitionLinePoints = GeometryManager.GetPointsFromTransitionLines(transitionLineCurves);

                    // Process modifications to regions based on transition lines
                    var modifiedRegionsCurves = GeometryManager.ModifyRegionCurvesWithTransitions(transitionLineCurves, regions);
                    var modifiedRegionsLoops = GeometryManager.CreateLoopsForRegions(modifiedRegionsCurves);
                    var modifiedRegions = GeometryManager.CreateModifiedRegions(modifiedRegionsLoops, filledRegionType);

                    // Merge modified region data into project DTO
                    project = _geometryGatherer.MergeRegionsGeometry(modifiedRegions, highTrafficRegions, transitionLinePoints, RevitManager.Document);
                }
            }
            else
            {
                // Handle the case where no transition lines are selected but regions need to be processed
                project = _geometryGatherer.MergeRegionsGeometry(regions, highTrafficRegions, transitionLinePoints, RevitManager.Document);
            }


            if (project == null)
                return Result.Failed;

            if (regions == null || !regions.Any())
            {
                RevitDialogHelper.ShowNotification("Please select at least one region.");
                return Result.Cancelled;
            }

            SaveCurrentDocument();

            project.RevitVersion = AppConstants.Version;
            project.AddinVersion = _fileVersion;
            Guid? projectId = null;
            var cleanDocService = new CleanDocumentService();
            var tempDocPath = cleanDocService.CleanCopyOfDocument(RevitManager.Document);
            if (!string.IsNullOrEmpty(tempDocPath))
            {
                projectId = _webApiService.AddProject(project, tempDocPath);
                cleanDocService.DeleteDocument(tempDocPath);
            }

            if (!projectId.HasValue)
            {
                RevitDialogHelper.ShowNotification("Something wen wrong. Please try create project later.");
                LogHelper.Warning("Create command failed.");
                return Result.Cancelled;
            }

            using (var trans = new Transaction(RevitManager.Document, "Set global parameter"))
            {
                trans.Start();
                GlobalParameterHelper.SetProjectId(projectId.Value, RevitManager.Document);
                trans.Commit();
            }

            SaveCurrentDocument();

            var fileName = Path.GetFileName(RevitManager.Document.PathName);
            ApplicationExecutor.Run(projectId.Value, fileName);

            LogHelper.Information("Create command completed.");
            return Result.Succeeded;
        }

        private void SaveCurrentDocument()
        {
            try
            {
                RevitManager.Document.Save();
            }
            catch (CentralModelException centralModelException)
                when (centralModelException.Message.StartsWith("Username does not match"))
            {
                throw new System.InvalidOperationException(
                    "The project cannot be saved because the user name you are \n" +
                    "using is not the same as the user who created the local file.\n" +
                    "The Revit username has been changed since the last working on the file.\n" +
                    "The Revit username can change when:\n" +
                    "- The Autodesk ID has been changed.\n" +
                    "- Signing in with the Autodesk ID Updates the Revit Username."
                );
            }
            catch (Exception exception)
            {
                throw exception;
            }
        }
    }
}