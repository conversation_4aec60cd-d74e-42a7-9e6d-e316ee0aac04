﻿using System;
using System.Collections.Generic;
using System.Linq;
using OfficeOpenXml;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Converters;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Helpers
{
    public class PageModelCategoriesParser
    {
        private const string WorksheetName = "Model Categories";

        public void ParseSheetAndUpdateModelCategories(ExcelPackage package, Document doc, Dictionary<string, View> viewTemplates, Dictionary<string, Category> modelCategories, LocalLogger localLogger)
        {
            var worksheet = package.Workbook.Worksheets[WorksheetName];
            if (worksheet == null)
                throw new InvalidOperationException($"Worksheet '{WorksheetName}' not found.");

            // Statistics tracking
            int processedRows = 0;
            int updatedCategories = 0;
            var errors = new List<string>();

            int startRow = 2;
            int endRow = worksheet.Dimension.End.Row;

            // Определяем список категорий, для которых нельзя менять настройки
            var nonModifiableCategories = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "HVAC Zones"
            };

            for (int row = startRow; row <= endRow; row++)
            {
                try
                {
                    string vtName = worksheet.Cells[row, 1].Text.Trim();
                    if (string.IsNullOrEmpty(vtName)) continue;
                    if (!viewTemplates.TryGetValue(vtName, out View viewTemplate)) continue;

                    string catName = worksheet.Cells[row, 2].Text.Trim();
                    if (string.IsNullOrEmpty(catName)) continue;
                    if (!modelCategories.TryGetValue(catName, out Category category)) continue;
                    ElementId catId = category.Id;

                    if (nonModifiableCategories.Contains(catName))
                    {
                        continue;
                    }

                    string visText = worksheet.Cells[row, 3].Text.Trim();
                    if (!string.IsNullOrEmpty(visText))
                    {
                        try
                        {
                            // Обновляем видимость только для модифицируемых категорий
                            viewTemplate.SetCategoryHidden(catId, !visText.Equals("Yes", StringComparison.OrdinalIgnoreCase));
                        }
                        catch (Exception ex)
                        {
                            errors.Add($"Row {row}: Error setting visibility for template '{vtName}', category '{catName}': {ex.Message}");
                        }
                    }

                    OverrideGraphicSettings ogs = null;
                    try
                    {
                        ogs = viewTemplate.GetCategoryOverrides(catId);
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"Row {row}: Error getting overrides for template '{vtName}', category '{catName}': {ex.Message}");
                        continue;
                    }

                    processedRows++;
                    updatedCategories++;

                    try
                    {
                        string detailLevelStr = worksheet.Cells[row, 4].Text.Trim();
                        if (!string.IsNullOrEmpty(detailLevelStr))
                            ogs.SetDetailLevel(DetailLevelConverter.ParseDetailLevel(detailLevelStr));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting detail level for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        var projLineColor = GetRevitColorFromCell(worksheet.Cells[row, 5]);
                        if (projLineColor != null)
                            ogs.SetProjectionLineColor(projLineColor);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting projection line color for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        string projLinePatternName = worksheet.Cells[row, 6].Text.Trim();
                        if (!string.IsNullOrEmpty(projLinePatternName))
                            ogs.SetProjectionLinePatternId(GetLinePatternIdByName(doc, projLinePatternName));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting projection line pattern for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        if (int.TryParse(worksheet.Cells[row, 7].Text.Trim(), out int projLineWeight))
                            ogs.SetProjectionLineWeight(projLineWeight);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting projection line weight for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        var surfForeColor = GetRevitColorFromCell(worksheet.Cells[row, 8]);
                        if (surfForeColor != null)
                            ogs.SetSurfaceForegroundPatternColor(surfForeColor);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting surface foreground color for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        string surfForePatternName = worksheet.Cells[row, 9].Text.Trim();
                        if (!string.IsNullOrEmpty(surfForePatternName))
                            ogs.SetSurfaceForegroundPatternId(GetFillPatternIdByName(doc, surfForePatternName));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting surface foreground pattern for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        var surfBackColor = GetRevitColorFromCell(worksheet.Cells[row, 10]);
                        if (surfBackColor != null)
                            ogs.SetSurfaceBackgroundPatternColor(surfBackColor);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting surface background color for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        string surfBackPatternName = worksheet.Cells[row, 11].Text.Trim();
                        if (!string.IsNullOrEmpty(surfBackPatternName))
                            ogs.SetSurfaceBackgroundPatternId(GetFillPatternIdByName(doc, surfBackPatternName));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting surface background pattern for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        if (int.TryParse(worksheet.Cells[row, 12].Text.Trim(), out int transparency))
                            ogs.SetSurfaceTransparency(transparency);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting transparency for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        var cutLineColor = GetRevitColorFromCell(worksheet.Cells[row, 13]);
                        if (cutLineColor != null)
                            ogs.SetCutLineColor(cutLineColor);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut line color for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        string cutLinePatternName = worksheet.Cells[row, 14].Text.Trim();
                        if (!string.IsNullOrEmpty(cutLinePatternName))
                            ogs.SetCutLinePatternId(GetLinePatternIdByName(doc, cutLinePatternName));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut line pattern for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        if (int.TryParse(worksheet.Cells[row, 15].Text.Trim(), out int cutLineWeight))
                            ogs.SetCutLineWeight(cutLineWeight);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut line weight for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        var cutForeColor = GetRevitColorFromCell(worksheet.Cells[row, 16]);
                        if (cutForeColor != null)
                            ogs.SetCutForegroundPatternColor(cutForeColor);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut foreground color for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        string cutForePatternName = worksheet.Cells[row, 17].Text.Trim();
                        if (!string.IsNullOrEmpty(cutForePatternName))
                            ogs.SetCutForegroundPatternId(GetFillPatternIdByName(doc, cutForePatternName));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut foreground pattern for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        var cutBackColor = GetRevitColorFromCell(worksheet.Cells[row, 18]);
                        if (cutBackColor != null)
                            ogs.SetCutBackgroundPatternColor(cutBackColor);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut background color for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        string cutBackPatternName = worksheet.Cells[row, 19].Text.Trim();
                        if (!string.IsNullOrEmpty(cutBackPatternName))
                            ogs.SetCutBackgroundPatternId(GetFillPatternIdByName(doc, cutBackPatternName));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut background pattern for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        string halftoneStr = worksheet.Cells[row, 20].Text.Trim();
                        if (!string.IsNullOrEmpty(halftoneStr))
                            ogs.SetHalftone(halftoneStr.Equals("Yes", StringComparison.OrdinalIgnoreCase));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting halftone for template '{vtName}', category '{catName}': {ex.Message}"); }

                    try
                    {
                        viewTemplate.SetCategoryOverrides(catId, ogs);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error updating category overrides for template '{vtName}', category '{catName}': {ex.Message}"); }
                }
                catch (Exception ex)
                {
                    errors.Add($"Row {row}: Error processing row in Model Categories: {ex.Message}");
                }
            }

            // Log summary
            if (errors.Any())
            {
                localLogger?.Error($"Model Categories processing completed with {errors.Count} errors:");
                foreach (var error in errors.Take(20))
                {
                    localLogger?.Error($"  {error}");
                }
                if (errors.Count > 20)
                    localLogger?.Error($"  ... and {errors.Count - 20} more errors");
            }

            localLogger?.Information($"Model Categories processed: {processedRows} rows, {updatedCategories} categories updated");
        }

        private Autodesk.Revit.DB.Color GetRevitColorFromCell(ExcelRange cell)
        {
            var rgb = cell.Style.Fill.BackgroundColor.Rgb;
            if (string.IsNullOrEmpty(rgb) || rgb.Length != 8) return null;
            int r = int.Parse(rgb.Substring(2, 2), System.Globalization.NumberStyles.HexNumber);
            int g = int.Parse(rgb.Substring(4, 2), System.Globalization.NumberStyles.HexNumber);
            int b = int.Parse(rgb.Substring(6, 2), System.Globalization.NumberStyles.HexNumber);
            return new Autodesk.Revit.DB.Color((byte)r, (byte)g, (byte)b);
        }

        private ElementId GetLinePatternIdByName(Document doc, string patternName)
        {
            if (string.IsNullOrEmpty(patternName)) return ElementId.InvalidElementId;
            var lpe = new FilteredElementCollector(doc)
                .OfClass(typeof(LinePatternElement))
                .Cast<LinePatternElement>()
                .FirstOrDefault(x => x.Name.Equals(patternName, StringComparison.OrdinalIgnoreCase));
            return lpe?.Id ?? ElementId.InvalidElementId;
        }

        private ElementId GetFillPatternIdByName(Document doc, string patternName)
        {
            if (string.IsNullOrEmpty(patternName)) return ElementId.InvalidElementId;
            var fpe = new FilteredElementCollector(doc)
                .OfClass(typeof(FillPatternElement))
                .Cast<FillPatternElement>()
                .FirstOrDefault(x => x.Name.Equals(patternName, StringComparison.OrdinalIgnoreCase));
            return fpe?.Id ?? ElementId.InvalidElementId;
        }
    }
}
