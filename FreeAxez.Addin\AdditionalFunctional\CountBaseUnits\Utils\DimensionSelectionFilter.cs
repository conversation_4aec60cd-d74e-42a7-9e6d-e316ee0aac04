﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;

namespace FreeAxez.Addin.AdditionalFunctional.CountBaseUnits.Utils
{
    public class DimensionSelectionFilter : ISelectionFilter
    {
        public bool AllowElement(Element elem)
        {
            if (elem is Dimension)
            {
                return true;
            }

            return false;
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            return false;
        }
    }
}
