using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Infrastructure;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using Microsoft.Win32;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;

public class AutoCADInvoker : IAutoCADInvoker
{
    private readonly AutoCADVersionService _versionService;

    public AutoCADInvoker() : this(new AutoCADVersionService())
    {
    }

    public AutoCADInvoker(AutoCADVersionService versionService)
    {
        _versionService = versionService ?? throw new ArgumentNullException(nameof(versionService));
    }

    public async Task<string> ExtractLayersAsync(string dwgFilePath, string outputFilePath)
    {
        return await Task.Run(() => ExtractLayers(dwgFilePath, outputFilePath));
    }

    public async Task<bool> ReplaceLayersAsync(string dwgFilePath, string mappingFilePath)
    {
        return await Task.Run(() => ReplaceLayers(dwgFilePath, mappingFilePath));
    }

    public async Task<bool> DeleteEmptyLayersAsync(string dwgFilePath)
    {
        return await Task.Run(() => DeleteEmptyLayers(dwgFilePath));
    }

    public async Task<bool> MergeLayersAsync(string dwgFilePath, string mergeDataFilePath)
    {
        return await Task.Run(() => MergeLayers(dwgFilePath, mergeDataFilePath));
    }

    private string ExtractLayers(string dwgFilePath, string outputFilePath)
    {
        var consolePath = GetCoreConsolePath();
        if (string.IsNullOrEmpty(consolePath))
            throw new InvalidOperationException("AutoCAD Core Console not found for selected version.");

        if (!File.Exists(dwgFilePath))
            throw new FileNotFoundException($"DWG file not found: {dwgFilePath}");

        var outputDir = Path.GetDirectoryName(outputFilePath);
        if (!Directory.Exists(outputDir))
            Directory.CreateDirectory(outputDir);

        var pluginPath = GetNetPluginPath();
        if (!File.Exists(pluginPath))
            throw new FileNotFoundException($"AutoCAD plugin not found: {pluginPath}");

        var absoluteOutputPath = Path.GetFullPath(outputFilePath);
        Environment.SetEnvironmentVariable("FREEAXEZ_OUTPUT_PATH", absoluteOutputPath);

        using (var tempFileManager = new TempFileManager())
        {
            var scriptPath = Path.Combine(tempFileManager.TempDirectory, "extract_layers.scr");
            var scriptContent =
                "SECURELOAD\n0\n" +
                $"NETLOAD \"{pluginPath}\"\n" +
                "EXTRACT_LAYERS\n" +
                "SECURELOAD\n1\n" +
                "QUIT\n";
            File.WriteAllText(scriptPath, scriptContent, Encoding.Default);
            var psi = new ProcessStartInfo
            {
                FileName = consolePath,
                Arguments = $"/i \"{dwgFilePath}\" /s \"{scriptPath}\" /product ACAD /l en-US",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            using (var process = Process.Start(psi))
            {
                if (!process.WaitForExit(600000)) // 10 minutes timeout
                {
                    process.Kill();
                    throw new TimeoutException($"Timeout processing {Path.GetFileName(dwgFilePath)}.");
                }

                var output = process.StandardOutput.ReadToEnd();
                var error = process.StandardError.ReadToEnd();

                LogHelper.Information($"AutoCAD ExtractLayers - Exit Code: {process.ExitCode}");
                LogHelper.Information($"AutoCAD ExtractLayers - Output: {output}");

                if (!string.IsNullOrEmpty(error))
                    LogHelper.Warning($"AutoCAD ExtractLayers - Error: {error}");

                if (process.ExitCode != 0)
                    throw new InvalidOperationException(
                        $"AutoCAD Core Console failed (exit {process.ExitCode}). Check logs for details.");
            }
        }

        if (!File.Exists(outputFilePath))
            throw new InvalidOperationException($"Output file was not created: {outputFilePath}");

        var fileInfo = new FileInfo(outputFilePath);
        if (fileInfo.Length == 0)
        {
            LogHelper.Error($"AutoCAD ExtractLayers - Output file is empty: {outputFilePath}");
            throw new InvalidOperationException($"Output file is empty: {outputFilePath}. Check logs for details.");
        }

        return outputFilePath;
    }

    private bool ReplaceLayers(string dwgFilePath, string mappingFilePath)
    {
        var consolePath = GetCoreConsolePath();
        if (string.IsNullOrEmpty(consolePath))
            throw new InvalidOperationException("AutoCAD Core Console not found for selected version.");

        if (!File.Exists(dwgFilePath))
            throw new FileNotFoundException($"DWG file not found: {dwgFilePath}");

        var outputDir = Path.GetDirectoryName(dwgFilePath);
        var pluginPath = GetNetPluginPath();
        var absoluteMappingPath = Path.GetFullPath(mappingFilePath);
        Environment.SetEnvironmentVariable("FREEAXEZ_MAPPING_PATH", absoluteMappingPath);

        using (var tempFileManager = new TempFileManager())
        {
            var scriptPath = Path.Combine(tempFileManager.TempDirectory, "replace_layers.scr");

            // Set environment variable for the JSON file path
            Environment.SetEnvironmentVariable("FREEAXEZ_UPDATE_DATA_PATH", mappingFilePath);

            var scriptContent =
                "SECURELOAD\n0\n" +
                $"NETLOAD \"{pluginPath}\"\n" +
                "UPDATE_LAYERS_FROM_BACKEND\n" +
                "QSAVE\n" +
                "SECURELOAD\n1\n" +
                "QUIT\n";
            File.WriteAllText(scriptPath, scriptContent, Encoding.Default);

            var psi = new ProcessStartInfo
            {
                FileName = consolePath,
                Arguments = $"/i \"{dwgFilePath}\" /s \"{scriptPath}\" /product ACAD /l en-US",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            using (var process = Process.Start(psi))
            {
                if (!process.WaitForExit(120000)) // 10 minutes timeout
                {
                    process.Kill();
                    throw new TimeoutException($"Timeout processing {Path.GetFileName(dwgFilePath)}.");
                }

                var output = process.StandardOutput.ReadToEnd();
                var error = process.StandardError.ReadToEnd();

                LogHelper.Information($"AutoCAD ReplaceLayers - Exit Code: {process.ExitCode}");
                LogHelper.Information($"AutoCAD ReplaceLayers - Output: {output}");

                if (!string.IsNullOrEmpty(error))
                    LogHelper.Warning($"AutoCAD ReplaceLayers - Error: {error}");

                if (process.ExitCode != 0)
                    throw new InvalidOperationException(
                        $"AutoCAD Core Console failed (exit {process.ExitCode}). Check logs for details.");
            }
        }

        return true;
    }

    private bool DeleteEmptyLayers(string dwgFilePath)
    {
        var consolePath = GetCoreConsolePath();
        if (string.IsNullOrEmpty(consolePath))
            throw new InvalidOperationException("AutoCAD Core Console not found for selected version.");

        if (!File.Exists(dwgFilePath))
            throw new FileNotFoundException($"DWG file not found: {dwgFilePath}");

        var outputDir = Path.GetDirectoryName(dwgFilePath);
        var pluginPath = GetNetPluginPath();

        using (var tempFileManager = new TempFileManager())
        {
            var scriptPath = Path.Combine(tempFileManager.TempDirectory, "delete_empty_layers.scr");
            var scriptContent =
                "SECURELOAD\n0\n" +
                $"NETLOAD \"{pluginPath}\"\n" +
                "DELETEEMPTYLAYERS\n" +
                "QSAVE\n" +
                "SECURELOAD\n1\n" +
                "QUIT\n";
            File.WriteAllText(scriptPath, scriptContent, Encoding.Default);

            var psi = new ProcessStartInfo
            {
                FileName = consolePath,
                Arguments = $"/i \"{dwgFilePath}\" /s \"{scriptPath}\" /product ACAD /l en-US",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            using (var process = Process.Start(psi))
            {
                if (!process.WaitForExit(120000)) // 10 minutes timeout
                {
                    process.Kill();
                    throw new TimeoutException($"Timeout processing {Path.GetFileName(dwgFilePath)}.");
                }

                var output = process.StandardOutput.ReadToEnd();
                var error = process.StandardError.ReadToEnd();

                LogHelper.Information($"AutoCAD DeleteEmptyLayers - Exit Code: {process.ExitCode}");
                LogHelper.Information($"AutoCAD DeleteEmptyLayers - Output: {output}");

                if (!string.IsNullOrEmpty(error))
                    LogHelper.Warning($"AutoCAD DeleteEmptyLayers - Error: {error}");

                if (process.ExitCode != 0)
                    throw new InvalidOperationException(
                        $"AutoCAD Core Console failed (exit {process.ExitCode}). Check logs for details.");
            }
        }

        return true;
    }

    private bool MergeLayers(string dwgFilePath, string mergeDataFilePath)
    {
        var consolePath = GetCoreConsolePath();
        if (string.IsNullOrEmpty(consolePath))
            throw new InvalidOperationException("AutoCAD Core Console not found for selected version.");

        if (!File.Exists(dwgFilePath))
            throw new FileNotFoundException($"DWG file not found: {dwgFilePath}");

        var outputDir = Path.GetDirectoryName(dwgFilePath);
        var pluginPath = GetNetPluginPath();
        var absoluteMergeDataPath = Path.GetFullPath(mergeDataFilePath);
        Environment.SetEnvironmentVariable("FREEAXEZ_MERGE_DATA_PATH", absoluteMergeDataPath);

        using (var tempFileManager = new TempFileManager())
        {
            var scriptPath = Path.Combine(tempFileManager.TempDirectory, "merge_layers.scr");
            var scriptContent =
                "SECURELOAD\n0\n" +
                $"NETLOAD \"{pluginPath}\"\n" +
                "MERGE_LAYERS\n" +
                "QSAVE\n" +
                "SECURELOAD\n1\n" +
                "QUIT\n";
            File.WriteAllText(scriptPath, scriptContent, Encoding.Default);

            var psi = new ProcessStartInfo
            {
                FileName = consolePath,
                Arguments = $"/i \"{dwgFilePath}\" /s \"{scriptPath}\" /product ACAD /l en-US",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            using (var process = Process.Start(psi))
            {
                if (!process.WaitForExit(120000)) // 10 minutes timeout
                {
                    process.Kill();
                    throw new TimeoutException($"Timeout processing {Path.GetFileName(dwgFilePath)}.");
                }

                var output = process.StandardOutput.ReadToEnd();
                var error = process.StandardError.ReadToEnd();

                LogHelper.Information($"AutoCAD MergeLayers - Exit Code: {process.ExitCode}");
                LogHelper.Information($"AutoCAD MergeLayers - Output: {output}");

                if (!string.IsNullOrEmpty(error))
                    LogHelper.Warning($"AutoCAD MergeLayers - Error: {error}");

                if (process.ExitCode != 0)
                    throw new InvalidOperationException(
                        $"AutoCAD Core Console failed (exit {process.ExitCode}). Check logs for details.");
            }
        }

        return true;
    }

    private string GetNetPluginPath()
    {
        var currentDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        var selectedVersion = GetSelectedAutoCADVersion();

        if (selectedVersion == 0)
        {
            MessageWindow.ShowDialog("Error",
                "No AutoCAD version selected. Please select an AutoCAD version in the main window.",
                MessageType.Error);
            throw new InvalidOperationException("No AutoCAD version selected");
        }

        // Verify the selected version is still available
        var installation = _versionService.GetInstallation(selectedVersion);
        if (installation == null || !installation.IsValid)
        {
            MessageWindow.ShowDialog("Error",
                $"Selected AutoCAD version {selectedVersion} is no longer available. " +
                "Please select a different version in the main window.",
                MessageType.Error);
            throw new InvalidOperationException($"AutoCAD {selectedVersion} not available");
        }

        // Choose plugin based on selected AutoCAD version
        var pluginFileName = $"FreeAxez.AutoCAD.Plugin.{selectedVersion}.dll";
        var localPluginPath = Path.Combine(currentDir, pluginFileName);

        if (File.Exists(localPluginPath))
            return localPluginPath;

        MessageWindow.ShowDialog("Error",
            $"AutoCAD plugin not found for version {selectedVersion}. " +
            $"Expected file: {pluginFileName}",
            MessageType.Error);
        throw new FileNotFoundException($"AutoCAD plugin not found: {localPluginPath}");
    }

    private int GetSelectedAutoCADVersion()
    {
        return Properties.Settings.Default.SelectedAutoCADVersion;
    }

    private string GetCoreConsolePath()
    {
        var selectedVersion = GetSelectedAutoCADVersion();
        if (selectedVersion == 0)
            return null;

        return _versionService.GetCoreConsolePath(selectedVersion);
    }


}