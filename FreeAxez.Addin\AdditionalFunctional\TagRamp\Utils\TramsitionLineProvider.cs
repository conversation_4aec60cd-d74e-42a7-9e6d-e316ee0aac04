﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TagRamp.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagRamp.Utils
{
    public class TramsitionLineProvider
    {
        private const double MaximumProjectPointOffset = 0.**********;
        private const string FamilySymbolLengthParameterName = "C Length";

        public List<TramsitionLine> Get(List<FamilyInstance> rampInstances)
        {
            List<FamilyInstance> middleRampComponents = GetMiddleRampComponents(rampInstances);

            List<TramsitionLine> output = new List<TramsitionLine>();

            while (middleRampComponents.Count > 0)
            {
                var familyInstance = middleRampComponents.First();
                var startLocationPoint = (familyInstance.Location as LocationPoint).Point;
                var transform = familyInstance.GetTotalTransform();
                var directionCurve = Line.CreateUnbound(startLocationPoint, transform.BasisX);

                List<FamilyInstance> projectedFamilyInstances = middleRampComponents
                    .Where(candidate =>
                    {
                        XYZ candidateLocationPoint = (candidate.Location as LocationPoint).Point;
                        IntersectionResult intersectionResult = directionCurve.Project(candidateLocationPoint);
                        return intersectionResult.Distance >= 0
                            && intersectionResult.Distance < MaximumProjectPointOffset;
                    })
                    .ToList();

                List<XYZ> projectedPoints = projectedFamilyInstances
                    .Select(f => (f.Location as LocationPoint).Point)
                    .ToList();

                List<XYZ> orderedProjectedPoints = TramsitionLine
                    .OrderPointsByCurveDirection(directionCurve, projectedPoints);

                if (projectedPoints.Count > 1)
                {
                    var line = Line.CreateBound(orderedProjectedPoints.First(), orderedProjectedPoints.Last());

                    output.Add(TramsitionLine.Create(line.Evaluate(0.5, true),
                                                     line.Direction,
                                                     transform.BasisY,
                                                     familyInstance.Symbol
                                                        .LookupParameter(FamilySymbolLengthParameterName).AsDouble(),
                                                     familyInstance.Symbol.FamilyName
                                                        .Split(new char[] { '-', '_' }).Last()));

                    middleRampComponents.RemoveAll(
                        f => projectedFamilyInstances.Contains(f));
                }
                else
                {
                    return output;
                }
            }

            return output;
        }

        private List<FamilyInstance> GetMiddleRampComponents(List<FamilyInstance> rampComponents)
        {
            List<FamilyInstance> middleRampComponents = rampComponents
                .Where(s => s.Symbol.FamilyName
                    .Split(new char[] { '-', '_' }).Length == 7)
                .Distinct()
                .ToList();

            if (middleRampComponents.Count < 2)
            {
                throw new NullReferenceException("Select correct ramp configuration.");
            }

            return middleRampComponents;
        }
    }
}