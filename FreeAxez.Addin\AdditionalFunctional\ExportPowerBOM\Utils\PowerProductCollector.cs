﻿using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Utils;
using FreeAxez.Addin.Models;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBOM.Utils
{
    public class PowerProductCollector
    {
        private static Dictionary<Type, string> _optionalAccessoryNameByType = new Dictionary<Type, string>()
        {
            { typeof(Grommet), "Grommet" },
            { typeof(CableSleeve), "Cable Sleeve" },
            { typeof(WallBoot), "In-Wall Boot" },
            { typeof(LadderBoot), "Ladder Boot" },
            { typeof(JunctionBox), "Junction Box" },
            { typeof(BaseUnitCutout), "Standard Base Unit w/Cutout" },
            { typeof(BaseUnitHighCapacity), "High Capacity Base Unit" },
            { typeof(BaseUnitHalfHighCapacity), "High Capacity Half Base Unit" },
            { typeof(PlateHighCapacity), "High Capacity Plate" },
            { typeof(PlateLargeHighCapacity), "High Capacity Plate Large" },
            { typeof(OutletCover), "Outlet Cover" },
            { typeof(BaseUnitOutlet), "Base Unit with Grommet" },
            { typeof(PlateCornerOutlet), "Corner Plate with Grommet" },
            { typeof(PlateChannelOutlet), "Channel Plate with Grommet" },
            { typeof(OutletCoverPlate), "Outlet Cover Plate" },
        };

        private static Dictionary<Type, string> _guaranteedAccessoryNameByType = new Dictionary<Type, string>()
        {
            { typeof(PowerVoiceDataFloorBox), "Power Voice Data Floor Box" },
            { typeof(SpinLockCircuitSplitter), "SpinLock Circuit Splitter" },
        };

        public static List<string> GetAccessoriesNames()
        {
            var output = new List<string>();

            var accessories = CollectAccessories();

            // Filter only optional accesories
            foreach (var accessory in accessories)
            {
                if (_optionalAccessoryNameByType.ContainsKey(accessory.GetType()))
                {
                    output.Add(_optionalAccessoryNameByType[accessory.GetType()]);
                }
            }

            output.Sort();

            return output.Distinct().ToList();
        }

        public static List<Product> Collect(List<string> accessoryNames = null)
        {
            var output = new List<Product>();

            // Collect power elements
            output.AddRange(FloorBox.Collect());
            output.AddRange(Track.Collect());
            output.AddRange(Whip.CollectTrackWhips());
            output.AddRange(Whip.CollectSpinLocks());

            // Filter power elements by global rules
            output = output.Where(p => GlobalBOMProductFilter.PassesFilter(p.Element)).ToList();

            // Collect accessories (already filtered by global filter)
            output.AddRange(CollectAccessories(accessoryNames));

            return output;
        }

        public static bool IsAccessory(Product product)
        {
            return _guaranteedAccessoryNameByType.ContainsKey(product.GetType())
                || _optionalAccessoryNameByType.ContainsKey(product.GetType());
        }

        /// <summary>
        /// Returns all accessories or filters them by name. 
        /// </summary>
        /// <param name="accessoryNames">
        /// - null: Returns all accessories.  
        /// - Empty: Returns only guaranteed accessories.  
        /// - With values: Returns guaranteed accessories and filtered by the given names.  
        /// </param>
        /// <returns>Filtered list of accessories based on the provided criteria.</returns>
        private static List<Product> CollectAccessories(List<string> accessoryNames = null)
        {
            var output = new List<Product>();

            // Collect accessories
            output.AddRange(Grommet.Collect());
            output.AddRange(CableSleeve.Collect());
            output.AddRange(WallBoot.Collect());
            output.AddRange(LadderBoot.Collect());
            output.AddRange(JunctionBox.Collect());
            output.AddRange(PowerVoiceDataFloorBox.Collect());
            output.AddRange(SpinLockCircuitSplitter.Collect());
            output.AddRange(BaseUnitCutout.Collect());
            output.AddRange(BaseUnitHighCapacity.Collect());
            output.AddRange(BaseUnitHalfHighCapacity.Collect());
            output.AddRange(PlateHighCapacity.Collect());
            output.AddRange(PlateLargeHighCapacity.Collect());
            output.AddRange(OutletCover.Collect());
            output.AddRange(BaseUnitOutlet.Collect());
            output.AddRange(PlateCornerOutlet.Collect());
            output.AddRange(PlateChannelOutlet.Collect());
            output.AddRange(OutletCoverPlate.Collect());

            if (accessoryNames != null)
            {
                output = output
                    .Where(p => _guaranteedAccessoryNameByType.ContainsKey(p.GetType())
                             || accessoryNames.Contains(_optionalAccessoryNameByType[p.GetType()]))
                    .ToList();
            }

            // Filter products by global rules
            output = output.Where(p => GlobalBOMProductFilter.PassesFilter(p.Element)).ToList();

            return output;
        }
    }
}
