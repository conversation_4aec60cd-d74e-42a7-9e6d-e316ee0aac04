﻿<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Navigation.UserNavigation"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls"
             mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <StackPanel>
            <controls:NavButton Command="{Binding FamiliesPageCommand}"
                                GroupName="NavRadioGroup"
                                IconTemplate="{StaticResource FamilyIcon}"
                                ButtonText="Families"
                                IsChecked="True"/>
            <controls:NavButton Command="{Binding DetailsPageCommand}"
                                GroupName="NavRadioGroup"
                                IconTemplate="{StaticResource DocumentIcon}"
                                ButtonText="Details"/>
            <controls:NavButton Command="{Binding FamilyUpdatesPageCommand}"
                                GroupName="NavRadioGroup"
                                IconTemplate="{StaticResource RefreshIcon}"
                                ButtonText="Family Updates"/>
            <controls:NavButton Command="{Binding UnmatchedFamiliesPageCommand}"
                                GroupName="NavRadioGroup"
                                IconTemplate="{StaticResource AlertIcon}"
                                ButtonText="Unmatches"/>
            <controls:NavButton Command="{Binding HistoryPageCommand}"
                                GroupName="NavRadioGroup"
                                IconTemplate="{StaticResource HistoryIcon}"
                                ButtonText="History"/>

            <!--<RadioButton Style="{StaticResource NavButtonStyle}"
                         Command="{Binding FamiliesPageCommand}"
                         IsChecked="True">
                        <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource FamilyIcon}"
                                    VerticalAlignment="Center"
                                    Margin="15 0 0 0"/>
                            <TextBlock Text="Families"
                                       Style="{StaticResource TextH4}"
                                       Foreground="White"
                                       Margin="10 0 0 0"/>
                        </StackPanel>
                    </RadioButton>
                    <RadioButton Style="{StaticResource NavButtonStyle}"
                                 Command="{Binding FamilyUpdatesPageCommand}">
                        <StackPanel Orientation="Horizontal">
                            <ContentControl Template="{StaticResource RefreshIcon}"
                                            VerticalAlignment="Center"
                                            Margin="15 0 0 0"/>
                            <TextBlock Text="Family Updates"
                                       Style="{StaticResource TextH4}"
                                       Foreground="White"
                                       Margin="10 0 0 0"/>
                        </StackPanel>
                    </RadioButton>
            <RadioButton Style="{StaticResource NavButtonStyle}"
                         Command="{Binding UnmatchedFamiliesPageCommand}">
                <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource AlertIcon}"
                                    VerticalAlignment="Center"
                                    Margin="15 0 0 0"/>
                    <TextBlock Text="Unmatched Families"
                               Style="{StaticResource TextH4}"
                               Foreground="White"
                               Margin="10 0 0 0"/>
                </StackPanel>
            </RadioButton>
                    <RadioButton Style="{StaticResource NavButtonStyle}"
                                 Command="{Binding HistoryPageCommand}">
                        <StackPanel Orientation="Horizontal">
                            <ContentControl Template="{StaticResource HistoryIcon}"
                                            VerticalAlignment="Center"
                                            Margin="15 0 0 0"/>
                            <TextBlock Text="History"
                                       Style="{StaticResource TextH4}"
                                       Foreground="White"
                                       Margin="10 0 0 0"/>
                        </StackPanel>
                    </RadioButton>-->
        </StackPanel>
    </Grid>
</UserControl>
