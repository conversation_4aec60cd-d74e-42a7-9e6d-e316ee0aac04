﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Views;
using FreeAxez.Addin.AdditionalFunctional.WhipExport.ViewModels;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Models;
using System.Windows.Interop;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    public class ExportPowerBomCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            if (!AllWhipLengthsAreValid()) 
            {
                var continueExport = MessageWindow.ShowDialog(
                    "Some whips have an invalid length for the selected type.\n" +
                    "Fix them using Check Whip and retry export, or press Agree to continue anyway.", 
                    Infrastructure.UI.Enums.MessageType.Warning);

                if (continueExport != true)
                {
                    return Result.Cancelled;
                }
            }

            var powerBomView = new ExportPowerBomView();
            var handler = new WindowInteropHelper(powerBomView);
            handler.Owner = RevitManager.UIApplication.MainWindowHandle;
            powerBomView.ShowDialog();

            return Result.Succeeded;
        }

        private bool AllWhipLengthsAreValid()
        {
            var whipValidationModels = Whip.CollectFloorBoxWhips()
                .Select(w => new WhipViewModel(w.Element))
                .ToList();

            return whipValidationModels.All(w => w.CorrectTypeLength == "True");
        }
    }
}