﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Models;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils
{
    public class PanelScheduleCreator
    {
        private List<PanelScheduleView> _schedules;


        public PanelScheduleCreator()
        {
            _schedules = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(PanelScheduleView))
                .Cast<PanelScheduleView>()
                .Where(v => !v.IsPanelScheduleTemplate())
                .ToList();
        }


        public bool CreateIfNotExist(CircuitAssembly circuit)
        {
            if (circuit.Panel == null || circuit.Box == null || IsPanelScheduleExist(circuit.Panel.Id))
            {
                return false;
            }
            _schedules.Add(PanelScheduleView.CreateInstanceView(RevitManager.Document, circuit.Panel.Id));
            return true;
        }

        private bool IsPanelScheduleExist(ElementId panelId)
        {
            return _schedules.Any(s => s.GetPanel().GetIntegerValue() == panelId.GetIntegerValue());
        }
    }
}
