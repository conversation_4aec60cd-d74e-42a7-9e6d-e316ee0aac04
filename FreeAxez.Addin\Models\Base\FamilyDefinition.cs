﻿using Autodesk.Revit.DB;

namespace FreeAxez.Addin.Models.Base
{
    public class FamilyDefinition
    {
        public BuiltInCategory BuiltInCategory { get; set; }
        public List<string> FamilyNamesContains { get; set; }
        public List<string> FamilyNamesEndWith { get; set; }
        public List<string> FamilyNamesNotContains { get; set; }


        //private static readonly Dictionary<Type, FamilyDefinition> _definitions = new()
        //{
        //    { typeof(BaseUnit), new FamilyDefinition()
        //    {
        //        BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
        //        FamilyNamesContains = new List<string>(),
        //        FamilyNamesEndWith = new List<string>
        //        {
        //            "Base_Unit-Standard",
        //            "Base_Unit"
        //        },
        //        FamilyNamesNotContains = new List<string>
        //        {
        //            "Outlet",
        //            "High_Capacity",
        //            "Detail" // (Detail Only- Dont Use)
        //        }
        //    } },
        //};

        //public static IReadOnlyDictionary<Type, FamilyDefinition> Definitions => _definitions;
    }
}
