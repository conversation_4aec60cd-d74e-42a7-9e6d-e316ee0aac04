﻿using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Dialog;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages;

public class CategoriesPageVm : BasePageVm
{
    private readonly DialogManager _dialogManager;
    private ObservableCollection<LibraryCategoryDto> _categories;
    private bool _isLoading;

    public CategoriesPageVm()
    {
        _dialogManager = new DialogManager();
        LoadDataCommand = new AsyncRelayCommand(async () => await LoadData());
        AddCategoryCommand = new AsyncRelayCommand(async () => await AddCategory());
        EditCategoryCommand = new AsyncRelayCommand(async param => await EditCategory((LibraryCategoryDto)param));
        DeleteCategoryCommand =
            new AsyncRelayCommand(async param => await ConfirmAndDeleteCategory((LibraryCategoryDto)param));

        Task.Run(LoadData);
    }

    public ICommand LoadDataCommand { get; }
    public ICommand AddCategoryCommand { get; }
    public ICommand EditCategoryCommand { get; }
    public ICommand DeleteCategoryCommand { get; }

    public bool IsLoading
    {
        get => _isLoading;
        set
        {
            _isLoading = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<LibraryCategoryDto> Categories
    {
        get => _categories;
        set
        {
            _categories = value;
            OnPropertyChanged();
        }
    }

    private async Task LoadData()
    {
        try
        {
            IsLoading = true;
            var result = await DataLoader.LoadCategoriesPageData();
            Categories = result.Categories;
        }
        catch (Exception ex)
        {
            LogHelper.Error($"Failed to load category data due to {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async void OnDialogClose(bool? result)
    {
        if (result == true) await LoadData();
    }

    private Task AddCategory()
    {
        _dialogManager.ShowAddCategoryDialog(OnDialogClose);
        return Task.CompletedTask;
    }

    private Task EditCategory(LibraryCategoryDto category)
    {
        _dialogManager.ShowEditCategoryDialog(category, OnDialogClose);
        return Task.CompletedTask;
    }

    private Task ConfirmAndDeleteCategory(LibraryCategoryDto category)
    {
        _dialogManager.ShowDeleteCategoryDialog(category, OnDialogClose);
        return Task.CompletedTask;
    }
}