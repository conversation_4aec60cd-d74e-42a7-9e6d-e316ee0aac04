﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System;

#nullable enable

namespace FreeAxez.Addin.Services
{
    public class RevitGlobalParametersHandler
    {
        private readonly Document _document;

        public RevitGlobalParametersHandler(Document document)
        {
            _document = document;
        }

        /// <summary>
        /// Get Revit global parameter value by parameter name.
        /// </summary>
        /// <typeparam name="ValueType">ParameterValue object.</typeparam>
        /// <typeparam name="Value">ParemeterValue value.</typeparam>
        /// <param name="parameterName">Global parameter name.</param>
        /// <returns>Global parameter value</returns>
        /// <exception cref="ArgumentNullException">Throws when global parameter with this name is not found.</exception>
        /// <exception cref="ArgumentException">Throws when this is not global parameter.</exception>
        public Value? GetGlobalParameterValue<ValueType, Value>(string parameterName)
            where ValueType : notnull, ParameterValue
        {
            if (GlobalParametersManager.AreGlobalParametersAllowed(_document))
            {
                ElementId parameterId = GlobalParametersManager
                    .FindByName(RevitManager.Document, parameterName);

                if (parameterId.GetIntegerValue() == -1)
                    throw new ArgumentNullException(parameterName, $"\"{parameterName}\" global parameter was not found.");

                if (_document.GetElement(parameterId) is not GlobalParameter globalParameter)
                    throw new ArgumentException("This is not valid global parameter.");

                ParameterValue paramterValue = globalParameter.GetValue();

                ValueType? genericParameterValue = paramterValue as ValueType;
                return genericParameterValue switch
                {
                    StringParameterValue stringParameterValue
                        when typeof(Value) == typeof(string) => (Value)(object)stringParameterValue.Value,
                    DoubleParameterValue doubleParameterValue
                        when typeof(Value) == typeof(double) => (Value)(object)doubleParameterValue.Value,
                    IntegerParameterValue integerParameterValue
                        when typeof(Value) == typeof(int) => (Value)(object)integerParameterValue.Value,
                    ElementIdParameterValue elementIdParameterValue
                        when typeof(Value) == typeof(ElementId) => (Value)(object)elementIdParameterValue.Value,
                    _ => default,
                };
            }

            return default;
        }
    }
}