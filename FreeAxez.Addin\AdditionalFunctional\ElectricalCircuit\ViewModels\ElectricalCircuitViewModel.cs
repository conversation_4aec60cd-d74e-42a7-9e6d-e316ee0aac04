﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Enums;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Models;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.ViewModels
{
    public class ElectricalCircuitViewModel : WindowViewModel
    {
        public ElectricalCircuitViewModel(List<CircuitAssembly> circuitAssemblies)
        {
            CircuitAssemblies = circuitAssemblies
                .OrderBy(c => c.CircuitModelStatus == CircuitModelStatus.Valid) // Invalid first
                .ThenBy(c => c.CircuitElectricalStatus == CircuitElectricalStatus.Valid 
                          || c.CircuitElectricalStatus == CircuitElectricalStatus.Unknown) // Invalid first
                .ThenBy(c => c.Level)
                .ThenBy(c => c.CircuitAssemblyType)
                .ThenBy(c => c.Panel?.Id.GetIntegerValue())
                .ThenBy(c => c.Track?.Id.GetIntegerValue())
                .ThenBy(c => c.Whip?.Id.GetIntegerValue())
                .ThenBy(c => c.Box?.Id.GetIntegerValue())
                .ToList();

            IdCommand = new RelayCommand(OnIdCommandExecute);
            CloseCommand = new RelayCommand(OnCloseCommandExecute);
        }

        public List<CircuitAssembly> CircuitAssemblies { get; private set; }

        public ICommand IdCommand { get; set; }
        private void OnIdCommandExecute(object p)
        {
            if (p != null && int.TryParse(p.ToString(), out int id))
            {
                if (id != -1 && RevitManager.Document.GetElement(new ElementId(id)) != null)
                {
                    var elementIds = new List<ElementId>() { new ElementId(id) };
                    RevitManager.UIDocument.Selection.SetElementIds(elementIds);
                    RevitManager.UIDocument.ShowElements(elementIds);
                }
            }
        }

        public ICommand CloseCommand { get; set; }
        private void OnCloseCommandExecute(object p)
        {
            (p as Window).Close();
        }
    }
}
