﻿using System;
using System.Globalization;
using System.Text;
using System.Windows.Data;

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Converters;

public class FullNameToShortNameConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        string fullName = value as string;

        if (string.IsNullOrEmpty(fullName)) return value;

        StringBuilder shortNameStringBuilder = new StringBuilder();
        string[] fullNameSplited = fullName.Split(' ');
        foreach (string splittedName in fullNameSplited)
            shortNameStringBuilder.Append(splittedName.Substring(0, 1));

        return shortNameStringBuilder.ToString();
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
