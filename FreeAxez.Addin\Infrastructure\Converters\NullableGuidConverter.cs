using System;
using System.Globalization;
using System.Windows.Data;

namespace FreeAxez.Addin.Infrastructure.Converters
{
    public class NullableGuidConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Guid guid)
            {
                // If it's Guid.Empty, return null to clear selection
                return guid == Guid.Empty ? null : guid;
            }
            
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Guid guid)
            {
                // If it's Guid.Empty, return null to clear the mapping
                return guid == Guid.Empty ? (Guid?)null : guid;
            }
            
            // If value is null, return null
            return value;
        }
    }
}
