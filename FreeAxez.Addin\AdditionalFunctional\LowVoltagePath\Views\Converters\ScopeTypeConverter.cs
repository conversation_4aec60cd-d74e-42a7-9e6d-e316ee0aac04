using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;
using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Markup;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Views.Converters
{
    /// <summary>
    /// Converter for ScopeType enum to boolean for radio buttons
    /// </summary>
    public class ScopeTypeConverter : MarkupExtension, IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ScopeType scopeType && parameter is string parameterString)
            {
                if (Enum.TryParse<ScopeType>(parameterString, out var targetScope))
                {
                    return scopeType == targetScope;
                }
            }
            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isChecked && isChecked && parameter is string parameterString)
            {
                if (Enum.TryParse<ScopeType>(parameterString, out var targetScope))
                {
                    return targetScope;
                }
            }
            return Binding.DoNothing;
        }

        public override object ProvideValue(IServiceProvider serviceProvider)
        {
            return this;
        }
    }
}
