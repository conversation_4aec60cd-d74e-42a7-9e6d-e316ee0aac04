﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.FindAndReplaceViewName.Views.FindAndReplaceViewNameView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FindAndReplaceViewName.Views"
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen"
        Title="Rename View" 
        Height="800" 
        MinHeight="300"
        Width="750"
        MinWidth="300">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}"/>
    </Window.Style>

    <Grid Margin="0,10,0,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
            
        <!-- Search field -->
        <DockPanel Grid.Row="0" Margin="0,0,0,10">
            <TextBlock Text="Search:" 
                        Style="{StaticResource TextH5}"
                        VerticalAlignment="Center"
                       Width="60"
                        Margin="0,0,10,0"/>
            <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" 
                        Style="{StaticResource UiTextBox}"
                        Padding="5" />
        </DockPanel>

        <!-- Replace field -->
        <DockPanel Grid.Row="1" Margin="0,0,0,10">
            <TextBlock Text="Replace:" 
                       Style="{StaticResource TextH5}"
                       VerticalAlignment="Center" 
                       Width="60"
                       Margin="0,0,10,0"/>
            <TextBox Text="{Binding ReplaceText, UpdateSourceTrigger=PropertyChanged}" 
                        Style="{StaticResource UiTextBox}"
                        Padding="5" />
        </DockPanel>

        <!-- Filter panel -->
        <DockPanel Grid.Row="2"
                   Margin="0,0,0,10">
            <Button DockPanel.Dock="Right"
                    Content="X Clear"
                    Command="{Binding ClearFiltersCommand}"
                    Width="90"
                    Height="25"
                    Style="{StaticResource ButtonOutlinedRed}" />
            <TextBlock Text="Filter:"
                       Style="{StaticResource TextH5}"
                       Width="60"
                       Margin="0,0,10,0" />
            <CheckBox Content="View Name"
                      VerticalAlignment="Center"
                      IsChecked="{Binding ViewNameFilter}"
                      Margin="0,0,10,0" />
            <CheckBox Content="Sheet Title"
                      VerticalAlignment="Center"
                      IsChecked="{Binding SheetTitleFilter}" />
        </DockPanel>

        <!-- Models list -->
        <ListView Grid.Row="3" 
                  x:Name="ItemsListView"
                  ItemsSource="{Binding FilteredItems}" 
                  BorderThickness="1" 
                  BorderBrush="#DDDDDD"
                  Margin="0,0,0,10"
                  Style="{StaticResource ListViewBase}"
                  SelectionMode="Extended">
            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                    <Setter Property="Padding" Value="8" />
                    <Setter Property="BorderThickness" Value="0,0,0,1" />
                    <Setter Property="BorderBrush" Value="#EEEEEE" />
                </Style>
            </ListView.ItemContainerStyle>
            <ListView.ItemTemplate>
                <DataTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <CheckBox Grid.Column="0" 
                                  IsChecked="{Binding IsSelected}" 
                                  VerticalAlignment="Center"
                                  Margin="0,0,10,0"
                                  Checked="CheckBox_CheckedChanged"
                                  Unchecked="CheckBox_CheckedChanged"/>

                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <local:HighlightedTextBlock 
                                Text="{Binding Name}" 
                                SearchText="{Binding DataContext.SearchText, RelativeSource={RelativeSource AncestorType=ListView}}"
                                TextWrapping="Wrap" 
                                FontWeight="SemiBold"/>
                            <TextBlock Text="{Binding ViewTypeName}" 
                                       Foreground="#666666" 
                                       FontSize="11" 
                                       Margin="0,2,0,0"/>
                        </StackPanel>
                    </Grid>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>

        <!-- Information panel -->
        <StackPanel Grid.Row="4" 
                    Orientation="Horizontal" 
                    Margin="0,0,0,10">
            <TextBlock Text="{Binding FilteredCount, StringFormat=Results: {0}}" 
                       Style="{StaticResource TextH6}"
                       Margin="0,0,20,0"/>
            <TextBlock Text="{Binding SelectedCount, StringFormat=Selected: {0}}"
                       Style="{StaticResource TextH6}"/>
        </StackPanel>

        <!-- Button panel -->
        <UniformGrid Grid.Row="5" Rows="1" Columns="5">
            <Button Content="Check Filtered" 
                    Command="{Binding SelectAllCommand}" 
                    Style="{StaticResource ButtonOutlinedBlue}"
                    Margin="0,0,5,0" 
                    Padding="10,5"/>

            <Button Content="Uncheck Filtered" 
                    Command="{Binding ClearSelectionCommand}" 
                    Style="{StaticResource ButtonOutlinedBlue}"
                    Margin="5,0" 
                    Padding="10,5"/>

            <Button Content="Rename" 
                    Command="{Binding RenameCommand}" 
                    Style="{StaticResource ButtonOutlinedBlue}"
                    Margin="5,0" 
                    Padding="10,5"/>

            <Button Content="Apply" 
                    Command="{Binding ApplyCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"
                    Style="{StaticResource ButtonSimpleGreen}"
                    Margin="5,0" 
                    Padding="10,5"/>

            <Button Content="Cancel" 
                    Command="{Binding CancelCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"
                    Style="{StaticResource ButtonSimpleRed}"
                    Margin="5,0,0,0" 
                    Padding="10,5"/>
        </UniformGrid>
    </Grid>

</Window>
