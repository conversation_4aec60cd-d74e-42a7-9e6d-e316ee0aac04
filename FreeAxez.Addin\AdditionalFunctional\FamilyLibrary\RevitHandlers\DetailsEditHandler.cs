using System;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Dialog;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.RevitHandlers
{
    public class DetailsEditHandler : BaseExternalEventHandler
    {
        private LibraryItemDetailsDto _details;
        private object _viewModel;
        private readonly DialogManager _dialogManager;

        public DetailsEditHandler()
        {
            _dialogManager = new DialogManager();
        }

        public void SetData(LibraryItemDetailsDto details, object viewModel)
        {
            _details = details ?? throw new ArgumentNullException(nameof(details));
            _viewModel = viewModel;
        }

        public override void ExecuteInternal(UIApplication app)
        {
            try
            {
                // Show dialog with callback that handles the result
                _dialogManager.ShowEditDetailsDialog(_details, result =>
                {
                    try
                    {
                        // Update ViewModel if dialog was successful
                        if (result == true)
                        {
                            UpdateViewModel(false);
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"Error updating ViewModel after dialog: {ex.Message}");
                        UpdateViewModel(true);
                    }
                    finally
                    {
                        _details = null;
                        _viewModel = null;
                    }
                });
            }
            catch (Exception ex)
            {
                ErrorHandler.HandleError("Error in details edit operation", ex, onError: () => UpdateViewModel(true));
                _details = null;
                _viewModel = null;
            }
        }

        private void UpdateViewModel(bool isError)
        {
            if (_viewModel == null)
                return;

            try
            {
                if (_viewModel is DetailsPageVm detailsVm)
                {
                    if (!isError)
                        _ = detailsVm.LoadData();
                    detailsVm.IsLoading = false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error updating ViewModel: {ex.Message}");
            }
        }
    }
}
