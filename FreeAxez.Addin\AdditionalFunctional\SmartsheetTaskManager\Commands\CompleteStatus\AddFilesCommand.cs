﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.IO;
using System.Linq;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Commands.CompleteStatus;

public class AddFilesCommand : CommandBase
{
    private readonly CompleteStatusViewModel _completeStatusViewModel;

    public AddFilesCommand(CompleteStatusViewModel completeStatusViewModel)
    {
        _completeStatusViewModel = completeStatusViewModel;
    }

    public override void Execute(object parameter)
    {
        var openFileDialog = new Microsoft.Win32.OpenFileDialog
        {
            Title = "Select files.",
            Multiselect = true,
            Filter = "PDF Files|*.pdf|All Files|*.*"
        };

        if (openFileDialog.ShowDialog() == true)
        {
            string[] selectedFilePaths = openFileDialog.FileNames;

            IEnumerable<string> fileNames = new List<string>();
            if (_completeStatusViewModel.AttachedFilePaths.Any())
            {
                fileNames = _completeStatusViewModel.AttachedFilePaths.Select(Path.GetFileName);
            }

            foreach (var selectedFilePath in selectedFilePaths)
            {
                string selectedFileName = Path.GetFileName(selectedFilePath);

                if (!fileNames.Contains(selectedFileName))
                {
                    _completeStatusViewModel.AttachedFilePaths.Add(selectedFilePath);
                }
            }
        }
    }
}
