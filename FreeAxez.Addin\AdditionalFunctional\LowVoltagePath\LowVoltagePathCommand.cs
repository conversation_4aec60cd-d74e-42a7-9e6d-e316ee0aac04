using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    internal class LowVoltagePathCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            try
            {
                // Show UI dialog to get user settings
                var viewModel = new LowVoltagePathViewModel();
                var view = new LowVoltagePathView
                {
                    DataContext = viewModel
                };

                RevitManager.SetRevitAsWindowOwner(view);
                var dialogResult = view.ShowDialog();

                // User cancelled the dialog
                if (dialogResult != true)
                {
                    return Result.Cancelled;
                }

                // Get settings from the dialog
                var settings = viewModel.Settings;

                // Initialize services
                var dataCollector = new LowVoltageDataCollector(RevitManager.Document, RevitManager.Document.ActiveView);
                var validationService = new LowVoltageValidationService(RevitManager.Document, RevitManager.Document.ActiveView);
                var reportService = new LowVoltageReportService();

                // Collect data based on scope
                List<CurveElement> lines;
                List<FamilyInstance> outlets;

                if (settings.ScopeType == ScopeType.SelectedElements)
                {
                    (lines, outlets) = dataCollector.CollectSelectedElements();
                }
                else
                {
                    lines = dataCollector.CollectLowVoltageLines();
                    outlets = dataCollector.CollectLowVoltageOutlets();
                }

                // Validate data
                var validationResult = validationService.ValidateAll(lines, outlets, settings);

                // Show validation results and get user confirmation
                if (!reportService.ShowValidationResults(validationResult))
                {
                    return Result.Cancelled;
                }

                // Process the low voltage path generation
                var processor = new LowVoltageProcessor(RevitManager.Document, settings);
                var executionReport = processor.ProcessLowVoltagePath(lines, outlets);

                // Show execution results
                reportService.ShowExecutionResults(executionReport);

                return executionReport.IsSuccessful ? Result.Succeeded : Result.Failed;
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog($"An error occurred: {ex.Message}", MessageType.Error);
                return Result.Failed;
            }
        }
    }
}
