using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using NetTopologySuite.Geometries;
using System.Text.RegularExpressions;
using System.Windows.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    internal class LowVoltagePathCommand : BaseExternalCommand
    {
        private List<FamilySymbol> _annotationSymbols;

        public override Result Execute()
        {
            // Verify if the view is a floor plan
            if (IsFloorPlanView() == false)
            {
                MessageWindow.ShowDialog("This command can only be executed on a floor plan.", MessageType.Notify);
                return Result.Cancelled;
            }

            // Collect all LV MC lines on the active view, if no lines are found, show a message box and exit (LowVoltageLineCollector)
            var lowVoltageLines = CollectLowVoltageLines();
            if (lowVoltageLines.Count == 0)
            {
                MessageWindow.ShowDialog("No LV or MC lines found in the active view.", MessageType.Notify);
                return Result.Cancelled;
            }

            // Collect all outlet on the active view with LV MC count greater than 0 (LowVoltageOutletCollector)
            var lowVoltageOutlets = CollectLowVoltageOutlets();
            if (lowVoltageOutlets.Count == 0)
            {
                MessageWindow.ShowDialog("No low voltage electrical fixtures found in the current view.", MessageType.Notify);
                return Result.Cancelled;
            }

            // TODO: Group outlet and lines by LV MC tag (LowVoltageGroupService)

            // Convert outlets and lines to geometry models (NTSConverter)
            var outlets = lowVoltageOutlets
                .Select(i => new OutletModel()
                {
                    Id = i.Id.GetIntegerValue(),
                    Location = NTSConverter.XYZToPoint((i.Location as LocationPoint).Point),
                    WireCount = 1, // TODO: Default value, will be updated later by parameter values
                })
                .ToList();

            var lines = lowVoltageLines
                .Select(l => NTSConverter.CurveToLineString(l.GeometryCurve))
                .ToList();

            // Process the geometry models to create railing models and tag models (Buisness Logic)
            var lineGroupService = new LineGroupService();
            var lineGroups = lineGroupService.GroupLines(lines);

            var allFixedLines = new List<LineString>();
            var allOutlets = new List<OutletModel>();
            var allAnnotations = new List<AnnotationModel>();
            var allRailings = new List<RailingModel>();

            foreach (var group in lineGroups)
            {
                var groupOutlets = outlets.Where(o => group.Any(l => l.Distance(o.Location) < 2.0)).ToList();
                if (!groupOutlets.Any()) continue;

                var normalizationService = new LineNormalizationService();
                var minSegmentLength = RevitManager.Application.ShortCurveTolerance;
                var fixedLines = normalizationService.NormalizeLines(group, minSegmentLength);

                var analyzerService = new GeometryAnalyzerService();
                var geometryTree = analyzerService.AnalyzeGeometry(fixedLines, groupOutlets);

                var railingsGenerator = new RailingsGeneratorService();
                var railings = railingsGenerator.GenerateRailings(geometryTree);
                allRailings.AddRange(railings);

                var annotationPlacer = new AnnotationPlacerService();
                var annotations = annotationPlacer.PlaceAnnotations(railings, fixedLines, geometryTree.Root);

                allFixedLines.AddRange(fixedLines);
                allOutlets.AddRange(groupOutlets);
                allAnnotations.AddRange(annotations);
            }

            var exportService = new GeometryExportService();
            var geometryFilePath = @"C:\Users\<USER>\Desktop\geometry.json";
            exportService.ExportToJson(allFixedLines, allOutlets, allAnnotations, geometryFilePath);

            // Convert the geometry models to Revit geometry (NTSConverter)
            // Convert the railing models to Revit railings (RailingCreator)
            var revitRailings = new List<Railing>();
            using (var t = new Transaction(RevitManager.Document, "Create Railings"))
            {
                t.Start();

                var levelId = RevitManager.Document.ActiveView.GenLevel.Id;
                var railingType = new FilteredElementCollector(RevitManager.Document)
                    .OfClass(typeof(RailingType))
                    .FirstOrDefault() as RailingType;

                foreach (var railing in allRailings)
                {
                    var curveLoop = NTSConverter.LineStringToCurveLoop(railing.Path);
                    var revitRailing = Railing.Create(RevitManager.Document, curveLoop, railingType.Id, levelId);
                    revitRailing.LookupParameter("Quantity")?.Set(railing.WireCount);
                    revitRailings.Add(revitRailing);
                }

                t.Commit();
            }

            // Convert the tag models to Revit tags (AnnotationCreator)
            var createdAnnotations = new List<FamilyInstance>();
            using (var t = new Transaction(RevitManager.Document, "Create Annotation"))
            {
                t.Start();
                foreach (var annotation in allAnnotations)
                {
                    var origin = NTSConverter.PointToXYZ(annotation.Location);
                    var annotationSymbool = GetAnnotationSymbol(annotation.Orientation);
                    var annotationFamily = RevitManager.Document.Create.NewFamilyInstance(
                        origin, annotationSymbool, RevitManager.Document.ActiveView);
                    createdAnnotations.Add(annotationFamily);
                }
                t.Commit();
            }

            // Calculate quantity in Revit tags based on Revit tags and Revit railings (Separate command logic)
            var wireCountingService = new WireCountingService(revitRailings, createdAnnotations);
            wireCountingService.CountWires();

            // TODO: Remove lines from the model

            // TODO: Report

            return Result.Succeeded;
        }

        private bool IsFloorPlanView()
        {
            return RevitManager.Document.ActiveView.ViewType == ViewType.FloorPlan;
        }

        private List<CurveElement> CollectLowVoltageLines()
        {
            var lowVoltageRegex = new Regex(@"(LV|MC)[\s-_]*\d"); // LV1, LV_4, MC - 2, etc.

            return new FilteredElementCollector(RevitManager.Document, RevitManager.Document.ActiveView.Id)
                .OfClass(typeof(CurveElement))
                .WhereElementIsNotElementType()
                .Cast<CurveElement>()
                .Where(line => lowVoltageRegex.IsMatch(line.LineStyle.Name))
                .ToList();
        }

        private List<FamilyInstance> CollectLowVoltageOutlets()
        {
            return new FilteredElementCollector(RevitManager.Document, RevitManager.Document.ActiveView.Id)
                .OfCategory(BuiltInCategory.OST_ElectricalFixtures)
                .WhereElementIsNotElementType()
                .Cast<FamilyInstance>()
                .Where(i => HasLowVoltageWires(i))
                .ToList();
        }

        private List<Parameter> GetLowVoltageCountParameters(FamilyInstance familyInstance)
        {
            var output = new List<Parameter>();
            foreach (Parameter param in familyInstance.ParametersMap)
            {
                if (Regex.IsMatch(param.Definition.Name, @"^(LV|MC)[\s-_]*\d[\s-_]*Count$") // LV1 - Count, etc.
                    && param.StorageType == StorageType.Integer) // TODO: "MC 1 - Count" is a text parameter, need to handle it 
                {
                    output.Add(param);
                }
            }
            return output;
        }

        private bool HasLowVoltageWires(FamilyInstance familyInstance)
        {
            return GetLowVoltageCountParameters(familyInstance).Any(p => p.AsInteger() > 0);
        }

        public FamilySymbol GetAnnotationSymbol(Direction direction)
        {
            if (_annotationSymbols == null)
            {
                _annotationSymbols = new FilteredElementCollector(RevitManager.Document)
                    .OfClass(typeof(FamilySymbol))
                    .OfCategory(BuiltInCategory.OST_GenericAnnotation)
                    .Cast<FamilySymbol>()
                    .Where(s => s.FamilyName == "FA-Low_Voltage_Pathway_Arrow_Annotation") // TODO: Add to service constants
                    .ToList();
            }

            // TODO: Add validation for the annotation symbols
            return _annotationSymbols.FirstOrDefault(s => s.Name.Equals(direction.ToString())) ?? _annotationSymbols.FirstOrDefault();
        }
    }
}
