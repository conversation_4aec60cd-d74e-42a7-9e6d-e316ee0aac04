using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Details
{
    /// <summary>
    /// Helper class for detail selection operations
    /// </summary>
    public static class DetailSelectionHelper
    {
        /// <summary>
        /// Gets all drafting views from the document
        /// </summary>
        public static List<ViewDrafting> GetDraftingViews(Document document)
        {
            return new FilteredElementCollector(document)
                .OfClass(typeof(ViewDrafting))
                .Cast<ViewDrafting>()
                .ToList();
        }

        /// <summary>
        /// Checks if a drafting view contains non-empty content (excluding title blocks)
        /// </summary>
        public static bool IsDraftingViewNonEmpty(Document document, ViewDrafting view)
        {
            try
            {
                // Get all elements in the view except title blocks
                var annotations = AnnotationManager.GetAnnotations(view);
                return annotations.Count > 0;
            }
            catch (System.Exception ex)
            {
                LogHelper.Warning($"Failed to check if view {view.Name} is non-empty: {ex.Message}");
                return false;
            }
        }
    }
}
