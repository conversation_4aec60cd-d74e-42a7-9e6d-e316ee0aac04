<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals.AdminDeleteFamily"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             Width="400">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0"
                    Margin="0 0 0 20">
            <TextBlock Style="{DynamicResource TextBase}"
                       Text="Warning!!! Are you sure you want to delete this family version?"
                       Foreground="{StaticResource Red500}"
                       Margin="0 10 0 5"/>

            <TextBlock Style="{DynamicResource TextBase}"
                       TextWrapping="Wrap"
                       Text="If you remove this version of a family, user projects will no longer be searched for the current family name. This is especially true for old projects. Don't do this if you're not sure."
                       Foreground="Black"
                       Margin="0 10 0 5"/>
            <TextBlock Style="{DynamicResource TextBase}"
                       Text="{Binding Name}"
                       Foreground="Black"
                       FontWeight="Bold"
                       Margin="0 10 0 5"/>
        </StackPanel>
            <StackPanel Grid.Row="1" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Right">
                <Button Style="{StaticResource ButtonOutlinedRed}" 
                        Command="{Binding CancelCommand}">
                    Cancel
                </Button>
                <Button Content="Delete Family"
                        Margin="20 0 0 0"
                        Command="{Binding ApplyCommand}"
                        Style="{StaticResource ButtonSimpleBlue}" />
            </StackPanel>
    </Grid>
</UserControl>
