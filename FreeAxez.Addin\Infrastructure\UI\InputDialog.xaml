﻿<Window x:Class="FreeAxez.Addin.Infrastructure.UI.InputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        SizeToContent="WidthAndHeight"
        ResizeMode="NoResize" Loaded="Window_Loaded"
        Topmost="True">
    <StackPanel Margin="10">
        <DockPanel LastChildFill="True" Width="250" Height="25">
            <Label x:Name="label" Content="Label:"/>
            <TextBox x:Name="textBox" TextChanged="textBox_TextChanged" VerticalContentAlignment="Center" />
        </DockPanel>
        <Button x:Name="button" Margin="0,15,0,0"  HorizontalAlignment="Right" Content="OK" Width="80" Height="25" Click="Button_Click"/>
    </StackPanel>
</Window>
