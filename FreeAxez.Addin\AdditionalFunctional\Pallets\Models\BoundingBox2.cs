﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;

namespace FreeAxez.Addin.AdditionalFunctional.Pallets.Models
{
    public class BoundingBox2
    {
        private List<XYZ> _corners;
        private double _minX;
        private double _minY;
        private double _maxX;
        private double _maxY;


        public BoundingBox2(XYZ point, double offset)
        {
            if (offset <= 0)
            {
                throw new Exception("Too less offset for bounding box");
            }

            _minX = point.X - offset;
            _minY = point.Y - offset;
            _maxX = point.X + offset;
            _maxY = point.Y + offset;

            var c1 = new XYZ(_minX, _minY, 0);
            var c2 = new XYZ(_minX, _maxY, 0);
            var c3 = new XYZ(_maxX, _minY, 0);
            var c4 = new XYZ(_maxX, _maxY, 0);
            _corners = new List<XYZ>() { c1, c2, c3, c4 };
        }

        public BoundingBox2(XYZ point, double offsetX, double offsetY)
        {
            if (offsetX <= 0 || offsetY <= 0)
            {
                throw new Exception("Too less offset for bounding box");
            }

            _minX = point.X - offsetX;
            _minY = point.Y - offsetY;
            _maxX = point.X + offsetX;
            _maxY = point.Y + offsetY;

            var c1 = new XYZ(_minX, _minY, 0);
            var c2 = new XYZ(_minX, _maxY, 0);
            var c3 = new XYZ(_maxX, _minY, 0);
            var c4 = new XYZ(_maxX, _maxY, 0);
            _corners = new List<XYZ>() { c1, c2, c3, c4 };
        }


        public List<XYZ> Corners => _corners;


        public bool IsInside(XYZ point)
        {
            return point.X > _minX && point.X < _maxX && point.Y > _minY && point.Y < _maxY;
        }
    }
}
