﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ExportCAD.Models;
using FreeAxez.Addin.AdditionalFunctional.ExportCAD.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Data;
using System.Windows.Forms;
using System.Windows.Input;
using View = Autodesk.Revit.DB.View;

namespace FreeAxez.Addin.AdditionalFunctional.ExportCAD.ViewModels
{
    public class ExportCADViewModel : BaseViewModel
    {
        private string _sheetNameFilter = string.Empty;

        public ExportCADViewModel()
        {
            Prefix = Properties.Settings.Default.ExportCADPrefix;
            FolderPath = Properties.Settings.Default.ExportCADPath;
            ExportColorOptions = Properties.Settings.Default.EхportCADColor;
            IncludeFloatingInformation = Properties.Settings.Default.ExportCADIncludeFloatingInformation;
            ExportGrayOptions = !ExportColorOptions;

            if (!Directory.Exists(FolderPath))
            {
                FolderPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            }

            RevitViews = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(View))
                .WhereElementIsNotElementType()
                .OfType<ViewSheet>()
                .Where(viewSheet => viewSheet.CanBePrinted)
                .Select(viewSheet => new RevitView()
                {
                    Name = $"{viewSheet.SheetNumber} - {viewSheet.Name}",
                    ViewSheet = viewSheet
                })
                .OrderBy(revitView => revitView.Name)
                .ToList();

            RevitViewsCollectionView = CollectionViewSource.GetDefaultView(RevitViews);
            RevitViewsCollectionView.Filter = RevitViewsCollectionViewFilter;

            ExportCommand = new RelayCommand(OnExportCommandExecute);
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
            BrowseCommand = new RelayCommand(OnBrowseCommandExecute);
        }

        public bool ExportGrayOptions { get; set; }

        public bool ExportColorOptions { get; set; }

        public string FolderPath { get; set; }

        public string Prefix { get; set; }

        public List<RevitView> RevitViews { get; set; }

        public bool IncludeFloatingInformation { get; set; }

        public string SheetNameFilter
        {
            get => _sheetNameFilter;
            set
            {
                _sheetNameFilter = value;
                OnPropertyChanged(nameof(SheetNameFilter));
                RevitViewsCollectionView?.Refresh();
            }
        }

        public ICollectionView RevitViewsCollectionView { get; private set; }

        public ICommand ExportCommand { get; set; }
        
        private void OnExportCommandExecute(object p)
        {
            if (!Directory.Exists(FolderPath))
            {
                InfoDialog.ShowDialog("Warning", "The path doesn't exist.");
                return;
            }

            var selectedViews = RevitViews
                .Where(rv => rv.IsCheck)
                .Select(rv => rv.ViewSheet)
                .ToList();

            if (selectedViews.Count == 0)
            {
                InfoDialog.ShowDialog("Warning", "Views are not selected.\nPlease select at least one view.");
                return;
            }

            Properties.Settings.Default.EхportCADColor = ExportColorOptions;
            Properties.Settings.Default.ExportCADPath = FolderPath;
            Properties.Settings.Default.ExportCADPrefix = Prefix;
            Properties.Settings.Default.ExportCADIncludeFloatingInformation = IncludeFloatingInformation;
            Properties.Settings.Default.Save();

            (p as Window).Close();

            var exporter = new CADExporter(FolderPath, Prefix, ExportColorOptions, selectedViews, IncludeFloatingInformation);
            exporter.Export();
        }

        public ICommand CancelCommand { get; set; }
        private void OnCancelCommandExecute(object p)
        {
            (p as Window).Close();
        }

        public ICommand BrowseCommand { get; set; }
        private void OnBrowseCommandExecute(object p)
        {
            var browse = new FolderBrowserDialog();
            browse.Description = "Select the folder to export CAD files.";

            if (Directory.Exists(FolderPath))
            {
                browse.SelectedPath = FolderPath;
            }

            if (browse.ShowDialog() == DialogResult.OK)
            {
                FolderPath = browse.SelectedPath;
                OnPropertyChanged(nameof(FolderPath));
            }
        }

        private bool RevitViewsCollectionViewFilter(object @object)
        {
            if (@object is RevitView revitView)
            {
                string revitViewNameLower = revitView.Name.ToLower();
                string sheetNameFilterLower = _sheetNameFilter.ToLower();

                return revitViewNameLower.Contains(sheetNameFilterLower);
            }

            return false;
        }
    }
}
