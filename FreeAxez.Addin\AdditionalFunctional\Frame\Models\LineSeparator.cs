﻿using Autodesk.Revit.DB;
using System.Collections.Generic;

namespace FreeAxez.Addin.AdditionalFunctional.Frame.Models
{
    public class LineSeparator
    {
        private readonly List<Curve> _selectedLines;
        private readonly double _offset;
        private readonly double _moduleLength;

        public LineSeparator(List<Curve> selectedLines, double offset, double moduleLength)
        {
            _selectedLines = selectedLines;
            _offset = offset;
            _moduleLength = moduleLength;
        }


        public List<StraightElementPoints> StraightElementPoints { get; private set; }
        public List<AngleElement> AngleElements { get; private set; }


        public void CalculateElements()
        {
            StraightElementPoints = CalculateStraightElements();
            AngleElements = CalculateAngleElements();
        }

        private List<StraightElementPoints> CalculateStraightElements()
        {
            var straightElements = new List<StraightElementPoints>();
            var index = 0;
            foreach (var crv in _selectedLines)
            {
                var straightElement = new StraightElementPoints(crv, index, _selectedLines.Count);
                straightElement.CreateStartAndEndOffsetForLine(_offset);
                straightElement.DivideLine(_moduleLength);
                straightElements.Add(straightElement);
                index++;
            }

            return straightElements;
        }

        private List<AngleElement> CalculateAngleElements()
        {
            var elementList = new List<AngleElement>();
            int counter = 0;
            for (int i = 0; i < StraightElementPoints.Count - 1; i++)
            {
                var angleElementData = new AngleElement(StraightElementPoints[i].EndPoint, StraightElementPoints[i].Curve, StraightElementPoints[i + 1].Curve, counter);
                elementList.Add(angleElementData);
                counter++;
            }

            return elementList;
        }
    }
}
