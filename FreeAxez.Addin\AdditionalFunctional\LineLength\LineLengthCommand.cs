﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.LineToFlexPipe.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using System;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.LineLength
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    internal class LineLengthCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var selectedLines = RevitManager.UIDocument.Selection.GetElementIds()
                .Select(id => RevitManager.Document.GetElement(id))
                .Where(e => e is CurveElement)
                .Cast<CurveElement>()
                .ToList();

            if (selectedLines.Count == 0)
            {
                MessageWindow.ShowDialog("No curves have been selected.", MessageType.Notify);
                return Result.Cancelled;
            }

            var length = Math.Round(selectedLines.Sum(l => l.GeometryCurve.Length), 2);

            var mergedLines = LineMerger.GetMergedCurves(selectedLines);
            var firstLinePoints = LinesToHermiteSpline.GetPointsWithOffset(mergedLines.First(), true, 0);
            var hermiteSpline = HermiteSpline.Create(firstLinePoints, false);

            MessageWindow.ShowDialog(
                $"{ConvertToFractionalInches(length)} is the total length of the selected curves.\n" +
                $"{ConvertToFractionalInches(hermiteSpline.Length)} is the calculated length of the whip.",
                MessageType.Success);

            return Result.Succeeded;
        }

        private string ConvertToFractionalInches(double value)
        {
            var units = RevitManager.Document.GetUnits();
            var formatValue = new FormatValueOptions() { AppendUnitSymbol = true };

#if revit2020 || revit2021
            return UnitFormatUtils.Format(units, UnitType.UT_Length, value, true, false, formatValue);
#else
            return UnitFormatUtils.Format(units, SpecTypeId.Length, value, false, formatValue);
#endif
        }
    }
}
