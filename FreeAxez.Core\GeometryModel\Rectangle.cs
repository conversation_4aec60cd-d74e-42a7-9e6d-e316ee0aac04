﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace FreeAxez.Core.GeometryModel
{
    public class Rectangle
    {
        public PointModel BottomLeft;
        public PointModel TopRight;

        public PointModel BottomRight => new PointModel(Right, Bottom);
        public PointModel TopLeft => new PointModel(Left, Top);

        public double Top => TopRight.Y;
        public double Right => TopRight.X;
        public double Bottom => BottomLeft.Y;
        public double Left => BottomLeft.X;
        public double Width => Math.Abs(TopRight.X - BottomLeft.X);
        public double Height => Math.Abs(TopRight.Y - BottomLeft.Y);
        public double CenterX => (TopRight.X + BottomLeft.X) * 0.5;
        public double CenterY => (TopRight.Y + BottomLeft.Y) * 0.5;
        public PointModel Center => new PointModel(CenterX, CenterY);

        public static Rectangle CreateFromPoints(double leftX, double bottomY, double rightX, double topY)
        {
            return new Rectangle(leftX, bottomY, rightX - leftX, topY - bottomY);
        }

        public static Rectangle CreateFromPoints(IEnumerable<PointModel> points)
        {
            var minX = points.Min(p => p.X);
            var maxX = points.Max(p => p.X);
            var minY = points.Min(p => p.Y);
            var maxY = points.Max(p => p.Y);

            return CreateFromPoints(minX, minY, maxX, maxY);
        }

        public Rectangle(double leftX, double bottomY, double width, double height)
        {
            BottomLeft = new PointModel(leftX, bottomY);
            TopRight = new PointModel(leftX + width, bottomY + height);
        }
    }
}
