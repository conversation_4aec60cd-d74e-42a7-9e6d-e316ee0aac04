﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Enums;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.Frame.Utils
{
    public class FrameFamilyProvider
    {
        private const string FrameKey = "Frame";
        private const string FrameArrayKey = "Array";
        private const string CornerLongKey = "Long";
        private const string CornerShortKey = "Short";

        private const string Gridd40Key = "40";
        private const string Gridd70Key = "70";

        public FrameFamilyProvider(GriddType griddType, bool longCorner)
        {
            var symbols = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsElementType()
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .Cast<FamilySymbol>()
                .Where(s => s.FamilyName.Contains(FrameKey))
                .Where(s => s.FamilyName.Contains(FrameArrayKey) 
                         || s.FamilyName.Contains(CornerLongKey) 
                         || s.FamilyName.Contains(CornerShortKey))
                .ToList();

            var gridd = griddType == GriddType.Gridd40 ? Gridd40Key : Gridd70Key;
            var cornerLength = longCorner ? CornerLongKey : CornerShortKey;

            Symbol = symbols.Where(s => s.Name.Contains(gridd) && s.FamilyName.Contains(FrameArrayKey)).FirstOrDefault();
            CornerSymbol = symbols.Where(s => s.Name.Contains(gridd) && s.FamilyName.Contains(cornerLength)).FirstOrDefault();
        }

        public FamilySymbol Symbol { get; }
        public FamilySymbol CornerSymbol { get; }

        public bool ValidateFamilies(out string message)
        {
            var missingFamilies = new List<string>();

            if (Symbol == null)
                missingFamilies.Add("- Frame family (Array) is missing");

            if (CornerSymbol == null)
                missingFamilies.Add("- Corner frame family (Long or Short) is missing");

            if (missingFamilies.Count > 0)
            {
                message = "Required families are missing from the project:\n" +
                          string.Join("\n", missingFamilies) +
                          "\n\nFamilies must follow the naming rules:\n" +
                          "- The family name must contain the word 'Frame'\n" +
                          "- For frame: family name must include 'Array', and type name must contain '40' or '70'\n" +
                          "- For corner frame: family name must include 'Long' or 'Short', and type name must contain '40' or '70'";

                return false;
            }

            message = "";
            return true;
        }
    }
}
