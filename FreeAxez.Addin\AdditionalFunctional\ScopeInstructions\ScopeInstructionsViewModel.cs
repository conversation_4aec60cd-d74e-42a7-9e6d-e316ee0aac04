﻿using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FreeAxez.Addin.AdditionalFunctional.ScopeInstructions
{
    public class ScopeInstructionsViewModel : WindowViewModel
    {
        private readonly ScopeInstructionsView _scopeInstructionsView;
        private readonly ScopeInstructionService _scopeInstructionService;
        private List<ScopeInstruction> _scopeInstructions = [];

        public ScopeInstructionsViewModel(ScopeInstructionsView scopeInstructionsView, ScopeInstructionService scopeInstructionService)
        {
            _scopeInstructionsView = scopeInstructionsView;
            _scopeInstructionService = scopeInstructionService;
            Task.Run(InitializeAsync);
        }

        public List<ScopeInstruction> ScopeInstructions
        {
            get => _scopeInstructions;
            set
            {
                _scopeInstructions = value;
                OnPropertyChanged(nameof(ScopeInstructions));
            }
        }

        protected override void OnCancelCommandExecute(object sender)
        {
            _scopeInstructionsView?.Close();
        }

        private async Task InitializeAsync()
        {
            IList<ScopeInstruction> scopeInstructions = await _scopeInstructionService
                .GetAllAsync();

            ScopeInstructions = scopeInstructions
                .OrderBy(scopeInstruction => scopeInstruction.ScopeNumber)
                .ToList();
        }
    }
}
