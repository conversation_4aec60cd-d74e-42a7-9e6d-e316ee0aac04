﻿using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TagRamp.Models;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.TagRamp.Utils
{
    public class RampSlopeTagCreator
    {
        private const double RampSlopeTagTextHeight = 0.09375;
        public void Create(TramsitionLine tramsitionLine)
        {
            var rampSlopeTagSymbol = new RampFamilies().GetRampSlopeFamilySymbol(tramsitionLine.RampSlope); 
            var tagLocation = tramsitionLine.CenterPoint
                + (-tramsitionLine.RampDirection * tramsitionLine.TagOffset);

            var rampSlopeAnnotationSymbol = RevitManager.Document.Create
                .NewFamilyInstance(tagLocation,
                                   rampSlopeTagSymbol,
                                   RevitManager.Document.ActiveView) as AnnotationSymbol;

            rampSlopeAnnotationSymbol.Location.Rotate(
                Line.CreateUnbound(tagLocation, XYZ.BasisZ),
                tramsitionLine.RampDirection.AngleTo(XYZ.BasisY));

            rampSlopeAnnotationSymbol.Location.Move(
                -tramsitionLine.RampDirection * (3 * RampSlopeTagTextHeight)
                * RevitManager.Document.ActiveView.Scale * 0.05);
        }        
    }
}