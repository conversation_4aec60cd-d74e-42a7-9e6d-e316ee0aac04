﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Enums;
using System;

namespace FreeAxez.Addin.AdditionalFunctional.Pallets.Utils
{
    public class PalletConfigurator
    {
        public const double PalletLength = 48.0 / 12; // Feet
        public const double PalletWidth = 34.0 / 12; // Feet

        private readonly UnitCollector _unitCollector;
        private Dictionary<PalletType, List<FamilyInstance>> _unitsByPalletType;


        public PalletConfigurator(UnitCollector unitCollector) 
        {
            _unitCollector = unitCollector;
        }

        public void ConfigurePallets()
        {
            _unitsByPalletType = GroupUnitsByPalletTypes(_unitCollector.Units);
        }

        public static int GetNumberOfUnitsPerPallet(PalletType palletType, GriddType gridType)
        {
            if (gridType == GriddType.Gridd70)
            {
                switch (palletType)
                {
                    case PalletType.BaseUnit: return 72;
                    case PalletType.HalfBaseUnit: return 144;
                    case PalletType.CornerPlate: return 1800;
                    case PalletType.ChannelPlate: return 1680;
                    case PalletType.ReinforcedCornerPlate: return 980;
                    case PalletType.ReinforcedChannelPlate: return 842;
                    case PalletType.HighCapacityBaseUnit: return 72;
                    case PalletType.HighCapacityHalfBaseUnit: return 144;
                    default: return 0;
                }
            }

            switch (palletType)
            {
                case PalletType.BaseUnit: return 108;
                case PalletType.HalfBaseUnit: return 216;
                case PalletType.CornerPlate: return 1800;
                case PalletType.ChannelPlate: return 1680;
                case PalletType.ReinforcedCornerPlate: return 864;
                case PalletType.ReinforcedChannelPlate: return 864;
                case PalletType.HighCapacityBaseUnit: return 108;
                case PalletType.HighCapacityHalfBaseUnit: return 216;
                default: return 0;
            }
        }

        public static double GetRequiredStartRegionArea(GriddType gridType)
        {
            return Math.Round(GetNumberOfUnitsPerPallet(PalletType.BaseUnit, gridType) * PalletGrid.UnitStep * PalletGrid.UnitStep);
        }


        public int GetNumberOfUnitsPerPallet(PalletType palletType)
        {
            return GetNumberOfUnitsPerPallet(palletType, _unitCollector.GriddType);
        }

        public List<PalletType> GetPalletTypes()
        {
            return _unitsByPalletType.Keys.ToList();
        }

        public List<FamilyInstance> GetUnits(PalletType palletType)
        {
            if (_unitsByPalletType.ContainsKey(palletType))
            {
                return _unitsByPalletType[palletType];
            }

            return new List<FamilyInstance>();
        }

        public List<FamilyInstance> GetUnitsAssociatedWithBaseUnits()
        {
            var allBaseUnits = new List<FamilyInstance>();

            var allBaseUnitTypes = new List<PalletType>()
            {
                PalletType.BaseUnit,
                PalletType.HighCapacityBaseUnit,
            };

            foreach (var palletType in allBaseUnitTypes)
            {
                if (_unitsByPalletType.ContainsKey(palletType))
                {
                    allBaseUnits.AddRange(_unitsByPalletType[palletType]);
                }
            }

            return allBaseUnits;
        }


        private Dictionary<PalletType, List<FamilyInstance>> GroupUnitsByPalletTypes(List<FamilyInstance> units)
        {
            var output = units
                .GroupBy(i => GetPalletType(i))
                .Where(g => g.Key != PalletType.Undefined)
                .ToDictionary(f => f.Key, f => f.ToList());

            return output;
        }

        private PalletType GetPalletType(FamilyInstance instance)
        {
            var productName = instance.Symbol?.LookupParameter("Product Name")?.AsString();

            if (productName == "Base Unit")
            {
                return PalletType.BaseUnit;
            }
            else if (productName == "Standard Channel Plate")
            {
                return PalletType.ChannelPlate;
            }
            else if (productName == "Standard Corner Plate")
            {
                return PalletType.CornerPlate;
            }
            else if (productName == "Half Base Unit")
            {
                return PalletType.HalfBaseUnit;
            }
            else if (productName == "Reinforced Channel Plate")
            {
                return PalletType.ReinforcedChannelPlate;
            }
            else if (productName == "Reinforced Corner Plate")
            {
                return PalletType.ReinforcedCornerPlate;
            }
            else if (productName == "Channel Plate")
            {
                return PalletType.ReinforcedChannelPlate;
            }
            else if (productName == "High Capacity Base Unit")
            {
                return PalletType.HighCapacityBaseUnit;
            }
            else if (productName == "High Capacity Half Base Unit")
            {
                return PalletType.HighCapacityHalfBaseUnit;
            }
            else if (productName == "Base Unit Cutout")
            {
                // Most likely the unit is cut in place and there is no special type of pallet
                return PalletType.BaseUnit;
            }

            return PalletType.Undefined;
        }
    }
}
