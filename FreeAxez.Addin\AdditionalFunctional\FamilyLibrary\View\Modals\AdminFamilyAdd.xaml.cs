using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals;

public partial class AdminFamilyAdd : UserControl
{
    public AdminFamilyAdd()
    {
        InitializeComponent();
    }

    private void Rectangle_DragEnter(object sender, DragEventArgs e)
    {
        var rect = sender as Rectangle;
        if (rect != null)
        {
            rect.Fill = new SolidColorBrush(Colors.WhiteSmoke);
        }
    }

    private void Rectangle_DragLeave(object sender, DragEventArgs e)
    {
        var rect = sender as Rectangle;
        if (rect != null)
        {
            rect.Fill = new SolidColorBrush(Color.FromRgb(245, 245, 245));
        }
    }

    private void Rectangle_Drop(object sender, DragEventArgs e)
    {
        if (DataContext is AdminFamilyAddVm viewModel)
        {
            viewModel.FileDropCommand.Execute(e);
        }
    }
}