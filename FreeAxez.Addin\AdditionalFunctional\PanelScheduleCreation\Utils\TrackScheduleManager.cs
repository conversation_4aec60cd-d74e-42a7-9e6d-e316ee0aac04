﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.PanelScheduleCreation.Utils
{
    public class TrackScheduleManager
    {
        private const string TrackAssignmentParamName = "Track Assignment";
        private const string LevelParamName = "Level";
        private const string ViewSubCategoryParamName = "View Sub Category";
        private const string TrackFamilyNameSuffix = "-Track-";
        private List<ViewSchedule> _existingSchedules;
        private BaseSchedules _baseSchedules;


        public TrackScheduleManager()
        {
            _existingSchedules = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(ViewSchedule))
                .WhereElementIsNotElementType()
                .Cast<ViewSchedule>()
                .ToList();

            _baseSchedules = new BaseSchedules();
        }


        public static bool IsTrackSchedule(string name)
        {
            var descriptions = string.Join("|", 
                BaseSchedules.GetAllBaseScheduleTypeDescriptions());
            var trackScheduleNamePattern = @"_\(\d\)_(" + descriptions + ")$";
            return Regex.IsMatch(name, trackScheduleNamePattern);
        }

        public void CreateSchedules()
        {
            var tracks = GetAllValidTracks();
            if (tracks.Count == 0)
            {
                InfoDialog.ShowDialog("Warning", 
                    $"There are no placed instances of the track family in the project \n" +
                    $"or \"{TrackAssignmentParamName}\" parameter is not filled." +
                    $"The track array family name must contain \"{TrackFamilyNameSuffix}\",\n" +
                    $"for example \"Access-Floor-FreeAxez-GriddPower-50A-3Phase-Track-Array\".");
                return;
            }

            if (_baseSchedules.IsBaseScheduleNotExist(out List<string> missedBaseSchedules))
            {
                InfoDialog.ShowDialog("Error", 
                    $"There are no templates for track schedules in the project.\n" +
                    $"Please create the following specifications and try again:\n" +
                    $"{string.Join("\n", missedBaseSchedules)}");
                return;
            }

            var deleted = new List<string>();
            var created = new List<string>();
            using (var t = new Transaction(RevitManager.Document, "Create Track Schedules"))
            {
                t.Start();

                deleted = DeleteAllTrackSchedules();

                foreach (var track in tracks)
                {
                    foreach (var scheduleType in GetScheduleTypesForCreating())
                    {
                        var scheduleName = GetScheduleName(track, scheduleType);

                        if (IsScheduleWithNameExist(scheduleName))
                        {
                            continue;
                        }

                        var trackSchedule = CreateTrackSchedule(scheduleType, scheduleName);
                        created.Add(trackSchedule.Name);

                        var trackAssignment = track.LookupParameter(TrackAssignmentParamName).AsString();
                        UpdateFilterValue(trackSchedule, TrackAssignmentParamName, trackAssignment);

                        if (GetFilterValueType(trackSchedule, LevelParamName) == 
                                StorageType.ElementId)
                        {
                            UpdateFilterValue(trackSchedule, LevelParamName, track.LevelId);
                        }
                        else
                        {
                            UpdateFilterValue(trackSchedule, LevelParamName, 
                                RevitManager.Document.GetElement(track.LevelId).Name);
                        }

                        trackSchedule.LookupParameter(ViewSubCategoryParamName)
                            .Set(RevitManager.Document.GetElement(track.LevelId).Name);

                        if (scheduleType == ScheduleType.PanelCircuit)
                        {
                            SetHeader(trackSchedule, trackAssignment);
                        }
                    }
                }

                t.Commit();
            }

            InfoDialog.ShowDialog("Report", 
                $"Deleted track schedules - {deleted.Where(n => !created.Contains(n)).Count()}\n" +
                $"Regenerated track schedules - {created.Where(n => deleted.Contains(n)).Count()}\n" +
                $"Created track schedules - {created.Where(n => !deleted.Contains(n)).Count()}");

        }

        public List<ViewSchedule> GetTrackSchedules()
        {
            return _existingSchedules.Where(s => IsTrackSchedule(s.Name)).ToList();
        }

        private string GetScheduleTypeDescription(ScheduleType scheduleType)
        {
            return BaseSchedules.GetScheduleTypeDescription(scheduleType);
        }

        /// <returns>Names of deleted schedules.</returns>
        private List<string> DeleteAllTrackSchedules()
        {
            var output = new List<string>();
            
            var existingTrackSchedules = GetTrackSchedules();

            var openedTrackSchedule = existingTrackSchedules.FirstOrDefault(schedule => 
                schedule.Id.GetIntegerValue() == RevitManager.UIDocument.ActiveView.Id.GetIntegerValue());
            if (openedTrackSchedule != null)
            {
                existingTrackSchedules.Remove(openedTrackSchedule);
            }

            output = existingTrackSchedules.Select(s => s.Name).ToList();
            _existingSchedules.RemoveAll(s => existingTrackSchedules.Contains(s));
            RevitManager.Document.Delete(existingTrackSchedules.Select(s => s.Id).ToList());

            return output;
        }

        private List<ScheduleType> GetScheduleTypesForCreating()
        {
            var scheduleTypes = Enum.GetValues(typeof(ScheduleType)).Cast<ScheduleType>().ToList();

            if (Properties.Settings.Default.panelScheduleCreationConnectedComponents)
            {
                scheduleTypes.Remove(ScheduleType.ConnectedComponentsByNumber);
            }
            else
            {
                scheduleTypes.Remove(ScheduleType.ConnectedComponents);
            }

            return scheduleTypes;
        }

        /// <returns>Schedule position in the schedule group.</returns>
        private int GetSchedulePosition(ScheduleType scheduleType)
        {
            switch (scheduleType)
            {
                // ConnectedComponentsByNumber and ConnectedComponents have same position 4
                case ScheduleType.ConnectedComponentsByNumber: return (int)scheduleType;
                default: return (int)scheduleType + 1;
            }
        }

        private bool IsScheduleWithNameExist(string name)
        {
            return _existingSchedules.Any(s => s.Name == name);
        }

        private ViewSchedule CreateTrackSchedule(ScheduleType scheduleType, string scheduleName)
        {
            var baseSchedule = _baseSchedules.GetBaseSchedule(scheduleType);
            var trackSchedule = (ViewSchedule)RevitManager.Document.GetElement(
                baseSchedule.Duplicate(ViewDuplicateOption.Duplicate));
            trackSchedule.Name = scheduleName;

            _existingSchedules.Add(trackSchedule);
            return trackSchedule;
        }

        private StorageType GetFilterValueType(ViewSchedule schedule, string field)
        {
            var filters = schedule.Definition.GetFilters();
            var filter = filters.FirstOrDefault(f => 
                schedule.Definition.GetField(f.FieldId).GetName().Equals(field));
            if (filter == null)
            {
                throw new ArgumentException($"There is no filter for the \"{field}\" field " +
                    $"in the \"{schedule.Name}\" schedule.");
            }

            if (filter.IsElementIdValue) return StorageType.ElementId;
            else if (filter.IsIntegerValue) return StorageType.Integer;
            else if (filter.IsDoubleValue) return StorageType.Double;
            else return StorageType.String;
        }

        private void UpdateFilterValue(ViewSchedule schedule, string field, string value)
        {
            var filters = schedule.Definition.GetFilters();
            var filter = filters.FirstOrDefault(f => 
                schedule.Definition.GetField(f.FieldId).GetName().Equals(field));
            if (filter == null)
            {
                throw new ArgumentException($"There is no filter for the \"{field}\" field " +
                    $"in the \"{schedule.Name}\" schedule.");
            }

            filter.SetValue(value);
            schedule.Definition.SetFilter(filters.IndexOf(filter), filter);
        }

        private void UpdateFilterValue(ViewSchedule schedule, string field, ElementId value)
        {
            var filters = schedule.Definition.GetFilters();
            var filter = filters.FirstOrDefault(f => schedule.Definition.GetField(f.FieldId).GetName().Equals(field));
            if (filter == null)
            {
                throw new ArgumentException($"There is no filter for the \"{field}\" field " +
                    $"in the \"{schedule.Name}\" schedule.");
            }

            filter.SetValue(value);
            schedule.Definition.SetFilter(filters.IndexOf(filter), filter);
        }

        private string GetScheduleName(FamilyInstance track, ScheduleType scheduleType)
        {
            var level = RevitManager.Document.GetElement(track.LevelId);
            var trackAssignment = track.LookupParameter(TrackAssignmentParamName).AsString();
            var scheduleDescription = GetScheduleTypeDescription(scheduleType);
            var orderNumber = GetSchedulePosition(scheduleType);
            return $"{level.Name}_{trackAssignment}_({orderNumber})_{scheduleDescription}";
        }

        private List<FamilyInstance> GetAllValidTracks()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_ElectricalEquipment)
                .WhereElementIsNotElementType()
                .Where(e => e is FamilyInstance)
                .Cast<FamilyInstance>()
                .Where(f => f.Symbol.FamilyName.Contains(TrackFamilyNameSuffix))
                .Where(f => f.SuperComponent == null) // Do not include nested families of track array
                .Where(f => !string.IsNullOrWhiteSpace(f.LookupParameter(TrackAssignmentParamName).AsString()))
                .ToList();
        }

        private void SetHeader(ViewSchedule viewSchedule, string value)
        {
            viewSchedule.GetTableData().GetSectionData(SectionType.Header).SetCellText(0, 0, value);
        }
    }
}
