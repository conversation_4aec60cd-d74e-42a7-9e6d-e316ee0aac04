﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Structure;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Services;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils
{
    public class PanelCreator
    {
        private PanelCollector _panelCollector;
        private LevelHelper _levelHelper;

        public PanelCreator(LevelHelper levelHelper)
        {
            _levelHelper = levelHelper;

            _panelCollector = new PanelCollector(levelHelper);
        }

        public List<FamilyInstance> CreatePanelIfNotExistOnLevel()
        {
            var output = new List<FamilyInstance>();

            var panelSymbol = _panelCollector.GetPanelFamilySymbol();
            if (!panelSymbol.IsActive)
            {
                panelSymbol.Activate();
            }

            var panelInstances = _panelCollector.GetPanelFamilyInstances();

            foreach (var level in _levelHelper.SelectedLevels)
            {
                if (panelInstances.Any(p => _levelHelper.BelongsToTheLevel(p, level))) continue;

                var location = GetPanelPosition(level);
                var newInstance = RevitManager.Document.Create.NewFamilyInstance(
                    location, panelSymbol, level, StructuralType.NonStructural);

                TrySetElevationFromLevelParameter(newInstance);

                output.Add(newInstance);
            }

            return output;
        }

        private XYZ GetPanelPosition(Level level)
        {
            var levelHelper = new LevelHelper(new List<Level>() { level });

            var whipCollector = new WhipCollector(levelHelper);
            var trackWhips = whipCollector.GetWhipElementsForTracks();

            var trackCollector = new TrackCollector(levelHelper);
            var tracks = trackCollector.GetTrackFamilyInstances();
            var trackOutlines = tracks.Select(GeometryHelper.GetSolidsOutline).ToList();

            foreach (var whip in trackWhips)
            {
                var start = (whip.Location as LocationCurve).Curve.GetEndPoint(0);
                if (trackOutlines.All(o => !o.Contains(start, CircuitCollector.WhipIntersectionTolerance))) return start;

                var end = (whip.Location as LocationCurve).Curve.GetEndPoint(1);
                if (trackOutlines.All(o => !o.Contains(end, CircuitCollector.WhipIntersectionTolerance))) return end;
            }

            // Z coordinate is necessary for old panelboard families
            return new XYZ(0, 0, level.Elevation + 1);
        }

        private void TrySetElevationFromLevelParameter(FamilyInstance instance)
        {
            try
            {
                var elevationParameter = instance.get_Parameter(BuiltInParameter.INSTANCE_ELEVATION_PARAM);
                var defaultElevationParameter = instance.Symbol.get_Parameter(BuiltInParameter.FAMILY_FREEINST_DEFAULT_ELEVATION);
                if (elevationParameter != null && defaultElevationParameter != null)
                {
                    elevationParameter.Set(defaultElevationParameter.AsDouble());
                }
            }
            catch
            {
                LogHelper.Error($"Failed to fill Elevation from Level parameter for panel");
            }
        }
    }
}
