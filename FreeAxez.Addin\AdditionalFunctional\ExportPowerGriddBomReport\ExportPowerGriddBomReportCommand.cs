﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using System.Diagnostics;
using System.IO;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerGriddBomReport
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    public class ExportPowerGriddBomReportCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var analyzer = new BomFamilyAnalyzer();

            var desktop = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            var griddPath = Path.Combine(desktop, $"GriddBomUsedFamiliesReport_{RevitManager.Document.Title}.txt");
            var powerPath = Path.Combine(desktop, $"PowerBomUsedFamiliesReport_{RevitManager.Document.Title}.txt");

            //var sw = Stopwatch.StartNew();
            analyzer.ExportGriddReport(griddPath);
            analyzer.ExportPowerReport(powerPath);
            //sw.Stop();
            //var t1 = sw.ElapsedMilliseconds;

            return Result.Succeeded;
        }
    }
}
