﻿using System.Windows;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Interfaces;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.ViewModels
{
    public class ProgressViewModel : WindowViewModel, IProgressReporter
    {
        private readonly CancellationTokenSource _cancellationTokenSource;
        private double _progressValue;
        private string _taskStatus;
        private string _stepStatus;
        private bool _isCancelEnabled = true;

        public ProgressViewModel()
        {
        }
        public ProgressViewModel(CancellationTokenSource cancellationTokenSource)
        {
            _cancellationTokenSource = cancellationTokenSource;
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
        }

        public ICommand CancelCommand { get; }

        public bool IsCancelEnabled
        {
            get => _isCancelEnabled;
            set
            {
                _isCancelEnabled = value;
                OnPropertyChanged();
            }
        }

        public double ProgressValue
        {
            get => _progressValue;
            set
            {
                _progressValue = value;
                OnPropertyChanged();
            }
        }

        public string TaskStatus
        {
            get => _taskStatus;
            set
            {
                _taskStatus = value;
                OnPropertyChanged();
            }
        }

        public string StepStatus
        {
            get => _stepStatus;
            set
            {
                _stepStatus = value;
                OnPropertyChanged();
            }
        }

        public void ReportProgress(double value)
        {
            ProgressValue = value;
        }

        public void ReportStatus(string status)
        {
            TaskStatus = status;
        }

        public void ReportStepStatus(string status)
        {
            StepStatus = status;
        }

        private void OnCancelCommandExecute(object p)
        {
            IsCancelEnabled = false;

            var confirmationWindow = new ConfirmationView();
            var confirmationViewModel = new ConfirmationViewModel(result =>
            {
                if (result)
                {
                    _cancellationTokenSource.Cancel();

                    if (p is Window window)
                    {
                        window.Close();
                    }
                }
                else
                {
                    IsCancelEnabled = true;
                }

                confirmationWindow.Close();
            });

            confirmationWindow.DataContext = confirmationViewModel;
            if (p is Window window) confirmationWindow.Owner = window;
            confirmationWindow.ShowDialog();

            confirmationWindow.Closed += (sender, args) =>
            {
                IsCancelEnabled = true;
            };
        }
    }
}
