<Window x:Class="FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views.LineweightWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Lineweight"
        Height="400"
        Width="320"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0" 
                   Text="Lineweights:" 
                   FontWeight="Bold"
                   Margin="0,0,0,10"/>

        <!-- Lineweight List -->
        <ListBox Grid.Row="1"
                 ItemsSource="{Binding AvailableLineweights}"
                 SelectedItem="{Binding SelectedLineweight}"
                 ScrollViewer.VerticalScrollBarVisibility="Auto">
            <ListBox.ItemContainerStyle>
                <Style TargetType="ListBoxItem">
                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                    <Setter Property="Padding" Value="5,2"/>
                </Style>
            </ListBox.ItemContainerStyle>
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="30"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Line preview -->
                        <Rectangle Grid.Column="0"
                                   Height="{Binding VisualThickness}"
                                   Fill="Black"
                                   VerticalAlignment="Center"
                                   Margin="2"/>

                        <!-- Text -->
                        <TextBlock Grid.Column="1"
                                   Text="{Binding DisplayName}"
                                   VerticalAlignment="Center"
                                   Margin="5,0"/>
                    </Grid>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>

        <!-- Current selection info -->
        <Grid Grid.Row="2" Margin="0,10,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition/>
                <RowDefinition/>
            </Grid.RowDefinitions>
            
            <TextBlock Grid.Row="0" Grid.Column="0" Text="Original:" Margin="0,0,10,0"/>
            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding OriginalLineweight}"/>
            
            <TextBlock Grid.Row="1" Grid.Column="0" Text="New:" Margin="0,0,10,0"/>
            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SelectedLineweight.DisplayName}"/>
        </Grid>

        <!-- Buttons -->
        <Grid Grid.Row="4" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="OK"
                    Command="{Binding OkCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    Style="{StaticResource ButtonSimpleBlue}"
                    Height="30"/>
            <Button Grid.Column="2"
                    Content="Cancel"
                    Command="{Binding CancelCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    Style="{StaticResource ButtonSimpleRed}"
                    Height="30"/>
        </Grid>
    </Grid>
</Window>
