﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Views.BomSelectionView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.ViewModels"
        xmlns:converters="clr-namespace:FreeAxez.Addin.Infrastructure.Converters"
        mc:Ignorable="d"
        Title="BOM Type" 
        SizeToContent="WidthAndHeight"
        MinWidth="250" MinHeight="150" 
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResizeWithGrip">
    <Window.Resources>
        <ResourceDictionary>
            <converters:EnumToBooleanConverter x:Key="EnumToBooleanConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Window.DataContext>
        <local:BomSelectionViewModel/>
    </Window.DataContext>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <RadioButton Style="{StaticResource RadioButtonStyle}" Grid.Row="0" Content="Entire Project" IsChecked="{Binding SelectedOption, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=EntireProject}" GroupName="SelectionType"/>
        <RadioButton Style="{StaticResource RadioButtonStyle}" Grid.Row="1" Content="Selected Room" IsChecked="{Binding SelectedOption, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=SelectedRoom}" GroupName="SelectionType"/>
        <RadioButton Style="{StaticResource RadioButtonStyle}" Grid.Row="2" Content="Selected Region" IsChecked="{Binding SelectedOption, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=SelectedRegion}" GroupName="SelectionType"/>

        <Grid Grid.Row="4" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Button Content="Confirm" Style="{StaticResource ButtonSimpleBlue}" Command="{Binding ConfirmCommand}" Grid.Column="2" Margin="0,0,5,0" CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"/>
            <Button Content="Cancel" Style="{StaticResource ButtonOutlinedRed}" Command="{Binding CancelCommand}" CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}" Grid.Column="0"/>
        </Grid>
    </Grid>
</Window>
