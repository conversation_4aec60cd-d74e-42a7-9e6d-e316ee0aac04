using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using NetTopologySuite.Geometries;
using NetTopologySuite.Index.Strtree;
using FaPoint = FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data.Point;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Core.Elements;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.Border
{
    /// <summary>
    /// Fast Border processor.
    /// Green side is determined as the one of two long edges
    /// with minimum distance to any parallel contour line.
    /// Distance search uses STR-tree, so it works in O(log N).
    /// </summary>
    public class BorderProcessor
    {
        // Constants from original logic
        private const double ANGLE_PARALLEL_COS = 0.984807753; // cos 10°
        private const double QUERY_TOLERANCE = 1.0;            // inch
        private const double EPS = 1e-4;

        private readonly List<LineString> _contourLines = new();
        private readonly STRtree<LineString> _index = new();
        private MultiLineString _contourMulti;

        /// <summary>
        /// Creates BorderElement objects with correct placement calculated from area context
        /// </summary>
        public List<BorderElement> CreateBorderElements(
            List<List<LineSegmentData>> components,
            List<JsonLineData> areaLines,
            int idCounter,
            HashSet<LineSegmentData> usedSegments,
            Transform dwgTransform = null)
        {
            if (components == null || !components.Any())
                return new List<BorderElement>();

            BuildContourIndex(areaLines ?? new List<JsonLineData>());

            var borders = new List<BorderElement>();

            for (int i = 0; i < components.Count; i++)
            {
                var component = components[i];

                if (component.Count != 4) // Border must be a rectangle (4 lines)
                    continue;

                var segments = component.Select(s => ToLineString(s.Data)).ToList();
                var placement = PlaceRectangleFast(segments);

                var center = new FaPoint(placement.Insertion.X, placement.Insertion.Y, 0);

                // Apply DWG transform if provided
                if (dwgTransform != null && !dwgTransform.IsIdentity)
                {
                    var location = new XYZ(center.X / 12.0, center.Y / 12.0, 0);
                    var transformedLocation = dwgTransform.OfPoint(location);
                    center = new FaPoint(transformedLocation.X * 12.0, transformedLocation.Y * 12.0, 0);
                }

                var border = BorderElement.CreateWithPlacement(
                    idCounter++,
                    component,
                    dwgTransform,
                    center,
                    placement.RotationDeg,
                    placement.Length,
                    placement.Width,
                    i + 1);

                borders.Add(border);
            }

            return borders;
        }

        private void BuildContourIndex(IEnumerable<JsonLineData> areaLines)
        {
            _contourLines.Clear();
            foreach (var l in areaLines)
            {
                var ls = ToLineString(l);
                _contourLines.Add(ls);
                _index.Insert(ls.EnvelopeInternal, ls);
            }
            _contourMulti = new MultiLineString(_contourLines.ToArray());
        }

        private static LineString ToLineString(JsonLineData l) =>
            new LineString(new[]
            {
                new Coordinate(l.startPoint.x, l.startPoint.y),
                new Coordinate(l.endPoint.x, l.endPoint.y)
            });

        private double DistanceToContour(LineString side)
        {
            var env = side.EnvelopeInternal.Copy();
            env.ExpandBy(QUERY_TOLERANCE);

            var cands = _index.Query(env);
            if (!cands.Any())
                return side.Distance(_contourMulti);

            double best = double.MaxValue;

            // Unit vector of the side
            var dx1 = side.GetCoordinateN(1).X - side.GetCoordinateN(0).X;
            var dy1 = side.GetCoordinateN(1).Y - side.GetCoordinateN(0).Y;
            var len1 = Math.Sqrt(dx1 * dx1 + dy1 * dy1);
            dx1 /= len1; dy1 /= len1;

            foreach (var cand in cands)
            {
                var dx2 = cand.GetCoordinateN(1).X - cand.GetCoordinateN(0).X;
                var dy2 = cand.GetCoordinateN(1).Y - cand.GetCoordinateN(0).Y;
                var len2 = Math.Sqrt(dx2 * dx2 + dy2 * dy2);
                dx2 /= len2; dy2 /= len2;

                // Almost parallel?
                var cos = Math.Abs(dx1 * dx2 + dy1 * dy2);
                if (cos < ANGLE_PARALLEL_COS) continue;

                best = Math.Min(best, side.Distance(cand));
                if (best < EPS) break; // coincides
            }
            return best;
        }

        private ProcessedBorderResult PlaceRectangleFast(IReadOnlyList<LineString> rectSegs)
        {
            // Two long sides
            var longSides = rectSegs.OrderByDescending(s => s.Length).Take(2).ToArray();

            double d0 = DistanceToContour(longSides[0]);
            double d1 = DistanceToContour(longSides[1]);

            var green = d0 <= d1 ? longSides[0] : longSides[1];

            // Direction of green side
            var gx = green.GetCoordinateN(1).X - green.GetCoordinateN(0).X;
            var gy = green.GetCoordinateN(1).Y - green.GetCoordinateN(0).Y;
            var angle = Math.Atan2(gy, gx); // radians

            // Rotate all vertices to make green side horizontal
            double cos = Math.Cos(-angle);
            double sin = Math.Sin(-angle);

            var verts = rectSegs.SelectMany(ls => ls.Coordinates).Distinct().ToArray();
            var rot = verts.Select(v => new
            {
                Orig = v,
                X = v.X * cos - v.Y * sin,
                Y = v.X * sin + v.Y * cos
            }).ToList();

            double minX = rot.Min(v => v.X);
            double maxX = rot.Max(v => v.X);
            double minY = rot.Min(v => v.Y);
            double maxY = rot.Max(v => v.Y);

            // Where does green side lie after rotation?
            var aR = new Coordinate(green.GetCoordinateN(0).X * cos - green.GetCoordinateN(0).Y * sin,
                                    green.GetCoordinateN(0).X * sin + green.GetCoordinateN(0).Y * cos);
            var bR = new Coordinate(green.GetCoordinateN(1).X * cos - green.GetCoordinateN(1).Y * sin,
                                    green.GetCoordinateN(1).X * sin + green.GetCoordinateN(1).Y * cos);

            bool horiz = Math.Abs(aR.Y - bR.Y) < EPS;

            bool needFlip = horiz
                ? Math.Abs(aR.Y - maxY) < EPS          // horizontal at top?
                : Math.Abs(aR.X - maxX) < EPS;         // vertical at right?

            if (needFlip)
                angle = (angle + Math.PI) % (2 * Math.PI);

            // Pivot = top-left (minX, maxY) in border coordinate system
            double cosF = Math.Cos(-angle);
            double sinF = Math.Sin(-angle);

            var rotF = verts.Select(v => new
            {
                Orig = v,
                X = v.X * cosF - v.Y * sinF,
                Y = v.X * sinF + v.Y * cosF
            }).ToList();

            double minXF = rotF.Min(v => v.X);
            double maxYF = rotF.Max(v => v.Y);
            var pivot = rotF.First(v =>
                Math.Abs(v.X - minXF) < EPS &&
                Math.Abs(v.Y - maxYF) < EPS).Orig;

            return new ProcessedBorderResult
            {
                Insertion = pivot,
                RotationDeg = (angle * 180 / Math.PI + 360) % 360,
                Length = rectSegs.Max(s => s.Length),
                Width = rectSegs.Min(s => s.Length)
            };
        }

        /// <summary>
        /// Result of border placement calculation
        /// </summary>
        private sealed class ProcessedBorderResult
        {
            public Coordinate Insertion;
            public double RotationDeg;
            public double Length;
            public double Width;
        }
    }
}
