﻿using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Text;
using Microsoft.Win32;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.AutoCAD;

public class AutoCADService
{
    private const string PluginFileName = "FreeAxez.AutoCAD.Plugin.dll";

    public bool IsCoreConsoleAvailable()
    {
        return !string.IsNullOrEmpty(FindCoreConsolePath());
    }

    public void ExportLinesToText(string dwgPath, string outputFilePath)
    {
        ExportLines(dwgPath, outputFilePath);
    }

    private void ExportLines(string dwgPath, string outputFilePath)
    {
        var consolePath = FindCoreConsolePath();
        if (string.IsNullOrEmpty(consolePath))
            throw new InvalidOperationException("AutoCAD Core Console not found.");

        if (!File.Exists(dwgPath))
            throw new FileNotFoundException($"DWG file not found: {dwgPath}");

        // Ensure output directory exists
        var outputDir = Path.GetDirectoryName(outputFilePath);
        if (!Directory.Exists(outputDir))
            Directory.CreateDirectory(outputDir);

        // Find the .NET plugin
        var pluginPath = GetNetPluginPath();

        // Set environment variable for output path (absolute path)
        var absoluteOutputPath = Path.GetFullPath(outputFilePath);
        Environment.SetEnvironmentVariable("FREEAXEZ_OUTPUT_PATH", absoluteOutputPath);

        using (var tempFileManager = new TempFileManager())
        {
            // Create script to load plugin and run command
            var scriptPath = Path.Combine(tempFileManager.TempDirectory, "netplugin.scr");
            var scriptContent = $"NETLOAD \"{pluginPath}\"\nEXTRACT_BASE_LINES\nQUIT\n";
            File.WriteAllText(scriptPath, scriptContent, Encoding.Default);

            var psi = new ProcessStartInfo
            {
                FileName = consolePath,
                Arguments = $"/i \"{dwgPath}\" /s \"{scriptPath}\" /product ACAD /l en-US",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            using (var process = Process.Start(psi))
            {
                if (!process.WaitForExit(600000)) // Wait 10 minutes for large files
                {
                    process.Kill();
                    throw new TimeoutException($"Timeout processing {Path.GetFileName(dwgPath)}.");
                }

                var output = process.StandardOutput.ReadToEnd();
                var error = process.StandardError.ReadToEnd();

                // Save logs for debugging
                var outputLogFile = Path.Combine(outputDir, "autocad_netplugin_output.txt");
                var errorLogFile = Path.Combine(outputDir, "autocad_netplugin_error.txt");

                File.WriteAllText(outputLogFile, $"Exit Code: {process.ExitCode}\n\nOutput:\n{output}");
                File.WriteAllText(errorLogFile, $"Exit Code: {process.ExitCode}\n\nError:\n{error}");

                if (process.ExitCode != 0)
                    throw new InvalidOperationException(
                        $"AutoCAD Core Console failed (exit {process.ExitCode}). Check {outputLogFile} and {errorLogFile} for details.");
            }
        }

        // Check output file
        if (!File.Exists(outputFilePath))
        {
            var filesInDir = Directory.GetFiles(outputDir, "*.*").Select(Path.GetFileName);
            throw new InvalidOperationException(
                $"Output file was not created: {outputFilePath}. Files in directory: {string.Join(", ", filesInDir)}");
        }

        var fileInfo = new FileInfo(outputFilePath);
        if (fileInfo.Length == 0)
            throw new InvalidOperationException($"Output file is empty: {outputFilePath}");
    }


    private string GetNetPluginPath()
    {
        // Look for plugin in the same directory as current assembly
        var currentDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        var localPluginPath = Path.Combine(currentDir, PluginFileName);

        if (File.Exists(localPluginPath)) return localPluginPath;

        throw new FileNotFoundException(
            $"AutoCAD .NET plugin not found: {localPluginPath}. Please ensure {PluginFileName} is in the same directory as the main application.");
    }


    private static string FindCoreConsolePath()
    {
        var registryKey = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Autodesk\AutoCAD");
        if (registryKey == null)
            return null;

        var versions = registryKey.GetSubKeyNames()
            .Where(name => name.StartsWith("R", StringComparison.OrdinalIgnoreCase))
            .Select(name => new
            {
                Version = name,
                Year = int.Parse(new string(name.Skip(1).TakeWhile(char.IsDigit).ToArray()))
            })
            .OrderByDescending(x => x.Year)
            .ToList();

        if (!versions.Any())
            return null;

        foreach (var version in versions)
        {
            var versionKey = registryKey.OpenSubKey(version.Version);
            if (versionKey != null)
            {
                string installPath = null;
                foreach (var subKeyName in versionKey.GetSubKeyNames())
                {
                    var subKey = versionKey.OpenSubKey(subKeyName);
                    if (subKey != null)
                    {
                        installPath = subKey.GetValue("AcadLocation")?.ToString();
                        if (!string.IsNullOrEmpty(installPath))
                            break;
                    }
                }

                if (!string.IsNullOrEmpty(installPath))
                {
                    var coreConsolePath = Path.Combine(installPath, "accoreconsole.exe");
                    if (File.Exists(coreConsolePath))
                        return coreConsolePath;
                }
            }
        }

        return null;
    }


}