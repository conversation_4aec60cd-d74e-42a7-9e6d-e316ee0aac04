﻿using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastucture;

namespace FreeAxez.Addin.Services
{
    public class SelectionService
    {
        public List<FilledRegion> SelectRegions(UIApplication application)
        {
            IList<Reference> pickedrefs = null;
            LogHelper.Information("Selecting regions.");
            Selection sel = application.ActiveUIDocument.Selection;
            Document document = application.ActiveUIDocument.Document;
            try
            {
                pickedrefs = sel.PickObjects(ObjectType.Element, new FilledRegionFilter());
                if (pickedrefs != null)
                {
                    var regions = pickedrefs.Select(x => document.GetElement(x) as FilledRegion).ToList();

                    LogHelper.Information($"Regions selected: {regions.Select(x => x.Id.GetIntegerValue()).ToList()}");
                    return regions;
                }
            }
            catch (Exception ex)
            {
                return null;
            }
            return null;
        }

        public List<FilledRegion> SelectHighTraffic(UIApplication application)
        {
            LogHelper.Information("Selecting high traffic.");
         
            IList<Reference> pickedrefs = null;
            Selection sel = application.ActiveUIDocument.Selection;
            Document document = application.ActiveUIDocument.Document;
            try
            {
                pickedrefs = sel.PickObjects(ObjectType.Element, new FilledRegionFilter());
                if (pickedrefs != null)
                {
                    var regions = pickedrefs.Select(x => document.GetElement(x) as FilledRegion).ToList();

                    LogHelper.Information($"Regions high traffic: {regions.Select(x => x.Id.GetIntegerValue()).ToList()}");
                    return regions;
                }
            }
            catch (Exception ex)
            {
            }

            return null;
         
            /*Selection sel = application.ActiveUIDocument.Selection;
            Document document = application.ActiveUIDocument.Document;
            try
            {
                List<PickedBox> rectangles;

                do
                {
                    rectangles = new List<PickedBox>();

                    try
                    {
                        while (true)
                            rectangles.Add(sel.PickBox(PickBoxStyle.Crossing));
                    }
                    catch (OperationCanceledException)
                    {
                    }
                } while (!RevitDialogHelper.ShowConfirmation("Do you confirm selection?", "Yes", "No"));

                return rectangles;
            }
            catch (Exception ex)
            {
            }

            return null;*/
        }

        //public List<XYZ> SelectTransitionLines(UIApplication application, List<FilledRegion> selectedRegions)
        //{
        //    LogHelper.Information("Selecting transition lines.");
        //    Selection selection = application.ActiveUIDocument.Selection;
        //    Document document = application.ActiveUIDocument.Document;
        //    var selectedRegionIds = selectedRegions.Select(x => x.Id.GetIntegerValue()).ToList();
        //    var transactionLineSelectionFilter = new TransactionLineSelectionFilter(selectedRegionIds);

        //    var points = new List<XYZ>();

        //    try
        //    {
        //        points = selection
        //            .PickObjects(ObjectType.Edge, transactionLineSelectionFilter)
        //            .Select(r => (document.GetElement(r).GetGeometryObjectFromReference(r) as Edge).Evaluate(0.5))
        //            .ToList();
        //    }
        //    catch (Exception ex)
        //    {
        //    }

        //    return points;
        //}

        public List<Curve> SelectTransitionLines()
        {
            Selection selection = RevitManager.UIDocument.Selection;

            var transitionLines = new List<Curve>();

            try
            {
                IList<Reference> transitionLineRefs = selection.PickObjects(ObjectType.Element, new DetailLineSelectionFilter());
                foreach (var transitionLineRef in transitionLineRefs)
                {
                    Element elem = RevitManager.Document.GetElement(transitionLineRef);
                    if (elem is DetailLine detailLine)
                    {
                        Curve curve = (detailLine.GeometryCurve.Clone() as Curve);
                        transitionLines.Add(curve);
                    }
                }
            }
            catch (Exception ex)
            {
                TaskDialog.Show("Error", "Selection cancelled or an error occurred: " + ex.Message);
            }

            return transitionLines;
        }

        public List<Element> SelectElement(UIApplication application)
        {
            Selection sel = application.ActiveUIDocument.Selection;
            Document document = application.ActiveUIDocument.Document;

            var elem = sel.PickObjects(ObjectType.Element);

            return elem.Select(x => document.GetElement(x.ElementId)).ToList();
        }
    }
}