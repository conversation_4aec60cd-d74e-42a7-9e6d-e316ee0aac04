using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.AdditionalFunctional.ExportForClient.Utils;
using FreeAxez.Addin.AdditionalFunctional.FlexPipeToLine.Utils;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ExportForVR.Utils
{
    /// <summary>
    /// Converts FlexPipe elements to Railing elements by first converting them to lines.
    /// </summary>
    internal static class FlexPipeToRailings
    {
        /// <summary>
        /// Converts all FlexPipe elements in the document to Railing elements.
        /// The conversion process involves generating simplified lines from the FlexPipe's geometry
        /// and then creating Railings from those lines.
        /// </summary>
        /// <param name="doc">The Revit document.</param>
        /// <returns>A list of FlexPipe type names that were not converted because a matching RailingType was not found.</returns>
        public static List<string> Convert(Document doc)
        {
            var flexPipes = new FilteredElementCollector(doc)
                .OfClass(typeof(FlexPipe))
                .Cast<FlexPipe>()
                .ToList();

            if (flexPipes.Count == 0)
            {
                LogHelper.Information("No FlexPipes found to convert.");
                return new List<string>();
            }

            var railingTypes = new FilteredElementCollector(doc)
                .OfClass(typeof(RailingType))
                .Cast<RailingType>()
                .ToDictionary(rt => rt.Name, rt => rt);

            if (railingTypes.Count == 0)
            {
                LogHelper.Warning("No RailingTypes found in the project. Cannot convert FlexPipes.");
                return flexPipes.Select(p => p.get_Parameter(BuiltInParameter.ELEM_TYPE_PARAM).AsValueString()).Distinct().ToList();
            }

            // Find missing railing types upfront for consolidated logging
            var flexPipeTypeNames = flexPipes.Select(p => p.get_Parameter(BuiltInParameter.ELEM_TYPE_PARAM).AsValueString()).Distinct().ToList();
            var missingRailingTypes = flexPipeTypeNames.Where(name => !railingTypes.ContainsKey(name)).ToList();

            if (missingRailingTypes.Any())
            {
                LogHelper.Warning($"No matching RailingType found for the following FlexPipe types: {string.Join(", ", missingRailingTypes)}. Skipping conversion for these types.");
            }

            using (var transaction = new Transaction(doc, "Convert FlexPipes to Railings"))
            {
                transaction.Start();

                var failOpt = transaction.GetFailureHandlingOptions();
                failOpt.SetFailuresPreprocessor(new WarningSwallower());
                transaction.SetFailureHandlingOptions(failOpt);

                var pipesToDelete = new List<ElementId>();

                foreach (var pipe in flexPipes)
                {
                    var pipeTypeName = pipe.get_Parameter(BuiltInParameter.ELEM_TYPE_PARAM).AsValueString();
                    if (!railingTypes.TryGetValue(pipeTypeName, out var railingType))
                    {
                        // Logging is now done before the loop
                        continue;
                    }

                    var levelId = pipe.ReferenceLevel.Id;
                    if (levelId == null || levelId == ElementId.InvalidElementId)
                    {
                        LogHelper.Warning($"Could not determine level for FlexPipe {pipe.Id}. Skipping conversion.");
                        continue;
                    }

                    try
                    {
                        // Step 1: Convert FlexPipe to a list of simplified curves (lines)
                        var newCurves = FlexPipeToLineConverter.GetCurvesFromFlexPipe(pipe);

                        // Step 2: Use the generated lines to create Railings
                        if (newCurves.Any())
                        {
                            var curveLoops = CurveLoop.Create(newCurves);
                            Railing.Create(doc, curveLoops, railingType.Id, levelId);
                            pipesToDelete.Add(pipe.Id);
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"Failed to convert FlexPipe {pipe.Id}. Error: {ex.Message}");
                    }
                }

                if (pipesToDelete.Any())
                {
                    doc.Delete(pipesToDelete);
                    LogHelper.Information($"Converted and deleted {pipesToDelete.Count} FlexPipes.");
                }

                transaction.Commit();
            }
            return missingRailingTypes;
        }
    }
}
