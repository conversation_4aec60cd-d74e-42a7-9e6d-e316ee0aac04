﻿using System.Windows;
using System.Windows.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.FloorBoxNumbering.Views
{
    /// <summary>
    /// Interaction logic for FloorBoxesMarkingView.xaml
    /// </summary>
    public partial class FloorBoxNumberingView : Window
    {
        public FloorBoxNumberingView()
        {
            InitializeComponent();
            horizontalDirection.IsChecked = Properties.Settings.Default.FloorBoxNumberingHorizontalDirection;
            verticalDirection.IsChecked = !horizontalDirection.IsChecked;
            prefix.Text = Properties.Settings.Default.FloorBoxNumberingPrefix;
            startNumber.Text = Properties.Settings.Default.FloorBoxNumberingStartNumber.ToString();
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;

            Properties.Settings.Default.FloorBoxNumberingHorizontalDirection = (bool)horizontalDirection.IsChecked;
            Properties.Settings.Default.FloorBoxNumberingPrefix = prefix.Text;
            Properties.Settings.Default.FloorBoxNumberingStartNumber = int.Parse(startNumber.Text);
            Properties.Settings.Default.Save();

            this.Close();
        }

        private void startNumber_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (int.TryParse(startNumber.Text, out int result) && result > 0)
            {
                Properties.Settings.Default.FloorBoxNumberingStartNumber = result;
                return;
            }

            startNumber.Text = Properties.Settings.Default.FloorBoxNumberingStartNumber.ToString();
        }
    }
}
