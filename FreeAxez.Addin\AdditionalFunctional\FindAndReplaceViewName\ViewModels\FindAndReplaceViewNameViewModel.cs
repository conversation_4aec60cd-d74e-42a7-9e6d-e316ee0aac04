﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.FindAndReplaceViewName.ViewModels
{
    public class FindAndReplaceViewNameViewModel : WindowViewModel
    {
        private ObservableCollection<ViewNameModel> _allItems;
        private ICollectionView _filteredItems;
        private string _searchText = string.Empty;
        private string _replaceText = string.Empty;
        private bool _viewNameFilter = true;
        private bool _sheetTitleFilter = true;
        private bool _hasChanges = false;

        public ICollectionView FilteredItems
        {
            get => _filteredItems;
            set => Set(ref _filteredItems, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (Set(ref _searchText, value))
                {
                    // Apply filter when search text changes
                    ApplyFilter();
                }
            }
        }

        public string ReplaceText
        {
            get => _replaceText;
            set => Set(ref _replaceText, value);
        }

        public bool HasChanges
        {
            get => _hasChanges;
            set => Set(ref _hasChanges, value);
        }

        public bool ViewNameFilter
        {
            get
            {
                return _viewNameFilter;
            }
            set
            {
                if (Set(ref _viewNameFilter, value))
                {
                    ApplyFilter();
                }
            }
        }

        public bool SheetTitleFilter
        {
            get
            {
                return _sheetTitleFilter;
            }
            set
            {
                if (Set(ref _sheetTitleFilter, value))
                {
                    ApplyFilter();
                }
            }
        }

        public int FilteredCount => FilteredItems?.Cast<ViewNameModel>().Count() ?? 0;
        public int SelectedCount => FilteredItems?.Cast<ViewNameModel>().Count(i => i.IsSelected) ?? 0;

        public ICommand SelectAllCommand { get; }
        public ICommand ClearSelectionCommand { get; }
        public ICommand RenameCommand { get; }
        public ICommand ApplyCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand ClearFiltersCommand { get; }

        public FindAndReplaceViewNameViewModel(List<ViewNameModel> viewNameModles)
        {
            // Create test data
            _allItems = new ObservableCollection<ViewNameModel>(viewNameModles);

            // Initialize CollectionView for filtering and sorting
            FilteredItems = CollectionViewSource.GetDefaultView(_allItems);

            // Set up sorting (first by Name, then by Type)
            FilteredItems.SortDescriptions.Add(new SortDescription("Name", ListSortDirection.Ascending));
            FilteredItems.SortDescriptions.Add(new SortDescription("Type", ListSortDirection.Ascending));

            // Set up filtering
            FilteredItems.Filter = FilterItem;

            // Initialize commands
            SelectAllCommand = new RelayCommand(SelectAll);
            ClearSelectionCommand = new RelayCommand(ClearSelection);
            RenameCommand = new RelayCommand(Rename, CanRename);
            ApplyCommand = new RelayCommand(Apply, (object p) => HasChanges);
            CancelCommand = new RelayCommand(Cancel);
            ClearFiltersCommand = new RelayCommand(ClearFilters);
        }

        // Public method to update selected count (called from code-behind)
        public void UpdateSelectedCount()
        {
            OnPropertyChanged(nameof(SelectedCount));
            (RenameCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }

        // Filter predicate for CollectionView
        private bool FilterItem(object item)
        {
            var viewModelItem = item as ViewNameModel;

            if ((viewModelItem.ViewType == ViewType.DrawingSheet && SheetTitleFilter == false) ||
                (viewModelItem.ViewType == ViewType.FloorPlan && ViewNameFilter == false))
            {
                return false;
            }

            if (string.IsNullOrWhiteSpace(SearchText))
                return true;

            return viewModelItem != null &&
                   viewModelItem.Name.Contains(SearchText);
        }

        private void ApplyFilter()
        {
            // Reset selection for items that will be filtered out
            if (SheetTitleFilter == false || ViewNameFilter == false)
            {
                foreach (var item in _allItems)
                {
                    if ((item.ViewType == ViewType.DrawingSheet && SheetTitleFilter == false) ||
                        (item.ViewType == ViewType.FloorPlan && ViewNameFilter == false))
                    {
                        item.IsSelected = false;
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                foreach (var item in _allItems)
                {
                    if (!item.Name.Contains(SearchText))
                    {
                        item.IsSelected = false;
                    }
                }
            }

            // Refresh the view to apply the filter
            FilteredItems.Refresh();

            // Notify UI about count changes
            OnPropertyChanged(nameof(FilteredCount));
            OnPropertyChanged(nameof(SelectedCount));
        }

        private void SelectAll(object p)
        {
            // Select all items in the filtered view
            foreach (ViewNameModel item in FilteredItems)
            {
                item.IsSelected = true;
            }
            OnPropertyChanged(nameof(SelectedCount));
        }

        private void ClearSelection(object p)
        {
            // Clear selection for all items in the filtered view
            foreach (ViewNameModel item in FilteredItems)
            {
                item.IsSelected = false;
            }
            OnPropertyChanged(nameof(SelectedCount));
        }

        private void ClearFilters(object p)
        {
            _searchText = string.Empty;
            _replaceText = string.Empty;
            _viewNameFilter = true;
            _sheetTitleFilter = true;

            foreach (var item in _allItems)
            {
                item.IsSelected = false;
            }

            ApplyFilter();

            OnPropertyChanged(nameof(SearchText));
            OnPropertyChanged(nameof(ReplaceText));
            OnPropertyChanged(nameof(ViewNameFilter));
            OnPropertyChanged(nameof(SheetTitleFilter));
        }

        private bool CanRename(object p)
        {
            // Can rename only if search text is not empty and at least one item is selected
            return !string.IsNullOrWhiteSpace(SearchText) &&
                   FilteredItems.Cast<ViewNameModel>().Any(i => i.IsSelected);
        }

        private void Rename(object p)
        {
            if (ValidateNameConflicts() == false)
            {
                return;
            }

            // Check name uniqueness after replacement
            var selectedItems = FilteredItems.Cast<ViewNameModel>().Where(i => i.IsSelected).ToList();

            // Apply the replacement
            foreach (var item in selectedItems)
            {
                item.Name = item.Name.Replace(SearchText, ReplaceText);
            }

            // Clear selection for hiden items
            foreach (var item in _allItems)
            {
                item.IsSelected = false;
            }

            // Refresh the view to apply sorting after renaming
            FilteredItems.Refresh();

            OnPropertyChanged(nameof(SelectedCount));
            OnPropertyChanged(nameof(FilteredCount));

            HasChanges = true;
        }

        private bool ValidateNameConflicts()
        {
            // Unique view names items
            var allItems = _allItems.Cast<ViewNameModel>().Where(v => v.ViewType == ViewType.FloorPlan).ToList();
            var selectedItems = FilteredItems.Cast<ViewNameModel>().Where(i => i.IsSelected).Where(v => v.ViewType == ViewType.FloorPlan).ToList();

            // Check name uniqueness after replacement
            var newNames = selectedItems.Select(i => i.Name.Replace(SearchText, ReplaceText)).ToList();

            // Check for conflicts with existing names
            foreach (var newName in newNames)
            {
                var existingItem = allItems.FirstOrDefault(i => i.Name == newName);
                if (existingItem != null && !selectedItems.Contains(existingItem))
                {
                    MessageWindow.ShowDialog(
                        $"The view name is already in use by view {existingItem.View.Id}\n" +
                        $"{newName}",
                        //$"Name '{newName}' already exists (conflict with ViewId: {existingItem.View.Id})",
                        MessageType.Info);

                    return false;
                }
            }

            // Check for duplicates among new names
            var duplicates = newNames.GroupBy(n => n).Where(g => g.Count() > 1).Select(g => g.Key).ToList();
            if (duplicates.Any())
            {
                MessageWindow.ShowDialog(
                    $"Renaming will create duplicate names:\n" +
                    $"{string.Join("\n", duplicates)}",
                    MessageType.Info);

                return false;
            }

            return true;
        }

        private void Apply(object p)
        {
            var window = p as Window;
            window.DialogResult = true;
            window.Close();
        }

        private void Cancel(object p)
        {
            if (HasChanges)
            {
                var result = MessageWindow.ShowDialog(
                    "Do you really want to cancel all unapplied changes?", 
                    MessageType.Warning);

                if (result != true)
                {
                    return;
                }
            }

            var window = p as Window;
            window.DialogResult = false;
            window.Close();
        }
    }
}
