﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class InterlinkInput : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_ElectricalFixtures,
            FamilyNamesContains = new List<string>()
            {
                "Interlink-Input"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public InterlinkInput(Element element) : base(element)
        {
        }

        public static List<InterlinkInput> Collect()
        {
            return FamilyCollector.Instances.Select(g => new InterlinkInput(g)).ToList();
        }
    }
}
