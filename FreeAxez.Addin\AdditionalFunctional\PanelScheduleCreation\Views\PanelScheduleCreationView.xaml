﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.PanelScheduleCreation.Views.PanelScheduleCreationView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:FreeAxez.Addin.AdditionalFunctional.PanelScheduleCreation.ViewModels"
        SizeToContent="WidthAndHeight"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterScreen"
        Title="Track Schedule Creation"
        Topmost="True">
    <Window.DataContext>
        <vm:PanelScheduleCreationViewModel/>
    </Window.DataContext>
    <StackPanel Margin="10">
        <GroupBox Header="Schedule template of connected components">
            <StackPanel Margin="3">
                <RadioButton Margin="0,5,0,0" Content="{Binding ConnectedComponentsName}" GroupName="template" IsChecked="{Binding ConnectedComponents}"/>
                <RadioButton Margin="0,5,0,0" Content="{Binding ConnectedComponentsByNumberName}" GroupName="template" IsChecked="{Binding ConnectedComponentsByNumber}" />
            </StackPanel>
        </GroupBox>
        <Button Content="Create" Margin="0,20,0,0" Height="30" Command="{Binding CreateCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
    </StackPanel>
</Window>
