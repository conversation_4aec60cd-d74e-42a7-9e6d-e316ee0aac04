﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.PanelScheduleCreation.Utils;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement.Models
{
    internal class LevelSheets
    {
        private const string SheetNamePattern = "Rev{0}";
        public const string SheetNumberPattern = "GP-T-{0}{1}-{2}"; // GP-T-02B-11
        private const string SheetNumberPatternForLevelAndSize = @"^GP-T-{0}[A-Z]?-{1}$";
        private const string SheetNumberPatternForSize = @"^GP-T-\d\d[A-Z]?-{0}$";
        private const string AllTrackScheduleSheetNumberRegex = @"^GP-T-\d{2}[A-Z]?-\d{2}$";

        private List<ViewSheet> _sheets;
        private List<ViewSheet> _usedSheets;
        private int _levelNumber;
        private string _sheetHeight;


        public LevelSheets(int levelNumber, string sheetSize) 
        {
            _sheetHeight = Regex.Match(sheetSize, @"\d+").Value; // 24x36
            _levelNumber = levelNumber;
            _sheets = GetSheetsForLevel();
            _usedSheets = new List<ViewSheet>();
        }


        public static bool ProjectHasTemplateSheet(string sheetHeight)
        {
            return GetSheetTemplate(sheetHeight) != null;
        }

        private static ViewSheet GetSheetTemplate(string sheetHeight)
        {
            var sizePattern = string.Format(
                SheetNumberPatternForSize,
                sheetHeight);
            var sizeRegex = new Regex(sizePattern, RegexOptions.IgnoreCase);
            return new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(ViewSheet))
                .Cast<ViewSheet>()
                .OrderBy(s => s.SheetNumber)
                .FirstOrDefault(s => sizeRegex.IsMatch(s.SheetNumber));
        }

        public static List<string> GetAllTrackScheduleSheetSizes()
        {
            var anyTrackScheduleSheetRegex = new Regex(AllTrackScheduleSheetNumberRegex, RegexOptions.IgnoreCase);
            return new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(ViewSheet))
                .Cast<ViewSheet>()
                .Where(s => anyTrackScheduleSheetRegex.IsMatch(s.SheetNumber))
                .Select(s => s.LookupParameter("Sheet Size")?.AsString())
                .Where(s => !string.IsNullOrWhiteSpace(s))
                .Where(s => Regex.Match(s, @"\d+").Success)
                .OrderBy(s => s)
                .Distinct()
                .ToList();
        }

        public static void DeleteUnusedSheetsForSize(string sheetHeight)
        {
            var sizePattern = string.Format(
                SheetNumberPatternForSize,
                sheetHeight);
            var sizeRegex = new Regex(sizePattern, RegexOptions.IgnoreCase);

            var allSheetsForSize = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(ViewSheet))
                .Cast<ViewSheet>()
                .OrderBy(s => s.SheetNumber)
                .Where(s => sizeRegex.IsMatch(s.SheetNumber))
                .ToList();

            var sheetsToDelete = allSheetsForSize
                .Where(s => GetTrackScheduleInstancesFromSheet(s).Count == 0)
                .Select(s => s.Id)
                .ToList();

            if (allSheetsForSize.Count == sheetsToDelete.Count)
            {
                // Save at least one sheet for future
                sheetsToDelete.RemoveAt(0);
            }

            RevitManager.Document.Delete(sheetsToDelete);
        }

        private static List<ScheduleSheetInstance> GetTrackScheduleInstancesFromSheet(Element sheet)
        {
            return sheet
                .GetDependentElements(new ElementClassFilter(typeof(ScheduleSheetInstance)))
                .Select(id => RevitManager.Document.GetElement(id))
                .Cast<ScheduleSheetInstance>()
                .Where(s => TrackScheduleManager.IsTrackSchedule(s.Name))
                .ToList();
        }

        public void RemoveAllSchedulesFromSheets()
        {
            foreach (var sheet in _sheets)
            {
                var scheduleIds = GetTrackScheduleInstancesFromSheet(sheet)
                    .Select(s => s.Id).ToList();
                RevitManager.Document.Delete(scheduleIds);
            }
        }

        public bool PreviousSheetHasNotPlacedSchedules()
        {
            if (_usedSheets.Count > 0 &&
                GetTrackScheduleInstancesFromSheet(_usedSheets.Last()).Count == 0)
            {
                return true;
            }
            return false;
        }

        public ViewSheet GetNextSheetForLevel()
        {
            var sheet = _sheets.FirstOrDefault(s => !_usedSheets.Contains(s));
            if (sheet == null)
            {
                var templateSheet = GetSheetTemplate(_sheetHeight);
                sheet = DuplicateSheet(templateSheet);
                sheet.Name = string.Format(SheetNamePattern, _levelNumber);
                sheet.SheetNumber = string.Format(
                    SheetNumberPattern, _levelNumber.ToString("00"), GetNextLetter(), _sheetHeight);
                sheet.SetAdditionalRevisionIds(templateSheet.GetAdditionalRevisionIds());
                _sheets.Add(sheet);
            }
            _usedSheets.Add(sheet);
            sheet.Name = string.Format(SheetNamePattern, Properties.Settings.Default.panelSchedulePlacementRevision);
            return sheet;
        }

        public void RevomeUnusedSheets()
        {
            // TODO: need to change the view to be able to delete it,
            // but for this you need to interrupt the transaction.
            // This view will not be deleted now.
            var unusedSheetIds = _sheets
                .Where(s => !_usedSheets.Contains(s))
                .Where(s => s.Id.GetIntegerValue() != RevitManager.Document.ActiveView.Id.GetIntegerValue())
                .Select(s => s.Id)
                .ToList();

            RevitManager.Document.Delete(unusedSheetIds);
        }

        private List<ViewSheet> GetSheetsForLevel()
        {
            var trackSheetNumberRegex = new Regex(
                string.Format(SheetNumberPatternForLevelAndSize, _levelNumber.ToString("00"), _sheetHeight),
                RegexOptions.IgnoreCase);

            return new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(ViewSheet))
                .Cast<ViewSheet>()
                .Where(s => trackSheetNumberRegex.IsMatch(s.SheetNumber))
                .OrderBy(s => s.SheetNumber)
                .ToList();
        }

        private char GetNextLetter()
        {
            return (char)('A' + _sheets.Count);
        }

        private ViewSheet DuplicateSheet(ViewSheet viewSheet)
        {
            var titleBlock = viewSheet
                .GetDependentElements(new ElementCategoryFilter(BuiltInCategory.OST_TitleBlocks))
                .Select(id => RevitManager.Document.GetElement(id))
                .Cast<FamilyInstance>()
                .FirstOrDefault();

            if (titleBlock == null)
            {
                throw new InvalidOperationException(
                    $"The \"{viewSheet.SheetNumber} - {viewSheet.Name}\" sheet selected as a template does not have a title block");
            }

            var createdSheet = ViewSheet.Create(RevitManager.Document, titleBlock.GetTypeId());

            foreach (Parameter parameter in viewSheet.Parameters)
            {
                if (parameter.Definition.Name == "Sheet Number")
                {
                    continue;
                }

                try
                {
                    if (parameter.StorageType == StorageType.String)
                    {
                        createdSheet.get_Parameter(parameter.Definition).Set(parameter.AsString());
                    }
                    else if (parameter.StorageType == StorageType.Integer)
                    {
                        if (parameter.Definition.Name == "Appears In Sheet List")
                        {
                            createdSheet.get_Parameter(parameter.Definition).Set(parameter.AsInteger());
                        }
                    }
                }
                catch { }
            }

            return createdSheet;
        }
    }
}
