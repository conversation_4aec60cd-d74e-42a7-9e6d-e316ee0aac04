﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.DataTransferObjects;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Windows.Media;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Mapping
{
    public static class DataTransferObjectMapping
    {
        private static readonly IEnumerable<Brush> _brushes = GenerateBrushes();

        public static IEnumerable<CommentViewModel> MapToCommentViewModels(this IEnumerable<CommentDto> commentDtos)
        {
            Dictionary<string, IEnumerable<CommentDto>> commentDtoGroups = commentDtos
                .GroupBy(comment => comment.CreatedBy)
                .ToDictionary(group => group.Key, group => group.AsEnumerable());

            IEnumerator<Brush> brushEnumerator = _brushes.GetEnumerator();

            List<CommentViewModel> comments = new List<CommentViewModel>();
            foreach (KeyValuePair<string, IEnumerable<CommentDto>> commentDtoGroup in commentDtoGroups)
            {
                brushEnumerator.MoveNext();
                Brush brush = brushEnumerator.Current;

                foreach (var commentDto in commentDtoGroup.Value)
                {
                    comments.Add(new CommentViewModel(commentDto.Text,
                                                           commentDto.CreatedBy,
                                                           commentDto.CreatedAt,
                                                           brush));
                }
            }

            return comments.OrderBy(c => c.CreatedAt);
        }

        private static IEnumerable<Brush> GenerateBrushes()
        {
            PropertyInfo[] colorProperties = typeof(Colors)
                .GetProperties(BindingFlags.Public | BindingFlags.Static);

            foreach (var colorProperty in colorProperties)
            {
                string propertyName = colorProperty.Name;
                if (propertyName.StartsWith("Dark"))
                    yield return new SolidColorBrush((Color)colorProperty.GetValue(null, null));
            }
        }
    }
}
