﻿using Autodesk.Revit.DB;
using OfficeOpenXml;

namespace FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Converters
{
    public class PartsVisibilityConverter
    {
        private static readonly Dictionary<PartsVisibility, string> PartsVisibilityMap = new()
        {
            { PartsVisibility.Unset, "Not Set" },
            { PartsVisibility.ShowPartsOnly, "Show Parts" },
            { PartsVisibility.ShowOriginalOnly, "Show Original" },
            { PartsVisibility.ShowPartsAndOriginal, "Show Both" }
        };

        public static string ConvertPartsVisibility(PartsVisibility visibility)
        {
            if (PartsVisibilityMap.TryGetValue(visibility, out var visibilityString)) return visibilityString;
            return "Unknown";
        }

        public static void AddPartsVisibilityValidation(ExcelWorksheet ws, string cellAddress)
        {
            var dv = ws.DataValidations.AddListValidation(cellAddress);
            foreach (var visibility in PartsVisibilityMap.Values) dv.Formula.Values.Add(visibility);
            dv.ShowErrorMessage = true;
            dv.ErrorTitle = "Invalid Parts Visibility";
            dv.Error = "Please select a valid parts visibility option.";
        }

        public static PartsVisibility ParsePartsVisibility(string visibilityString)
        {
            var reverseMap = new Dictionary<string, PartsVisibility>();
            foreach (var kvp in PartsVisibilityMap)
                reverseMap[kvp.Value] = kvp.Key;

            if (reverseMap.TryGetValue(visibilityString, out var pv))
                return pv;

            return PartsVisibility.Unset;
        }
    }
}
