using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using System.Text;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services
{
    /// <summary>
    /// Service for generating and displaying reports for Low Voltage Path operations
    /// </summary>
    public class LowVoltageReportService
    {
        /// <summary>
        /// Shows validation results to the user
        /// </summary>
        public bool ShowValidationResults(ValidationResult validationResult)
        {
            if (validationResult.IsValid)
            {
                // Show warnings if any
                if (validationResult.WarningMessages.Count > 0)
                {
                    var warningMessage = GenerateValidationWarningMessage(validationResult);
                    var result = MessageWindow.ShowDialog(warningMessage, MessageType.Warning);
                    return result == true; // User chose to continue
                }
                return true; // No warnings, proceed
            }
            else
            {
                // Show errors
                var errorMessage = GenerateValidationErrorMessage(validationResult);
                MessageWindow.ShowDialog(errorMessage, MessageType.Notify);
                return false; // Cannot proceed
            }
        }

        /// <summary>
        /// Shows execution results to the user
        /// </summary>
        public void ShowExecutionResults(ExecutionReport report)
        {
            if (report.IsSuccessful)
            {
                var message = GenerateSuccessMessage(report);
                MessageWindow.ShowDialog(message, MessageType.Success);
            }
            else
            {
                var message = GenerateFailureMessage(report);
                MessageWindow.ShowDialog(message, MessageType.Warning);
            }
        }

        /// <summary>
        /// Generates a detailed validation warning message
        /// </summary>
        private string GenerateValidationWarningMessage(ValidationResult validationResult)
        {
            var message = new StringBuilder();
            message.AppendLine("Validation completed with warnings:");
            message.AppendLine();

            foreach (var warning in validationResult.WarningMessages)
            {
                message.AppendLine($"⚠ {warning}");
            }

            message.AppendLine();
            message.AppendLine($"Found {validationResult.ValidOutletsCount} outlet(s) and {validationResult.ValidLinesCount} line(s) to process.");
            
            if (validationResult.OutletsWithExistingRailingsCount > 0)
            {
                message.AppendLine($"{validationResult.OutletsWithExistingRailingsCount} outlet(s) already have railings and will be skipped.");
            }

            message.AppendLine();
            message.AppendLine("Do you want to continue?");

            return message.ToString();
        }

        /// <summary>
        /// Generates a validation error message
        /// </summary>
        private string GenerateValidationErrorMessage(ValidationResult validationResult)
        {
            var message = new StringBuilder();
            message.AppendLine("Validation failed. Cannot proceed with the operation:");
            message.AppendLine();

            foreach (var error in validationResult.ErrorMessages)
            {
                message.AppendLine($"✗ {error}");
            }

            if (validationResult.WarningMessages.Count > 0)
            {
                message.AppendLine();
                message.AppendLine("Additional warnings:");
                foreach (var warning in validationResult.WarningMessages)
                {
                    message.AppendLine($"⚠ {warning}");
                }
            }

            return message.ToString();
        }

        /// <summary>
        /// Generates a success message for completed execution
        /// </summary>
        private string GenerateSuccessMessage(ExecutionReport report)
        {
            var message = new StringBuilder();
            message.AppendLine("Low Voltage Path generation completed successfully!");
            message.AppendLine();

            // Summary of created elements
            if (report.RailingsCreated > 0)
            {
                message.AppendLine($"✓ Created {report.RailingsCreated} railing(s)");
            }

            if (report.AnnotationsCreated > 0)
            {
                message.AppendLine($"✓ Created {report.AnnotationsCreated} annotation(s)");
            }

            if (report.LinesDeleted > 0)
            {
                message.AppendLine($"✓ Deleted {report.LinesDeleted} line(s)");
            }

            // Processing statistics
            message.AppendLine();
            message.AppendLine($"Processed {report.TotalOutletsProcessed} outlet(s) and {report.TotalLinesProcessed} line(s)");

            // Warnings about unconnected outlets
            if (report.UnconnectedOutlets > 0)
            {
                message.AppendLine();
                message.AppendLine($"⚠ Warning: {report.UnconnectedOutlets} outlet(s) could not be connected to railings");
                
                if (report.UnconnectedOutletIds.Count > 0 && report.UnconnectedOutletIds.Count <= 10)
                {
                    message.AppendLine($"Unconnected outlet IDs: {string.Join(", ", report.UnconnectedOutletIds)}");
                }
                else if (report.UnconnectedOutletIds.Count > 10)
                {
                    message.AppendLine($"Unconnected outlet IDs: {string.Join(", ", report.UnconnectedOutletIds.Take(10))} and {report.UnconnectedOutletIds.Count - 10} more...");
                }
            }

            // Performance information
            if (report.ExecutionTimeMs > 0)
            {
                message.AppendLine();
                message.AppendLine($"Execution time: {report.ExecutionTimeMs:N0} ms");
            }

            return message.ToString();
        }

        /// <summary>
        /// Generates a failure message when no elements were created
        /// </summary>
        private string GenerateFailureMessage(ExecutionReport report)
        {
            var message = new StringBuilder();
            message.AppendLine("Low Voltage Path generation completed, but no elements were created.");
            message.AppendLine();

            message.AppendLine($"Processed {report.TotalOutletsProcessed} outlet(s) and {report.TotalLinesProcessed} line(s)");

            if (report.UnconnectedOutlets > 0)
            {
                message.AppendLine();
                message.AppendLine($"All {report.UnconnectedOutlets} outlet(s) could not be connected to railings.");
                message.AppendLine("This might be due to:");
                message.AppendLine("• Outlets are too far from lines");
                message.AppendLine("• Lines are not properly connected");
                message.AppendLine("• Outlets already have existing railings");
            }

            message.AppendLine();
            message.AppendLine("Please check your model and try again.");

            return message.ToString();
        }

        /// <summary>
        /// Generates a summary report for logging or debugging
        /// </summary>
        public string GenerateDetailedReport(ValidationResult validation, ExecutionReport execution)
        {
            var report = new StringBuilder();
            report.AppendLine("=== Low Voltage Path Generation Report ===");
            report.AppendLine($"Generated at: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            // Validation section
            report.AppendLine("VALIDATION RESULTS:");
            report.AppendLine($"Status: {(validation.IsValid ? "PASSED" : "FAILED")}");
            report.AppendLine($"Valid outlets: {validation.ValidOutletsCount}");
            report.AppendLine($"Valid lines: {validation.ValidLinesCount}");
            report.AppendLine($"Outlets with existing railings: {validation.OutletsWithExistingRailingsCount}");

            if (validation.ErrorMessages.Count > 0)
            {
                report.AppendLine("Errors:");
                foreach (var error in validation.ErrorMessages)
                {
                    report.AppendLine($"  - {error}");
                }
            }

            if (validation.WarningMessages.Count > 0)
            {
                report.AppendLine("Warnings:");
                foreach (var warning in validation.WarningMessages)
                {
                    report.AppendLine($"  - {warning}");
                }
            }

            // Execution section
            if (execution != null)
            {
                report.AppendLine();
                report.AppendLine("EXECUTION RESULTS:");
                report.AppendLine($"Status: {(execution.IsSuccessful ? "SUCCESS" : "PARTIAL/FAILED")}");
                report.AppendLine($"Railings created: {execution.RailingsCreated}");
                report.AppendLine($"Annotations created: {execution.AnnotationsCreated}");
                report.AppendLine($"Lines deleted: {execution.LinesDeleted}");
                report.AppendLine($"Unconnected outlets: {execution.UnconnectedOutlets}");
                report.AppendLine($"Execution time: {execution.ExecutionTimeMs} ms");

                if (execution.UnconnectedOutletIds.Count > 0)
                {
                    report.AppendLine($"Unconnected outlet IDs: {string.Join(", ", execution.UnconnectedOutletIds)}");
                }
            }

            return report.ToString();
        }
    }
}
