﻿using FreeAxez.Addin.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Models
{
    public class ProductGroupPriorityComparer : IComparer<ProductGroup>
    {
        private readonly Dictionary<Type, int> _typePriorities;

        public ProductGroupPriorityComparer()
        {
            _typePriorities = new Dictionary<Type, int>
            {
                { typeof(Whip), 1 },
                { typeof(Track), 2 },
                { typeof(FloorBox), 3 },
            };
        }

        public int Compare(ProductGroup x, ProductGroup y)
        {
            var xProduct = x.Products.First().GetType();
            var yProduct = y.Products.First().GetType();

            int xPriority = _typePriorities.ContainsKey(xProduct) ? _typePriorities[xProduct] : int.MaxValue;
            int yPriority = _typePriorities.ContainsKey(yProduct) ? _typePriorities[yProduct] : int.MaxValue;

            return xPriority.CompareTo(yPriority);
        }
    }
}
