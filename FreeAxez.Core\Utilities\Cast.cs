﻿using System;
using System.Collections.Generic;
using System.Linq;
using Accord;
using Accord.Math;
using Accord.Math.Geometry;
using FreeAxez.Core.GeometryModel;

namespace FreeAxez.Core.Utilities
{
    public class CastRectangleData
    {
        public Rectangle Rectangle;
        public object Data;
    }

    public class CastLineData
    {
        public LineModel Line;
        public object Data;
    }

    public class CastDataToPrepare
    {
        public ICollection<CastLineData> Lines = new List<CastLineData>();
        public ICollection<CastRectangleData> Rectangles = new List<CastRectangleData>();
    }

    public class CastResult
    {
        public readonly PointModel Intersection;
        public readonly double Distance;
        public readonly object Data;

        public CastResult(PointModel intersection, double distance, object data)
        {
            Intersection = intersection;
            Distance = distance;
            Data = data;
        }
    }

    public class WideCastResult
    {
        public class Cast
        {
            public PointModel Origin;
            public CastResult CastResult;
        }

        public Cast[] Result;
    }

    public class SliceResult
    {
        public class Slice
        {
            public readonly PointModel Point1;
            public readonly PointModel Point2;

            public Slice(PointModel point1, PointModel point2)
            {
                Point1 = point1;
                Point2 = point2;
            }
        }

        public readonly Cast.TwoDirection Direction;
        public readonly Slice[] Slices;

        public SliceResult(Cast.TwoDirection direction, Slice[] slices)
        {
            Direction = direction;
            Slices = slices;
        }
    }

    public class CastArea
    {
        public class Cell
        {
            public List<CastLineData> Lines;
            public ICollection<CastRectangleData> Rectangles;
        }

        public Cell[][] Grid;
        public double CellSize;
        public PointModel MinPosition;
    }

    public static class Cast
    {
        public enum Direction
        {
            Up,
            Right,
            Down,
            Left
        }

        public enum TwoDirection
        {
            Horizontal,
            Vertical
        }

        /// <summary>
        /// Casts a ray at direction to find the closest intersection.
        /// </summary>
        /// <param name="origin">Point to start ray at</param>
        /// <param name="direction">Direction to shoot ray at</param>
        /// <param name="lines">Lines to check intersection with</param>
        /// <returns>Intersection point, distance to that point</returns>
        [Obsolete("This method neither has area optimizations nor immediate raycasting. Possible performance issues.")]
        public static CastResult RayCast(PointModel origin, PointModel direction, ICollection<LineModel> lines)
        {
            var beamLine = Line.FromPoints(new Point((float)origin.X, (float)origin.Y), new Point((float)(origin.X + direction.X), (float)(origin.Y + direction.Y)));

            Point? closestPoint = null;
            double shortestPointDistance = 0;

            double minDouble = Math.Pow(0.1, MathUtilities.DefaultPrecision);

            double epsilon = Math.Pow(0.1, MathUtilities.DefaultPrecision);

            foreach (var regionLine in lines)
            {
                if (MathUtilities.GetLineLength(regionLine.StartPoint, regionLine.EndPoint) < epsilon)
                    continue;

                var lineP1 = new Point((float)regionLine.StartPoint.X, (float)regionLine.StartPoint.Y);
                var lineP2 = new Point((float)regionLine.EndPoint.X, (float)regionLine.EndPoint.Y);

                if (beamLine.DistanceToPoint(lineP1) < minDouble && beamLine.DistanceToPoint(lineP2) < minDouble)
                {
                    return Distance.Euclidean(origin.X, origin.Y, regionLine.StartPoint.X, regionLine.StartPoint.Y)
                            < Distance.Euclidean(origin.X, origin.Y, regionLine.EndPoint.X, regionLine.EndPoint.Y)
                        ? new CastResult(regionLine.StartPoint, 0, null)
                        : new CastResult(regionLine.EndPoint, 0, null);
                }

                var intersection = beamLine.GetIntersectionWith(new LineSegment(lineP1, lineP2));

                if (!intersection.HasValue)
                    continue;

                if (Vector3.Dot(new Vector3((float)(intersection.Value.X - origin.X), (float)(intersection.Value.Y - origin.Y), 0), new Vector3((float)direction.X, (float)direction.Y, 0)) < 0)
                    continue;

                double intersectionDistance = Distance.Euclidean(
                    origin.X, origin.Y,
                    intersection.Value.X, intersection.Value.Y);

                if (!closestPoint.HasValue)
                {
                    closestPoint = intersection.Value;
                    shortestPointDistance = intersectionDistance;
                    continue;
                }

                if (intersectionDistance < shortestPointDistance)
                {
                    closestPoint = intersection;
                    shortestPointDistance = intersectionDistance;
                }
            }

            return closestPoint.HasValue ? new CastResult(new PointModel(closestPoint.Value.X, closestPoint.Value.Y), shortestPointDistance, null) : null;
        }

        /// <summary>
        /// Casts a ray at direction to find the closest intersection. Direction is limited for better performance.
        /// </summary>
        /// <param name="origin">Point to start ray at</param>
        /// <param name="direction">Direction to shoot ray at</param>
        /// <param name="area">Result of <see cref="PrepareArea"/>></param>
        /// <returns>Intersection point, distance to that point</returns>
        public static CastResult RayCast(PointModel origin, Direction direction, CastArea area)
        {
            return RayCastIf(origin, direction, _ => true, area);
        }

        public static CastResult RayCastIf(PointModel origin, Direction direction, Func<CastResult, bool> hitCondition, CastArea area)
        {
            var cellDirection = new IntPoint(0, 0);
            switch (direction)
            {
                case Direction.Up:
                    cellDirection = new IntPoint(0, 1);
                    break;
                case Direction.Right:
                    cellDirection = new IntPoint(1, 0);
                    break;
                case Direction.Down:
                    cellDirection = new IntPoint(0, -1);
                    break;
                case Direction.Left:
                    cellDirection = new IntPoint(-1, 0);
                    break;
            }

            var rayDirection = new PointModel(cellDirection.X, cellDirection.Y);

            var beamLine = Line.FromPoints(
                new Point((float)origin.X, (float)origin.Y), 
                new Point((float)(origin.X + rayDirection.X), (float)(origin.Y + rayDirection.Y)));

            var currentCell = new IntPoint(
                GetCell(origin.X, area.MinPosition.X, area.CellSize),
                GetCell(origin.Y, area.MinPosition.Y, area.CellSize));

            if (currentCell.X < 0)
                currentCell.X = 0;
            else if (currentCell.X >= area.Grid[0].Length)
                currentCell.X = area.Grid[0].Length - 1;

            if (currentCell.Y < 0)
                currentCell.Y = 0;
            else if (currentCell.Y >= area.Grid.Length)
                currentCell.Y = area.Grid.Length - 1;

            // If origin point is inside some rectangle - don't try to cast a ray.
            var intersectedRectangle = area.Grid[currentCell.Y][currentCell.X].Rectangles.FirstOrDefault(rectangle => GeometryUtilities.IsPointInsideRectangle(rectangle.Rectangle, origin));
            if (intersectedRectangle != null)
                return new CastResult(origin, 0, intersectedRectangle.Data);

            bool castColumn = direction == Direction.Up || direction == Direction.Down;
            int iter;
            int iterEnd;
            int iterInc;
            int perpendicularIter;

            if (castColumn)
            {
                iter = currentCell.Y;
                perpendicularIter = currentCell.X;
                iterEnd = area.Grid.Length;
                iterInc = cellDirection.Y;
            }
            else
            {
                iter = currentCell.X;
                perpendicularIter = currentCell.Y;
                iterEnd = area.Grid[0].Length;
                iterInc = cellDirection.X;
            }

            for (; iter >= 0 && iter < iterEnd; iter += iterInc)
            {
                var currentCellX = !castColumn ? iter : perpendicularIter;
                var currentCellY = castColumn ? iter : perpendicularIter;
                var linesToCheck = area.Grid[currentCellY][currentCellX].Lines;

                Point? closestPoint = null;
                double shortestPointDistance = 0;
                object closestData = null;

                foreach (var lineToCheck in linesToCheck)
                {
                    if (lineToCheck.Line.StartPoint.EqualTo(lineToCheck.Line.EndPoint))
                        continue;

                    var lineP1 = new Point((float)lineToCheck.Line.StartPoint.X, (float)lineToCheck.Line.StartPoint.Y);
                    var lineP2 = new Point((float)lineToCheck.Line.EndPoint.X, (float)lineToCheck.Line.EndPoint.Y);

                    // Ray is inside line. Technically intersected.
                    if (MathUtilities.Approximately(beamLine.DistanceToPoint(lineP1), 0) && MathUtilities.Approximately(beamLine.DistanceToPoint(lineP2), 0))
                    {
                        var result = Distance.Euclidean(origin.X, origin.Y, lineToCheck.Line.StartPoint.X, lineToCheck.Line.StartPoint.Y)
                               < Distance.Euclidean(origin.X, origin.Y, lineToCheck.Line.EndPoint.X, lineToCheck.Line.EndPoint.Y)
                            ? new CastResult(lineToCheck.Line.StartPoint, 0, lineToCheck.Data)
                            : new CastResult(lineToCheck.Line.EndPoint, 0, lineToCheck.Data);

                        if (hitCondition(result))
                            return result;
                            
                        continue;
                    }

                    var intersection = beamLine.GetIntersectionWith(new LineSegment(lineP1, lineP2));

                    if (!intersection.HasValue)
                        continue;
                    
                    // Intersect only in one direction.
                    if (Vector3.Dot(
                        new Vector3((float)(intersection.Value.X - origin.X), (float)(intersection.Value.Y - origin.Y), 0),
                        new Vector3((float)rayDirection.X, (float)rayDirection.Y, 0)) < 0)
                        continue;

                    double intersectionDistance = Distance.Euclidean(
                        origin.X, origin.Y,
                        intersection.Value.X, intersection.Value.Y);

                    if (!closestPoint.HasValue)
                    {
                        closestPoint = intersection.Value;
                        shortestPointDistance = intersectionDistance;
                        closestData = lineToCheck.Data;
                        continue;
                    }

                    if (intersectionDistance < shortestPointDistance)
                    {
                        closestPoint = intersection;
                        shortestPointDistance = intersectionDistance;
                        closestData = lineToCheck.Data;
                    }
                }

                if (closestPoint.HasValue)
                {
                    var result = new CastResult(new PointModel(closestPoint.Value.X, closestPoint.Value.Y), shortestPointDistance, closestData);
                    if (hitCondition(result))
                        return result;
                }
            }

            return null;
        }

        /// <summary>
        /// Casts multiple rays at direction with a spread to find the closest intersection for each one. For a small gap could be viewed as a line or a wave.
        /// Direction is limited for better performance.
        /// </summary>
        /// <param name="corner1">Start point of the cast line</param>
        /// <param name="corner2">End point of the cast line</param>
        /// <param name="direction">Direction to shoot ray at</param>
        /// <param name="castsCount">Amount or rays to cast. Larger - more precise</param>
        /// <param name="simplifiedRegionArea">Result of <see cref="PrepareArea"/></param>
        /// <returns>Multiple intersection points, distances to those points</returns>
        public static WideCastResult WideCast(PointModel corner1, PointModel corner2, Direction direction, int castsCount, CastArea simplifiedRegionArea)
        {
            var originPoints = Enumerable.Range(0, castsCount).Select(n => (double)n / castsCount).Select(n => MathUtilities.Lerp(corner1, corner2, n)).ToArray();

            return new WideCastResult
            {
                Result = originPoints
                    .Select(o => new WideCastResult.Cast { Origin = o, CastResult = RayCast(o, direction, simplifiedRegionArea) })
                    .ToArray()
            };
        }

        /// <summary>
        /// Slices the whole area at specific point. Expecting the area is closed space - provides an array of 'inside' spaces. While outside are not includes in the result.
        /// Slice is 1 dimensional.
        /// </summary>
        /// <example>
        /// -----------
        /// |   |  |  |
        /// |---|---  |
        /// |   |     |
        /// -----------
        /// -> Slicing bottom part ->
        /// |   |     |
        /// </example>
        /// <param name="originXOrY">Coordinate of a point which creates infinite line that slices the area</param>
        /// <param name="direction">Orientation of the line</param>
        /// <param name="area">Result of <see cref="PrepareArea"/></param>
        /// <returns>1 dimensional area slice</returns>

        public static SliceResult SliceHorizontal(double y, CastArea area)
        {
            return SliceArea(y, TwoDirection.Horizontal, area);
        }

        public static SliceResult SliceVertical(double x, CastArea area)
        {
            return SliceArea(x, TwoDirection.Vertical, area);
        }

        private static SliceResult SliceArea(double originXOrY, TwoDirection direction, CastArea area)
        {
            var cellDirectionPositive = new IntPoint(0, 0);
            var cellDirectionNegative = new IntPoint(0, 0);
            switch (direction)
            {
                case TwoDirection.Vertical:
                    cellDirectionPositive = new IntPoint(0, 1);
                    cellDirectionNegative = new IntPoint(0, -1);
                    break;
                case TwoDirection.Horizontal:
                    cellDirectionPositive = new IntPoint(1, 0);
                    cellDirectionNegative = new IntPoint(1, 0);
                    break;
            }

            var rayDirectionPositive = new PointModel(cellDirectionPositive.X, cellDirectionPositive.Y);
            var rayDirectionNegative = new PointModel(cellDirectionNegative.X, cellDirectionNegative.Y);

            bool castColumn = direction == TwoDirection.Vertical;
            int iter;
            int iterEnd;
            int iterInc;
            int perpendicularIter;

            if (castColumn)
            {
                iter = 0;
                perpendicularIter = GetCell(originXOrY, area.MinPosition.X, area.CellSize);
                iterEnd = area.Grid.Length;
                iterInc = cellDirectionPositive.Y;
            }
            else
            {
                iter = 0;
                perpendicularIter = GetCell(originXOrY, area.MinPosition.Y, area.CellSize);
                iterEnd = area.Grid[0].Length;
                iterInc = cellDirectionPositive.X;
            }

            var originX = castColumn ? originXOrY : 0;
            var originY = castColumn ? 0 : originXOrY;

            var beamLine = Line.FromPoints(
                new Point((float)originX, (float)originY),
                new Point((float)(originX + rayDirectionPositive.X), (float)(originY + rayDirectionPositive.Y)));

            //var intersectionLines = new List<SliceResult.Slice>();
            var intersectionPoints = new List<PointModel>();

            for (; iter >= 0 && iter < iterEnd; iter += iterInc)
            {
                var currentCellX = !castColumn ? iter : perpendicularIter;
                var currentCellY = castColumn ? iter : perpendicularIter;
                var linesToCheck = area.Grid[currentCellY][currentCellX].Lines;

                foreach (var lineToCheck in linesToCheck)
                {
                    if (lineToCheck.Line.StartPoint.EqualTo(lineToCheck.Line.EndPoint))
                        continue;

                    var lineP1 = new Point((float)lineToCheck.Line.StartPoint.X, (float)lineToCheck.Line.StartPoint.Y);
                    var lineP2 = new Point((float)lineToCheck.Line.EndPoint.X, (float)lineToCheck.Line.EndPoint.Y);

                    // Ray is inside line. Technically intersected.
                    if (MathUtilities.Approximately(beamLine.DistanceToPoint(lineP1), 0) && MathUtilities.Approximately(beamLine.DistanceToPoint(lineP2), 0))
                    {
                        intersectionPoints.AddRange(castColumn
                            ? new PointModel[] {lineToCheck.Line.StartPoint, lineToCheck.Line.EndPoint}
                                .OrderBy(p => p.Y)
                            : new PointModel[] {lineToCheck.Line.StartPoint, lineToCheck.Line.EndPoint}
                                .OrderBy(p => p.X));

                        continue;
                    }

                    var intersection = beamLine.GetIntersectionWith(new LineSegment(lineP1, lineP2));

                    if (!intersection.HasValue)
                        continue;

                    // Same line could be in multiple cells. Process only intersections in the cells that contain it.
                    if (GetCell(intersection.Value.X, area.MinPosition.X, area.CellSize) != currentCellX
                        || GetCell(intersection.Value.Y, area.MinPosition.Y, area.CellSize) != currentCellY)
                        continue;

                    intersectionPoints.Add(new PointModel(intersection.Value.X, intersection.Value.Y));
                }
            }

            intersectionPoints = castColumn 
                ? intersectionPoints.DistinctBy(p => p.Y).ToList() 
                : intersectionPoints.DistinctBy(p => p.X).ToList();

            if (intersectionPoints.Count == 0)
                return new SliceResult(direction, new SliceResult.Slice[] { });

            intersectionPoints = castColumn 
                ? intersectionPoints.OrderBy(p => p.Y).ToList() 
                : intersectionPoints.OrderBy(p => p.X).ToList();

            var resultSlices = new List<SliceResult.Slice>();

            for (int i = 1; i < intersectionPoints.Count; i += 2)
                resultSlices.Add(new SliceResult.Slice(intersectionPoints[i - 1], intersectionPoints[i]));

            return new SliceResult(direction, resultSlices.ToArray());
        }

        /// <summary>
        /// Tests if a point is inside sliced area.
        /// </summary>
        /// <param name="xOrY">Coordinate of a point in the slice</param>
        /// <param name="result">Result of <see cref="SliceArea"/></param>
        /// <returns>Test result</returns>
        public static bool IsInsideSlice(double xOrY, SliceResult result)
        {
            return result.Direction == TwoDirection.Vertical
                ? result.Slices.Any(s => MathUtilities.IsNumberBetween(xOrY, s.Point1.Y, s.Point2.Y))
                : result.Slices.Any(s => MathUtilities.IsNumberBetween(xOrY, s.Point1.X, s.Point2.X));
        }

        /// <summary>
        /// Tests whether a figure specified by lines is intersecting something.
        /// </summary>
        /// <param name="testArea">Test figure</param>
        /// <param name="area">Result of <see cref="PrepareArea"/></param>
        /// <returns>Test result</returns>
        public static CastResult AreaCast(IEnumerable<ILineModel> testArea, CastArea area)
        {
            var cellCoordinates = testArea
                .SelectMany(l => new[]
                {
                    new { CellX = GetCell(l.StartPointGetter.X, area.MinPosition.X, area.CellSize), CellY = GetCell(l.StartPointGetter.Y, area.MinPosition.Y, area.CellSize) },
                    new { CellX = GetCell(l.EndPointGetter.X, area.MinPosition.X, area.CellSize), CellY = GetCell(l.EndPointGetter.Y, area.MinPosition.Y, area.CellSize) }
                })
                .ToArray();

            // Test area can be outside cast area. Ignore out of range coordinates.
            var minCellX = Math.Max(cellCoordinates.Min(c => c.CellX), 0);
            var minCellY = Math.Max(cellCoordinates.Min(c => c.CellY), 0);
            var maxCellX = Math.Min(cellCoordinates.Max(c => c.CellX), area.Grid[0].Length - 1);
            var maxCellY = Math.Min(cellCoordinates.Max(c => c.CellY), area.Grid.Length - 1);

            // TODO Optimization: exclude already tested lines.
            for (int y = minCellY; y <= maxCellY; ++y)
            {
                for (int x = minCellX; x <= maxCellX; ++x)
                {
                    foreach (var line in area.Grid[y][x].Lines)
                    {
                        foreach (var testLine in testArea)
                        {
                            try
                            {
                                if (GeometryUtilities.AreLinesIntersected(testLine, line.Line))
                                    return new CastResult(null, 0, null);
                            }
                            catch (InvalidOperationException e)
                            {
                                // HACK
                                if (e.Message.StartsWith("Overlapping segments do not have a single intersection point"))
                                    return new CastResult(null, 0, null);
                            }
                        }
                    }
                }
            }

            return null;
        }

        private static CastArea CreateCastArea(CastDataToPrepare dataToPrepare, double cellSize)
        {
            var allLines = dataToPrepare.Lines.Concat(
                dataToPrepare.Rectangles.SelectMany(
                    r => GeometryUtilities.ConvertRectangleToLines(r.Rectangle).Select(
                        l => new CastLineData { Line = l, Data = r.Data }))).ToArray();

            var area = new CastArea();

            var minX = allLines.Min(l => Math.Min(l.Line.StartPoint.X, l.Line.EndPoint.X));
            var minY = allLines.Min(l => Math.Min(l.Line.StartPoint.Y, l.Line.EndPoint.Y));
            var maxX = allLines.Max(l => Math.Max(l.Line.StartPoint.X, l.Line.EndPoint.X));
            var maxY = allLines.Max(l => Math.Max(l.Line.StartPoint.Y, l.Line.EndPoint.Y));

            var cellXCount = GetCell(maxX, minX, cellSize) + 1;
            var cellYCount = GetCell(maxY, minY, cellSize) + 1;

            area.CellSize = cellSize;
            area.MinPosition = new PointModel(minX, minY);

            area.Grid = new CastArea.Cell[cellYCount][];
            for (int i = 0; i < area.Grid.Length; ++i)
                area.Grid[i] = new CastArea.Cell[cellXCount];

            for (int y = 0; y < area.Grid.Length; ++y)
                for (int x = 0; x < area.Grid[0].Length; ++x)
                    area.Grid[y][x] = new CastArea.Cell { Lines = new List<CastLineData>(), Rectangles = new List<CastRectangleData>() };

            return area;
        }

        /// <summary>
        /// Simplifies incoming data to use in other cast operations.
        /// </summary>
        /// <param name="dataToPrepare">Input data that needs to be simplified</param>
        /// <param name="cellSize">Cell size of the mapped to incoming area grid</param>
        /// <returns>Simplified input area</returns>
        public static CastArea PrepareArea(CastDataToPrepare dataToPrepare, double cellSize)
        {
            var area = CreateCastArea(dataToPrepare, cellSize);
            PrepareArea(dataToPrepare, area);

            return area;
        }

        public static void PrepareArea(CastDataToPrepare dataToPrepare,CastArea area)
        {
            // https://github.com/cgyurgyik/fast-voxel-traversal-algorithm/blob/master/overview/FastVoxelTraversalOverview.md
            // https://github.com/feelinfine/tracer/blob/master/tracer.h

            foreach (var line in dataToPrepare.Lines)
            {
                if (line.Line.StartPoint.EqualTo(line.Line.EndPoint))
                    continue;

                var ray = new Vector3((float)(line.Line.EndPoint.X - line.Line.StartPoint.X), (float)(line.Line.EndPoint.Y - line.Line.StartPoint.Y), 0);
                ray.Normalize();

                var currentCell = new IntPoint(
                    GetCell(line.Line.StartPoint.X, area.MinPosition.X, area.CellSize),
                    GetCell(line.Line.StartPoint.Y, area.MinPosition.Y, area.CellSize));

                var endCell = new IntPoint(
                    GetCell(line.Line.EndPoint.X, area.MinPosition.X, area.CellSize),
                    GetCell(line.Line.EndPoint.Y, area.MinPosition.Y, area.CellSize));

                var offsetStartPoint = line.Line.StartPoint - new PointModel(area.MinPosition.X, area.MinPosition.Y);

                var stepX = MathUtilities.Approximately(ray.X, 0) ? 0 : ray.X > 0 ? 1 : -1;
                var stepY = MathUtilities.Approximately(ray.Y, 0) ? 0 : ray.Y > 0 ? 1 : -1;

                // Distance to the nearest square side.
                var nextBoundaryX = stepX >= 0
                    ? (currentCell.X + 1) * area.CellSize - offsetStartPoint.X 
                    : offsetStartPoint.X - (currentCell.X * area.CellSize);

                var nextBoundaryY = stepY >= 0
                    ? (currentCell.Y + 1) * area.CellSize - offsetStartPoint.Y
                    : offsetStartPoint.Y - (currentCell.Y * area.CellSize);

                // How far along the ray we must move to cross the first vertical or horizontal grid line.
                var rayStepToVerticalCellSide = MathUtilities.Approximately(ray.X, 0) 
                    ? double.PositiveInfinity
                    : nextBoundaryX / ray.X;

                var rayStepToHorizontalCellSide = MathUtilities.Approximately(ray.Y, 0)
                    ? double.PositiveInfinity
                    : nextBoundaryY / ray.Y;

                // How far along the ray we must move for horizontal or vertical component of such movement to equal the cell size.
                var dx = MathUtilities.Approximately(ray.X, 0)
                    ? double.PositiveInfinity 
                    : area.CellSize / ray.X;

                var dy = MathUtilities.Approximately(ray.Y, 0)
                    ? double.PositiveInfinity
                    : area.CellSize / ray.Y;

                area.Grid[currentCell.Y][currentCell.X].Lines.Add(line);

                while (!(currentCell.X == endCell.X && currentCell.Y == endCell.Y))
                {
                    if (Math.Abs(rayStepToVerticalCellSide) < Math.Abs(rayStepToHorizontalCellSide))
                    {
                        rayStepToVerticalCellSide += dx; // To the next vertical grid line.
                        currentCell.X += stepX;
                    }
                    else
                    {
                        rayStepToHorizontalCellSide += dy; // To the next horizontal grid line.
                        currentCell.Y += stepY;
                    }

                    area.Grid[currentCell.Y][currentCell.X].Lines.Add(line);
                }
            }

            foreach (var rectangle in dataToPrepare.Rectangles)
            {
                for (int cellY = GetCell(rectangle.Rectangle.Bottom, area.MinPosition.Y, area.CellSize),
                    maxCellY = GetCell(rectangle.Rectangle.Top, area.MinPosition.Y, area.CellSize);
                    cellY <= maxCellY;
                    ++cellY)
                {
                    for (int cellX = GetCell(rectangle.Rectangle.Left, area.MinPosition.X, area.CellSize),
                        maxCellX = GetCell(rectangle.Rectangle.Right, area.MinPosition.X, area.CellSize);
                        cellX <= maxCellX;
                        ++cellX)
                    {
                        area.Grid[cellY][cellX].Lines.AddRange(GeometryUtilities.ConvertRectangleToLines(rectangle.Rectangle).Select(
                            l => new CastLineData { Line = l, Data = rectangle.Data }));
                        area.Grid[cellY][cellX].Rectangles.Add(rectangle);
                    }
                }
            }
        }

        public static CastArea ExtendArea(CastDataToPrepare dataToPrepare, CastArea sourceArea)
        {
            // TODO Resize grid based on new data.

            var area = new CastArea();

            area.CellSize = sourceArea.CellSize;
            area.MinPosition = new PointModel(sourceArea.MinPosition.X, sourceArea.MinPosition.Y);

            area.Grid = new CastArea.Cell[sourceArea.Grid.Length][];
            for (int i = 0; i < area.Grid.Length; ++i)
                area.Grid[i] = new CastArea.Cell[sourceArea.Grid[0].Length];

            for (int y = 0; y < area.Grid.Length; ++y)
                for (int x = 0; x < area.Grid[0].Length; ++x)
                    area.Grid[y][x] = new CastArea.Cell { Lines = new List<CastLineData>(), Rectangles = new List<CastRectangleData>() };

            for (int y = 0; y < area.Grid.Length; ++y)
            {
                for (int x = 0; x < area.Grid[0].Length; ++x)
                {
                    area.Grid[y][x].Lines = sourceArea.Grid[y][x].Lines.Select(l => new CastLineData{  Line = l.Line, Data = l.Data}).ToList();
                    area.Grid[y][x].Rectangles = sourceArea.Grid[y][x].Rectangles.Select(l => new CastRectangleData() { Rectangle = l.Rectangle, Data = l.Data }).ToList();
                }
            }

            PrepareArea(dataToPrepare, area);

            return area;
        }

        private static int GetCell(double current, double min, double cellSize)
        {
            return (int)Math.Truncate((current - min) / cellSize);
        }
    }
}
