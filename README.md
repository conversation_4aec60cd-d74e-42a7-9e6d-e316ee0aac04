## Create Release
1. Increase the plugin version "_version" property in Build.cs in the Build project
2. Merge the release branch to master
3. Build msi installer:
    - Place the certificate in *Signature\Anguleris_Technologies_LLC.pfx*
    - Run the "nuke" PowerShell command in the root of the solution
    - After the build process completes, the new installer will be saved in the *Output* folder
4. Create a tag that corresponds to the plugin version in the format "v1.1.1.0"
5. Upload the MSI installer to an Azure blob
6. Post a release message with notes to the Slack channel

### Required Tools
- Nuke installed globally
- WixSharp does not need to be installed as the necessary libraries are already included in the installer project
- Signtool *C:\Program Files (x86)\Microsoft SDKs\ClickOnce\SignTool\signtool.exe* from the Microsoft SDK
