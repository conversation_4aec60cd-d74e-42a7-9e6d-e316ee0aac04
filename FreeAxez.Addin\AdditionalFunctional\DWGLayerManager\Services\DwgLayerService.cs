using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Media;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Api;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;
using Newtonsoft.Json;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;

public class DwgLayerService
{
    private readonly IAutoCADInvoker _autoCADInvoker;

    public DwgLayerService(IAutoCADInvoker autoCADInvoker)
    {
        _autoCADInvoker = autoCADInvoker;
    }

    public async Task<List<DwgLayerInfo>> ExtractLayersFromDwgAsync(string dwgFilePath)
    {
        if (!File.Exists(dwgFilePath))
            throw new FileNotFoundException($"DWG file not found: {dwgFilePath}");

        var tempFile = Path.GetTempFileName();
        try
        {
            await _autoCADInvoker.ExtractLayersAsync(dwgFilePath, tempFile);
            var json = File.ReadAllText(tempFile);
            var layerInfos = JsonConvert.DeserializeObject<List<AutoCADLayerInfo>>(json);
            return layerInfos?.Select(ConvertFromAutoCADLayerInfo).ToList() ?? new List<DwgLayerInfo>();
        }
        finally
        {
            try
            {
                if (File.Exists(tempFile)) File.Delete(tempFile);
            }
            catch
            {
                // Ignore temp file deletion errors - they don't affect functionality
            }
        }
    }

    public async Task<bool> UpdateLayersFromBackendAsync(string dwgFilePath, object updateRequest)
    {
        if (!File.Exists(dwgFilePath))
            throw new FileNotFoundException($"DWG file not found: {dwgFilePath}");

        var tempUpdateFile = Path.GetTempFileName();
        try
        {
            var json = JsonConvert.SerializeObject(updateRequest, Formatting.Indented);
            File.WriteAllText(tempUpdateFile, json);
            return await _autoCADInvoker.ReplaceLayersAsync(dwgFilePath, tempUpdateFile);
        }
        finally
        {
            try
            {
                if (File.Exists(tempUpdateFile)) File.Delete(tempUpdateFile);
            }
            catch
            {
                // Ignore temp file deletion errors - they don't affect functionality
            }
        }
    }

    public async Task<bool> MergeLayersAsync(string dwgFilePath, MergeLayersRequest request)
    {
        if (!File.Exists(dwgFilePath))
            throw new FileNotFoundException($"DWG file not found: {dwgFilePath}");

        var tempMergeFile = Path.GetTempFileName();
        try
        {
            var json = JsonConvert.SerializeObject(request, Formatting.Indented);
            File.WriteAllText(tempMergeFile, json);

            return await _autoCADInvoker.MergeLayersAsync(dwgFilePath, tempMergeFile);
        }
        finally
        {
            try
            {
                if (File.Exists(tempMergeFile)) File.Delete(tempMergeFile);
            }
            catch
            {
                // Ignore temp file deletion errors - they don't affect functionality
            }
        }
    }

    public async Task<bool> DeleteEmptyLayersAsync(string dwgFilePath)
    {
        if (!File.Exists(dwgFilePath))
        {
            throw new FileNotFoundException($"DWG file not found: {dwgFilePath}");
        }
        var result = await _autoCADInvoker.DeleteEmptyLayersAsync(dwgFilePath);

        return result;
    }

    private DwgLayerInfo ConvertFromAutoCADLayerInfo(AutoCADLayerInfo autocadInfo)
    {
        return new DwgLayerInfo
        {
            Name = autocadInfo.Name,
            Color = ParseColorFromString(autocadInfo.Color),
            Linetype = autocadInfo.Linetype,
            LinetypeDescription = autocadInfo.LinetypeDescription,
            Lineweight = autocadInfo.Lineweight,
            Transparency = autocadInfo.Transparency,
            LineCount = autocadInfo.LineCount,
            PolylineCount = autocadInfo.PolylineCount,
            TotalObjects = autocadInfo.TotalObjects,

            // New detailed object counts
            ModelSpaceObjects = autocadInfo.ModelSpaceObjects,
            PaperSpaceObjects = autocadInfo.PaperSpaceObjects,
            BlockDefinitionObjects = autocadInfo.BlockDefinitionObjects,
            Purgeable = autocadInfo.Purgeable
        };
    }

    private Color ParseColorFromString(string colorString)
    {
        try
        {
            // Parse AutoCAD Color format: "Color [A=255, R=255, G=255, B=0]"
            if (colorString.StartsWith("Color ["))
            {
                var match = Regex.Match(colorString, @"R=(\d+), G=(\d+), B=(\d+)");
                if (match.Success)
                {
                    var r = byte.Parse(match.Groups[1].Value);
                    var g = byte.Parse(match.Groups[2].Value);
                    var b = byte.Parse(match.Groups[3].Value);
                    var result = Color.FromRgb(r, g, b);
                    return result;
                }
            }

            // Try to parse as hex color (#AARRGGBB or #RRGGBB)
            if (colorString.StartsWith("#"))
            {
                if (colorString.Length == 9) // #AARRGGBB
                {
                    var r = Convert.ToByte(colorString.Substring(3, 2), 16);
                    var g = Convert.ToByte(colorString.Substring(5, 2), 16);
                    var b = Convert.ToByte(colorString.Substring(7, 2), 16);
                    var result = Color.FromRgb(r, g, b);
                    return result;
                }

                if (colorString.Length == 7) // #RRGGBB
                {
                    var r = Convert.ToByte(colorString.Substring(1, 2), 16);
                    var g = Convert.ToByte(colorString.Substring(3, 2), 16);
                    var b = Convert.ToByte(colorString.Substring(5, 2), 16);
                    var result = Color.FromRgb(r, g, b);
                    return result;
                }
            }

            // Try to parse as RGB values
            if (colorString.Contains(","))
            {
                var parts = colorString.Split(',');
                if (parts.Length >= 3)
                {
                    var r = byte.Parse(parts[0].Trim());
                    var g = byte.Parse(parts[1].Trim());
                    var b = byte.Parse(parts[2].Trim());
                    var result = Color.FromRgb(r, g, b);
                    return result;
                }
            }

            return Color.FromRgb(0, 0, 0); // Black to indicate parsing failure
        }
        catch (Exception ex)
        {
            return Color.FromRgb(0, 0, 0); // Black to indicate parsing failure
        }
    }
}

// Model for AutoCAD layer info JSON
public class AutoCADLayerInfo
{
    public string Name { get; set; }
    public string Color { get; set; }
    public string Linetype { get; set; }
    public string LinetypeDescription { get; set; }
    public double Lineweight { get; set; }
    public int Transparency { get; set; }
    public int LineCount { get; set; }
    public int PolylineCount { get; set; }
    public int TotalObjects { get; set; }

    // New detailed object counts
    public int ModelSpaceObjects { get; set; }
    public int PaperSpaceObjects { get; set; }
    public int BlockDefinitionObjects { get; set; }
    public bool Purgeable { get; set; }
}