using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Api;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;

public class LayerMappingOperations
{
    private readonly DwgLayerService _dwgLayerService;
    private readonly DwgLayerManagerApiService _apiService;

    public LayerMappingOperations(DwgLayerService dwgLayerService, DwgLayerManagerApiService apiService)
    {
        _dwgLayerService = dwgLayerService ?? throw new ArgumentNullException(nameof(dwgLayerService));
        _apiService = apiService ?? throw new ArgumentNullException(nameof(apiService));
    }

    public async Task<bool> ExecuteMappingAsync(
        string dwgFilePath,
        IEnumerable<DwgLayerInfo> dwgLayers,
        IEnumerable<LayerModel> freeAxezLayers,
        List<LinetypeModel> linetypes,
        IProgress<string> progress = null)
    {
        try
        {
            progress?.Report("Preparing merge data...");

            var mergeRequest = await GenerateMergeRequest(dwgLayers, freeAxezLayers, linetypes, progress);
            if (mergeRequest == null)
            {
                return false;
            }

            return await ExecuteActualMerge(dwgFilePath, mergeRequest, progress);
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog("Error", $"Failed to execute mapping: {ex.Message}", MessageType.Error);
            return false;
        }
    }

    private async Task<MergeLayersRequest> GenerateMergeRequest(
        IEnumerable<DwgLayerInfo> dwgLayers,
        IEnumerable<LayerModel> freeAxezLayers,
        List<LinetypeModel> linetypes,
        IProgress<string> progress = null)
    {
        progress?.Report("Analyzing layer mappings...");

        var mappedLayers = dwgLayers.Where(l => l.MappedLayerId.HasValue).ToList();
        if (!mappedLayers.Any())
        {
            MessageWindow.ShowDialog("Warning", "No layers are mapped to FreeAxez layers.", MessageType.Warning);
            return null;
        }

        var request = new MergeLayersRequest { DryRun = false };

        // 1. Generate mappings
        progress?.Report("Creating layer mappings...");

        foreach (var dwgLayer in mappedLayers)
        {
            var freeAxezLayer = freeAxezLayers.FirstOrDefault(fl => fl.Id == dwgLayer.MappedLayerId.Value);
            if (freeAxezLayer != null)
                request.Mappings.Add(new LayerMapping
                {
                    SourceLayer = dwgLayer.Name,
                    TargetLayer = freeAxezLayer.Name
                });
        }

        // 2. Generate unique FreeAxez layers (no duplicates)
        progress?.Report("Processing FreeAxez layers...");

        var uniqueFreeAxezLayers = request.Mappings
            .Select(m => freeAxezLayers.FirstOrDefault(fl => fl.Name == m.TargetLayer))
            .Where(fl => fl != null)
            .GroupBy(fl => fl.Name)
            .Select(g => g.First())
            .ToList();

        foreach (var layer in uniqueFreeAxezLayers)
            request.FreeAxezLayers.Add(new FreeAxezLayerDto
            {
                Name = layer.Name,
                Color = $"#{layer.Color.R:X2}{layer.Color.G:X2}{layer.Color.B:X2}",
                Linetype = layer.LinetypeName ?? "Continuous",
                Lineweight = layer.Lineweight01mm == -3 ? 0 : layer.Lineweight01mm / 100.0,
                Transparency = layer.TransparencyPct == 0 ? -1 : layer.TransparencyPct
            });

        // 3. Generate unique linetypes (no duplicates)
        progress?.Report("Collecting linetypes...");

        var uniqueLinetypes = request.FreeAxezLayers
            .Select(fl => fl.Linetype)
            .Distinct()
            .ToList();

        foreach (var linetypeName in uniqueLinetypes)
        {
            var linetype = linetypes.FirstOrDefault(lt => lt.Name == linetypeName);
            if (linetype != null)
                request.Linetypes.Add(new LinetypeDto
                {
                    Id = linetype.Id,
                    Name = linetype.Name,
                    Description = linetype.Description,
                    PatternRaw = linetype.PatternRaw,
                    UpdatedUtc = linetype.UpdatedUtc
                });
        }

        return request;
    }

    private async Task<bool> ExecuteActualMerge(string dwgFilePath, MergeLayersRequest request, IProgress<string> progress = null)
    {
        progress?.Report("Starting merge process...");

        var success = await _dwgLayerService.MergeLayersAsync(dwgFilePath, request);

        if (success)
        {
            progress?.Report("Merge completed successfully");
            return true;
        }
        else
        {
            MessageWindow.ShowDialog("Error", "Failed to merge layers", MessageType.Error);
            return false;
        }
    }
}
