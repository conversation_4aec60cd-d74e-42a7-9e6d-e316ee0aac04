﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.UserAccountControl.View.ResetPasswordView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             xmlns:viewModel="clr-namespace:FreeAxez.Addin.AdditionalFunctional.UserAccountControl.ViewModel"
             mc:Ignorable="d" 
             Title ="Reset Password"
             SizeToContent="Height"
             WindowStartupLocation="CenterScreen"
             Width="300">
    <Window.Resources>
        <ResourceDictionary>
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.DataContext>
        <viewModel:ResetPasswordVm/>
    </Window.DataContext>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0">
                <TextBlock Style="{StaticResource TextBase}" 
                           Text="Email" 
                           Foreground="Black"
                           Margin="0 10 0 5">
                </TextBlock>
                <TextBox Style="{StaticResource UiTextBox}" 
                         Tag="Enter Your Email To Reset Password" 
                         Name="EmailTextBox" 
                         VerticalContentAlignment="Center"
                         Margin="0,0,0,10" 
                         Text="{Binding UserEmail, UpdateSourceTrigger=PropertyChanged}" 
                         />
                <TextBlock Text="{Binding Error}" 
                           Foreground="Red" 
                           Visibility="{Binding Error, Converter={StaticResource StringToVisibilityConverter}}" 
                           Margin="0 10"/>
                <ProgressBar Height="8" 
                             Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}" 
                             IsIndeterminate="True" 
                             Margin="0 10"/>
            </StackPanel>
            <StackPanel Grid.Row="1"  
                        Orientation="Horizontal" 
                        HorizontalAlignment="Right">
                <Button Content="Reset password" 
                        Command="{Binding ResetPasswordCommand}" 
                        Style="{StaticResource ButtonSimpleBlue}" 
                        IsEnabled="{Binding CanApply}"
                        CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"
                        />
            </StackPanel>
    </Grid>
</Window>
