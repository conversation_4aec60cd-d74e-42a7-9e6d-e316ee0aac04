using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;
using Microsoft.Win32;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;

public class AutoCADVersionService
{
    private const int MinSupportedVersion = 2022;
    private const int MaxSupportedVersion = 2026;
    
    private List<AutoCADInstallation> _cachedInstallations;

    public List<AutoCADInstallation> GetInstalledVersions()
    {
        if (_cachedInstallations != null)
            return _cachedInstallations;

        _cachedInstallations = ScanForAutoCADInstallations();
        return _cachedInstallations;
    }

    public bool IsAutoCADAvailable()
    {
        return GetInstalledVersions().Any();
    }

    public AutoCADInstallation GetInstallation(int version)
    {
        return GetInstalledVersions().FirstOrDefault(x => x.Version == version);
    }

    public string GetCoreConsolePath(int version)
    {
        var installation = GetInstallation(version);
        return installation?.CoreConsolePath;
    }

    public AutoCADInstallation GetHighestVersion()
    {
        return GetInstalledVersions()
            .OrderByDescending(x => x.Version)
            .FirstOrDefault();
    }

    public List<int> GetAvailableVersionNumbers()
    {
        return GetInstalledVersions()
            .Select(x => x.Version)
            .OrderByDescending(x => x)
            .ToList();
    }

    private List<AutoCADInstallation> ScanForAutoCADInstallations()
    {
        var installations = new List<AutoCADInstallation>();

        try
        {
            var registryKey = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Autodesk\AutoCAD");
            if (registryKey == null)
                return installations;

            var versionKeys = registryKey.GetSubKeyNames()
                .Where(name => name.StartsWith("R", StringComparison.OrdinalIgnoreCase))
                .ToList();

            foreach (var versionKey in versionKeys)
            {
                var installation = TryCreateInstallation(registryKey, versionKey);
                if (installation != null && installation.IsValid)
                {
                    installations.Add(installation);
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error scanning AutoCAD installations: {ex.Message}");
        }

        return installations.Distinct().ToList();
    }

    private int ExtractVersionFromPath(string path)
    {
        try
        {
            // Look for 4-digit year in the path (2022, 2023, 2024, 2025, 2026)
            var match = Regex.Match(path, @"20(2[2-6])");
            if (match.Success)
            {
                var year = int.Parse(match.Value);
                if (year >= MinSupportedVersion && year <= MaxSupportedVersion)
                    return year;
            }

            return 0; // No supported version found in path
        }
        catch
        {
            return 0; // Error parsing
        }
    }

    private AutoCADInstallation TryCreateInstallation(RegistryKey registryKey, string versionKey)
    {
        try
        {
            var versionRegistryKey = registryKey.OpenSubKey(versionKey);
            if (versionRegistryKey == null)
                return null;

            foreach (var subKeyName in versionRegistryKey.GetSubKeyNames())
            {
                var subKey = versionRegistryKey.OpenSubKey(subKeyName);
                if (subKey != null)
                {
                    var installPath = subKey.GetValue("AcadLocation")?.ToString();
                    if (!string.IsNullOrEmpty(installPath))
                    {
                        var coreConsolePath = Path.Combine(installPath, "accoreconsole.exe");
                        if (File.Exists(coreConsolePath))
                        {
                            // Extract version from the actual path, not registry key
                            var actualVersion = ExtractVersionFromPath(coreConsolePath);
                            if (actualVersion >= MinSupportedVersion && actualVersion <= MaxSupportedVersion)
                            {
                                return new AutoCADInstallation
                                {
                                    Version = actualVersion,
                                    InstallPath = installPath,
                                    CoreConsolePath = coreConsolePath
                                };
                            }
                        }
                    }
                }
            }
        }
        catch
        {
            // Ignore errors for individual installations
        }

        return null;
    }

    public void RefreshInstallations()
    {
        _cachedInstallations = null;
    }
}
