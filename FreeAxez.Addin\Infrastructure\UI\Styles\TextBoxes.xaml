﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="ColorsPalette.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="UiTextBox"
           TargetType="{x:Type TextBox}"
           BasedOn="{StaticResource {x:Type TextBox}}">
        <Setter Property="Height"
                Value="32" />
        <Setter Property="Width"
                Value="Auto" />
        <Setter Property="FontSize"
                Value="13" />
        <Setter Property="Foreground"
                Value="{StaticResource Gray700}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBox}">
                    <Grid>
                        <Border CornerRadius="8"
                                x:Name="textBorder"
                                Background="{StaticResource Gray25}"
                                BorderBrush="{StaticResource Gray200}"
                                BorderThickness="1">
                            <Grid>
                                <TextBox Margin="5 0"
                                         VerticalAlignment="Center"
                                         TextWrapping="Wrap"
                                         Text="{Binding Path=Text,
                                    RelativeSource={RelativeSource TemplatedParent},
                                    Mode=TwoWay,
                                    UpdateSourceTrigger=PropertyChanged}"
                                         x:Name="textBoxText"
                                         Background="Transparent"
                                         Panel.ZIndex="2"
                                         BorderThickness="0" />
                                <TextBlock Margin="8 0"
                                           VerticalAlignment="Center"
                                           Text="{TemplateBinding Tag}"
                                           Foreground="{StaticResource Gray600}"
                                           IsHitTestVisible="False">
                                    <TextBlock.Style>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="Visibility"
                                                    Value="Collapsed"></Setter>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Text, ElementName = textBoxText}"
                                                             Value="">
                                                    <Setter Property="Visibility"
                                                            Value="Visible"></Setter>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </Grid>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter TargetName="textBorder"
                                    Property="BorderBrush"
                                    Value="{StaticResource Gray300}" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocusWithin"
                                 Value="True">
                            <Setter TargetName="textBorder"
                                    Property="BorderBrush"
                                    Value="{StaticResource Gray400}" />
                            <Setter TargetName="textBorder"
                                    Property="BorderThickness"
                                    Value="2" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="PasswordBox"
           TargetType="{x:Type PasswordBox}">
        <Setter Property="Height"
                Value="30" />
        <Setter Property="Width"
                Value="Auto" />
        <Setter Property="FontSize"
                Value="13" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type PasswordBox}">
                    <Grid>
                        <Border CornerRadius="5"
                                Background="{StaticResource Gray25}"
                                BorderBrush="{StaticResource Gray100}"
                                BorderThickness="1"
                                x:Name="pBorder">
                            <ScrollViewer x:Name="PART_ContentHost"
                                          Margin="5 0"
                                          Focusable="False"
                                          VerticalAlignment="Center" />
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="Search"
           TargetType="{x:Type TextBox}"
           BasedOn="{StaticResource {x:Type TextBox}}">
        <Setter Property="Height"
                Value="32" />
        <Setter Property="Width"
                Value="Auto" />
        <Setter Property="FontSize"
                Value="13" />
        <Setter Property="Foreground"
                Value="{StaticResource Gray700}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBox}">
                    <Grid>
                        <Border CornerRadius="8"
                                x:Name="Border"
                                Background="{StaticResource Gray25}"
                                BorderBrush="{StaticResource Gray200}"
                                BorderThickness="1">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="30" />
                                    <ColumnDefinition />
                                </Grid.ColumnDefinitions>
                                <Border Grid.Column="0">
                                    <Viewbox Width="18"
                                             Height="18">
                                        <Canvas Width="24"
                                                Height="24">
                                            <Ellipse x:Name="SearchEllipse"
                                                     Stroke="{StaticResource Gray400}"
                                                     StrokeThickness="2"
                                                     Width="16"
                                                     Height="16"
                                                     Canvas.Left="2.5"
                                                     Canvas.Top="2.5" />
                                            <Line x:Name="SearchLine"
                                                  Stroke="{StaticResource Gray400}"
                                                  StrokeThickness="2"
                                                  X1="16.65"
                                                  Y1="16.65"
                                                  X2="21"
                                                  Y2="21"
                                                  StrokeLineJoin="Round" />
                                        </Canvas>
                                    </Viewbox>
                                </Border>
                                <TextBox Grid.Column="1"
                                         Margin="5 0"
                                         VerticalAlignment="Center"
                                         Text="{Binding Path=Text,
                                        RelativeSource={RelativeSource TemplatedParent},
                                        Mode=TwoWay,
                                        UpdateSourceTrigger=PropertyChanged}"
                                         x:Name="searchText"
                                         Background="Transparent"
                                         Panel.ZIndex="2"
                                         BorderThickness="0" />
                                <TextBlock x:Name="PlaceholderTextBlock"
                                           Grid.Column="1"
                                           Margin="8 0"
                                           VerticalAlignment="Center"
                                           Text="{TemplateBinding Tag}"
                                           Foreground="{StaticResource Gray600}"
                                           IsHitTestVisible="False">
                                    <TextBlock.Style>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="Visibility"
                                                    Value="Collapsed"></Setter>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Text, ElementName = searchText}"
                                                             Value="">
                                                    <Setter Property="Visibility"
                                                            Value="Visible"></Setter>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </Grid>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter TargetName="Border"
                                    Property="BorderBrush"
                                    Value="{StaticResource Gray300}" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocusWithin"
                                 Value="True">
                            <Setter TargetName="Border"
                                    Property="BorderBrush"
                                    Value="{StaticResource Gray400}" />
                            <Setter TargetName="Border"
                                    Property="BorderThickness"
                                    Value="2" />
                            <Setter TargetName="SearchEllipse"
                                    Property="Stroke"
                                    Value="{StaticResource Gray500}" />
                            <Setter TargetName="SearchLine"
                                    Property="Stroke"
                                    Value="{StaticResource Gray500}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="UiTextBoxWhite"
           TargetType="{x:Type TextBox}"
           BasedOn="{StaticResource {x:Type TextBox}}">
        <Setter Property="Height"
                Value="30" />
        <Setter Property="Width"
                Value="Auto" />
        <Setter Property="FontSize"
                Value="13" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBox}">
                    <Grid>
                        <Border CornerRadius="5"
                                x:Name="textBorder"
                                Background="White"
                                BorderBrush="{StaticResource Gray300}"
                                BorderThickness="1">
                            <Grid>
                                <TextBox Margin="5 0"
                                         VerticalAlignment="Center"
                                         TextWrapping="Wrap"
                                         Text="{Binding Path=Text,
                                    RelativeSource={RelativeSource TemplatedParent},
                                    Mode=TwoWay,
                                    UpdateSourceTrigger=PropertyChanged}"
                                         x:Name="textBoxText"
                                         Background="Transparent"
                                         Panel.ZIndex="2"
                                         BorderThickness="0" />
                                <TextBlock Margin="8 0"
                                           VerticalAlignment="Center"
                                           Text="{TemplateBinding Tag}"
                                           Foreground="{StaticResource Gray600}"
                                           IsHitTestVisible="False">
                                    <TextBlock.Style>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="Visibility"
                                                    Value="Collapsed"></Setter>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Text, ElementName = textBoxText}"
                                                             Value="">
                                                    <Setter Property="Visibility"
                                                            Value="Visible"></Setter>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </Grid>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter TargetName="textBorder"
                                    Property="BorderBrush"
                                    Value="{StaticResource Gray600}" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocusWithin"
                                 Value="True">
                            <Setter TargetName="textBorder"
                                    Property="BorderBrush"
                                    Value="{StaticResource Gray600}" />
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                 Value="False">
                            <Setter TargetName="textBorder"
                                    Property="Background"
                                    Value="{StaticResource Gray25}" />
                            <Setter Property="Foreground"
                                    Value="{StaticResource Gray400}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
