﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.CutUnitByVoid.View.CutUnitByVoidView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        WindowStartupLocation="CenterScreen"
        Topmost="True"
        Title="Cut Elements"
        ResizeMode="NoResize"
        SizeToContent="WidthAndHeight">
    <Grid Margin="10">
        <StackPanel>
            <TextBlock Text="Warning!" HorizontalAlignment="Center"/>
            <TextBlock/>
            <TextBlock HorizontalAlignment="Center" Text="Due to the large number of elements, the cutting operation may take some time."/>
            <TextBlock HorizontalAlignment="Center" Text="It is recommended to save the project before running the plugin."/>
            <StackPanel Margin="0,20,0,0" Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Margin="0,0,10,0" Content="Select Cutting Instance" Height="25" Width="150" Click="SelectCuttingInstance_Click"/>
                <Button Content="Cancel" Height="25" Width="150" Click="Cancel_Click"/>
            </StackPanel>
        </StackPanel>
    </Grid>
</Window>
