﻿<Window x:Class="FreeAxez.Addin.Infrastructure.UI.Views.MessageView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:viewModels="clr-namespace:FreeAxez.Addin.Infrastructure.UI.ViewModels"
        mc:Ignorable="d"
        Title=""
        SizeToContent="Height"
        ResizeMode="NoResize"
        Width="400"
        WindowStartupLocation="CenterScreen">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.DataContext>
        <viewModels:MessageViewModel Title="Info" Message="Info Message" DefaultMessageType="Info"/>
    </Window.DataContext>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Grid>
        <Grid>
            <StackPanel>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0 5 0 20">
                    <ContentControl Template="{StaticResource WarningIcon}" Visibility="{Binding WarningVisibility}"/>
                    <ContentControl Template="{StaticResource InfoIcon}" Visibility="{Binding InfoVisibility}"/>
                    <ContentControl Template="{StaticResource SuccessIcon}" Visibility="{Binding SuccessVisibility}"/>
                    <ContentControl Template="{StaticResource ErrorIcon}" Visibility="{Binding ErrorVisibility}"/>
                    <ContentControl Template="{StaticResource NotifyIcon}" Visibility="{Binding NotifyVisibility}"/>
                    <TextBlock Text="{Binding Title}"
                            VerticalAlignment="Center"
                            Margin="10 0"
                            FontSize="13" 
                            FontWeight="Bold"/>
                </StackPanel>
                <ScrollViewer VerticalScrollBarVisibility="Auto" 
                           HorizontalScrollBarVisibility="Disabled" 
                           Margin="0 0 0 20"
                           MaxHeight="500">
                    <TextBox Text="{Binding Message}" 
                            FontSize="13" 
                            IsReadOnly="True"
                            BorderThickness="0"
                            Background="Transparent"
                            TextWrapping="Wrap"/>
                </ScrollViewer >
                <Grid Visibility="{Binding WarningVisibility}">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Button Grid.Column="0" Content="Cancel" Style="{StaticResource ButtonSimpleLight}" Command="{Binding CancelDialogCommand}" CommandParameter="{Binding RelativeSource={RelativeSource AncestorType={x:Type Window}}}"/>
                    <Button Grid.Column="2" Content="Agree" Style="{StaticResource ButtonSimpleYellow}" Command="{Binding OkDialogCommand}" CommandParameter="{Binding RelativeSource={RelativeSource AncestorType={x:Type Window}}}"/>
                </Grid>
                <Button Content="Ok" 
                     Style="{StaticResource ButtonSimpleBlue}" 
                     Command="{Binding OkDialogCommand}"
                     CommandParameter="{Binding RelativeSource={RelativeSource AncestorType={x:Type Window}}}"
                     Visibility="{Binding InfoVisibility}"/>
                <Button Content="Ok" 
                     Style="{StaticResource ButtonSimpleGreen}" 
                     Command="{Binding OkDialogCommand}"
                     CommandParameter="{Binding RelativeSource={RelativeSource AncestorType={x:Type Window}}}"
                     Visibility="{Binding SuccessVisibility}"/>
                <Button Content="Ok" 
                     Style="{StaticResource ButtonSimpleRed}"
                     Command="{Binding OkDialogCommand}"
                     CommandParameter="{Binding RelativeSource={RelativeSource AncestorType={x:Type Window}}}"
                     Visibility="{Binding ErrorVisibility}"/>
                <Button Content="Ok" 
                        Style="{StaticResource ButtonSimplePurple}"
                        Command="{Binding OkDialogCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType={x:Type Window}}}"
                        Visibility="{Binding NotifyVisibility}"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
