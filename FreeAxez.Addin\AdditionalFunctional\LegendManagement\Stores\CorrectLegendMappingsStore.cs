﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.LegendManagement.Models;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.LegendManagement.Stores
{
    public class CorrectLegendMappingsStore
    {
        private const string SheetSize11x17 = "11x17";
        private const string SheetSize24x36 = "24x36";
        private const string SheetSize30x42 = "30x42";
        private const string SheetSize36x48 = "36x48";

        private const string Cover = "COVER";
        private const string GriddInternal = "01 - GRIDD INTERNAL";
        private const string PowerInternal = "02 - POWER INTERNAL";
        private const string PathwayInternal = "03 - PATHWAY INTERNAL";
        private const string MacInternal = "04 - MAC INTERNAL";
        private const string GriddFloorPlan = "01 - GRIDD FLOOR PLAN";
        private const string GriddAreaPlan = "02 - GRIDD AREA PLAN";
        private const string GriddPreliminaryPlan = "03 - GRIDD PRELIMINARY PLAN";
        private const string GriddInstallPlan = "04 - GRIDD INSTALL PLAN";
        private const string GriddDetails = "05 - GRIDD DETAILS";
        private const string GriddCurbFrame = "06 - GRIDD CURB & FRAME";
        private const string GriddStagingPlan = "07 - GRIDD STAGING PLAN";
        private const string GriddInstallZone = "08 - GRIDD INSTALL ZONE";
        private const string GriddReinforcedPlan = "10 - GRIDD REINFORCED PLAN";
        private const string PowerPreliminaryPlan = "01 - POWER PRELIMINARY PLAN";
        private const string PowerPlan = "02 - POWER PLAN";
        private const string PowerInstallPlan = "03 - POWER INSTALL PLAN";
        private const string PowerDetails = "04 - POWER DETAILS";
        private const string TrackSchedules = "05 - TRACK SCHEDULES";
        private const string PathwayZone = "01 - PATHWAY ZONE";
        private const string PathwayCableDropPlan = "02 - PATHWAY CABLE DROP PLAN";
        private const string PathwayFieldWired = "03 - PATHWAY FIELD WIRED";
        private const string PathwayLowVoltagePlan = "04 - PATHWAY LOW VOLTAGE PLAN";
        private const string PathwayPowerAndLowVoltagePlan = "05 - PATHWAY POWER AND LOW VOLTAGE PLAN";
        private const string MacRemove = "01 - MAC REMOVE";
        private const string MacRelocateSameTrack = "02 - MAC RELOCATE - SAME TRACK";
        private const string MacRelocateDifferentTrack = "03 - MAC RELOCATE - DIFFERENT TRACK";
        private const string MacNew = "04 - MAC NEW";
        private const string MacFinish = "05 - MAC FINISH";

        private long _invalidElementIdIntegerValue = ElementId.InvalidElementId.GetIntegerValue();


        public CorrectLegendMappingsStore()
        {
            Dictionary = Initialize();
        }

        public Dictionary<CompositeSheetKey, List<Legend>> Dictionary { get; }

        private Dictionary<CompositeSheetKey, List<Legend>> Initialize()
        {
            return Initialize11x17()
                .Concat(Initialize24x36())
                .Concat(Initialize30x42())
                .Concat(Initialize36x48())
                .ToDictionary(pair => pair.Key, pair => pair.Value);
        }

        private Dictionary<CompositeSheetKey, List<Legend>> Initialize11x17()
        {
            return new Dictionary<CompositeSheetKey, List<Legend>>
            {
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, Cover),
                        new Sheet(_invalidElementIdIntegerValue, "G00-C-11")),
                    new List<Legend>()
                },
                #region 01 - GRIDD INTERNAL
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GRIDD INTERNAL")
                    }
                },
                #endregion
                #region 03 - GRIDD PRELIMINARY PLAN
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - PRELIM INSTALL - RAMP"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - PRELIM INSTALL - RECCESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - PRELIM INSTALL - RAMP"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - PRELIM INSTALL - RECCESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - PRELIM INSTALL - RAMP"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - PRELIM INSTALL - RECCESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - PRELIM INSTALL - RAMP"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - PRELIM INSTALL - RECCESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - PRELIM INSTALL - RAMP"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - PRELIM INSTALL - RECCESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - PRELIM INSTALL - RAMP"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - PRELIM INSTALL - RECCESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - STANDARD")
                    }
                },
                #endregion
                #region 04 - GRIDD INSTALL PLAN
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-01A-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - GRIDD INSTALL PLAN  - RAMP"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - GRIDD INSTLL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-02A-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - GRIDD INSTALL PLAN  - RAMP"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - GRIDD INSTLL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-03A-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - GRIDD INSTALL PLAN  - RAMP"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - GRIDD INSTLL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-04A-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - GRIDD INSTALL PLAN  - RAMP"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - GRIDD INSTLL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-05A-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - GRIDD INSTALL PLAN  - RAMP"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - GRIDD INSTLL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-06A-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - GRIDD INSTALL PLAN  - RAMP"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-I - GRIDD INSTLL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "SMALL - NOTE - G-I - GRIDD INSTALLPLAN - STANDARD")
                    }
                },
                #endregion
                #region 05 - GRIDD DETAILS
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddDetails),
                        new Sheet(_invalidElementIdIntegerValue, "G-D-A")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "GRIDD BORDER COMPONENT CONDITIONS 11x17"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-A BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-A HALF BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-B BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-B HALF BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-C HALF BASE UNIT"),

                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-D BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-E BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-F BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-G BASE UNIT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddDetails),
                        new Sheet(_invalidElementIdIntegerValue, "G-D-B")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "GRIDD 40 RECESSED FLOOR DETAIL - 11"),
                        new(_invalidElementIdIntegerValue, "GRIDD 70 RECESSED FLOOR DETAIL - 11")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddDetails),
                        new Sheet(_invalidElementIdIntegerValue, "G-D-C")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "DETAIL - FULL OUTLET COVER TO REPLACE BASE UNIT AT WALL"),
                        new(_invalidElementIdIntegerValue, "DETAIL - OUTLET COVER TO REPLACE BASE UNIT AT WALL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddDetails),
                        new Sheet(_invalidElementIdIntegerValue, "G-D-D")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "DETAIL - OUTLET COVER TO REPLACE HALF BASE UNIT AT WALL"),
                        new(_invalidElementIdIntegerValue, "DETAIL - WALL OUTLET COVER TO REPLACE HALF UNIT AT WALL")
                    }
                },
                #endregion
                #region 06 - GRIDD CURB & FRAME
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                #endregion
                #region 07 - GRIDD STAGING PLAN
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-S - STAGING - 70R")
                    }
                },
                #endregion
                #region 10 - GRIDD REINFORCED PLAN
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                #endregion
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - SHEET KEYNOTES - FM LEVEL 01")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-I - POWER INSTALL PLAN"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-I - POWER INSTALL TAG LEGEND"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-P - SHEET KEYNOTES - FM LEVEL 01")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - G-P - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-01-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-02-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-03-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-04-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-05-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize11x17),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-06-11")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "SMALL - MAC LEGEND")
                    }
                }
            };
        }

        private Dictionary<CompositeSheetKey, List<Legend>> Initialize24x36()
        {
            return new Dictionary<CompositeSheetKey, List<Legend>>
            {
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, Cover),
                        new Sheet(_invalidElementIdIntegerValue, "G00-C-24")),
                    new List<Legend>()
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-01A-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-02A-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-03A-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-04A-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-05A-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-06A-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddDetails),
                        new Sheet(_invalidElementIdIntegerValue, "G-D-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "GRIDD 40 RECESSED FLOOR DETAIL - 24"),
                        new(_invalidElementIdIntegerValue, "GRIDD 70 RECESSED FLOOR DETAIL - 24"),
                        new(_invalidElementIdIntegerValue, "GRIDD BORDER COMPONENT CONDITIONS 24x36"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-A BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-A HALF BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-B BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-B HALF BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-C HALF BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-D BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-E BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-F BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-G BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "DETAIL - FULL OUTLET COVER TO REPLACE BASE UNIT AT WALL"),
                        new(_invalidElementIdIntegerValue, "DETAIL - OUTLET COVER TO REPLACE BASE UNIT AT WALL"),
                        new(_invalidElementIdIntegerValue, "DETAIL - OUTLET COVER TO REPLACE HALF BASE UNIT AT WALL"),
                        new(_invalidElementIdIntegerValue, "DETAIL - WALL OUTLET COVER TO REPLACE HALF UNIT AT WALL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - SHEET KEYNOTES - FM LEVEL 01")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - SHEET KEYNOTES - FM LEVEL 01")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - SHEET KEYNOTES - FM LEVEL 01")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - SHEET KEYNOTES - FM LEVEL 01")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - SHEET KEYNOTES - FM LEVEL 01")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - SHEET KEYNOTES - FM LEVEL 01")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - SHEET KEYNOTES - FM LEVEL 01")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-01-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-02-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-03-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-04-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-05-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize24x36),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-06-24")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                }
            };
        }

        private Dictionary<CompositeSheetKey, List<Legend>> Initialize30x42()
        {
            return new Dictionary<CompositeSheetKey, List<Legend>>
            {
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, Cover),
                        new Sheet(_invalidElementIdIntegerValue, "G00-C-30")),
                    new List<Legend>()
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-01A-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-02A-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-03A-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-04A-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-05A-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-06A-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddDetails),
                        new Sheet(_invalidElementIdIntegerValue, "G-D-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "GRIDD 40 RECESSED FLOOR DETAIL - 30"),
                        new(_invalidElementIdIntegerValue, "GRIDD 70 RECESSED FLOOR DETAIL - 30"),
                        new(_invalidElementIdIntegerValue, "GRIDD BORDER COMPONENT CONDITIONS 30x42"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-A BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-A HALF BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-B BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-B HALF BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-C HALF BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-D BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-E BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-F BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-G BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "DETAIL - FULL OUTLET COVER TO REPLACE BASE UNIT AT WALL"),
                        new(_invalidElementIdIntegerValue, "DETAIL - OUTLET COVER TO REPLACE BASE UNIT AT WALL"),
                        new(_invalidElementIdIntegerValue, "DETAIL - OUTLET COVER TO REPLACE HALF BASE UNIT AT WALL"),
                        new(_invalidElementIdIntegerValue, "DETAIL - WALL OUTLET COVER TO REPLACE HALF UNIT AT WALL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - SHEET KEYNOTES - FM LEVEL 01")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL TAG LEGEND"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - SHEET KEYNOTES - FM LEVEL 01")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-01-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-02-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-03-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-04-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-05-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize30x42),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-06-30")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                }
            };
        }

        private Dictionary<CompositeSheetKey, List<Legend>> Initialize36x48()
        {
            return new Dictionary<CompositeSheetKey, List<Legend>>
            {
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, Cover),
                        new Sheet(_invalidElementIdIntegerValue, "G00-C-36")),
                    new List<Legend>()
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInternal),
                        new Sheet(_invalidElementIdIntegerValue, "GInternal-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GRIDD INTERNAL")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-P-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL  - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-P - PRELIM INSTALL - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-01A-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-02A-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-03A-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-04A-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-05A-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-I-06A-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - BORDER COMPONENT CONDITIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RAMP"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-I - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - RECESSED FLOOR"),
                        new(_invalidElementIdIntegerValue, "LARGE - NOTE - GI - GRIDD INSTALL PLAN - STANDARD")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddDetails),
                        new Sheet(_invalidElementIdIntegerValue, "G-D-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "GRIDD 40 RECESSED FLOOR DETAIL - 36"),
                        new(_invalidElementIdIntegerValue, "GRIDD 70 RECESSED FLOOR DETAIL - 36"),
                        new(_invalidElementIdIntegerValue, "GRIDD BORDER COMPONENT CONDITIONS 36x48"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-A BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-A HALF BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-B BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-B HALF BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-C HALF BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-D BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-E BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-F BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "AXON DETAIL - OPTION-G BASE UNIT"),
                        new(_invalidElementIdIntegerValue, "DETAIL - FULL OUTLET COVER TO REPLACE BASE UNIT AT WALL"),
                        new(_invalidElementIdIntegerValue, "DETAIL - OUTLET COVER TO REPLACE BASE UNIT AT WALL"),
                        new(_invalidElementIdIntegerValue, "DETAIL - OUTLET COVER TO REPLACE HALF BASE UNIT AT WALL"),
                        new(_invalidElementIdIntegerValue, "DETAIL - WALL OUTLET COVER TO REPLACE HALF UNIT AT WALL"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G - BORDER COMPONENT CONDITIONS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddCurbFrame),
                        new Sheet(_invalidElementIdIntegerValue, "G-CF-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-CF - CURB AND FRAME")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddStagingPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-S-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 40R"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-S - STAGING - 70R")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, GriddReinforcedPlan),
                        new Sheet(_invalidElementIdIntegerValue, "G-R-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - G-R - GRIDD REINFORCMENT")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPreliminaryPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-PR-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-PR - PRELIMINARY POWER PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - SHEET KEYNOTES - FM LEVEL 01")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-P-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - FIELD WIRED POWER"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER 3-PHASE SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - NO FM CABLES BY FREEAXEZ"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - MODULAR POWER DUAL CIRCUIT SYSTEM - STANDARD"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - POWER PLAN - QUICK-CONNECT SPIN LOCK")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-P - SHEET KEYNOTES - FM LEVEL 01")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerInstallPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GP-I-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP - POWER ABBREVIATIONS"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-I - POWER INSTALL PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PowerDetails),
                        new Sheet(_invalidElementIdIntegerValue, "GP-D-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - SHEET NOTES"),
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GP-D - POWER DETAILS")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayCableDropPlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-D-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-D - CABLE DROP PLAN")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayFieldWired),
                        new Sheet(_invalidElementIdIntegerValue, "GC-P-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-P - PATHWAY FIELD WIRED - POWER - FIELD WIRED")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-LV-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-LV - PATHWAY POWER PLAN - LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, PathwayPowerAndLowVoltagePlan),
                        new Sheet(_invalidElementIdIntegerValue, "GC-PLV-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - LEGEND - GC-PLV - PATHWAY POWER PLAN - POWER AND LOW VOLTAGE")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRemove),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-D-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateSameTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RS-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacRelocateDifferentTrack),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-RD-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacNew),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-N-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-01-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-02-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-03-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-04-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-05-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                },
                {
                    new CompositeSheetKey(
                        new SheetSize(_invalidElementIdIntegerValue, SheetSize36x48),
                        new SheetSorting(_invalidElementIdIntegerValue, MacFinish),
                        new Sheet(_invalidElementIdIntegerValue, "GPM-F-06-36")),
                    new List<Legend>
                    {
                        new(_invalidElementIdIntegerValue, "LARGE - MAC LEGEND")
                    }
                }
            };
        }
    }
}
