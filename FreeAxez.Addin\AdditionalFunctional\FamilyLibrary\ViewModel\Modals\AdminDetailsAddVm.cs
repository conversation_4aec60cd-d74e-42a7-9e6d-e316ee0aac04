﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Properties;
using Microsoft.Win32;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;

public class AdminDetailsAddVm : AdminDetailsBaseVm
{
    public AdminDetailsAddVm()
    {
        ChooseFileCommand = new RelayCommand(ExecuteChooseFile);
        ApplyCommand = new RelayCommand(ExecuteApply);
        FileDropCommand = new RelayCommand(DragOver);
    }

    public ICommand ChooseFileCommand { get; private set; }
    public ICommand FileDropCommand { get; private set; }
    public ICommand ApplyCommand { get; private set; }

    protected override void ExecuteChooseFile(object parameter)
    {
        var openFileDialog = new OpenFileDialog
        {
            Multiselect = true,
            Filter = "Revit Files (*.rvt;*.rfa;*.rte)|*.rvt;*.rfa;*.rte"
        };

        if (openFileDialog.ShowDialog() == true)
            ProcessFiles(openFileDialog.FileNames);
    }

    private void DragOver(object parameter)
    {
        if (parameter is DragEventArgs e && e.Data.GetDataPresent(DataFormats.FileDrop))
        {
            var files = ((string[])e.Data.GetData(DataFormats.FileDrop))
                .Where(f =>
                {
                    var ext = Path.GetExtension(f).ToLowerInvariant();
                    return ext == ".rvt" || ext == ".rfa" || ext == ".rte";
                });

            ProcessFiles(files);
        }
    }

    private void ProcessFiles(IEnumerable<string> files)
    {
        var tempFolderPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());

        try
        {
            Directory.CreateDirectory(tempFolderPath);

            foreach (var file in files)
            {
                var fileName = Path.GetFileName(file);
                if (DetailsToUpload.Any(f => f.Name == fileName))
                    continue;

                var tempFilePath = Path.Combine(tempFolderPath, fileName);
                try
                {
                    File.Copy(file, tempFilePath, true);
                }
                catch (IOException ex)
                {
                    LogHelper.Error($"Cannot access file: {fileName}, {ex.Message}");
                    continue;
                }

                var (success, errorMessage) = ProcessSingleFile(tempFilePath, fileName);
                if (!success)
                {
                    LogHelper.Warning($"Skipping {fileName}: {errorMessage}");
                    MessageWindow.ShowDialog("Incompatible File", $"{fileName} - {errorMessage}", MessageType.Notify);
                }
            }

            OnPropertyChanged(nameof(HasUploadedFiles));
            OnPropertyChanged(nameof(CanApply));
        }
        finally
        {
            CleanupTempFolder(tempFolderPath);
        }
    }

    private (bool Success, string ErrorMessage) ProcessSingleFile(string tempFilePath, string fileName)
    {
        Document revitDoc = null;
        try
        {
            var fileBytes = File.ReadAllBytes(tempFilePath);
            var path = ModelPathUtils.ConvertUserVisiblePathToModelPath(tempFilePath);
            revitDoc = RevitManager.Application.OpenDocumentFile(path, new OpenOptions());

            var filePreview = GetThumbnail(tempFilePath);
            var fileInfo = new FileInfo(tempFilePath);

            var detailsItemDto = new LibraryItemDetailsDto
            {
                Name = fileName,
                Description = "",
                FileType = Path.GetExtension(tempFilePath).ToUpperInvariant().Replace(".", ""),
                RevitVersion = RevitManager.RevitVersion,
                CreatedBy = Properties.Settings.Default.UserEmail,
                DateCreated = DateTime.Now,
                LastDateUpdated = DateTime.Now
            };

            var fileDetail = new DetailsFileInfoNew
            {
                DataContext = new DetailsFileInfoNewVm(detailsItemDto, this)
                {
                    FileSize = $"{fileInfo.Length / 1.049e+6:0.0} MB",
                    UploadProgress = 100,
                    FilePreview = filePreview,
                    FileBytes = fileBytes,
                    RevitVersion = RevitManager.RevitVersion
                }
            };

            UploadFileInfo.Add(fileDetail);
            DetailsToUpload.Add(detailsItemDto);

            return (true, null);
        }
        catch (Autodesk.Revit.Exceptions.FileAccessException ex)
        {
            return (false, "Created in newer version or corrupted");
        }
        catch (Exception ex)
        {
            return (false, $"Error: {ex.Message}");
        }
        finally
        {
            revitDoc?.Close(false);
        }
    }

    private void CleanupTempFolder(string tempFolderPath)
    {
        try
        {
            if (!string.IsNullOrEmpty(tempFolderPath) && Directory.Exists(tempFolderPath))
                Directory.Delete(tempFolderPath, true);
        }
        catch (Exception ex)
        {
            LogHelper.Warning($"Failed to delete temp folder: {ex.Message}");
        }
    }

    private async void ExecuteApply(object parameter)
    {
        IsUploading = true;
        foreach (var fileDetail in UploadFileInfo)
        {
            if (fileDetail.DataContext is DetailsFileInfoNewVm vm)
            {
                try
                {
                    var fileExtension = "." + vm.FileType.ToLowerInvariant();
                    var (filePath, fileError) = await ApiService.Instance.UploadDetailsFile(vm.FileBytes, vm.FileName, fileExtension);
                    var (imagePath, imageError) = await ApiService.Instance.UploadDetailsImage(vm.FilePreview);

                    if (fileError != null || imageError != null)
                    {
                        if (fileError != null) LoadingErrors.Add(fileError);
                        if (imageError != null) LoadingErrors.Add(imageError);
                        continue;
                    }

                    vm.DetailsItem.FilePath = filePath;
                    vm.DetailsItem.ImagePath = imagePath;

                    var response = await ApiService.Instance.AddDetailsAsync(vm.DetailsItem);
                    if (!response.IsSuccessStatusCode)
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        LoadingErrors.Add($"Failed to add details: {errorContent}");
                    }
                }
                catch (Exception ex)
                {
                    LoadingErrors.Add($"Error: {ex.Message}");
                    LogHelper.Error($"An error occurred: {ex.Message}");
                }
            }
        }

        IsUploading = false;

        if (LoadingErrors.Count > 0)
            ShowLoadingErrorsMessage();
        else
            ShowSuccessMessage();

        CloseModal(true);
    }
}
