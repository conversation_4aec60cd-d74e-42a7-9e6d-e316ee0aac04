<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Pages.SharedHistoryPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:pages="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages"
             xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="900">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <controls:HeaderBar PageName="History"/>

        <!-- Loading Indicator -->
        <Grid Grid.Row="1" Grid.RowSpan="2"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
              Background="White">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <Ellipse Style="{StaticResource LoadingSpinner}" Margin="0,0,0,20"/>
                <TextBlock Text="Loading history..."
                           Style="{StaticResource TextBase}"
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Tabs -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10,0,0,10">
                <RadioButton Content="Families"
                             IsChecked="{Binding IsFamiliesTabSelected}"
                             Command="{Binding SelectFamiliesTabCommand}"
                             Style="{StaticResource TabButtonStyle}"
                             Margin="0 0 10 0"/>
                <RadioButton Content="Details"
                             IsChecked="{Binding IsDetailsTabSelected}"
                             Command="{Binding SelectDetailsTabCommand}"
                             Style="{StaticResource TabButtonStyle}"/>
            </StackPanel>

            <!-- Families History -->
            <DataGrid Grid.Row="1"
                Style="{DynamicResource DataGridWithoutBorders}"
                VirtualizingStackPanel.IsVirtualizing="True"
                VirtualizingStackPanel.VirtualizationMode="Recycling"
                EnableRowVirtualization="True"
                EnableColumnVirtualization="True"
                ColumnHeaderStyle="{DynamicResource DataGridColumnHeader}"
                ItemsSource="{Binding FamilyHistoryEntries}"
                HorizontalScrollBarVisibility="Hidden"
                Background="#F8F9FF"
                AutoGenerateColumns="False"
                CanUserDeleteRows="False"
                CanUserResizeColumns="True"
                Margin="10,0"
                CanUserAddRows="False"
                CanUserReorderColumns="False"
                HeadersVisibility="Column"
                SelectionMode="Single"
                SelectionUnit="FullRow"
                IsReadOnly="True"
                Visibility="{Binding IsFamiliesTabSelected, Converter={StaticResource BooleanToVisibilityConverter}}">
                <DataGrid.Columns>
                    <DataGridTemplateColumn Header="Product Name" Width="*" MinWidth="120" SortMemberPath="ProductName">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding ProductName}"
                                           TextWrapping="Wrap" FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Family Name" Width="*" MinWidth="140" SortMemberPath="Name">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Name}"
                                           TextWrapping="Wrap" FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Version" Width="*" MinWidth="80" SortMemberPath="Version">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Version}"
                                           TextWrapping="Wrap" FontWeight="Normal" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Created By" Width="*" MinWidth="120" SortMemberPath="CreatedBy">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding CreatedBy}"
                                           TextWrapping="Wrap" FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Updated By" Width="*" MinWidth="120" SortMemberPath="UpdatedBy">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding UpdatedBy}"
                                           TextWrapping="Wrap" FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Action" Width="*" MinWidth="100" SortMemberPath="Action">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Action}"
                                           TextWrapping="Wrap" FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Version Notes" Width="*" MinWidth="130">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding ChangesDescription}"
                                           TextWrapping="Wrap" FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Time Stamp" Width="*" MinWidth="80" SortMemberPath="Timestamp">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Timestamp, StringFormat=\{0:dd/MM/yyyy HH:mm\}}"
                                           TextWrapping="Wrap" FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>

            <!-- Details History -->
            <DataGrid Grid.Row="1"
                Style="{DynamicResource DataGridWithoutBorders}"
                VirtualizingStackPanel.IsVirtualizing="True"
                VirtualizingStackPanel.VirtualizationMode="Recycling"
                EnableRowVirtualization="True"
                EnableColumnVirtualization="True"
                ColumnHeaderStyle="{DynamicResource DataGridColumnHeader}"
                ItemsSource="{Binding DetailsHistoryEntries}"
                HorizontalScrollBarVisibility="Hidden"
                Background="#F8F9FF"
                AutoGenerateColumns="False"
                CanUserDeleteRows="False"
                CanUserResizeColumns="True"
                Margin="10,0"
                CanUserAddRows="False"
                CanUserReorderColumns="False"
                HeadersVisibility="Column"
                SelectionMode="Single"
                SelectionUnit="FullRow"
                IsReadOnly="True"
                Visibility="{Binding IsDetailsTabSelected, Converter={StaticResource BooleanToVisibilityConverter}}">
                <DataGrid.Columns>
                    <DataGridTemplateColumn Header="File Name" Width="*" MinWidth="140" SortMemberPath="Name">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Name}"
                                           TextWrapping="Wrap" FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Created By" Width="*" MinWidth="120" SortMemberPath="CreatedBy">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding CreatedBy}"
                                           TextWrapping="Wrap" FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Updated By" Width="*" MinWidth="120" SortMemberPath="UpdatedBy">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding UpdatedBy}"
                                           TextWrapping="Wrap" FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Action" Width="*" MinWidth="100" SortMemberPath="Action">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Action}"
                                           TextWrapping="Wrap" FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Notes" Width="*" MinWidth="130">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding ChangesDescription}"
                                           TextWrapping="Wrap" FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="Time Stamp" Width="*" MinWidth="80" SortMemberPath="Timestamp">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Timestamp, StringFormat=\{0:dd/MM/yyyy HH:mm\}}"
                                           TextWrapping="Wrap" FontWeight="Light" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</UserControl>
