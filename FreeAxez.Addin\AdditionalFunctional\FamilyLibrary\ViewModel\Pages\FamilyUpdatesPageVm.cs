﻿using System.Collections.ObjectModel;
using System.Windows.Data;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages;

public class FamilyUpdatesPageVm : BasePageVm
{
    private ObservableCollection<LibraryCategoryDto> _categories;
    private ObservableCollection<LibraryItemDto> _families;
    private bool _isLoading;
    private ObservableCollection<string> _revitVersions;
    private LibraryCategoryDto _selectedCategory;
    private string _selectedRevitVersion;


    public FamilyUpdatesPageVm()
    {
        ResetFiltersCommand = new RelayCommand(ResetFilters);
        LoadFamiliesCommand = new AsyncRelayCommand(async () => await LoadFamilies());
        DownloadToRevitCommand = new RelayCommand(ExecuteDownloadToRevit);
        Task.Run(LoadFamilies);
    }

    public ICommand LoadFamiliesCommand { get; }
    public ICommand DownloadToRevitCommand { get; }
    public ICommand ResetFiltersCommand { get; set; }

    public bool IsLoading
    {
        get => _isLoading;
        set
        {
            _isLoading = value;

            OnPropertyChanged();
        }
    }

    public ObservableCollection<LibraryItemDto> Families
    {
        get => _families;
        set
        {
            _families = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<LibraryCategoryDto> Categories
    {
        get => _categories;
        set
        {
            _categories = value;
            OnPropertyChanged();
        }
    }

    public LibraryCategoryDto SelectedCategory
    {
        get => _selectedCategory;
        set
        {
            _selectedCategory = value;

            OnPropertyChanged();
            Filter();
        }
    }

    public ObservableCollection<string> RevitVersions
    {
        get => _revitVersions;
        set
        {
            _revitVersions = value;
            OnPropertyChanged();
        }
    }

    public string SelectedRevitVersion
    {
        get => _selectedRevitVersion;
        set
        {
            _selectedRevitVersion = value;

            OnPropertyChanged();
            Filter();
        }
    }

    public async Task LoadFamilies()
    {
        try
        {
            var result = await DataLoader.LoadFamilyUpdatesPageData();

            Categories = result.Categories;
            Families = result.Families;
            Families = new ObservableCollection<LibraryItemDto>(
                result.Families.Where(f =>
                    Categories.Any(c => c.Id == f.CategoryId && c.IsFreeAxezCategory)));
            RevitVersions = result.RevitVersions;
        }
        catch (Exception ex)
        {
            LogHelper.Error("Error: " + ex.Message);
        }
    }

    private void ExecuteDownloadToRevit(object parameter)
    {
        IsLoading = true;
        var libraryItemDto = parameter is LibraryItemVm vm ? vm.LibraryItem : parameter as LibraryItemDto;

        if (libraryItemDto == null)
        {
            ErrorHandler.HandleError("No library item provided", new ArgumentNullException(nameof(libraryItemDto)));
            IsLoading = false;
            return;
        }

        try
        {
            LogHelper.Information($"Requesting family download to Revit: {libraryItemDto.Name}");

            var handler = FamilyLibraryCore.FamilyDownloadToRevitHandler;
            var externalEvent = FamilyLibraryCore.FamilyDownloadToRevitEvent;

            if (handler == null || externalEvent == null)
            {
                throw new InvalidOperationException("Family download handler or external event is not initialized.");
            }

            handler.SetData(libraryItemDto, this);
            externalEvent.Raise();
            LogHelper.Information($"FamilyDownloadToRevitHandler executed for: {libraryItemDto.Name}");
        }
        catch (Exception ex)
        {
            ErrorHandler.HandleError("Error requesting family download", ex, onError: () => IsLoading = false);
        }
    }

    protected override void Filter()
    {
        var view = CollectionViewSource.GetDefaultView(Families);
        view.Filter = item =>
        {
            var family = item as LibraryItemDto;
            if (family == null) return false;

            var category = Categories.FirstOrDefault(c => c.Id == family.CategoryId);
            if (category == null || !category.IsFreeAxezCategory) return false;

            return ItemFilter.FilterLibraryItem(family, SearchText, SelectedCategory, SelectedRevitVersion);
        };

        view.Refresh();
    }

    private void ResetFilters(object parameter)
    {
        SelectedCategory = null;
        SelectedRevitVersion = null;
        SearchText = string.Empty;
        Filter();
    }
}