using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ViewScale.ViewModels;

public class ViewScaleModel : BaseViewModel
{
    private static readonly Dictionary<int, string> ScaleToStringMap = new Dictionary<int, string>
    {
        { 1, "12\" = 1'-0\"" },
        { 2, "6\" = 1'-0\"" },
        { 4, "3\" = 1'-0\"" },
        { 8, "1 1/2\" = 1'-0\"" },
        { 12, "1\" = 1'-0\"" },
        { 16, "3/4\" = 1'-0\"" },
        { 24, "1/2\" = 1'-0\"" },
        { 32, "3/8\" = 1'-0\"" },
        { 48, "1/4\" = 1'-0\"" },
        { 64, "3/16\" = 1'-0\"" },
        { 96, "1/8\" = 1'-0\"" },
        { 120, "1\" = 10'-0\"" },
        { 128, "3/32\" = 1'-0\"" },
        { 192, "1/16\" = 1'-0\"" },
        { 240, "1\" = 20'-0\"" },
        { 256, "3/64\" = 1'-0\"" },
        { 360, "1\" = 30'-0\"" },
        { 384, "1/32\" = 1'-0\"" },
        { 480, "1\" = 40'-0\"" },
        { 600, "1\" = 50'-0\"" },
        { 720, "1\" = 60'-0\"" },
        { 768, "1/64\" = 1'-0\"" },
        { 960, "1\" = 80'-0\"" },
        { 1200, "1\" = 100'-0\"" },
        { 1920, "1\" = 160'-0\"" },
        { 2400, "1\" = 200'-0\"" },
        { 3600, "1\" = 300'-0\"" },
        { 4800, "1\" = 400'-0\"" }
    };

    private string _title;
    private string _viewTemplate;
    private bool _isViewTemplate;
    private string _scaleString;
    private int _scale;
    private int _originalScale;
    private int _previousValidScale; // TODO: Validation not work
    private List<string> _scaleStringList;
    private bool _isScaleDependOnViewTemplate;


    public ViewScaleModel(
        string title, 
        int id,
        string viewTemplate, 
        bool isViewTemplate, 
        int initialScale, 
        bool isScaleDependOnViewTemplate)
    {
        _title = title;
        Id = id;
        _viewTemplate = isViewTemplate ? "<Template>" : string.IsNullOrWhiteSpace(viewTemplate) ? "<None>" : viewTemplate;
        _isViewTemplate = isViewTemplate;
        _originalScale = initialScale;
        _scale = initialScale;
        _previousValidScale = initialScale;
        _isScaleDependOnViewTemplate = isScaleDependOnViewTemplate;

        // Initialize scale string list
        _scaleStringList = new List<string>() { "Custom" }; // Add "Custom" option first
        ScaleToStringMap.Keys
            .OrderBy(k => k)
            .ToList()
            .ForEach(key => _scaleStringList.Add(ScaleToStringMap[key]));

        // Set scale string value
        _scaleString = ScaleToStringMap.ContainsKey(_scale) ? ScaleToStringMap[_scale] : "Custom";
        OnPropertyChanged(nameof(ScaleString));
    }


    public string Title
    {
        get => _title;
    }

    public int Id { get; }

    public string ViewTemplate
    {
        get => _viewTemplate;
    }

    public bool IsViewTemplate
    {
        get => _isViewTemplate;
    }

    public List<string> ScaleStringList
    {
        get => _scaleStringList;
        private set => Set(ref _scaleStringList, value);
    }

    public string ScaleString
    {
        get => _scaleString;
        set
        {
            if (Set(ref _scaleString, value))
            {
                if (_scaleString == "Custom")
                {
                    return;
                }

                // Find the scale value for the selected scale string
                var scaleEntry = ScaleToStringMap.FirstOrDefault(kvp => kvp.Value == _scaleString);
                if (scaleEntry.Key != 0) // Key found
                {
                    _scale = scaleEntry.Key;
                    _previousValidScale = _scale;
                    OnPropertyChanged(nameof(Scale));
                }
            }
        }
    }

    public int Scale
    {
        get => _scale;
        set
        {
            // Validate: only positive integers allowed
            if (value <= 0)
            {
                // Revert to previous valid value
                Set(ref _scale, _previousValidScale);
                return;
            }

            if (Set(ref _scale, value))
            {
                _previousValidScale = _scale;

                // Check if the new scale value exists in the predefined dictionary
                if (ScaleToStringMap.ContainsKey(_scale))
                {
                    // Update ScaleString to the predefined value and exit Custom mode
                    _scaleString = ScaleToStringMap[_scale];
                    OnPropertyChanged(nameof(ScaleString));
                }
                else
                {
                    // Keep ScaleString as "Custom" if scale is not in dictionary
                    if (_scaleString != "Custom")
                    {
                        _scaleString = "Custom";
                        OnPropertyChanged(nameof(ScaleString));
                    }
                }
            }
        }
    }

    public bool IsScaleChanged
    {
        get => _scale != _originalScale;
    }

    /// <summary>
    /// Block TextBox and ComboBox with scales that depend on the view template.
    /// </summary>
    public bool IsScaleNotDependOnViewTemplate
    {
        get => !_isScaleDependOnViewTemplate;
    }
}
