<Window x:Class="FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views.LoadOrReloadLinetypesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Load or Reload Linetypes" 
        Height="450" 
        Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- File selection -->
        <Grid Grid.Row="0" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="File ..."
                    Command="{Binding BrowseFileCommand}"
                    Style="{StaticResource ButtonOutlinedBlue}"
                    Margin="0,0,5,0"/>

            <TextBox Grid.Column="1"
                     Text="{Binding SelectedFilePath}"
                     Style="{StaticResource UiTextBox}"
                     VerticalContentAlignment="Center"
                     TextWrapping="NoWrap"
                     Height="30"/>
        </Grid>

        <!-- Available Linetypes label -->
        <TextBlock Grid.Row="1" 
                   Text="Available Linetypes" 
                   FontWeight="Bold"
                   Margin="0,0,0,10"/>

        <!-- Linetypes list -->
        <DataGrid Grid.Row="2"
                  Style="{DynamicResource DataGridWithoutBorders}"
                  ColumnHeaderStyle="{DynamicResource DataGridColumnHeader}"
                  ItemsSource="{Binding AvailableLinetypes}"
                  SelectedItem="{Binding SelectedLinetype}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  CanUserReorderColumns="False"
                  CanUserResizeRows="False"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  SelectionMode="Single"
                  Margin="0,0,0,15">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="Linetype" 
                                    Binding="{Binding Name}" 
                                    Width="150"
                                    IsReadOnly="True"/>
                <DataGridTextColumn Header="Description" 
                                    Binding="{Binding Description}" 
                                    Width="*"
                                    IsReadOnly="True"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Buttons -->
        <Grid Grid.Row="3" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="OK"
                    Command="{Binding OkCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    Style="{StaticResource ButtonSimpleBlue}"
                    Height="30"/>
            <Button Grid.Column="2"
                    Content="Cancel"
                    Command="{Binding CancelCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    Style="{StaticResource ButtonSimpleRed}"
                    Height="30"/>
        </Grid>
    </Grid>
</Window>
