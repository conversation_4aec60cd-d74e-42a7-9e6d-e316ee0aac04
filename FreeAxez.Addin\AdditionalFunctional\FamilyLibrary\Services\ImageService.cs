﻿using System.IO;
using System.Windows.Media.Imaging;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;

public static class ImageService
{
    public static byte[] BitmapSourceToByteArray(BitmapSource image)
    {
        using (var stream = new MemoryStream())
        {
            var encoder = new PngBitmapEncoder();
            encoder.Frames.Add(BitmapFrame.Create(image));
            encoder.Save(stream);
            return stream.ToArray();
        }
    }
}