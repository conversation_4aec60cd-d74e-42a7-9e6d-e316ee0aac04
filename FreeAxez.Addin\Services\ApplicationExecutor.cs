﻿using System.Diagnostics;
using System.IO;
using System.Reflection;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.Services
{
    public static class ApplicationExecutor
    {
        private static Process _browserProcess;
        private static ProcessStartInfo _startInfo;

        public static void Create()
        {
            var assemblyDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            var exe = Path.Combine(assemblyDir, "BrowserApp", Properties.Settings.Default.browserAppName);

            if (!File.Exists(exe))
            {
                LogHelper.Error($"Browser application not found at path: {exe}");
                MessageWindow.ShowDialog($"Browser application not found at path: {exe}", Infrastructure.UI.Enums.MessageType.Error);
            }

            LogHelper.Information($"Creating app executor. path:{exe}");

            _startInfo = new ProcessStartInfo(exe);
            _startInfo.UseShellExecute = false; // The Process object must have the UseShellExecute property set to false in order to use environment variables
            _startInfo.WorkingDirectory = Path.GetDirectoryName(exe);
        }

        public static void Run(Guid projectId, string fileName)
        {
            if (_browserProcess == null || _browserProcess.HasExited)
            {
                // If using the debug add-on, the StartupApp will not start and startInfo will be empty
                if (_startInfo == null)
                {
                    Create();
                }

                LogHelper.Information($"Starting browser process. Uid: {projectId}, FileName: {fileName}");

                _startInfo.Arguments = $"\"{projectId}\" \"{fileName}\"";

                try
                {
                    _browserProcess = Process.Start(_startInfo);
                    if (_browserProcess == null)
                    {
                        LogHelper.Error("Failed to start browser process. Process.Start returned null.");
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Exception while starting browser process: {ex.Message}");
                }
            }
        }

        public static void RunUpdatePage()
        {
            try
            {
                var url = "https://freeaxez.bimsmith.com/download";
                ProcessStartInfo startInfo = new ProcessStartInfo(url)
                {
                    UseShellExecute = true
                };
                Process.Start(startInfo);
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Failed to open update page: {ex.Message}");
            }
        }

        public static void Stop()
        {
            if (_browserProcess != null && !_browserProcess.HasExited)
            {
                try
                {
                    _browserProcess.Kill();
                    LogHelper.Information("Browser process stopped.");
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Failed to stop browser process: {ex.Message}");
                }
            }
        }
    }
}