﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.DeleteViewBySize.Models;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.DeleteViewBySize.ViewModels
{
    public class DeleteViewBySizeViewModel : BaseViewModel
    {
        private const string ViewSizeParameterName = "View Size";
        private const string SheetSizeParameterName = "Sheet Size";

        // A group of views to be hidden from the user in order to exclude the possibility of removing any view from this group
        private const string VariesGroupName = "VARIES"; 


        public DeleteViewBySizeViewModel()
        {
            ViewSizes = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(View))
                .WhereElementIsNotElementType()
                .OfType<View>()
                .Where(view => !view.IsTemplate)
                .GroupBy(GetSizeValue)
                .Where(group =>
                {
                    bool isParameterValueEmpty = string.IsNullOrWhiteSpace(group.Key);
                    bool isParameterValueEqualVariesGroupName = string.Equals(
                        group.Key, VariesGroupName, System.StringComparison.OrdinalIgnoreCase);

                    return !isParameterValueEmpty && !isParameterValueEqualVariesGroupName;
                })
                .Select(group =>
                {
                    var viewElements = group
                        .Select(view => (Element)view)
                        .ToList();

                    return new ViewSize
                    {
                        Name = group.Key,
                        Views = viewElements
                    };
                })
                .OrderBy(viewSize => viewSize.Name)
                .ToList();

            DeleteViewsCommand = new RelayCommand(OnDeleteViewsCommandExecute);
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
        }


        public List<ViewSize> ViewSizes { get; set; }

        public ICommand DeleteViewsCommand { get; set; }
        private void OnDeleteViewsCommandExecute(object p)
        {
            if (ViewSizes.Where(s => !s.IsCheck).Count() == 0)
            {
                InfoDialog.ShowDialog("Warning", "It is not possible to delete all sizes.\nClear the checkbox for at least one size.", p as Window);
                return;
            }

            (p as Window).Close();

            ChangeActiveViewIfNecessary();

            using (var t = new Transaction(RevitManager.Document))
            {
                t.Start("Delete Selected Sizes");

                foreach (var size in ViewSizes)
                {
                    if (!size.IsCheck)
                    {
                        continue;
                    }

                    foreach (var view in size.Views)
                    {
                        try
                        {
                            RevitManager.Document.Delete(view.Id);
                        }
                        catch { }
                    }
                }

                t.Commit();
            }
        }

        public ICommand CancelCommand { get; set; }
        private void OnCancelCommandExecute(object p)
        {
            (p as Window).Close();
        }


        private string GetSizeValue(Element element)
        {
            var viewSizeParameter = element.LookupParameter(ViewSizeParameterName);
            if (viewSizeParameter != null)
            {
                return viewSizeParameter.AsString();
            }
            
            var sheetSizeParameter = element.LookupParameter(SheetSizeParameterName);
            if (sheetSizeParameter != null)
            {
                return sheetSizeParameter.AsString();
            }

            return string.Empty;
        }

        private void ChangeActiveViewIfNecessary()
        {
            var viewsForDelete = ViewSizes.Where(v => v.IsCheck).SelectMany(v => v.Views).Select(v => v.Id.GetIntegerValue()).ToHashSet<int>();

            if (!viewsForDelete.Contains(RevitManager.UIDocument.ActiveView.Id.GetIntegerValue()))
            {
                return;
            }

            var openedViews = RevitManager.UIDocument.GetOpenUIViews().Select(v => v.ViewId.GetIntegerValue()).ToHashSet<int>();
            var availableViewsToChange = openedViews.Except(viewsForDelete);

            if (availableViewsToChange.Count() > 0)
            {
                RevitManager.UIDocument.ActiveView = RevitManager.Document.GetElement(new ElementId(availableViewsToChange.First())) as View;
            }
            else
            {
                View default3DView = null;

                using (var transaction = new Transaction(RevitManager.Document))
                {
                    transaction.Start("Default 3D View");

                    var viewFamilyTypeId = new FilteredElementCollector(RevitManager.Document)
                        .OfClass(typeof(ViewFamilyType))
                        .Where(t => (t as ViewFamilyType).ViewFamily == ViewFamily.ThreeDimensional)
                        .Select(t => t.Id)
                        .First();

                    default3DView = View3D.CreateIsometric(RevitManager.Document, viewFamilyTypeId);

                    transaction.Commit();
                }

                RevitManager.UIDocument.ActiveView = default3DView;
            }
        }
    }
}
