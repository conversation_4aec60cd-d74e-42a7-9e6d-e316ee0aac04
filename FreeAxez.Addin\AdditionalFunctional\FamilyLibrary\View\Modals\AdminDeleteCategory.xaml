<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals.AdminDeleteCategory"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             mc:Ignorable="d"
             Width="300"
             >
    <UserControl.Resources>
        <ResourceDictionary>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0" Margin="0 0 0 20">
            <TextBlock Style="{StaticResource TextBase}"
                       Text="Are you sure you want to delete this category?"
                       TextWrapping="Wrap"
                       FontWeight="SemiBold"
                       Margin="0 10 0 5"
                       Visibility="{Binding CanDeleteCategory, 
                        Converter={StaticResource BooleanToVisibilityConverter}}" />

            <TextBlock Style="{StaticResource TextBase}"
                       Text="You cannot delete this category as it is associated with one or more families."
                       FontWeight="SemiBold"
                       Margin="0 10 0 5"
                       TextWrapping="Wrap"
                       Visibility="{Binding CanDeleteCategory, Converter={StaticResource InverseBooleanToVisibilityConverter}}" />
        </StackPanel>
        <Grid Grid.Row="1" >
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Button Grid.Column="0" 
                    Style="{StaticResource ButtonOutlinedRed}" 
                    Command="{Binding CancelCommand}">
                Cancel
            </Button>
            <Button Grid.Column="2" 
                    Content="Delete Category" 
                    Command="{Binding ApplyCommand}"
                    Visibility="{Binding CanDeleteCategory, 
                    Converter={StaticResource BooleanToVisibilityConverter}}"
                    Style="{StaticResource ButtonSimpleBlue}" />
        </Grid>
    </Grid>
</UserControl>
