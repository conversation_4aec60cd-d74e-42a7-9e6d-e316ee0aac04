﻿using FreeAxez.Addin.AdditionalFunctional.PanelScheduleCreation.Utils;
using FreeAxez.Addin.Infrastructure;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.PanelScheduleCreation.ViewModels
{
    public class PanelScheduleCreationViewModel : BaseViewModel
    {
        public PanelScheduleCreationViewModel()
        {
            ConnectedComponents = Properties.Settings.Default.panelScheduleCreationConnectedComponents;
            ConnectedComponentsByNumber = !ConnectedComponents;
            CreateCommand = new RelayCommand(OnCreateCommandExecute);
        }

        public string ConnectedComponentsName => BaseSchedules.ConnectedComponentsName;
        public string ConnectedComponentsByNumberName => BaseSchedules.ConnectedComponentsByNumber;
        public bool ConnectedComponents { get; set; }
        public bool ConnectedComponentsByNumber { get; set; }

        public ICommand CreateCommand { get; set; }
        private void OnCreateCommandExecute(object p)
        {
            (p as Window).Close();
            SaveSettings();

            var trackScheduleManager = new TrackScheduleManager();
            trackScheduleManager.CreateSchedules();
        }

        private void SaveSettings()
        {
            Properties.Settings.Default.panelScheduleCreationConnectedComponents = ConnectedComponents;
            Properties.Settings.Default.Save();
        }
    }
}
