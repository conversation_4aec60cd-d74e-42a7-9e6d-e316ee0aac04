using System.IO;
using System.Linq;
using System.Windows.Forms;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Core.Elements;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Infrastructure;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Interfaces;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.Area;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.AutoCAD;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.Extraction;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Revit.Placement;

using Newtonsoft.Json;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Workflow;

/// <summary>
///     Central workflow coordinator that orchestrates the complete process:
///     CAD data extraction → Element creation → Revit placement
/// </summary>
public class GriddBuilderWorkflow
{
    private readonly AutoCADService _autocadService;
    private readonly ElementExtractor _elementExtractor;
    private readonly ElementPlacer _elementPlacer;
    private readonly AreaProcessor _areaProcessor;

    public GriddBuilderWorkflow(AutoCADService autocadService)
    {
        _autocadService = autocadService ?? throw new ArgumentNullException(nameof(autocadService));
        _elementExtractor = new ElementExtractor();
        _elementPlacer = new ElementPlacer();
        _areaProcessor = new AreaProcessor();
    }

    /// <summary>
    /// Executes the complete workflow: extracts CAD data, creates elements, and places them in Revit
    /// </summary>
    public async Task<BuildResult> ExecuteAsync(Document document, DwgLinkInfo dwgLinkInfo, int baseUnitHeight)
    {
        return await Task.Run(() => Execute(document, dwgLinkInfo, baseUnitHeight, null, CancellationToken.None));
    }

    /// <summary>
    /// Executes the complete workflow with progress reporting and cancellation support
    /// </summary>
    public BuildResult Execute(Document document, DwgLinkInfo dwgLinkInfo, int baseUnitHeight,
        IProgressReporter progressReporter, CancellationToken cancellationToken)
    {
        if (document == null) throw new ArgumentNullException(nameof(document));
        if (dwgLinkInfo?.IsValid != true) throw new ArgumentException("Invalid DWG link info", nameof(dwgLinkInfo));
        if (baseUnitHeight != 40 && baseUnitHeight != 70) throw new ArgumentException("Base unit height must be 40 or 70", nameof(baseUnitHeight));

        using (var tempFileManager = new TempFileManager())
        {
            try
            {
                // Step 1/5: Extract CAD data
                progressReporter?.ReportStepStatus("Step 1/5: Extracting lines from AutoCAD...");
                progressReporter?.ReportStatus("Starting AutoCAD and exporting data...");
                progressReporter?.ReportProgress(0);
                Application.DoEvents();
                cancellationToken.ThrowIfCancellationRequested();

                _autocadService.ExportLinesToText(dwgLinkInfo.DwgFilePath, tempFileManager.JsonFilePath);

                progressReporter?.ReportStatus("Reading data from file...");
                progressReporter?.ReportProgress(20);
                Application.DoEvents();
                cancellationToken.ThrowIfCancellationRequested();

                var jsonContent = File.ReadAllText(tempFileManager.JsonFilePath);
                var cadData = JsonConvert.DeserializeObject<JsonLineCollection>(jsonContent);

                if (cadData?.lines == null || !cadData.lines.Any())
                    return BuildResult.CreateSuccess(dwgLinkInfo.GetDisplayName(), "No lines found in DWG file");

                // Step 2/5: Extract and recognize elements
                progressReporter?.ReportStepStatus("Step 2/5: Grouping lines and recognizing elements...");
                progressReporter?.ReportStatus("Analyzing geometry and creating elements...");
                progressReporter?.ReportProgress(40);
                Application.DoEvents();
                cancellationToken.ThrowIfCancellationRequested();

                var elements = _elementExtractor.ExtractAllElements(cadData.lines, dwgLinkInfo.Transform, dwgLinkInfo.DwgFilePath);

                // Step 3/5: Place elements in Revit
                progressReporter?.ReportStepStatus("Step 3/5: Placing elements in Revit...");
                progressReporter?.ReportStatus("Creating families in project...");
                progressReporter?.ReportProgress(60);
                Application.DoEvents();
                cancellationToken.ThrowIfCancellationRequested();

                var result = _elementPlacer.PlaceAllElements(elements, document, dwgLinkInfo.Level, baseUnitHeight, dwgLinkInfo.Transform, dwgLinkInfo.GetDisplayName(), progressReporter, cancellationToken);

                // Step 4/6: Process area data for floors
                progressReporter?.ReportStepStatus("Step 4/6: Processing area data...");
                progressReporter?.ReportStatus("Searching and analyzing A-AREA-PATT layer...");
                progressReporter?.ReportProgress(70);
                Application.DoEvents();
                cancellationToken.ThrowIfCancellationRequested();

                var areaLines = cadData.lines.Where(l => l.layer == "A-AREA-PATT").ToList();
                if (areaLines.Any())
                {
                    progressReporter?.ReportStatus("Creating floors from areas...");
                    _areaProcessor.ProcessAreaData(areaLines, document, dwgLinkInfo.Level, dwgLinkInfo.Transform, result);
                }
                else
                {
                    result.AreaProcessingMessage = "No lines found in A-AREA-PATT layer";
                }

                // Step 5/6: Process void cutting from area offset
                progressReporter?.ReportStepStatus("Step 5/6: Processing area void cutting...");
                progressReporter?.ReportStatus("Creating void from area offset and cutting elements...");
                progressReporter?.ReportProgress(85);
                Application.DoEvents();
                cancellationToken.ThrowIfCancellationRequested();

                // Step 6/6: Complete
                progressReporter?.ReportStepStatus("Step 6/6: Completing...");
                progressReporter?.ReportStatus("Operation completed successfully!");
                progressReporter?.ReportProgress(100);
                Application.DoEvents();

                return result;
            }
            catch (OperationCanceledException)
            {
                progressReporter?.ReportStatus("Operation was cancelled by user.");
                return BuildResult.CreateFailure(dwgLinkInfo.GetDisplayName(), "Operation was cancelled by user");
            }
            catch (Exception ex)
            {
                progressReporter?.ReportStatus($"Error: {ex.Message}");
                return BuildResult.CreateFailure(dwgLinkInfo.GetDisplayName(), ex.Message);
            }
        }
    }
}