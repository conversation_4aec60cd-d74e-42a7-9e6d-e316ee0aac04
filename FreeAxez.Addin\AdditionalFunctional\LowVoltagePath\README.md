# Low Voltage Path Generator

## Overview

The Low Voltage Path Generator is a Revit command that automates the creation of railings and annotations to model low voltage lines in a Revit project. This tool processes electrical fixtures (outlets) with low voltage parameters and converts line elements into railings with proper wire count calculations.

## Purpose

This command addresses the workflow of creating low voltage electrical pathways by:
- Converting LV/MC lines into railings
- Creating directional annotations along the pathways
- Calculating and setting wire counts based on outlet parameters
- Providing comprehensive validation and reporting

## Business Logic

### Core Assumptions
1. **One outlet - one railing**: Each outlet connects to exactly one railing
2. **Line type ignored**: The line type/size parameter is not considered
3. **Wire count summation**: Wire counts are summed across all parameters for a single railing

### Processing Workflow

#### 1. Data Collection
- Finds outlets with `LV# - Count` parameters > 0
- Collects LV/MC lines based on line style names matching pattern `(LV|MC)[\s-_]*\d`
- Supports both entire view and selected elements scope

#### 2. Geometric Analysis
- Groups lines into connected networks
- Associates outlets with nearby line groups (within 2 feet tolerance)
- Normalizes line geometry for processing
- Builds geometric trees for pathway analysis

#### 3. Railing Creation
- Generates railings along line paths
- Skips creation if railing already exists leading to panel
- Sets wire count parameters on created railings

#### 4. Annotation Placement
- Creates directional arrow annotations along railings
- Calculates wire counts by summing from nearby railings (within 1 inch tolerance)

#### 5. Parameter Updates
- Updates `Quantity` parameter on railings with outlet wire counts
- Updates `Quantity` parameter on annotations with summed railing counts

## User Interface

### Settings Panel
- **Scope Selection**: Radio buttons for "Entire View" or "Selected Elements Only"
  - "Entire View": Processes all LV/MC elements in the current view
  - "Selected Elements Only": Allows user to pick specific elements using selection filter after clicking Execute
- **Railing Type**: Dropdown list of available railing types in the project
- **Delete Lines**: Checkbox option to remove original lines after conversion

### Settings Persistence
- User preferences are automatically saved and restored:
  - Selected scope type (Entire View/Selected Elements)
  - Selected railing type name
  - Delete lines option

### Element Selection (Selected Elements Only)
- After clicking Execute, user can pick elements using intelligent selection filter
- Filter automatically allows only:
  - CurveElements with LV/MC line styles
  - Electrical fixtures with low voltage parameters (LV# - Count > 0)
- User can cancel selection to abort operation

### Validation Messages
- **View Type**: Command only works on floor plan views
- **Missing outlets**: Context-aware messages for view vs. selected elements scope
- **Missing lines**: Context-aware messages for view vs. selected elements scope
- **Existing connections**: Warns about outlets already connected to railings
- **Missing railing types**: No railing types available in project

### Execution Report
- Count of created railings and annotations
- Count of unconnected outlets with their IDs
- Processing statistics and execution time
- Warnings about skipped elements

## Architecture

### Main Components
```
LowVoltagePath/
├── LowVoltagePathCommand.cs     # Main command entry point
├── Constants/
│   └── LowVoltageConstants.cs   # Configuration constants
├── Models/
│   ├── LowVoltagePathSettings.cs    # UI settings model
│   ├── ValidationResult.cs          # Validation result model
│   └── ExecutionReport.cs           # Execution report model
├── ViewModels/
│   └── LowVoltagePathViewModel.cs   # UI logic and element selection
├── Views/
│   ├── LowVoltagePathView.xaml      # Main UI window
│   ├── LowVoltagePathView.xaml.cs   # Code-behind
│   └── Converters/
│       └── ScopeTypeConverter.cs    # XAML value converter
├── Utils/
│   └── LowVoltageSelectionFilter.cs # Element selection filter
└── Services/
    ├── LowVoltageDataCollector.cs   # Data collection service
    ├── LowVoltageValidationService.cs # Validation service
    ├── LowVoltageProcessor.cs       # Main processing service
    └── LowVoltageReportService.cs   # Reporting service
```

## Key Classes

### LowVoltagePathCommand.cs
- **Purpose**: Main entry point that orchestrates the entire workflow
- **Inherits**: `BaseExternalCommand`
- **Key Methods**: `Execute()`
- **Responsibilities**: Validates view type, shows UI, coordinates services, handles exceptions
- **New Features**: Pre-execution view validation, improved error handling

### LowVoltagePathViewModel.cs
- **Purpose**: UI logic and user interaction management
- **Inherits**: `WindowViewModel`
- **Key Methods**:
  - `PickSelectedElements()` - Interactive element selection with filter
  - `LoadSettings()` / `SaveSettings()` - Settings persistence
- **New Features**: Settings persistence, post-dialog element selection

### LowVoltageDataCollector.cs
- **Purpose**: Collects and converts Revit elements to processing models
- **Key Methods**:
  - `CollectLowVoltageLines()` - Finds LV/MC lines
  - `CollectLowVoltageOutlets()` - Finds outlets with LV parameters
  - `CalculateOutletWireCount()` - Sums wire counts from parameters
- **Architecture**: Uses static RevitManager instead of injected dependencies

### LowVoltageValidationService.cs
- **Purpose**: Validates prerequisites before processing
- **Key Methods**:
  - `ValidateViewType()` - Ensures floor plan view
  - `ValidateData()` - Context-aware validation for scope type
  - `CountOutletsWithExistingRailings()` - Checks proximity to existing railings
- **New Features**: Existing railing detection, scope-aware error messages

### LowVoltageProcessor.cs
- **Purpose**: Main processing engine for geometry and Revit element creation
- **Key Methods**:
  - `ProcessLowVoltagePath()` - Main processing workflow
  - `CreateRailings()` - Creates Revit railings
  - `CreateAnnotations()` - Creates Revit annotations
- **Architecture**: Uses static RevitManager, simplified constructor

### LowVoltageReportService.cs
- **Purpose**: Generates and displays user reports
- **Key Methods**:
  - `ShowValidationResults()` - Displays validation messages
  - `ShowExecutionResults()` - Shows completion report
- **New Features**: Uses MessageType.Notify instead of Error for validation issues

### LowVoltageSelectionFilter.cs
- **Purpose**: Intelligent element selection filter
- **Implements**: `ISelectionFilter`
- **Key Methods**:
  - `AllowElement()` - Filters LV/MC lines and electrical fixtures
  - `HasLowVoltageWires()` - Validates outlet parameters

## Dependencies

### Existing Services (Reused)
- `LineGroupService` - Groups connected lines
- `LineNormalizationService` - Normalizes line geometry
- `GeometryAnalyzerService` - Analyzes geometric relationships
- `RailingsGeneratorService` - Generates railing models
- `AnnotationPlacerService` - Places annotation models
- `WireCountingService` - Updates wire counts
- `NTSConverter` - Converts between Revit and NTS geometry

### External Dependencies
- NetTopologySuite for geometric operations
- Revit API for element creation and manipulation

## Configuration

### Constants (LowVoltageConstants.cs)
- Parameter names for wire quantities
- Family names for annotations
- Regex patterns for line and parameter matching
- Tolerance values for geometric operations
- Default values and file paths

## Error Handling

- Comprehensive validation before execution
- Transaction rollback on errors
- User-friendly error messages
- Detailed logging for debugging

## Future Enhancements

The architecture supports future development of:
1. **Update Command**: Separate command to update existing railings/annotations without recreation
2. **Advanced Grouping**: Group outlets and lines by LV/MC tags
3. **Custom Parameters**: Support for text-based count parameters
4. **Batch Processing**: Process multiple views simultaneously

## Usage Notes

### Prerequisites
- **View Type**: Must be executed on a floor plan view (validated before UI opens)
- **Railing Types**: At least one railing type must exist in the project
- **Annotation Family**: `FA-Low_Voltage_Pathway_Arrow_Annotation` should be loaded (optional)
- **Element Requirements**:
  - Outlets must have `LV# - Count` or `MC# - Count` parameters with values > 0
  - Lines must have line styles matching pattern `(LV|MC)[\s-_]*\d`

### Workflow
1. **Pre-validation**: Command automatically checks view type before opening UI
2. **Settings**: Configure scope, railing type, and options (settings are remembered)
3. **Element Selection**: If "Selected Elements Only" is chosen, pick elements after clicking Execute
4. **Processing**: Automatic validation, processing, and reporting
5. **Results**: View detailed report with creation counts and any issues

### Settings Persistence
- All user preferences are automatically saved to `Properties.Settings`
- Settings include: scope type, railing type selection, delete lines option
- Settings are restored when command is run again

### Element Selection Filter
- Intelligent filter only shows relevant elements during selection
- Automatically excludes elements without required parameters
- Supports multi-selection with standard Revit selection tools
