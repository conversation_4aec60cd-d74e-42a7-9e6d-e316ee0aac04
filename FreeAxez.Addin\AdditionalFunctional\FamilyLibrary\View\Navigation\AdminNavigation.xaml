<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Navigation.AdminNavigation"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls"
             mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <StackPanel>
            <controls:NavButton Command="{Binding FamiliesPageCommand}"
                                GroupName="NavRadioGroup"
                                IconTemplate="{StaticResource FamilyIcon}"
                                ButtonText="Families"
                                IsChecked="True"/>
            <controls:NavButton Command="{Binding DetailsPageCommand}"
                                GroupName="NavRadioGroup"
                                IconTemplate="{StaticResource DocumentIcon}"
                                ButtonText="Details"
                                IsChecked="{Binding IsDetailsChecked}"/>
            <controls:NavButton Command="{Binding CategoriesPageCommand}"
                                GroupName="NavRadioGroup"
                                IconTemplate="{StaticResource TabletIcon}"
                                ButtonText="Family Categories"
                                IsChecked="{Binding IsCategoriesChecked}"/>
            <controls:NavButton Command="{Binding HistoryPageCommand}"
                                GroupName="NavRadioGroup"
                                IconTemplate="{StaticResource HistoryIcon}"
                                ButtonText="History"
                                IsChecked="{Binding IsHistoryChecked}"/>
            <!--<RadioButton Style="{StaticResource NavButtonStyle}"
                         Command="{Binding FamiliesPageCommand}"
                         IsChecked="True"
                         Margin="5"
            >
                        <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <ContentControl Template="{StaticResource FamilyIcon}"
                                            VerticalAlignment="Center"/>
                            <TextBlock Text="Families"
                                       Margin="0 5"
                                       Style="{StaticResource TextBase}"
                                       FontWeight="SemiBold"
                                       Foreground="White"/>
                        </StackPanel>
                    </RadioButton>
                    <RadioButton Style="{StaticResource NavButtonStyle}"
                         Command="{Binding CategoriesPageCommand}" Margin="5">
                        <StackPanel Orientation="Horizontal">
                            <ContentControl Template="{StaticResource TabletIcon}"
                                            VerticalAlignment="Center"
                                            Margin="15 0 0 0"/>
                    <TextBlock Text="Categories"
                                       Style="{StaticResource TextH4}"
                                       FontWeight="Bold"
                                       Foreground="White"
                                       Margin="10 0 0 0"/>
                        </StackPanel>
                    </RadioButton>
                    <RadioButton Style="{StaticResource NavButtonStyle}"
                                 Command="{Binding HistoryPageCommand}">
                        <StackPanel Orientation="Horizontal">
                            <ContentControl Template="{StaticResource HistoryIcon}"
                                            VerticalAlignment="Center"
                                            Margin="15 0 0 0"/>
                            <TextBlock Text="History"
                                       Style="{StaticResource TextH4}"
                                       FontWeight="Bold"
                                       Foreground="White"
                                       Margin="10 0 0 0"/>
                        </StackPanel>
                    </RadioButton>-->
        </StackPanel>
    </Grid>
</UserControl>
