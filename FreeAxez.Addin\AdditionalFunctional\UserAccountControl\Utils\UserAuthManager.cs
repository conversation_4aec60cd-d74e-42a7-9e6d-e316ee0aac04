﻿using System.Text;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Properties;
using System.Security.Cryptography;

namespace FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Utils;

public class UserAuthManager
{
    private static List<RibbonPanel> _panels = new();
    private static List<PushButton> _managementButtons = new();
    private static PushButton _libraryAdminButton;

    public static void SetRibbonElementsList(List<RibbonPanel> panels, List<PushButton> buttons,
        PushButton libraryAdminButton)
    {
        _panels = panels;
        _managementButtons = buttons;
        _libraryAdminButton = libraryAdminButton;
    }

    public static void SetRibbonElementsEnabled(bool enabled, bool isAdmin = false)
    {
        foreach (var panel in _panels) panel.Enabled = enabled;

        foreach (var button in _managementButtons) button.Enabled = enabled;

        _libraryAdminButton.Visible = isAdmin;
    }

    public static void SaveCredentials(string username, string token)
    {
        Settings.Default.UserEmail = username;
        Settings.Default.AuthToken = EncryptString(token);
        Settings.Default.Save();
    }

    public static (string Username, string Token) GetCredentials()
    {
        var username = Settings.Default.UserEmail;
        var encryptedToken = Settings.Default.AuthToken;

        if (!string.IsNullOrEmpty(encryptedToken))
        {
            var token = DecryptString(encryptedToken);
            return (username, token);
        }

        return (null, null);
    }

    public static void RemoveCredentials()
    {
        Settings.Default.UserEmail = "";
        Settings.Default.AuthToken = "";
        Settings.Default.Save();
    }

    private static string EncryptString(string input)
    {
        var data = Encoding.Unicode.GetBytes(input);
        byte[] encrypted = ProtectedData.Protect(data, null, DataProtectionScope.CurrentUser);
        return Convert.ToBase64String(encrypted);
    }

    private static string DecryptString(string encrypted)
    {
        var data = Convert.FromBase64String(encrypted);
        byte[] decrypted = ProtectedData.Unprotect(data, null, DataProtectionScope.CurrentUser);
        return Encoding.Unicode.GetString(decrypted);
    }
}