using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;

public static class LineweightService
{
    private static readonly Dictionary<short, string> LineweightDisplayNames = new()
    {
        { -3, "Default" },
        { 0, "0.00 mm" },
        { 5, "0.05 mm" },
        { 9, "0.09 mm" },
        { 13, "0.13 mm" },
        { 15, "0.15 mm" },
        { 18, "0.18 mm" },
        { 20, "0.20 mm" },
        { 25, "0.25 mm" },
        { 30, "0.30 mm" },
        { 35, "0.35 mm" },
        { 40, "0.40 mm" },
        { 50, "0.50 mm" },
        { 53, "0.53 mm" },
        { 60, "0.60 mm" },
        { 70, "0.70 mm" },
        { 80, "0.80 mm" },
        { 90, "0.90 mm" },
        { 100, "1.00 mm" },
        { 106, "1.06 mm" },
        { 120, "1.20 mm" },
        { 140, "1.40 mm" },
        { 158, "1.58 mm" },
        { 200, "2.00 mm" },
        { 211, "2.11 mm" }
    };

    public static List<LineweightItem> GetAllLineweights()
    {
        return LineweightDisplayNames
            .Select(kvp => new LineweightItem(kvp.Key, kvp.Value))
            .ToList();
    }

    public static string GetDisplayName(short lineweight)
    {
        return LineweightDisplayNames.TryGetValue(lineweight, out var displayName)
            ? displayName
            : $"{lineweight / 100.0:F2} mm";
    }

    public static LineweightItem GetLineweightItem(short lineweight)
    {
        var displayName = GetDisplayName(lineweight);
        return new LineweightItem(lineweight, displayName);
    }
}