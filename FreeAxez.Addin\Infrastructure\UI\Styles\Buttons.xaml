﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!--Colors-->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="ColorsPalette.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!--Base Button-->
    <Style TargetType="Button"
           x:Key="ButtonBase">
        <Setter Property="Foreground"
                Value="White" />
        <Setter Property="FontSize"
                Value="12" />
        <Setter Property="FontWeight"
                Value="Bold" />
        <Setter Property="Height"
                Value="30" />
    </Style>

    <!--Simple Button-->
    <Style TargetType="Button"
           x:Key="ButtonSimple"
           BasedOn="{StaticResource ButtonBase}">
        <Setter Property="BorderThickness"
                Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="Border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Margin="20 0" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled"
                                 Value="False">
                            <Setter Property="Background"
                                    Value="DarkGray" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button"
           x:Key="ButtonSimpleGray"
           BasedOn="{StaticResource ButtonSimple}">
        <Setter Property="Background"
                Value="{StaticResource Gray500}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Gray600}" />
            </Trigger>
            <Trigger Property="IsPressed"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Gray800}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="Button"
           x:Key="ButtonSimpleLight"
           BasedOn="{StaticResource ButtonSimple}">
        <Setter Property="Foreground"
                Value="Black" />
        <Setter Property="Background"
                Value="{StaticResource Gray100}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Gray200}" />
            </Trigger>
            <Trigger Property="IsPressed"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Gray300}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="Button"
           x:Key="ButtonSimpleRed"
           BasedOn="{StaticResource ButtonSimple}">
        <Setter Property="Background"
                Value="{StaticResource Gray500}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Gray600}" />
            </Trigger>
            <Trigger Property="IsPressed"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Gray700}" />
            </Trigger>
        </Style.Triggers>
    </Style>


    <Style TargetType="Button"
           x:Key="ButtonSimpleBlue"
           BasedOn="{StaticResource ButtonSimple}">
        <Setter Property="Background"
                Value="{StaticResource Blue500}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Blue400}" />
            </Trigger>
            <Trigger Property="IsPressed"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Blue800}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="Button"
           x:Key="ButtonSimpleYellow"
           BasedOn="{StaticResource ButtonSimple}">
        <Setter Property="Background"
                Value="{StaticResource Yellow500}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Yellow600}" />
            </Trigger>
            <Trigger Property="IsPressed"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Yellow800}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="Button"
           x:Key="ButtonSimpleGreen"
           BasedOn="{StaticResource ButtonSimple}">
        <Setter Property="Background"
                Value="{StaticResource Green500}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Green600}" />
            </Trigger>
            <Trigger Property="IsPressed"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Green800}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="Button"
           x:Key="ButtonSimplePurple"
           BasedOn="{StaticResource ButtonSimple}">
        <Setter Property="Background"
                Value="{StaticResource Purple500}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Purple600}" />
            </Trigger>
            <Trigger Property="IsPressed"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Purple800}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--Rounded Button-->
    <Style TargetType="Button"
           x:Key="ButtonRounded"
           BasedOn="{StaticResource ButtonBase}">
        <Setter Property="BorderThickness"
                Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="Border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="15">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--Outlined Button-->
    <Style x:Key="ButtonOutlinedBase"
           TargetType="Button"
           BasedOn="{StaticResource ButtonBase}">
        <Setter Property="Foreground"
                Value="Transparent" />
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="OutlinedBorder"
                            Background="Transparent"
                            BorderThickness="0.5"
                            BorderBrush="{TemplateBinding Foreground}"
                            CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Margin="20,0">
                            <ContentPresenter.Resources>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground"
                                            Value="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}" />
                                </Style>
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Background}"
                                    TargetName="OutlinedBorder" />
                        </Trigger>
                        <Trigger Property="IsPressed"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Background}"
                                    TargetName="OutlinedBorder" />
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                 Value="False">
                            <Setter Property="Foreground"
                                    Value="{StaticResource Gray100}" />
                            <Setter Property="Background"
                                    Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Background}"
                                    TargetName="OutlinedBorder" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ButtonOutlinedGray"
           BasedOn="{StaticResource ButtonOutlinedBase}"
           TargetType="Button">
        <Setter Property="Foreground"
                Value="{StaticResource Gray500}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Gray500}" />
                <Setter Property="Foreground"
                        Value="White" />
            </Trigger>
            <Trigger Property="IsPressed"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Gray700}" />
                <Setter Property="Foreground"
                        Value="White" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="ButtonOutlinedRed"
           BasedOn="{StaticResource ButtonOutlinedBase}"
           TargetType="Button">
        <Setter Property="Foreground"
                Value="{StaticResource Red500}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Red500}" />
                <Setter Property="Foreground"
                        Value="White" />
            </Trigger>
            <Trigger Property="IsPressed"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Red700}" />
                <Setter Property="Foreground"
                        Value="White" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="ButtonOutlinedYellow"
           BasedOn="{StaticResource ButtonOutlinedBase}"
           TargetType="Button">
        <Setter Property="Foreground"
                Value="{StaticResource Yellow500}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Yellow50}" />
            </Trigger>
            <Trigger Property="IsPressed"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Yellow100}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="ButtonOutlinedGreen"
           BasedOn="{StaticResource ButtonOutlinedBase}"
           TargetType="Button">
        <Setter Property="Foreground"
                Value="{StaticResource Green500}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Green500}" />
                <Setter Property="Foreground"
                        Value="White" />
            </Trigger>
            <Trigger Property="IsPressed"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Green700}" />
                <Setter Property="Foreground"
                        Value="White" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="ButtonOutlinedBlue"
           BasedOn="{StaticResource ButtonOutlinedBase}"
           TargetType="Button">
        <Setter Property="Foreground"
                Value="{StaticResource Blue500}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Blue500}" />
                <Setter Property="Foreground"
                        Value="White" />
            </Trigger>
            <Trigger Property="IsPressed"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Blue700}" />
                <Setter Property="Foreground"
                        Value="White" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="ButtonOutlinedPurple"
           BasedOn="{StaticResource ButtonOutlinedBase}"
           TargetType="Button">
        <Setter Property="Foreground"
                Value="{StaticResource Purple500}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Purple500}" />
                <Setter Property="Foreground"
                        Value="White" />
            </Trigger>
            <Trigger Property="IsPressed"
                     Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Purple700}" />
                <Setter Property="Foreground"
                        Value="White" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--Close button-->
    <Style TargetType="Button"
           x:Key="ButtonClose">
        <Setter Property="Width"
                Value="30" />
        <Setter Property="Height"
                Value="30" />
        <Setter Property="FontSize"
                Value="15" />
        <Setter Property="FontWeight"
                Value="SemiBold" />
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="Foreground"
                Value="{StaticResource Gray400}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="Border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="15">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center">
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="{StaticResource Gray100}"
                                    TargetName="Border" />
                            <Setter Property="Foreground"
                                    Value="{StaticResource Gray900}" />
                        </Trigger>
                        <Trigger Property="IsPressed"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="{StaticResource Gray300}"
                                    TargetName="Border" />
                            <Setter Property="Foreground"
                                    Value="White" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button"
           x:Key="ButtonCloseMain">
        <Setter Property="Width"
                Value="40" />
        <Setter Property="Height"
                Value="40" />
        <Setter Property="FontSize"
                Value="15" />
        <Setter Property="Background"
                Value="White" />
        <Setter Property="Foreground"
                Value="DimGray" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="Border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="0,12,0,0">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center">
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="#e57373"
                                    TargetName="Border" />
                            <Setter Property="Foreground"
                                    Value="White" />
                        </Trigger>
                        <Trigger Property="IsPressed"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="#c85a5a"
                                    TargetName="Border" />
                            <Setter Property="Foreground"
                                    Value="White" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button"
           x:Key="ButtonCloseSecond">
        <Setter Property="Width"
                Value="40" />
        <Setter Property="Height"
                Value="30" />
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="Foreground"
                Value="{StaticResource Gray600}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="Border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="0,8,0,0">
                        <Viewbox Width="16" Height="16"
                                 HorizontalAlignment="Center"
                                 VerticalAlignment="Center">
                            <Canvas Width="24" Height="24">
                                <Path x:Name="CloseIcon"
                                      Data="M18.3 5.71a.996.996 0 0 0-1.41 0L12 10.59 7.11 5.7A.996.996 0 1 0 5.7 7.11L10.59 12 5.7 16.89a.996.996 0 1 0 1.41 1.41L12 13.41l4.89 4.89a.996.996 0 1 0 1.41-1.41L13.41 12l4.89-4.89c.38-.38.38-1.02 0-1.4z"
                                      Fill="{TemplateBinding Foreground}" />
                            </Canvas>
                        </Viewbox>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetName="Border"
                                                        Storyboard.TargetProperty="Background.Color"
                                                        To="#FF4444"
                                                        Duration="0:0:0.15"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetName="Border"
                                                        Storyboard.TargetProperty="Background.Color"
                                                        To="Transparent"
                                                        Duration="0:0:0.15"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                            <Setter Property="Fill"
                                    Value="White"
                                    TargetName="CloseIcon" />
                        </Trigger>
                        <Trigger Property="IsPressed"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="#CC3333"
                                    TargetName="Border" />
                            <Setter Property="Fill"
                                    Value="White"
                                    TargetName="CloseIcon" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button"
           x:Key="CloseButtonStyle">
        <Setter Property="Width"
                Value="40" />
        <Setter Property="Height"
                Value="47" />
        <Setter Property="FontSize"
                Value="15" />
        <Setter Property="Background"
                Value="White" />
        <Setter Property="Foreground"
                Value="DimGray" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="Border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="0,12,0,0">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center">
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="#e57373"
                                    TargetName="Border" />
                            <Setter Property="Foreground"
                                    Value="White" />
                        </Trigger>
                        <Trigger Property="IsPressed"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="#c85a5a"
                                    TargetName="Border" />
                            <Setter Property="Foreground"
                                    Value="White" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button"
           x:Key="CloseSmallButtonStyle">
        <Setter Property="Width"
                Value="30" />
        <Setter Property="Height"
                Value="30" />
        <Setter Property="FontSize"
                Value="15" />
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="Foreground"
                Value="DimGray" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="Border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="15">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center">
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="#e57373"
                                    TargetName="Border" />
                            <Setter Property="Foreground"
                                    Value="White" />
                        </Trigger>
                        <Trigger Property="IsPressed"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="#c85a5a"
                                    TargetName="Border" />
                            <Setter Property="Foreground"
                                    Value="White" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button"
           x:Key="CloseExtraSmallButtonStyle">
        <Setter Property="Width"
                Value="20" />
        <Setter Property="Height"
                Value="20" />
        <Setter Property="FontSize"
                Value="10" />
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="Foreground"
                Value="DimGray" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="Border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="15">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center">
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="#e57373"
                                    TargetName="Border" />
                            <Setter Property="Foreground"
                                    Value="White" />
                        </Trigger>
                        <Trigger Property="IsPressed"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="#c85a5a"
                                    TargetName="Border" />
                            <Setter Property="Foreground"
                                    Value="White" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Tab Button Style for History page -->
    <Style TargetType="RadioButton"
           x:Key="TabButtonStyle">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource Gray600}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Padding" Value="15,8"/>
        <Setter Property="Margin" Value="0,0,5,0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="RadioButton">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{StaticResource Gray300}"
                            BorderThickness="0,0,0,2"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource Blue500}"/>
                            <Setter Property="Foreground" Value="{StaticResource Blue500}"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Foreground" Value="{StaticResource Blue400}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="RadioButton"
           x:Key="NavButtonStyle">
        <Setter Property="HorizontalAlignment"
                Value="Center" />
        <Setter Property="VerticalAlignment"
                Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="RadioButton">
                    <Border x:Name="border"
                            Height="70"
                            Width="90"
                            CornerRadius="5"
                            BorderThickness="0">
                        <Border.Background>
                            <SolidColorBrush x:Name="BorderBackground"
                                             Color="#125b8a">
                            </SolidColorBrush>
                        </Border.Background>
                        <Grid>
                            <Border x:Name="Indicator"
                                    Height="35"
                                    Width="3"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    Background="White"
                                    Visibility="Collapsed"
                                    CornerRadius="2" />
                            <ContentPresenter />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter Property="Visibility"
                                    TargetName="Indicator"
                                    Value="Visible" />
                        </Trigger>
                        <EventTrigger RoutedEvent="Border.MouseEnter"
                                      SourceName="border">
                            <BeginStoryboard>
                                <Storyboard>
                                    <ColorAnimation Storyboard.TargetName="BorderBackground"
                                                    Storyboard.TargetProperty="Color"
                                                    From="#125b8a"
                                                    To="#2c6da4"
                                                    Duration="0:0:0.3" />
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                        <EventTrigger RoutedEvent="Border.MouseLeave"
                                      SourceName="border">
                            <BeginStoryboard>
                                <Storyboard>
                                    <ColorAnimation Storyboard.TargetName="BorderBackground"
                                                    Storyboard.TargetProperty="Color"
                                                    From="#2c6da4"
                                                    To="#125b8a"
                                                    Duration="0:0:0.3" />
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                        <Trigger Property="IsChecked"
                                 Value="True">
                            <Setter Property="Background"
                                    TargetName="border"
                                    Value="#0f4a75" />
                            <Setter Property="Visibility"
                                    TargetName="Indicator"
                                    Value="Visible" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="RoundIconButton"
           TargetType="Button">
        <Setter Property="Width"
                Value="30" />
        <Setter Property="Height"
                Value="30" />
        <Setter Property="Foreground"
                Value="{StaticResource Gray500}" />
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="BorderBrush"
                Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="15">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="#e57373" />
                            <Setter Property="Foreground"
                                    Value="White" />
                        </Trigger>
                        <Trigger Property="IsPressed"
                                 Value="True">
                            <Setter Property="Background"
                                    Value="#c85a5a" />
                            <Setter Property="Foreground"
                                    Value="White" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="RadioButtonStyle"
           TargetType="RadioButton">
        <Setter Property="Foreground"
                Value="{StaticResource Gray900}" />
        <Setter Property="FontSize"
                Value="13" />
        <Setter Property="Margin"
                Value="0,2,0,2" />
        <Setter Property="Padding"
                Value="5,2,5,2" />
        <Setter Property="HorizontalContentAlignment"
                Value="Left" />
        <Setter Property="VerticalContentAlignment"
                Value="Center" />
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="RadioButton">
                    <DockPanel VerticalAlignment="Center">
                        <Border Width="18"
                                Height="18"
                                BorderThickness="2"
                                CornerRadius="9"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                Background="{TemplateBinding Background}">
                            <Ellipse Width="10"
                                     Height="10"
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"
                                     Fill="{StaticResource Blue400}"
                                     Visibility="Collapsed"
                                     x:Name="CheckMark" />
                        </Border>
                        <ContentPresenter DockPanel.Dock="Right"
                                          VerticalAlignment="Center"
                                          Margin="5,0,0,0" />
                    </DockPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked"
                                 Value="True">
                            <Setter TargetName="CheckMark"
                                    Property="Visibility"
                                    Value="Visible" />
                            <Setter Property="BorderBrush"
                                    Value="{StaticResource Blue500}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                            <Setter Property="BorderBrush"
                                    Value="{StaticResource Blue400}" />
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                 Value="False">
                            <Setter Property="Foreground"
                                    Value="{StaticResource Gray500}" />
                            <Setter Property="BorderBrush"
                                    Value="{StaticResource Gray500}" />
                            <Setter TargetName="CheckMark"
                                    Property="Fill"
                                    Value="{StaticResource Gray500}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Loading Spinner -->
    <Style x:Key="LoadingSpinner" TargetType="Ellipse">
        <Setter Property="Width" Value="60"/>
        <Setter Property="Height" Value="60"/>
        <Setter Property="Stroke" Value="{StaticResource Blue500}"/>
        <Setter Property="StrokeThickness" Value="4"/>
        <Setter Property="StrokeDashArray" Value="10,10"/>
        <Setter Property="Fill" Value="Transparent"/>
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <RotateTransform/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard RepeatBehavior="Forever">
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                                         From="0" To="360" Duration="0:0:1"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>