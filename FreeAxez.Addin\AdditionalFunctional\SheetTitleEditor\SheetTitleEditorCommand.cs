﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.SheetTitleEditor.Models;
using FreeAxez.Addin.AdditionalFunctional.SheetTitleEditor.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.SheetTitleEditor.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Services;
using System.Text;

namespace FreeAxez.Addin.AdditionalFunctional.SheetTitleEditor
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    public class SheetTitleEditorCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var sheetTitleModels = GetSheetTitleModels()
                .OrderBy(s => s.BrowserPath)
                .ThenBy(s => s.SheetNumber)
                .ToList();

            var viewModel = new SheetTitleEditorViewModel(sheetTitleModels);
            var view = new SheetTitleEditorView();
            view.DataContext = viewModel;
            RevitManager.SetRevitAsWindowOwner(view);
            view.ShowDialog();

            // No changes made or window closed
            if (sheetTitleModels.Any(s => s.IsTitleChanged) == false || view.DialogResult != true)
            {
                if (view.DialogResult == true)
                {
                    MessageWindow.ShowDialog(
                        "No changes were made to the sheet titles.",
                        Infrastructure.UI.Enums.MessageType.Info);
                }
                return Result.Cancelled;
            }

            // Update titles
            var updatedTitles = new List<SheetTitleModel>();
            using (var t = new Transaction(RevitManager.Document, "Update Sheet Titles"))
            {
                t.Start();
                foreach (var sheetTitleModel in sheetTitleModels)
                {
                    if (sheetTitleModel.IsTitleChanged && sheetTitleModel.UpdateSheetTitleInModel())
                    {
                        updatedTitles.Add(sheetTitleModel);
                    }
                }
                t.Commit();
            }

            // Check if any titles were updated
            if (updatedTitles.Count == 0)
            {
                MessageWindow.ShowDialog(
                    "No sheet titles were updated. Please check the parameters and try again.",
                    Infrastructure.UI.Enums.MessageType.Notify);

                return Result.Cancelled;
            }

            // Prepare report
            var report = new StringBuilder();
            report.AppendLine("The following sheet titles were updated:");
            foreach (var title in updatedTitles)
            {
                report.AppendLine($"{title.SheetNumber} - {title.SheetName}\n`{title.SheetTitle}`\n");
            }

            MessageWindow.ShowDialog(
                report.ToString(),
                Infrastructure.UI.Enums.MessageType.Success);

            return Result.Succeeded;
        }

        private List<SheetTitleModel> GetSheetTitleModels()
        {
            var sheetTitleModels = new List<SheetTitleModel>();

            var sheets = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsNotElementType()
                .OfClass(typeof(ViewSheet))
                .Cast<ViewSheet>()
                .ToList();

            foreach (var sheet in sheets)
            {
                var model = new SheetTitleModel(sheet);
                sheetTitleModels.Add(model);
            }

            return sheetTitleModels;
        }
    }
}
