﻿using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Utils;
using FreeAxez.Addin.Models;
using FreeAxez.Addin.Models.Base;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Utils
{
    public class GriddProductCollector
    {
        private static Dictionary<Type, string> _optionalProductNameByType = new Dictionary<Type, string>()
        {
            { typeof(FloorBox), "Field Wired" },
            { typeof(Grommet), "Grommet" },
            { typeof(CableSleeve), "Cable Sleeve" },
            { typeof(WallBoot), "In-Wall Boot" },
            { typeof(LadderBoot), "Ladder Boot" },
            { typeof(JunctionBox), "Junction Box" },
            { typeof(PowerVoiceDataFloorBox), "Power Voice Data Floor Box" },
            { typeof(OutletCover), "Outlet Cover" },
            { typeof(BaseUnitOutlet), "Base Unit with Grommet" },
            { typeof(PlateCornerOutlet), "Corner Plate with Grommet" },
            { typeof(PlateChannelOutlet), "Channel Plate with Grommet" },
            { typeof(OutletCoverPlate), "Outlet Cover Plate" },
        };

        public static List<string> GetOptionalProductNames()
        {
            var output = new List<string>();

            var products = new List<Product>();
            products.AddRange(FloorBox.CollectFieldWired());
            products.AddRange(Grommet.Collect());
            products.AddRange(CableSleeve.Collect());
            products.AddRange(WallBoot.Collect());
            products.AddRange(LadderBoot.Collect());
            products.AddRange(JunctionBox.Collect());
            products.AddRange(PowerVoiceDataFloorBox.Collect());
            products.AddRange(OutletCover.Collect());
            products.AddRange(BaseUnitOutlet.Collect());
            products.AddRange(PlateCornerOutlet.Collect());
            products.AddRange(PlateChannelOutlet.Collect());
            products.AddRange(OutletCoverPlate.Collect());

            products = products.Where(p => GlobalBOMProductFilter.PassesFilter(p.Element)).ToList();
            foreach (var product in products)
            {
                if (_optionalProductNameByType.ContainsKey(product.GetType()))
                {
                    output.Add(_optionalProductNameByType[product.GetType()]);
                }
            }

            output.Sort();

            return output.Distinct().ToList();
        }

        public static List<Product> Collect(List<string> productNames = null)
        {
            var output = new List<Product>();

            output.AddRange(AirVent.Collect());
            output.AddRange(BaseUnit.Collect());
            output.AddRange(BaseUnitCutout.Collect());
            output.AddRange(BaseUnitHalfHighCapacity.Collect());
            output.AddRange(BaseUnitHighCapacity.Collect());
            output.AddRange(BaseUnitHeavyDuty.Collect());
            output.AddRange(BaseUnitOutlet.Collect());
            output.AddRange(BorderEndCover.Collect());
            output.AddRange(BorderEndCoverLong.Collect());
            output.AddRange(BorderL.Collect());
            output.AddRange(BorderReinforcingBand.Collect());
            output.AddRange(CableSleeve.Collect());
            output.AddRange(Curb.Collect());
            output.AddRange(CurbCorner.Collect());
            output.AddRange(FloorBox.CollectFieldWired()); // NOT ALL FB
            output.AddRange(FrameSingle.Collect());
            output.AddRange(FrameAnchor.Collect());
            output.AddRange(FrameCornerLong.Collect());
            output.AddRange(FrameCornerShort.Collect());
            output.AddRange(FrameEndCap.Collect());
            output.AddRange(Grommet.Collect());
            output.AddRange(HalfBaseUnit.Collect());
            output.AddRange(JunctionBox.Collect());
            output.AddRange(LadderBoot.Collect());
            output.AddRange(OutletCover.Collect());
            output.AddRange(OutletCoverPlate.Collect());
            output.AddRange(PlateChannel.Collect());
            output.AddRange(PlateChannelHalf.Collect());
            output.AddRange(PlateChannelHalfReinforced.Collect());
            output.AddRange(PlateChannelOutlet.Collect());
            output.AddRange(PlateChannelReinforced.Collect());
            output.AddRange(PlateCorner.Collect());
            output.AddRange(PlateCornerOutlet.Collect());
            output.AddRange(PlateCornerReinforced.Collect());
            output.AddRange(PlateHighCapacity.Collect());
            output.AddRange(PlateLargeHighCapacity.Collect());
            output.AddRange(PlateTrapezoid.Collect());
            output.AddRange(PlateTrapezoidL.Collect());
            output.AddRange(PowerVoiceDataFloorBox.Collect());
            output.AddRange(QuarterBaseUnit.Collect());
            output.AddRange(TransitionRamp.Collect());
            output.AddRange(WallBoot.Collect());
            output.AddRange(UndersheetRoll.Collect());

            if (productNames != null)
            {
                output = output
                    .Where(p =>
                    {
                        var productType = p.GetType();
                        var inOptionalProductList = _optionalProductNameByType.ContainsKey(productType);
                        var inSelectedProductList = inOptionalProductList && productNames.Contains(_optionalProductNameByType[productType]);
                        return !inOptionalProductList || inSelectedProductList;
                    })
                    .ToList();
            }

            output = output.Where(p => GlobalBOMProductFilter.PassesFilter(p.Element)).ToList();

            return output;
        }
    }
}
