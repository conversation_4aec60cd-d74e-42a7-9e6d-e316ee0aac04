using System;
using System.Collections.Generic;
using System.Linq;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;


namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Processing.Geometry
{
    /// <summary>
    /// Service for grouping border lines into rectangles
    /// Assumes all borders are already closed rectangles made of 4 connected lines
    /// </summary>
    public static class BorderGroupingService
    {
        /// <summary>
        /// Main method: finds all border rectangles by grouping connected lines
        /// </summary>
        public static List<List<LineSegmentData>> FindBorderRectangles(List<LineSegmentData> segments)
        {
            if (!segments.Any()) return new List<List<LineSegmentData>>();

            var result = new List<List<LineSegmentData>>();
            var usedSegments = new HashSet<int>();

            System.Console.WriteLine($"[BorderGroupingService] Processing {segments.Count} segments");
            System.Console.WriteLine($"[BorderGroupingService] Segment indices: [{string.Join(", ", segments.Select(s => s.Index))}]");

            foreach (var segment in segments)
            {
                if (usedSegments.Contains(segment.Index))
                {
                    System.Console.WriteLine($"[BorderGroupingService] Skipping already used segment {segment.Index}");
                    continue;
                }

                // Try to build a rectangle starting from this segment
                var rectangle = BuildRectangleFromSegment(segment, segments, usedSegments);
                if (rectangle != null && rectangle.Count == 4)
                {
                    result.Add(rectangle);
                    System.Console.WriteLine($"[BorderGroupingService] Found rectangle #{result.Count} with segments: [{string.Join(", ", rectangle.Select(s => s.Index))}]");

                    // Mark all segments as used
                    foreach (var seg in rectangle)
                    {
                        usedSegments.Add(seg.Index);
                    }
                }
            }

            System.Console.WriteLine($"[BorderGroupingService] Found {result.Count} rectangles total");

            // Deduplicate rectangles by geometry (center + dimensions)
            var uniqueRectangles = DeduplicateRectangles(result);
            System.Console.WriteLine($"[BorderGroupingService] After deduplication: {uniqueRectangles.Count} unique rectangles");

            return uniqueRectangles;
        }

        /// <summary>
        /// Build a rectangle by finding 4 connected lines starting from given segment
        /// </summary>
        private static List<LineSegmentData> BuildRectangleFromSegment(
            LineSegmentData startSegment,
            List<LineSegmentData> allSegments,
            HashSet<int> usedSegments)
        {
            var rectangle = new List<LineSegmentData> { startSegment };
            var currentSegment = startSegment;
            const double tolerance = 0.01; // 0.01 inch tolerance for connection

            // Try to find 3 more connected segments to complete the rectangle
            for (int i = 0; i < 3; i++)
            {
                var nextSegment = FindConnectedSegment(currentSegment, allSegments, usedSegments, tolerance);
                if (nextSegment == null) return null; // Not a complete rectangle

                rectangle.Add(nextSegment);
                currentSegment = nextSegment;
            }

            // Verify the last segment connects back to the first
            if (IsConnected(currentSegment, startSegment, tolerance))
            {
                // Check if this is a perfect rectangle or needs correction
                if (IsRectangle(rectangle))
                {
                    return rectangle; // Perfect rectangle - use original lines
                }
                else
                {
                    // Trapezoid - convert to minimum bounding rectangle
                    return ConvertTrapezoidToRectangle(rectangle);
                }
            }

            return null; // Not a closed rectangle
        }

        /// <summary>
        /// Find a segment that connects to the end of the current segment
        /// </summary>
        private static LineSegmentData FindConnectedSegment(
            LineSegmentData currentSegment,
            List<LineSegmentData> allSegments,
            HashSet<int> usedSegments,
            double tolerance)
        {
            var currentEnd = new Point2D(currentSegment.Data.endPoint.x, currentSegment.Data.endPoint.y);

            foreach (var segment in allSegments)
            {
                if (usedSegments.Contains(segment.Index)) continue;
                if (segment.Index == currentSegment.Index) continue;

                var segStart = new Point2D(segment.Data.startPoint.x, segment.Data.startPoint.y);
                var segEnd = new Point2D(segment.Data.endPoint.x, segment.Data.endPoint.y);

                // Check if this segment starts where current segment ends
                if (IsPointNear(currentEnd, segStart, tolerance) || IsPointNear(currentEnd, segEnd, tolerance))
                {
                    return segment;
                }
            }

            return null;
        }

        /// <summary>
        /// Check if two segments are connected (share an endpoint)
        /// </summary>
        private static bool IsConnected(LineSegmentData seg1, LineSegmentData seg2, double tolerance)
        {
            var seg1Start = new Point2D(seg1.Data.startPoint.x, seg1.Data.startPoint.y);
            var seg1End = new Point2D(seg1.Data.endPoint.x, seg1.Data.endPoint.y);
            var seg2Start = new Point2D(seg2.Data.startPoint.x, seg2.Data.startPoint.y);
            var seg2End = new Point2D(seg2.Data.endPoint.x, seg2.Data.endPoint.y);

            return IsPointNear(seg1End, seg2Start, tolerance) ||
                   IsPointNear(seg1End, seg2End, tolerance) ||
                   IsPointNear(seg1Start, seg2Start, tolerance) ||
                   IsPointNear(seg1Start, seg2End, tolerance);
        }

        /// <summary>
        /// Check if two points are near each other within tolerance
        /// </summary>
        private static bool IsPointNear(Point2D p1, Point2D p2, double tolerance)
        {
            return Math.Abs(p1.X - p2.X) <= tolerance && Math.Abs(p1.Y - p2.Y) <= tolerance;
        }

        /// <summary>
        /// Check if 4 connected lines form a perfect rectangle
        /// </summary>
        private static bool IsRectangle(List<LineSegmentData> segments, double tolerance = 0.1)
        {
            if (segments.Count != 4) return false;

            // Calculate direction vectors for each side
            var directions = new List<Vector2D>();
            foreach (var seg in segments)
            {
                var dx = seg.Data.endPoint.x - seg.Data.startPoint.x;
                var dy = seg.Data.endPoint.y - seg.Data.startPoint.y;
                var length = Math.Sqrt(dx * dx + dy * dy);
                if (length > 0)
                {
                    directions.Add(new Vector2D(dx / length, dy / length));
                }
            }

            if (directions.Count != 4) return false;

            // Check if opposite sides are parallel and adjacent sides are perpendicular
            bool parallel01 = Math.Abs(Vector2D.Dot(directions[0], directions[2])) > 1 - tolerance;
            bool parallel12 = Math.Abs(Vector2D.Dot(directions[1], directions[3])) > 1 - tolerance;
            bool orthogonal = Math.Abs(Vector2D.Dot(directions[0], directions[1])) < tolerance;

            return parallel01 && parallel12 && orthogonal;
        }

        /// <summary>
        /// Convert trapezoid to minimum bounding rectangle using NetTopologySuite
        /// </summary>
        private static List<LineSegmentData> ConvertTrapezoidToRectangle(List<LineSegmentData> trapezoidSegments)
        {
            try
            {
                // Use NetTopologySuite only for trapezoid correction
                var gf = NetTopologySuite.NtsGeometryServices.Instance.CreateGeometryFactory();

                var coordinates = new List<NetTopologySuite.Geometries.Coordinate>();
                foreach (var seg in trapezoidSegments)
                {
                    coordinates.Add(new NetTopologySuite.Geometries.Coordinate(seg.Data.startPoint.x, seg.Data.startPoint.y));
                }
                // Close the polygon
                coordinates.Add(coordinates[0]);

                var polygon = gf.CreatePolygon(coordinates.ToArray());
                var minRect = new NetTopologySuite.Algorithm.MinimumDiameter(polygon).GetMinimumRectangle();

                // Convert back to LineSegmentData with corrected coordinates
                return PolygonToSegments((NetTopologySuite.Geometries.Polygon)minRect, trapezoidSegments[0].Data.layer);
            }
            catch
            {
                // If conversion fails, return original segments
                return trapezoidSegments;
            }
        }

        /// <summary>
        /// Convert NetTopologySuite polygon to LineSegmentData list
        /// </summary>
        private static List<LineSegmentData> PolygonToSegments(NetTopologySuite.Geometries.Polygon polygon, string originalLayer)
        {
            var segments = new List<LineSegmentData>();
            var coords = polygon.ExteriorRing.Coordinates;

            // Use negative indices to avoid conflicts with existing segments
            // This ensures corrected trapezoid segments don't interfere with original indexing
            for (int i = 0; i < 4; i++)
            {
                var start = coords[i];
                var end = coords[i + 1];
                var length = Math.Sqrt(Math.Pow(end.X - start.X, 2) + Math.Pow(end.Y - start.Y, 2));

                segments.Add(new LineSegmentData
                {
                    Index = -(i + 1000), // Use large negative indices to avoid conflicts
                    Data = new JsonLineData
                    {
                        startPoint = new JsonPoint3D { x = start.X, y = start.Y },
                        endPoint = new JsonPoint3D { x = end.X, y = end.Y },
                        length = length,
                        layer = originalLayer + "-CORRECTED" // Mark as corrected trapezoid
                    },
                    Segment = new NetTopologySuite.Geometries.LineSegment(start, end),
                    IsPrimaryEdge = true
                });
            }

            return segments;
        }

        /// <summary>
        /// Simple 2D point structure
        /// </summary>
        private struct Point2D
        {
            public double X { get; }
            public double Y { get; }

            public Point2D(double x, double y)
            {
                X = x;
                Y = y;
            }
        }

        /// <summary>
        /// Simple 2D vector structure
        /// </summary>
        private struct Vector2D
        {
            public double X { get; }
            public double Y { get; }

            public Vector2D(double x, double y)
            {
                X = x;
                Y = y;
            }

            public static double Dot(Vector2D a, Vector2D b)
            {
                return a.X * b.X + a.Y * b.Y;
            }
        }

        /// <summary>
        /// Remove duplicate rectangles based on center + dimensions
        /// </summary>
        private static List<List<LineSegmentData>> DeduplicateRectangles(List<List<LineSegmentData>> rectangles)
        {
            const double tolerance = 0.01; // 0.01 inch tolerance
            var unique = new List<List<LineSegmentData>>();

            foreach (var rect in rectangles)
            {
                if (!IsRectangleDuplicate(rect, unique, tolerance))
                {
                    // Reassign indices starting from 1
                    var reindexedRect = ReassignIndices(rect, unique.Count);
                    unique.Add(reindexedRect);
                }
            }

            return unique;
        }

        /// <summary>
        /// Check if rectangle is duplicate of any in the unique list
        /// </summary>
        private static bool IsRectangleDuplicate(List<LineSegmentData> rectangle, List<List<LineSegmentData>> uniqueRectangles, double tolerance)
        {
            var rectInfo = GetRectangleInfo(rectangle);

            foreach (var uniqueRect in uniqueRectangles)
            {
                var uniqueInfo = GetRectangleInfo(uniqueRect);

                // Compare center + dimensions
                if (Math.Abs(rectInfo.CenterX - uniqueInfo.CenterX) <= tolerance &&
                    Math.Abs(rectInfo.CenterY - uniqueInfo.CenterY) <= tolerance &&
                    Math.Abs(rectInfo.Width - uniqueInfo.Width) <= tolerance &&
                    Math.Abs(rectInfo.Length - uniqueInfo.Length) <= tolerance)
                {
                    return true; // Duplicate found
                }
            }

            return false; // Not a duplicate
        }

        /// <summary>
        /// Get rectangle center and dimensions
        /// </summary>
        private static (double CenterX, double CenterY, double Width, double Length) GetRectangleInfo(List<LineSegmentData> rectangle)
        {
            // Get all unique points
            var points = rectangle.SelectMany(seg => new[]
            {
                new { X = seg.Data.startPoint.x, Y = seg.Data.startPoint.y },
                new { X = seg.Data.endPoint.x, Y = seg.Data.endPoint.y }
            }).Distinct().ToList();

            // Calculate center
            var centerX = points.Average(p => p.X);
            var centerY = points.Average(p => p.Y);

            // Calculate dimensions (min/max lengths)
            var lengths = rectangle.Select(seg => seg.Data.length).ToList();
            var width = lengths.Min();
            var length = lengths.Max();

            return (centerX, centerY, width, length);
        }

        /// <summary>
        /// Reassign indices to rectangle segments starting from base index
        /// </summary>
        private static List<LineSegmentData> ReassignIndices(List<LineSegmentData> rectangle, int baseIndex)
        {
            var reindexed = new List<LineSegmentData>();

            for (int i = 0; i < rectangle.Count; i++)
            {
                var seg = rectangle[i];
                var newSeg = new LineSegmentData
                {
                    Index = baseIndex * 4 + i + 1, // Start from 1, not 0
                    Data = seg.Data,
                    Segment = seg.Segment,
                    IsPrimaryEdge = seg.IsPrimaryEdge
                };
                reindexed.Add(newSeg);
            }

            return reindexed;
        }
    }
}
