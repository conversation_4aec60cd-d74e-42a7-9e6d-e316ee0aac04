﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Views.SendRequestView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
        mc:Ignorable="d" 
        Title="Send Update Request"
        Height="150" Width="400"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize"
        Closed="Window_Closed">
    
    <Window.Resources>
        <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/AdditionalFunctional/SmartsheetTaskManager/Styles/Styles.xaml"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <TextBlock Text="{Binding RequestText}"
               TextWrapping="WrapWithOverflow"
               Grid.Row="0" Margin="10 10 5 5"/>

        <StackPanel Orientation="Horizontal"
                HorizontalAlignment="Right"
                VerticalAlignment="Bottom"
                Grid.Row="1">
            <Button Content="Ok"
                Command="{Binding OkCommand}"
                Style="{StaticResource CommonButtonStyle}"
                CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor,
                                    AncestorType={x:Type Window}}}"
                Width="60" Height="25"
                Margin="0 0 5 10"/>
            <Button Content="Cancel"
                Command="{Binding CancelCommand}"
                CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor,
                                    AncestorType={x:Type Window}}}"
                Style="{StaticResource CommonButtonStyle}"
                Width="60" Height="25"
                Margin="5 0 10 10"/>
        </StackPanel>

    </Grid>
</Window>
