using System.Windows.Media.Imaging;
using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Details
{
    /// <summary>
    /// Type of detail view
    /// </summary>
    public enum DetailViewType
    {
        DraftingView
    }

    /// <summary>
    /// ViewModel for a single detail view
    /// </summary>
    public class DetailViewModel : BaseViewModel
    {
        private bool _isSelected;
        private bool _isEnabled = true;
        private BitmapSource _previewImage;

        public ElementId ElementId { get; set; }
        public string Name { get; set; }
        public DetailViewType ViewType { get; set; }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                if (_isEnabled != value)
                {
                    _isEnabled = value;
                    OnPropertyChanged();
                }
            }
        }

        public BitmapSource PreviewImage
        {
            get => _previewImage;
            set
            {
                if (_previewImage != value)
                {
                    _previewImage = value;
                    OnPropertyChanged();
                }
            }
        }

        public string TypeDisplayName
        {
            get
            {
                return ViewType switch
                {
                    DetailViewType.DraftingView => "Drafting View",
                    _ => "Unknown"
                };
            }
        }
    }
}
