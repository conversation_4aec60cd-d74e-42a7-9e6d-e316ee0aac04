﻿using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.Ramp.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.Ramp.Models
{
    public class RampLine
    {
        public List<XYZ> Locations { get; set; }
        public XYZ Direction { get; set; }
        public XYZ RampComponentDirection { get; set; }
        public bool IsLeftSideSlope { get; set; }
        public bool IsRightSideSlope { get; set; }
        public RampAngleType AngleTypeLeft { get; set; }
        public RampAngleType AngleTypeRight { get; set; }
        public double AngleLeft { get; set; }
        public double AngleRight { get; set; }
        public double EndMiddleRampComponentWidth { get; set; }

        public static RampLine Create(List<XYZ> locations,
                                      XYZ direction,
                                      XYZ rampComponentDirection,
                                      bool isLeftSideSlope,
                                      bool isRightSideSlope,
                                      RampAngleType angleTypeLeft,
                                      RampAngleType angleTypeRight,
                                      double angleLeft,
                                      double angleRight,
                                      double endMiddleRampComponentWidth)
        {
            return new RampLine
            {
                Locations = locations,
                Direction = direction,
                RampComponentDirection = rampComponentDirection,
                IsLeftSideSlope = isLeftSideSlope,
                IsRightSideSlope = isRightSideSlope,
                AngleTypeLeft = angleTypeLeft,
                AngleTypeRight = angleTypeRight,
                AngleLeft = angleLeft,
                AngleRight = angleRight,
                EndMiddleRampComponentWidth = endMiddleRampComponentWidth
            };
        }

        public static List<XYZ> OrderPointsByCurveDirection(Curve curve, List<XYZ> points)
        {
            var lineDirection = (curve as Line).Direction;

            List<XYZ> sortedPoints = new List<XYZ>();

            if (lineDirection.X >= 0 && lineDirection.Y >= 0)
            {
                sortedPoints = points.OrderBy(p => Math.Round(p.X, 2))
                    .ThenBy(p => Math.Round(p.Y, 2)).ToList();
            }
            else if (lineDirection.X >= 0 && lineDirection.Y <= 0)
            {
                sortedPoints = points.OrderBy(p => Math.Round(p.X, 2))
                    .ThenByDescending(p => Math.Round(p.Y, 2)).ToList();
            }
            else if (lineDirection.X <= 0 && lineDirection.Y <= 0)
            {
                sortedPoints = points.OrderByDescending(p => Math.Round(p.X, 2))
                    .ThenByDescending(p => Math.Round(p.Y, 2)).ToList();
            }
            else
            {
                sortedPoints = points.OrderByDescending(p => Math.Round(p.X, 2))
                    .ThenBy(p => Math.Round(p.Y, 2)).ToList();
            }

            return sortedPoints;
        }

        public static List<List<CurveElement>> Group(List<CurveElement> curveElements)
        {
            var groups = new List<List<CurveElement>>();
            List<CurveElement> currentGroup = new List<CurveElement>();

            for (int i = 0; i < curveElements.Count; i++)
            {
                var currentCurveElement = curveElements[i];

                if (currentGroup.Count == 0)
                {
                    currentGroup.Add(currentCurveElement);
                }
                else
                {
                    var previousCurveElement = currentGroup.Last();

                    var comparisonResult = currentCurveElement.GeometryCurve
                        .Intersect(previousCurveElement.GeometryCurve);

                    if (comparisonResult == SetComparisonResult.Overlap)
                    {
                        currentGroup.Add(currentCurveElement);
                    }
                    else
                    {
                        groups.Add(currentGroup);
                        currentGroup = new List<CurveElement> { currentCurveElement };
                    }
                }
            }

            if (currentGroup.Count > 0)
            {
                groups.Add(currentGroup);
            }

            return groups;
        }

        public static List<Curve> CorrectCurveOrder(List<Curve> curves)
        {
            if (curves.Count == 1) return curves;

            var correctOrder = new List<Curve>();

            foreach (var curve in curves)
            {
                var countOfIntersection = 0;
                foreach (var nextCurve in curves)
                {
                    if (curve == nextCurve)
                    {
                        continue;
                    }

                    var result = curve.Intersect(nextCurve);
                    if (result == SetComparisonResult.Subset || result == SetComparisonResult.Overlap)
                    {
                        countOfIntersection++;
                    }
                }
                if (countOfIntersection == 1)
                {
                    correctOrder.Add(curve);
                    break;
                }
            }

            for (int i = 0; i < curves.Count - 1; i++)
            {
                var nextLine = curves
                    .Where(c => correctOrder.Last().Intersect(c) == SetComparisonResult.Subset
                             || correctOrder.Last().Intersect(c) == SetComparisonResult.Overlap)
                    .Where(c => !correctOrder.Contains(c))
                    .First();

                correctOrder.Add(nextLine);
            }

            return correctOrder;
        }

        public static List<Curve> CorrectCurveDirections(List<Curve> curves)
        {
            if (curves.Count == 1) return curves;

            var output = new List<Curve>();

            var firstStart = curves[0].GetEndPoint(0);
            var nextStart = curves[1].GetEndPoint(0);
            var nextEnd = curves[1].GetEndPoint(1);
            if (firstStart.IsAlmostEqualTo(nextStart) || firstStart.IsAlmostEqualTo(nextEnd))
            {
                var revercedCurve = Line.CreateBound(curves[0].GetEndPoint(index: 1), curves[0].GetEndPoint(0));
                output.Add(revercedCurve);
            }
            else
            {
                output.Add(curves[0]);
            }

            for (int i = 1; i < curves.Count; i++)
            {
                var previousEnd = output.Last().GetEndPoint(1);
                var currentStart = curves[i].GetEndPoint(0);
                if (previousEnd.IsAlmostEqualTo(currentStart))
                {
                    output.Add(curves[i]);
                }
                else
                {
                    var revercedCurve = Line.CreateBound(curves[i].GetEndPoint(1), curves[i].GetEndPoint(0));
                    output.Add(revercedCurve);
                }
            }

            return output;
        }
    }
}