﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using FreeAxez.Addin.Models;
using System.IO;
using System.Text;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.WhipExport.ViewModels
{
    public class WhipExportViewModels : WindowViewModel
    {
        public WhipExportViewModels()
        {
            Whips = CollectAndValidateWhips();

            ExportCommand = new RelayCommand(OnExportCommandExecute);
            IdCommand = new RelayCommand(OnIdCommandExecute);
            CloseCommand = new RelayCommand(OnCloseCommandExecute);
        }

        public List<WhipViewModel> Whips { get; set; }

        public ICommand ExportCommand { get; set; }
        private void OnExportCommandExecute(object p)
        {
            (p as Window).Close();

            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Title = "Export Report";
            saveFileDialog.FileName = 
                $"WhipExportParameters_FromProject_{RevitManager.Document.Title}_{DateTime.Now.ToString("yyMMdd_HH-mm")}.csv";
            saveFileDialog.Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*";

            var report = CreateReport(Whips);
            if (saveFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                File.WriteAllText(saveFileDialog.FileName, report);
            }
        }

        public ICommand IdCommand { get; set; }
        private void OnIdCommandExecute(object p)
        {
            if (int.TryParse(p.ToString(), out int id))
            {
                if (id != 0 && RevitManager.Document.GetElement(new ElementId(id)) != null)
                {
                    var elementIds = new List<ElementId>() { new ElementId(id) };
                    RevitManager.UIDocument.Selection.SetElementIds(elementIds);
                    RevitManager.UIDocument.ShowElements(elementIds);
                }
            }
        }

        public ICommand CloseCommand { get; set; }
        private void OnCloseCommandExecute(object p)
        {
            (p as Window).Close();
        }


        private List<WhipViewModel> CollectAndValidateWhips()
        {
            var whips = Whip.CollectFloorBoxWhips().Select(w => new WhipViewModel(w.Element)).ToList();
            whips = whips
                .OrderBy(w => w.CorrectTypeLength) // True or False (False first)
                .ThenBy(w => w.Level)
                .ThenBy(w => w.Id)
                .ToList();

            return whips;
        }

        private string CreateReport(List<WhipViewModel> whips)
        {
            StringBuilder output = new StringBuilder();

            output.AppendLine(string.Join(",",
                nameof(WhipViewModel.Id),
                nameof(WhipViewModel.Level),
                nameof(WhipViewModel.TrackAssignment),
                nameof(WhipViewModel.TypeName),
                nameof(WhipViewModel.ProjectionLength),
                nameof(WhipViewModel.FullLength),
                nameof(WhipViewModel.TypeLength),
                nameof(WhipViewModel.CorrectTypeLength)));

            foreach (var whip in whips)
            {
                output.AppendLine(string.Join(",",
                    whip.Id,
                    whip.Level,
                    whip.TrackAssignment,
                    whip.TypeName,
                    whip.ProjectionLength,
                    whip.FullLength,
                    whip.TypeLength,
                    whip.CorrectTypeLength));
            }

            return output.ToString();
        }
    }
}
