﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Controls">

    <Style TargetType="{x:Type controls:LoadingSpinner}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type controls:LoadingSpinner}">
                    <ControlTemplate.Resources>
                        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
                    </ControlTemplate.Resources>
                    <Ellipse Width="100"
                             Height="100"
                             RenderTransformOrigin="0.5 0.5"
                             Stroke="#3576BA"
                             StrokeDashArray="58 78"
                             StrokeThickness="3"
                             Visibility="{TemplateBinding IsExecuting,
                                Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Ellipse.RenderTransform>
                            <RotateTransform x:Name="Rotation" Angle="0"/>
                        </Ellipse.RenderTransform>
                        <Ellipse.Triggers>
                            <EventTrigger RoutedEvent="Loaded">
                                <BeginStoryboard>
                                    <Storyboard RepeatBehavior="Forever">
                                        <DoubleAnimation Storyboard.TargetName="Rotation"
                                                         Storyboard.TargetProperty="Angle"
                                                         From="0" To="360"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </EventTrigger>
                        </Ellipse.Triggers>
                    </Ellipse>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>