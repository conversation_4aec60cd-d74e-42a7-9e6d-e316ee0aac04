﻿using System.Windows;
using System.Windows.Input;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages;

public class BasePageVm : BaseViewModel
{
    private string _searchText;
    protected readonly LibraryDataLoader DataLoader;
    protected readonly LibraryItemFilter ItemFilter;

    public BasePageVm()
    {
        DataLoader = new LibraryDataLoader();
        ItemFilter = new LibraryItemFilter();
        CloseMainWindowCommand = new RelayCommand(OnCloseMainWindowCommandExecute);
    }
    public ICommand CloseMainWindowCommand { get; set; }

    public string SearchText
    {
        get => _searchText;
        set
        {
            _searchText = value;
            OnPropertyChanged();
            Filter();
        }
    }
    private void OnCloseMainWindowCommandExecute(object p)
    {
        (p as Window)?.Close();
    }

    protected virtual void Filter()
    {
    }

}