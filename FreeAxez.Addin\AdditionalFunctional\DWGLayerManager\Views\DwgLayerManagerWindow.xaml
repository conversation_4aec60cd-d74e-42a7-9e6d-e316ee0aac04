<Window x:Class="FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views.DwgLayerManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:converters="clr-namespace:FreeAxez.Addin.Infrastructure.Converters"
        Title="DWG Layer Manager"
        Height="700"
        Width="1200"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/Icons.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
            <converters:ColorToBrushConverter x:Key="ColorToBrushConverter"/>
            <converters:LayerNameToEnabledConverter x:Key="LayerNameToEnabledConverter"/>
            <converters:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
            <converters:NullableGuidConverter x:Key="NullableGuidConverter"/>
            <converters:ExcludeCurrentLayerConverter x:Key="ExcludeCurrentLayerConverter"/>
        </ResourceDictionary>
    </Window.Resources>
    
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <Grid Grid.Row="0" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0"
                       Text="DWG File:"
                       VerticalAlignment="Center"
                       Margin="0,0,10,0"/>

            <TextBox Grid.Column="1"
                     Text="{Binding DwgMappingVM.SelectedDwgFilePath, UpdateSourceTrigger=PropertyChanged}"
                     Tag="Select DWG File For Management"
                     Style="{StaticResource UiTextBox}"
                     VerticalContentAlignment="Center"
                     TextWrapping="NoWrap"
                     Height="30"/>

            <Button Grid.Column="2"
                    Content="Browse..."
                    Command="{Binding DwgMappingVM.SelectDwgFileCommand}"
                    Style="{StaticResource ButtonOutlinedBlue}"
                    Margin="10,0,0,0"/>
        </Grid>

        <!-- AutoCAD Version Selection -->
        <Grid Grid.Row="1" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0"
                       Text="AutoCAD Version:"
                       VerticalAlignment="Center"
                       Margin="0,0,10,0"/>

            <ComboBox Grid.Column="1"
                      ItemsSource="{Binding AvailableAutoCADVersions}"
                      SelectedItem="{Binding SelectedAutoCADVersion}"
                      Style="{StaticResource Combobox}"
                      VerticalContentAlignment="Center"/>
        </Grid>

        <!-- Search -->
        <TextBox Grid.Row="2"
                 Text="{Binding DwgMappingVM.SearchText, UpdateSourceTrigger=PropertyChanged}"
                 Style="{StaticResource Search}"
                 Tag="Search Layers"
                 Height="30"
                 Margin="0,0,0,10">
        </TextBox>



        <!-- Content Area -->
        <Grid Grid.Row="4">
            <!-- Message when no DWG file selected -->
            <TextBlock Text="Select DWG File For Management"
                       Style="{StaticResource TextH2}"
                       Foreground="{StaticResource Blue500}"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Visibility="{Binding IsDwgFileSelected, Converter={StaticResource InverseBoolToVisibilityConverter}}"/>

            <!-- Data Grid when DWG file is selected -->
            <DataGrid Style="{DynamicResource DataGridWithoutBorders}"
                      ColumnHeaderStyle="{DynamicResource DataGridColumnHeader}"
                      ItemsSource="{Binding DwgMappingVM.FilteredDwgLayers}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      CanUserReorderColumns="False"
                      CanUserResizeRows="False"
                      HeadersVisibility="Column"
                      SelectionMode="Single"
                      Margin="0,0,0,10"
                      Visibility="{Binding IsDwgFileSelected, Converter={StaticResource BoolToVisibilityConverter}}">
            <DataGrid.Columns>
                <DataGridTextColumn Header="Layer Name"
                                    Binding="{Binding Name}"
                                    Width="120"
                                    IsReadOnly="True"/>
                <DataGridTemplateColumn Header="Color" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Rectangle Fill="{Binding Color, Converter={StaticResource ColorToBrushConverter}}"
                                       Width="20" Height="20"
                                       Stroke="Gray" StrokeThickness="1"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="Linetype"
                                    Binding="{Binding LinetypeDisplayName}"
                                    Width="120"
                                    IsReadOnly="True"/>
                <DataGridTextColumn Header="Lineweight"
                                    Binding="{Binding LineweightDisplay}"
                                    Width="80"
                                    IsReadOnly="True"/>
                <DataGridTextColumn Header="Transparency"
                                    Binding="{Binding TransparencyDisplay}"
                                    Width="90"
                                    IsReadOnly="True"/>
                <DataGridTextColumn Header="Model Objects"
                                    Binding="{Binding ModelSpaceObjects}"
                                    Width="100"
                                    IsReadOnly="True"/>
                <DataGridTextColumn Header="Paper Objects"
                                    Binding="{Binding PaperSpaceObjects}"
                                    Width="100"
                                    IsReadOnly="True"/>
                <DataGridTextColumn Header="Block Objects"
                                    Binding="{Binding BlockDefinitionObjects}"
                                    Width="100"
                                    IsReadOnly="True"/>
                <DataGridTemplateColumn Header="Purgeable" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Grid HorizontalAlignment="Center" VerticalAlignment="Center">
                                <!-- Trash icon for purgeable layers -->
                                <Viewbox Width="16" Height="16"
                                       Visibility="{Binding Purgeable, Converter={StaticResource BoolToVisibilityConverter}}">
                                    <Canvas Width="512" Height="512">
                                        <Path Data="M167.2 49.7L160 64H64C46.3 64 32 78.3 32 96S46.3 128 64 128H448c17.7 0 32-14.3 32-32s-14.3-32-32-32H352l-7.2-14.3C339.4 38.8 328.3 32 316.2 32H195.8c-12.1 0-23.2 6.8-28.6 17.7zM448 160H64L85.2 499c1.6 25.3 22.6 45 47.9 45H378.9c25.3 0 46.3-19.7 47.9-45L448 160z"
                                              Fill="Red" />
                                    </Canvas>
                                </Viewbox>

                                <!-- Lock icon for non-purgeable layers -->
                                <Viewbox Width="16" Height="16">
                                    <Viewbox.Style>
                                        <Style TargetType="Viewbox">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Purgeable}" Value="False">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Viewbox.Style>
                                    <Canvas Width="512" Height="512">
                                        <Path Data="M144 144v48H304V144c0-44.2-35.8-80-80-80s-80 35.8-80 80zM80 192V144C80 64.5 144.5 0 224 0s144 64.5 144 144v48h16c35.3 0 64 28.7 64 64V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V256c0-35.3 28.7-64 64-64H80z"
                                              Fill="Gray" />
                                    </Canvas>
                                </Viewbox>
                            </Grid>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Header="Merge To FreeAxez Layer" Width="200">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <ComboBox SelectedValue="{Binding MappedLayerId, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource NullableGuidConverter}}"
                                      SelectedValuePath="Id"
                                      DisplayMemberPath="Name"
                                      Style="{StaticResource Combobox}">
                                <ComboBox.ItemsSource>
                                    <MultiBinding Converter="{StaticResource ExcludeCurrentLayerConverter}">
                                        <Binding Path="DataContext.DwgMappingVM.FreeAxezLayers" RelativeSource="{RelativeSource AncestorType=Window}"/>
                                        <Binding Path="Name"/>
                                    </MultiBinding>
                                </ComboBox.ItemsSource>
                            </ComboBox>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
            </DataGrid>
        </Grid>

        <!-- Buttons -->
        <Grid Grid.Row="5" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Command="{Binding OpenLayerSettingsCommand}"
                    Style="{StaticResource ButtonOutlinedBlue}">
                <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource SettingsIcon}" Margin="0,0,8,0"/>
                    <TextBlock Text="FreeAxez Layers" VerticalAlignment="Center"
                               Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                </StackPanel>
            </Button>

            <Button Grid.Column="2"
                    Command="{Binding UpdateLayersFromBackendCommand}"
                    Style="{StaticResource ButtonOutlinedBlue}"
                    IsEnabled="{Binding CanUpdateLayersFromBackend}">
                <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource DownloadIcon}" Margin="0,0,8,0"/>
                    <TextBlock Text="Sync FreeAxez Layers" VerticalAlignment="Center"
                               Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                </StackPanel>
            </Button>

            <Button Grid.Column="4"
                    Command="{Binding DeleteEmptyLayersCommand}"
                    Style="{StaticResource ButtonOutlinedRed}"
                    IsEnabled="{Binding CanPurgeUnused}">
                <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource TrashIcon}" Margin="0,0,8,0"/>
                    <TextBlock Text="Purge Unused" VerticalAlignment="Center"
                               Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                </StackPanel>
            </Button>

            <Button Grid.Column="6"
                    Command="{Binding DwgMappingVM.ExecuteMappingCommand}"
                    Style="{StaticResource ButtonSimpleBlue}">
                <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource MergeIcon}" Margin="0,0,8,0"/>
                    <TextBlock Text="Merge Layers" VerticalAlignment="Center"
                               Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                </StackPanel>
            </Button>
        </Grid>

        <!-- Integrated Progress Overlay -->
        <Border Grid.Row="4"
                Background="White"
                Opacity="0.95"
                Visibility="{Binding DwgMappingVM.IsProcessing, Converter={StaticResource BoolToVisibilityConverter}}">
            <Grid>
                <StackPanel Orientation="Horizontal"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center">

                    <!-- Animated Spinner -->
                    <Border Width="24" Height="24"
                            Margin="0,0,12,0"
                            RenderTransformOrigin="0.5,0.5">
                        <Border.RenderTransform>
                            <RotateTransform x:Name="SpinnerRotate"/>
                        </Border.RenderTransform>
                        <Border.Triggers>
                            <EventTrigger RoutedEvent="Loaded">
                                <BeginStoryboard>
                                    <Storyboard RepeatBehavior="Forever">
                                        <DoubleAnimation Storyboard.TargetName="SpinnerRotate"
                                                       Storyboard.TargetProperty="Angle"
                                                       From="0" To="360" Duration="0:0:1"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </EventTrigger>
                        </Border.Triggers>

                        <!-- Spinner Circle -->
                        <Ellipse Width="24" Height="24"
                                StrokeThickness="3"
                                Stroke="{StaticResource Blue500}"
                                StrokeDashArray="15,5"
                                StrokeStartLineCap="Round"
                                StrokeEndLineCap="Round"/>
                    </Border>

                    <!-- Progress Text -->
                    <StackPanel Orientation="Vertical">
                        <TextBlock Text="{Binding DwgMappingVM.ProgressTitle}"
                                   FontSize="14"
                                   FontWeight="SemiBold"
                                   Foreground="{StaticResource Blue500}"
                                   Margin="0,0,0,2"/>

                        <TextBlock Text="{Binding DwgMappingVM.ProgressText}"
                                   FontSize="12"
                                   Foreground="{StaticResource Blue500}"
                                   Opacity="0.8"/>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
