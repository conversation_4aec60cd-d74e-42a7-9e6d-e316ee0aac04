using System;
using System.IO;
using System.Net;
using System.Threading.Tasks;
using System.Windows;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Details;

/// <summary>
///     Handles the processing of Details content loading
/// </summary>
public static class DetailsContentProcessor
{
    /// <summary>
    ///     Processes Details content loading asynchronously
    /// </summary>
    public static async Task ProcessAsync(LibraryItemDetailsDto detailsItem)
    {
        if (detailsItem == null)
            return;

        try
        {
            // Check if current document is a family document
            if (RevitManager.Document.IsFamilyDocument)
            {
                MessageBox.Show(
                    "The current document is a family document. Unable to load details.",
                    "Operation Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                return;
            }

            var handler = FamilyLibraryCore.DetailsDownloadToRevitHandler;
            var externalEvent = FamilyLibraryCore.DetailsDownloadToRevitEvent;

            if (handler == null || externalEvent == null)
            {
                throw new InvalidOperationException("Details download handler or external event is not initialized.");
            }

            handler.SetData(detailsItem);
            externalEvent.Raise();
        }
        catch (Exception ex)
        {
            MessageBox.Show(
                $"Error processing Details: {ex.Message}",
                "Error",
                MessageBoxButton.OK,
                MessageBoxImage.Error);
        }
    }
}