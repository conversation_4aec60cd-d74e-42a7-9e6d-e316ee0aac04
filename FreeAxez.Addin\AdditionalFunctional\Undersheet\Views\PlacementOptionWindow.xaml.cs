﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.Undersheet.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System.Windows;

namespace FreeAxez.Addin.AdditionalFunctional.Undersheet.Views
{
    /// <summary>
    /// Interaction logic for PalletsView.xaml
    /// </summary>
    public partial class PlacementOptionWindow : Window
    {
        private Line _startLine;
        private FilledRegion _placementRegion;


        public PlacementOptionWindow()
        {
            InitializeComponent();
        }


        public static bool ShowDialog(out Line startLine, out FilledRegion placementRegion)
        {
            startLine = null;
            placementRegion = null;

            var window = new PlacementOptionWindow();
            if (window.ShowDialog() != true)
            {
                return false;
            }

            startLine = window._startLine;
            placementRegion = window._placementRegion;

            return true;
        }

        private void placeUndersheets_Click(object sender, RoutedEventArgs e)
        {
            if (_startLine == null)
            {
                InfoDialog.ShowDialog("Warning", "The starting line has not been selected.");
                return;
            }

            if (selectPlacementRegion.IsChecked == true &&  _placementRegion == null)
            {
                InfoDialog.ShowDialog("Warning", "The placement region has not been selected.");
                return;
            }

            this.DialogResult = true;
            this.Close();
        }

        private void startLine_Click(object sender, RoutedEventArgs e)
        {
            this.Hide();

            Line line = null;
            Element element = null;

            try
            {
                var reference = RevitManager.UIDocument.Selection.PickObject(
                    Autodesk.Revit.UI.Selection.ObjectType.Element, new CurveElementSelectionFilter());

                element = RevitManager.Document.GetElement(reference);
                line = (RevitManager.Document.GetElement(reference) as CurveElement).GeometryCurve as Line;
            }
            catch
            {
                
            }

            if (line == null)
            {
                _startLine = null;
                startLine.Content = $"Select";
                InfoDialog.ShowDialog("Warning", "The starting line has not been selected.");
            }
            else
            {
                _startLine = line;
                startLine.Content = $"Id: {element.Id.GetIntegerValue()}";
            }

            this.ShowDialog();
        }

        private void placementArea_Click(object sender, RoutedEventArgs e)
        {
            this.Hide();
            FilledRegion filledRegion = null;

            try
            {
                var reference = RevitManager.UIDocument.Selection.PickObject(Autodesk.Revit.UI.Selection.ObjectType.Element, new FilledRegionSelectionFilter());
                filledRegion = RevitManager.Document.GetElement(reference) as FilledRegion;
            }
            catch
            {

            }

            if (filledRegion == null)
            {
                _placementRegion = null;
                placementArea.Content = $"Select";
                InfoDialog.ShowDialog("Warning", "The placement region has not been selected.");
            }
            else
            {
                _placementRegion = filledRegion;
                placementArea.Content = $"Id: {_placementRegion.Id.GetIntegerValue()}";
            }

            this.ShowDialog();
        }

        private void selectPlacementRegion_Checked(object sender, RoutedEventArgs e)
        {
            if (selectPlacementRegion.IsChecked == true)
            {
                selectPlacementRegionButton.Visibility = System.Windows.Visibility.Visible;
            }
            else
            {
                _placementRegion = null;
                placementArea.Content = "Select";
                selectPlacementRegionButton.Visibility = System.Windows.Visibility.Collapsed;
            }
        }
    }
}
