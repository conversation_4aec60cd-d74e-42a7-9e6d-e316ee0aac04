<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <LangVersion>latest</LangVersion>
    <EnableNullable>true</EnableNullable>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <Configurations>Release 2022;Release 2023;Release 2024;Release 2025;Release 2026</Configurations>
    <PackageId>FreeAxez.AutoCAD.Plugin</PackageId>
    <IsPackable>false</IsPackable>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <PlatformTarget>x64</PlatformTarget>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
  </PropertyGroup>

  <PropertyGroup Condition="$(Configuration.Contains('Release'))">
    <Optimize>true</Optimize>
    <DebugType>none</DebugType>
    <DefineConstants>$(DefineConstants);RELEASE</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="$(Configuration.Contains('2022'))">
    <AssemblyName>FreeAxez.AutoCAD.Plugin.2022</AssemblyName>
    <AutoCADApiPackageVersion>24.1.51000</AutoCADApiPackageVersion>
    <DefineConstants>ACAD2022</DefineConstants>
    <TargetFramework>net48</TargetFramework>
  </PropertyGroup>

  <PropertyGroup Condition="$(Configuration.Contains('2023'))">
    <AssemblyName>FreeAxez.AutoCAD.Plugin.2023</AssemblyName>
    <AutoCADApiPackageVersion>24.2.0</AutoCADApiPackageVersion>
    <DefineConstants>ACAD2023</DefineConstants>
    <TargetFramework>net48</TargetFramework>
  </PropertyGroup>

  <PropertyGroup Condition="$(Configuration.Contains('2024'))">
    <AssemblyName>FreeAxez.AutoCAD.Plugin.2024</AssemblyName>
    <AutoCADApiPackageVersion>24.3.0</AutoCADApiPackageVersion>
    <DefineConstants>ACAD2024</DefineConstants>
    <TargetFramework>net48</TargetFramework>
  </PropertyGroup>

  <PropertyGroup Condition="$(Configuration.Contains('2025'))">
    <AssemblyName>FreeAxez.AutoCAD.Plugin.2025</AssemblyName>
    <AutoCADApiPackageVersion>25.0.0</AutoCADApiPackageVersion>
    <DefineConstants>ACAD2025</DefineConstants>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <PropertyGroup Condition="$(Configuration.Contains('2026'))">
    <AssemblyName>FreeAxez.AutoCAD.Plugin.2026</AssemblyName>
    <AutoCADApiPackageVersion>25.1.0</AutoCADApiPackageVersion>
    <DefineConstants>ACAD2026</DefineConstants>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>



  <ItemGroup>
    <PackageReference Include="AutoCAD.NET.Core" Version="$(AutoCADApiPackageVersion)" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
</Project>