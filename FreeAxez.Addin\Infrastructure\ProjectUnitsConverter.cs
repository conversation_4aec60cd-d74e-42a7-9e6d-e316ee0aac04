﻿using Autodesk.Revit.DB;

namespace FreeAxez.Addin.Infrastructure
{
    public class ProjectUnitsConverter
    {
        private const double Accuracy32 = 0.03125; // 1/32"


        /// <summary>
        /// Formats a number with units into a string.
        /// </summary>
        /// <param name="value">The value to format, in Revit's internal units.</param>
        /// <returns>The formatted string.</returns>
        public static string FormatLength(double value)
        {
#if revit2018 || revit2019 || revit2020
            return UnitFormatUtils.Format(RevitManager.Document.GetUnits(), UnitType.UT_Length, value, false, true);
#else
            return UnitFormatUtils.Format(RevitManager.Document.GetUnits(), SpecTypeId.Length, value, true);
#endif
        }

        /// <summary>
        /// Formats a number with units into a string.
        /// </summary>
        /// <param name="value">The value to format, in Revit's internal units.</param>
        /// <returns>The formatted string.</returns>
        public static string FormatLengthToFractionalInches(double value)
        {
#if revit2018 || revit2019 || revit2020
            var format = new FormatOptions(DisplayUnitType.DUT_FRACTIONAL_INCHES);
            format.Accuracy = Accuracy32; // 1/32"
            var formatOptions = new FormatValueOptions();
            formatOptions.SetFormatOptions(format);
            return UnitFormatUtils.Format(RevitManager.Document.GetUnits(), UnitType.UT_Length, value, false, true, formatOptions);
#else
            var format = new FormatOptions(UnitTypeId.FractionalInches);
            format.Accuracy = Accuracy32; // 1/32"
            var formatOptions = new FormatValueOptions();
            formatOptions.SetFormatOptions(format);
            return UnitFormatUtils.Format(RevitManager.Document.GetUnits(), SpecTypeId.Length, value, true, formatOptions);
#endif
        }

        /// <summary>
        /// Parses a formatted string into a number with units if possible.
        /// </summary>
        /// <param name="value">The string to parse.</param>
        /// <param name="result">The parsed value, in Revit's internal units. Ignore this value if the function returns false.</param>
        /// <returns>True if the string can be parsed, false otherwise.</returns>
        public static bool TryParseLength(string value, out double result)
        {
#if revit2018 || revit2019 || revit2020
            return UnitFormatUtils.TryParse(RevitManager.Document.GetUnits(), UnitType.UT_Length, value, out result);
#else
            return UnitFormatUtils.TryParse(RevitManager.Document.GetUnits(), SpecTypeId.Length, value, out result); 
#endif
        }

        /// <summary>
        /// Parses a formatted string into a number with units if possible.
        /// </summary>
        /// <param name="value">The string to parse.</param>
        /// <param name="result">The parsed value, in Revit's internal units. Ignore this value if the function returns false.</param>
        /// <returns>True if the string can be parsed, false otherwise.</returns>
        public static bool TryParseLengthFromFractionalInches(string value, out double result)
        {
#if revit2018 || revit2019 || revit2020
            var format = new FormatOptions(DisplayUnitType.DUT_FRACTIONAL_INCHES);
            format.Accuracy = Accuracy32; // 1/32"
            var parsingOptions = new ValueParsingOptions();
            parsingOptions.SetFormatOptions(format);
            return UnitFormatUtils.TryParse(RevitManager.Document.GetUnits(), UnitType.UT_Length, value, parsingOptions, out result);
#else
            var format = new FormatOptions(UnitTypeId.FractionalInches);
            format.Accuracy = Accuracy32; // 1/32"
            var parsingOptions = new ValueParsingOptions();
            parsingOptions.SetFormatOptions(format);
            return UnitFormatUtils.TryParse(RevitManager.Document.GetUnits(), SpecTypeId.Length, value, out result); 
#endif
        }

        /// <summary>
        /// Converts millimeters to feets.
        /// </summary>
        /// <param name="millimeters">Millimeters to convert.</param>
        /// <returns>Converted millimeters to feets.</returns>
        public static double ToFeetAndInches(double millimeters)
        {
            #if revit2018 || revit2019 || revit2020
                double feetAndInches = UnitUtils.Convert(millimeters, DisplayUnitType.DUT_MILLIMETERS, DisplayUnitType.DUT_FEET_FRACTIONAL_INCHES);
            #else
                double feetAndInches = UnitUtils.Convert(millimeters, UnitTypeId.Millimeters, UnitTypeId.Feet);
            #endif
                return feetAndInches;
        }

        /// <summary>
        /// Converts degrees to radians.
        /// </summary>
        /// <param name="degrees">Degrees to convert.</param>
        /// <returns>Converted degrees to radians.</returns>
        public static double ToRadians(double degrees)
        {
            #if revit2018 || revit2019 || revit2020
                double radians = UnitUtils.Convert(degrees, DisplayUnitType.DUT_DECIMAL_DEGREES, DisplayUnitType.DUT_RADIANS);
            #else
                double radians = UnitUtils.Convert(degrees, UnitTypeId.Degrees, UnitTypeId.Radians);
            #endif
                return radians;
        }

        /// <summary>
        /// Converts radians to degrees.
        /// </summary>
        /// <param name="radians">Radians to convert.</param>
        /// <returns>Converted degrees to radians.</returns>
        public static double ToDegrees(double radians)
        {
            #if revit2018 || revit2019 || revit2020 
                double degrees = UnitUtils.Convert(radians, DisplayUnitType.DUT_RADIANS, DisplayUnitType.DUT_DECIMAL_DEGREES);
            #else
                double degrees = UnitUtils.Convert(radians, UnitTypeId.Radians, UnitTypeId.Degrees);
            #endif
                return degrees;
        }
    }
}
