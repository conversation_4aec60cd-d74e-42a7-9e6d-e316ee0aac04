using System;
using System.Globalization;
using System.IO;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.ApplicationServices.Core;
using Autodesk.AutoCAD.Colors;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;
using FreeAxez.AutoCAD.Plugin.Models;
using Newtonsoft.Json;

namespace FreeAxez.AutoCAD.Plugin.Commands
{
    public class UpdateLayersFromBackendCommand
    {
        [CommandMethod("UPDATE_LAYERS_FROM_BACKEND", CommandFlags.Session)]
        public void UpdateLayersFromBackend()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            Database db = doc.Database;

            string jsonPath = Environment.GetEnvironmentVariable("FREEAXEZ_UPDATE_DATA_PATH") ?? string.Empty;

            if (!File.Exists(jsonPath))
            {
                var errorMsg = "FREEAXEZ_UPDATE_DATA_PATH not set or file not found.";
                ed.WriteMessage($"\n{errorMsg}");
                return;
            }

            UpdateLayersRequest updateRequest;
            try
            {
                var jsonContent = File.ReadAllText(jsonPath);
                updateRequest = JsonConvert.DeserializeObject<UpdateLayersRequest>(jsonContent)!
                               ?? throw new InvalidOperationException("Deserialisation returned null");
            }
            catch (System.Exception ex)
            {
                var errorMsg = $"Invalid update JSON: {ex.Message}";
                ed.WriteMessage($"\n{errorMsg}\n");
                return;
            }

            if (updateRequest.FreeAxezLayers == null || !updateRequest.FreeAxezLayers.Any())
            {
                ed.WriteMessage("\nNo FreeAxez layers found in JSON file.\n");
                return;
            }

            using (DocumentLock docLock = doc.LockDocument())
            {
                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    try
                    {
                        // First ensure linetypes exist
                        EnsureLinetypes(db, tr, updateRequest.Linetypes);

                        // Then update existing layers with FreeAxez properties
                        int updatedCount = UpdateExistingLayers(db, tr, updateRequest.FreeAxezLayers, null);

                        tr.Commit();

                        try
                        {
                            db.SaveAs(doc.Name, true, DwgVersion.Current, db.SecurityParameters);
                            ed.WriteMessage($"\nDocument saved: {Path.GetFileName(doc.Name)}\n");
                        }
                        catch (Autodesk.AutoCAD.Runtime.Exception saveEx)
                        {
                            ed.WriteMessage($"\nWarning: Changes applied but save failed: {saveEx.Message}\n");
                            ed.WriteMessage("Please save the document manually (Ctrl+S)\n");
                        }

                        ed.WriteMessage($"\nUpdate layers from backend completed successfully. Updated {updatedCount} layers.\n");
                    }
                    catch (Autodesk.AutoCAD.Runtime.Exception ex)
                    {
                        tr.Abort();
                        ed.WriteMessage($"\nError during update operation: {ex.Message}\n");
                    }
                }
            }
        }

        private static void EnsureLinetypes(Database db, Transaction tr, System.Collections.Generic.List<LinetypeDto> list)
        {
            if (list == null)
                return;

            var ltTable = (LinetypeTable)tr.GetObject(db.LinetypeTableId, OpenMode.ForRead);

            foreach (var dto in list)
            {
                try
                {
                    if (ltTable.Has(dto.Name))
                        continue;

                    if (!ltTable.IsWriteEnabled)
                        ltTable.UpgradeOpen();

                    var rec = new LinetypeTableRecord
                    {
                        Name = dto.Name,
                        Comments = string.IsNullOrWhiteSpace(dto.Description) ? dto.Name : dto.Description
                    };

                    if (TryParsePattern(dto.PatternRaw, out double[] pattern))
                    {
                        rec.NumDashes = pattern.Length;
                        for (int i = 0; i < pattern.Length; i++)
                            rec.SetDashLengthAt(i, pattern[i]);
                    }
                    else
                    {
                        rec.NumDashes = 0;
                    }

                    ltTable.Add(rec);
                    tr.AddNewlyCreatedDBObject(rec, true);
                }
                catch
                {
                    // Ignore linetype creation errors
                }
            }
        }

        private static int UpdateExistingLayers(Database db, Transaction tr, System.Collections.Generic.List<FreeAxezLayer> freeAxezLayers, System.Collections.Generic.List<LayerMapping> mappings)
        {
            if (freeAxezLayers == null)
                return 0;

            var layerTbl = (LayerTable)tr.GetObject(db.LayerTableId, OpenMode.ForRead);
            var ltypeTbl = (LinetypeTable)tr.GetObject(db.LinetypeTableId, OpenMode.ForRead);
            int updatedCount = 0;

            // Update layers by matching names directly (like EnsureLayers in MergeLayersCommand)
            foreach (var freeAxezLayer in freeAxezLayers)
            {
                try
                {
                    // Only update if layer exists in DWG
                    if (!layerTbl.Has(freeAxezLayer.Name))
                        continue;

                    // Get existing layer record and update its properties
                    var layerRecord = (LayerTableRecord)tr.GetObject(layerTbl[freeAxezLayer.Name], OpenMode.ForWrite);

                    // Update layer properties with FreeAxez data (same as EnsureLayers)
                    layerRecord.Color = ParseColor(freeAxezLayer.Color);
                    layerRecord.LineWeight = MapLineweight(freeAxezLayer.Lineweight);
                    layerRecord.Transparency = ToTransparency(freeAxezLayer.Transparency);
                    layerRecord.LinetypeObjectId = ltypeTbl.Has(freeAxezLayer.Linetype)
                        ? ltypeTbl[freeAxezLayer.Linetype]
                        : ltypeTbl["Continuous"];

                    updatedCount++;
                }
                catch
                {
                    // Ignore layer update errors
                }
            }

            return updatedCount;
        }

        // --- 1. MapLineweight: identical to MergeLayersCommand -----------------
        private static LineWeight MapLineweight(double mm)
        {
            if (mm <= 0)
                return LineWeight.ByLayer;

            int h = (int)Math.Round(mm * 100);   // hundredths of mm

            return h switch
            {
                <= 5 => LineWeight.LineWeight005,
                <= 9 => LineWeight.LineWeight009,
                <= 13 => LineWeight.LineWeight013,
                <= 15 => LineWeight.LineWeight015,
                <= 18 => LineWeight.LineWeight018,
                <= 20 => LineWeight.LineWeight020,
                <= 25 => LineWeight.LineWeight025,
                <= 30 => LineWeight.LineWeight030,
                <= 35 => LineWeight.LineWeight035,
                <= 40 => LineWeight.LineWeight040,
                <= 50 => LineWeight.LineWeight050,
                <= 53 => LineWeight.LineWeight053,
                <= 60 => LineWeight.LineWeight060,
                <= 70 => LineWeight.LineWeight070,
                <= 80 => LineWeight.LineWeight080,
                <= 90 => LineWeight.LineWeight090,
                <= 100 => LineWeight.LineWeight100,
                <= 106 => LineWeight.LineWeight106,
                <= 120 => LineWeight.LineWeight120,
                <= 140 => LineWeight.LineWeight140,
                <= 158 => LineWeight.LineWeight158,
                <= 200 => LineWeight.LineWeight200,
                _ => LineWeight.LineWeight211
            };
        }

        // --- 2. ParseColor: same fall-back as MergeLayersCommand ----------------
        private static Color ParseColor(string spec)
        {
            if (string.IsNullOrWhiteSpace(spec) ||
                spec.Equals("ByLayer", StringComparison.OrdinalIgnoreCase))
                return Color.FromColorIndex(ColorMethod.ByLayer, 256);

            if (spec.StartsWith("#") && spec.Length == 7)
            {
                try
                {
                    byte r = Convert.ToByte(spec.Substring(1, 2), 16);
                    byte g = Convert.ToByte(spec.Substring(3, 2), 16);
                    byte b = Convert.ToByte(spec.Substring(5, 2), 16);
                    return Color.FromRgb(r, g, b);
                }
                catch
                {
                    // fall through
                }
            }

            // identical default as in the merge command
            return Color.FromColorIndex(ColorMethod.ByAci, 7);
        }

        // --- 3. TryParsePattern: tolerant parser from MergeLayersCommand --------
        private static bool TryParsePattern(string raw, out double[] pattern)
        {
            pattern = Array.Empty<double>();

            if (string.IsNullOrWhiteSpace(raw))
                return false;

            string clean = raw.StartsWith("A,", StringComparison.OrdinalIgnoreCase)
                           ? raw.Substring(2)
                           : raw;

            string[] parts = clean.Split(new[] { ',', ';' },
                                         StringSplitOptions.RemoveEmptyEntries);

            var list = new System.Collections.Generic.List<double>(parts.Length);
            foreach (string p in parts)
                if (double.TryParse(p.Trim(),
                                    NumberStyles.Float,
                                    CultureInfo.InvariantCulture,
                                    out double v))
                    list.Add(v);

            if (list.Count == 0)
                return false;

            pattern = list.ToArray();
            return true;
        }

        private static Transparency ToTransparency(int val)
        {
            if (val < 0)
            {
                return new Transparency();
            }

            if (val > 90)
            {
                val = 90;
            }

            return new Transparency((byte)val);
        }
    }
}
