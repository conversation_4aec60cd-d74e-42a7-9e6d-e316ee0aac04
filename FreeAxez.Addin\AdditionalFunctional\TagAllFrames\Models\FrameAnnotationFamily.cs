﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllFrames.Models
{
    public class FrameAnnotationFamily
    {
        private const string AnnotationSymbolRegex = @"FRAME[\s-_](CORNER[\s-_])?TYPICAL";

        public FrameAnnotationFamily()
        {
            var annotationSymbolNameRegex = new Regex(AnnotationSymbolRegex, RegexOptions.IgnoreCase);

            var annotationSymbols = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsElementType()
                .OfCategory(BuiltInCategory.OST_GenericAnnotation)
                .Cast<FamilySymbol>()
                .Where(s => annotationSymbolNameRegex.IsMatch(s.Name))
                .ToList();

            TagSymbol = annotationSymbols.FirstOrDefault(s => !s.Name.Contains("CORNER"));
            CornerTagSymbol = annotationSymbols.FirstOrDefault(s => s.Name.Contains("CORNER"));
        }

        public FamilySymbol TagSymbol { get; private set; }
        public FamilySymbol CornerTagSymbol { get; private set; }

        public bool IsFamilyNotExist(out string message)
        {
            if (TagSymbol == null || CornerTagSymbol == null)
            {
                message = "There is no general annotation for frames in the project.\n" +
                    "Please load the family 'FA-Annotation_Frame' with types 'GRIDD CURB TYPICAL' and 'GRIDD CURB CORNER TYPICAL'.";
                return true;
            }

            message = "";
            return false;
        }
    }
}