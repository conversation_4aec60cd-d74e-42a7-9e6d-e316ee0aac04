using NetTopologySuite.Geometries;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;
using System.Text.Json;
using System.IO;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services;

public class GeometryExportService
{
    private const double ANNOTATION_SIZE = 3.0;
    public void ExportToJson(List<LineString> lines, List<OutletModel> outlets, List<AnnotationModel> annotations, string filePath)
    {
        var data = new
        {
            Lines = lines.Select(line => new
            {
                Coordinates = line.Coordinates.Select(c => new { X = c.X, Y = c.Y }).ToArray(),
                Length = line.Length
            }).ToArray(),
            Outlets = outlets.Select(outlet => new
            {
                Id = outlet.Id,
                X = outlet.Location.X,
                Y = outlet.Location.Y
            }).ToArray(),
            Annotations = annotations.Select(annotation => new
            {
                Id = annotation.Id,
                X = annotation.Location.X,
                Y = annotation.Location.Y,
                OrientationLine = GetOrientationLine(annotation)
            }).ToArray()
        };

        var json = JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
        File.WriteAllText(filePath, json);
    }

    private object GetOrientationLine(AnnotationModel annotation)
    {
        var location = annotation.Location;
        var halfSize = ANNOTATION_SIZE / 2.0;
        Coordinate start, end;

        switch (annotation.Orientation)
        {
            case Direction.Up:
                start = new Coordinate(location.X, location.Y);
                end = new Coordinate(location.X, location.Y + ANNOTATION_SIZE);
                break;
            case Direction.Down:
                start = new Coordinate(location.X, location.Y);
                end = new Coordinate(location.X, location.Y - ANNOTATION_SIZE);
                break;
            case Direction.Left:
                start = new Coordinate(location.X, location.Y);
                end = new Coordinate(location.X - ANNOTATION_SIZE, location.Y);
                break;
            case Direction.Right:
                start = new Coordinate(location.X, location.Y);
                end = new Coordinate(location.X + ANNOTATION_SIZE, location.Y);
                break;
            default:
                return null;
        }

        return new[] { new { X = start.X, Y = start.Y }, new { X = end.X, Y = end.Y } };
    }
}
