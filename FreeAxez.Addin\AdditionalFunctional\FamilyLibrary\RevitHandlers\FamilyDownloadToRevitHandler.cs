using System.IO;
using System.Net;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.RevitHandlers;

public class FamilyDownloadToRevitHandler : BaseExternalEventHandler
{
    private LibraryItemDto _libraryItemDto;
    private object _viewModel;

    public void SetData(LibraryItemDto libraryItemDto, object viewModel = null)
    {
        _libraryItemDto = libraryItemDto ?? throw new ArgumentNullException(nameof(libraryItemDto));
        _viewModel = viewModel;
    }

    public override void ExecuteInternal(UIApplication app)
    {
        string localFilePath = null;

        try
        {
            if (app.ActiveUIDocument.Document.IsFamilyDocument)
                throw new InvalidOperationException("Cannot load family into a family document.");

            if (!IsRevitVersionCompatible(_libraryItemDto.RevitVersion))
                throw new InvalidOperationException(
                    $"Family version {_libraryItemDto.RevitVersion} is not compatible with current Revit version.");

            if (!LoadFamilyIntoRevit(app, out localFilePath))
                return;

            if (_libraryItemDto.Name != _libraryItemDto.MatchingName)
            {
                ReplaceFamilyType(app);
                DeleteOldFamily(app, _libraryItemDto.MatchingName);
            }

            FamilyLibraryCore.ShowMessage("Download to Revit",
                $"Family '{_libraryItemDto.Name}'  successfully.", MessageType.Success);
            UpdateViewModel(false);
        }
        catch (Exception ex)
        {
            ErrorHandler.HandleError("Error processing family download", ex, onError: () => UpdateViewModel(true));
        }
        finally
        {
            CleanupTempFile(localFilePath);
            _libraryItemDto = null;
            _viewModel = null;
        }
    }

    private bool LoadFamilyIntoRevit(UIApplication app, out string localFilePath)
    {
        localFilePath = null;

        try
        {
            localFilePath = DownloadFamilyFile(_libraryItemDto.FamilyFilePath, _libraryItemDto.Name);
            using var trans = new Transaction(app.ActiveUIDocument.Document, "Load Family");
            trans.Start();
            var isFamilyLoaded =
                app.ActiveUIDocument.Document.LoadFamily(localFilePath, new OverrideFamilyLoadOptions(), out _);
            trans.Commit();
            return true;
        }
        catch (Exception ex)
        {
            ErrorHandler.HandleError("Error loading family", ex);
            return false;
        }
    }

    private string DownloadFamilyFile(string familyFileUrl, string fileName)
    {
        var uniqueTempPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
        Directory.CreateDirectory(uniqueTempPath);
        var localFilePath = Path.Combine(uniqueTempPath, fileName);

        using var client = new WebClient();
        client.DownloadFile(new Uri(familyFileUrl), localFilePath);
        return localFilePath;
    }

    private void ReplaceFamilyType(UIApplication app)
    {
        var document = app.ActiveUIDocument.Document;
        var matchingName = Path.GetFileNameWithoutExtension(_libraryItemDto.MatchingName);
        var familyName = Path.GetFileNameWithoutExtension(_libraryItemDto.Name);

        var oldFamilyInstances = new FilteredElementCollector(document)
            .OfClass(typeof(FamilyInstance))
            .Cast<FamilyInstance>()
            .Where(fi => fi.Symbol.Family.Name == matchingName)
            .ToList();

        if (!oldFamilyInstances.Any())
            return;

        try
        {
            using var trans = new Transaction(document, "Change Family Type");
            trans.Start();
            var parametersToSkip = new HashSet<string> { "Version", "Product Name", "Description" };
            var replacedCount = 0;

            foreach (var oldInstance in oldFamilyInstances)
            {
                var parameterValues = GetParameterValues(oldInstance, parametersToSkip);
                var newType = new FilteredElementCollector(document)
                    .OfClass(typeof(FamilySymbol))
                    .Cast<FamilySymbol>()
                    .FirstOrDefault(fs => fs.Family.Name == familyName && fs.Name == oldInstance.Symbol.Name);

                if (newType != null)
                {
                    oldInstance.ChangeTypeId(newType.Id);
                    ApplyParameterValues(oldInstance, parameterValues);
                    replacedCount++;
                }
            }

            trans.Commit();
        }
        catch (Exception ex)
        {
            ErrorHandler.HandleError("Error replacing family types", ex);
        }
    }

    private void DeleteOldFamily(UIApplication app, string familyName)
    {
        var document = app.ActiveUIDocument.Document;
        var matchingName = Path.GetFileNameWithoutExtension(familyName);

        var oldFamily = new FilteredElementCollector(document)
            .OfClass(typeof(Family))
            .Cast<Family>()
            .FirstOrDefault(f => f.Name == matchingName);

        if (oldFamily == null)
            return;

        try
        {
            using var trans = new Transaction(document, "Delete Old Family");
            trans.Start();
            document.Delete(oldFamily.Id);
            trans.Commit();
        }
        catch (Exception ex)
        {
            ErrorHandler.HandleError("Error deleting old family", ex);
        }
    }

    private bool IsRevitVersionCompatible(string familyVersion)
    {
        int.TryParse(RevitManager.RevitVersion, out var projectVer);
        int.TryParse(familyVersion, out var familyVer);
        return familyVer <= projectVer;
    }

    private Dictionary<string, (StorageType, object)> GetParameterValues(FamilyInstance instance,
        HashSet<string> parametersToSkip)
    {
        var parameterValues = new Dictionary<string, (StorageType, object)>();
        foreach (Parameter param in instance.Parameters)
        {
            if (parametersToSkip.Contains(param.Definition.Name))
                continue;

            var storageType = param.StorageType;
            object value = storageType switch
            {
                StorageType.Double => param.AsDouble(),
                StorageType.Integer => param.AsInteger(),
                StorageType.String => param.AsString(),
                StorageType.ElementId => param.AsElementId(),
                _ => null
            };

            if (value != null)
                parameterValues[param.Definition.Name] = (storageType, value);
        }

        return parameterValues;
    }

    private void ApplyParameterValues(FamilyInstance instance,
        Dictionary<string, (StorageType, object)> parameterValues)
    {
        foreach (var kvp in parameterValues)
        {
            var newParam = instance.LookupParameter(kvp.Key);
            if (newParam == null || newParam.IsReadOnly)
                continue;

            try
            {
                switch (kvp.Value.Item1)
                {
                    case StorageType.Double:
                        newParam.Set((double)kvp.Value.Item2);
                        break;
                    case StorageType.Integer:
                        newParam.Set((int)kvp.Value.Item2);
                        break;
                    case StorageType.String:
                        newParam.Set((string)kvp.Value.Item2);
                        break;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Warning($"Failed to set parameter '{kvp.Key}': {ex.Message}");
            }
        }
    }

    private void CleanupTempFile(string tempFilePath)
    {
        if (string.IsNullOrEmpty(tempFilePath))
            return;

        try
        {
            var tempDirectory = Path.GetDirectoryName(tempFilePath);
            if (!string.IsNullOrEmpty(tempDirectory) && Directory.Exists(tempDirectory))
                Directory.Delete(tempDirectory, true);
        }
        catch (Exception ex)
        {
            LogHelper.Warning($"Failed to delete temp directory: {ex.Message}");
        }
    }

    private void UpdateViewModel(bool isError)
    {
        if (_viewModel == null)
            return;

        try
        {
            if (_viewModel is FamiliesPageVm familiesVm)
            {
                familiesVm.IsLoading = false;
            }
            else if (_viewModel is FamilyUpdatesPageVm updatesVm)
            {
                if (!isError)
                    _ = updatesVm.LoadFamilies();
                updatesVm.IsLoading = false;
            }
        }
        catch (Exception ex)
        {
            LogHelper.Error($"Error updating ViewModel: {ex.Message}");
        }
    }
}