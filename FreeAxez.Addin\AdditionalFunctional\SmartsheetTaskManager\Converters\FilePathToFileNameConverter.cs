﻿using System;
using System.Globalization;
using System.IO;
using System.Windows.Data;

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Converters;

public class FilePathToFileNameConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string filePath)
        {
            return Path.GetFileName(filePath);
        }
        return value;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
