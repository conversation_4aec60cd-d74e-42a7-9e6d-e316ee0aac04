<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Navigation.UserPagesPresenter"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:vm1="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages"
             xmlns:pages="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Pages"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="300">
    <UserControl.Resources>
        <ResourceDictionary>
            <DataTemplate DataType="{x:Type vm1:FamiliesPageVm}">
                <pages:UserFamiliesPage Style="{StaticResource PageStyle}"/>
            </DataTemplate>
            <DataTemplate DataType="{x:Type vm1:UnmatchedFamiliesPageVm}">
                <pages:UserUnmatchedFamiliesPage Style="{StaticResource PageStyle}"/>
            </DataTemplate>
            <DataTemplate DataType="{x:Type vm1:FamilyUpdatesPageVm}">
                <pages:UserFamilyUpdatesPage Style="{StaticResource PageStyle}"/>
            </DataTemplate>
            <DataTemplate DataType="{x:Type vm1:HistoryPageVm}">
                <pages:SharedHistoryPage Style="{StaticResource PageStyle}"/>
            </DataTemplate>
            <DataTemplate DataType="{x:Type vm1:DetailsPageVm}">
                <pages:UserDetailsPage Style="{StaticResource PageStyle}"/>
            </DataTemplate>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <ContentControl x:Name="Page"
                        Content="{Binding CurrentView}"/>
    </Grid>
</UserControl>
