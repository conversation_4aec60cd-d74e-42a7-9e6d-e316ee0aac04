﻿using FreeAxez.Addin.AdditionalFunctional.TagAllFrames.Models;
using FreeAxez.Addin.AdditionalFunctional.TagAllFrames.Utils;
using FreeAxez.Addin.AdditionalFunctional.TagRamp.Models;
using FreeAxez.Addin.AdditionalFunctional.TagRamp.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.TagRamp.ViewModels
{
    public class TagRampViewModel : BaseViewModel
    {
        private double _lengthToCenter;
        private bool _isSelectRampComponents;

        public TagRampViewModel()
        {
            _lengthToCenter = Properties.Settings.Default.TagRampLengthToCenterOfTag;
            TagType = Properties.Settings.Default.TagTypeRump;
            AnnotationType = !TagType;
            TagAll = Properties.Settings.Default.TagRampIsVisibleInView;
            TagSelected = !TagAll;

            CreateCommand = new RelayCommand(OnCreateRampTagsCommand);
            CancelCommand = new RelayCommand(OnCancelCommand);
        }

        public bool TagType { get; set; }
        public bool AnnotationType { get; set; }
        public bool TagAll { get; set; }
        public bool TagSelected { get; set; }

        public string LengthToCenter
        {
            get => ProjectUnitsConverter.FormatLengthToFractionalInches(_lengthToCenter);
            set
            {
                if (ProjectUnitsConverter.TryParseLengthFromFractionalInches(
                    value, out _lengthToCenter))
                {
                    OnPropertyChanged(nameof(LengthToCenter));
                }
            }
        }

        public bool IsVisibleInView
        {
            get => TagAll;
            set
            {
                if (TagAll != value)
                {
                    TagAll = value;
                    OnPropertyChanged(nameof(IsVisibleInView));
                }
            }
        }

        public bool IsSelectRampComponents
        {
            get => _isSelectRampComponents;
            set
            {
                if (_isSelectRampComponents != value)
                {
                    _isSelectRampComponents = value;
                    OnPropertyChanged(nameof(IsSelectRampComponents));
                }
            }
        }

        public ICommand CreateCommand { get; }
        public ICommand CancelCommand { get; }

        private void OnCreateRampTagsCommand(object obj)
        {
            SaveSettings();
            (obj as Window).Close();

            if (TagType)
            {
                var tagRampBuilder = new TagRampBuilder(_lengthToCenter, TagAll);
                var multicategoryTags = tagRampBuilder.CreateTagMulticategory();
                InfoDialog.ShowDialog("Report", $"Created {multicategoryTags.Count} tags.");
            }
            else
            {
                var annatationRampBuilder = new TagRampBuilder(_lengthToCenter,TagAll);
                var annatationTags = annatationRampBuilder.BuildAnnatation();
                InfoDialog.ShowDialog("Report", $"Created {annatationTags.Count} tags.");
            }
        }

        private void OnCancelCommand(object obj)
        {
            Window window = obj as Window;

            if (window.IsActive)
            {
                window.Close();
            }
        }
        private void SaveSettings()
        {
            Properties.Settings.Default.TagRampLengthToCenterOfTag = _lengthToCenter;
            Properties.Settings.Default.TagRampIsVisibleInView = TagAll;
            Properties.Settings.Default.TagTypeRump = TagType;
            Properties.Settings.Default.Save();
        }
    }
}