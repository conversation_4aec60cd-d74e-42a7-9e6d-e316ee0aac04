using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.ViewScale.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.ViewScale.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Services;
using System.Text;

namespace FreeAxez.Addin.AdditionalFunctional.ViewScale
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    internal class ViewScaleCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var viewScaleModels = GetViewScaleModels();

            var viewModel = new ViewScaleViewModel(viewScaleModels);
            var view = new ViewScaleView();
            view.DataContext = viewModel;
            RevitManager.SetRevitAsWindowOwner(view);
            var result = view.ShowDialog();

            if (result == true && viewScaleModels.Count(v => v.IsScaleChanged) > 0)
            {
                var report = UpdateScales(viewScaleModels);
                MessageWindow.ShowDialog(report, Infrastructure.UI.Enums.MessageType.Success);
            }

            return Result.Succeeded;
        }

        private List<ViewScaleModel> GetViewScaleModels()
        {
            var output = new List<ViewScaleModel>();

            var invalidViewTypes = new List<ViewType> 
            { 
                ViewType.ProjectBrowser, 
                ViewType.SystemBrowser,
                ViewType.Schedule,
                ViewType.DrawingSheet,
                ViewType.Undefined
            };

            var views = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsNotElementType()
                .OfClass(typeof(View))
                .Cast<View>()
                .Where(v => invalidViewTypes.Contains(v.ViewType) == false)
                .ToList();

            foreach (var view in views)
            {
                var title = view.Title;
                var viewTemplate = view.ViewTemplateId != ElementId.InvalidElementId
                    ? RevitManager.Document.GetElement(view.ViewTemplateId).Name
                    : string.Empty;
                var isViewTemplate = view.IsTemplate;
                var initialScale = view.Scale;
                var isDependOnViewTemplate = IsDependOnViewTemplate(view);

                var viewScaleModel = new ViewScaleModel(
                    title, view.Id.GetIntegerValue(), viewTemplate, isViewTemplate, initialScale, isDependOnViewTemplate);

                output.Add(viewScaleModel);
            }

            return output;
        }

        private bool IsDependOnViewTemplate(View view)
        {
            if (view.ViewTemplateId.Equals(ElementId.InvalidElementId))
                return false;

            if (view.IsTemplate)
                return false;

            var template = RevitManager.Document.GetElement(view.ViewTemplateId) as View;

            var parameters = template.GetNonControlledTemplateParameterIds();
            if (parameters.Any(p => p.GetIntegerValue() == (int)BuiltInParameter.VIEW_SCALE))
            {
                return false;
            }

            return true;
        }

        private string UpdateScales(List<ViewScaleModel> viewScaleModels)
        {
            var report = new StringBuilder();

            using (var t = new Transaction(RevitManager.Document, "Update View Scales"))
            {
                t.Start();

                var templates = viewScaleModels
                    .Where(v => v.IsViewTemplate && v.IsScaleChanged)
                    .OrderBy(v => v.Title)
                    .ToList();

                if (templates.Count > 0)
                {
                    report.AppendLine("UPDATED VIEW TEMPLATES:");

                    foreach (var viewScaleModel in templates)
                    {
                        if (UpdateScale(viewScaleModel, out string reportString) == false)
                        {
                            LogHelper.Error(reportString);
                        }
                        report.AppendLine(reportString);
                    }
                }

                var singleViews = viewScaleModels
                    .Where(v => v.IsViewTemplate == false && v.IsScaleChanged && v.IsScaleNotDependOnViewTemplate)
                    .OrderBy(v => v.Title)
                    .ToList();

                if (singleViews.Count > 0)
                {
                    if (templates.Count > 0) 
                        report.AppendLine();

                    report.AppendLine("UPDATED VIEWS:");

                    foreach (var viewScaleModel in singleViews)
                    {
                        if (UpdateScale(viewScaleModel, out string reportString) == false)
                        {
                            LogHelper.Error(reportString);
                        }
                        report.AppendLine(reportString);
                    }
                }

                t.Commit();
            }

            return report.ToString();
        }

        private bool UpdateScale(ViewScaleModel viewScaleModel, out string reportString)
        {
            try
            {
                if (viewScaleModel.Scale <= 0)
                {
                    reportString = 
                        $"FAILED TO SET {viewScaleModel.Title} TO {viewScaleModel.ScaleString} ({viewScaleModel.Scale}) SKIPPED INVALID SCALE";
                    return false;
                }

                var view = RevitManager.Document.GetElement(new ElementId(viewScaleModel.Id)) as View;
                var scaleParameter = view.get_Parameter(BuiltInParameter.VIEW_SCALE_PULLDOWN_IMPERIAL);
                scaleParameter.Set(viewScaleModel.Scale);
                reportString = $"{viewScaleModel.Title} > {viewScaleModel.ScaleString} ({viewScaleModel.Scale})";
                return true;
            }
            catch (Exception ex)
            {
                reportString = 
                    $"FAILED TO SET {viewScaleModel.Title} TO {viewScaleModel.ScaleString} ({viewScaleModel.Scale}) BECAUSE OF {ex.Message}";
                return false;
            }
        }
    }
}
