﻿using System.Windows;

namespace FreeAxez.Addin.Infrastructure.UI
{
    /// <summary>
    /// Interaction logic for InfoDialog.xaml
    /// </summary>
    public partial class InfoDialog : Window
    {
        public InfoDialog()
        {
            InitializeComponent();
        }


        public static void ShowDialog(string title, string message)
        {
            var infoDialog = new InfoDialog();

            infoDialog.Title = title;
            infoDialog.textBox.Text = message;
            infoDialog.WindowStartupLocation = WindowStartupLocation.CenterScreen;

            infoDialog.ShowDialog();
        }

        public static void ShowDialog(string title, string message, Window owner)
        {
            var infoDialog = new InfoDialog();

            infoDialog.Title = title;
            infoDialog.textBox.Text = message;
            infoDialog.Owner = owner;
            infoDialog.WindowStartupLocation = WindowStartupLocation.CenterOwner;

            infoDialog.ShowDialog();
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
