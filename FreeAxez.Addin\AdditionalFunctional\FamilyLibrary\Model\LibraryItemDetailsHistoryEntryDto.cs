using System;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model
{
    public class LibraryItemDetailsHistoryEntryDto
    {
        public Guid Id { get; set; }
        public Guid DetailsItemGuid { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
        public string Action { get; set; }
        public string ChangesDescription { get; set; }
        public string FilePath { get; set; }
        public string ImagePath { get; set; }
        public string FileType { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
