﻿using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System.Windows;
using System.Windows.Controls;

namespace FreeAxez.Addin.Infrastructure.UI.Views
{
    public partial class SelectLevelView : Window
    {
        public SelectLevelView(string title)
        {
            InitializeComponent();
            Title = title;
        }

        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            foreach (LevelViewModel revitView in dataGrid.SelectedItems)
            {
                revitView.IsCheck = (bool)(sender as CheckBox).IsChecked;
            }
        }
    }
}
