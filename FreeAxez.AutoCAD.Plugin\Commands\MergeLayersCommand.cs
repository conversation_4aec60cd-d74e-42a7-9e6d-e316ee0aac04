using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.ApplicationServices.Core;
using Autodesk.AutoCAD.Colors;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;
using FreeAxez.AutoCAD.Plugin.Models;
using Newtonsoft.Json;

namespace FreeAxez.AutoCAD.Plugin.Commands
{
    public class MergeLayersCommand
    {
        [CommandMethod("MERGE_LAYERS", CommandFlags.Session)]
        public void MergeLayers()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor   ed  = doc.Editor;
            Database db  = doc.Database;

            string jsonPath = Environment.GetEnvironmentVariable("FREEAXEZ_MERGE_DATA_PATH") ?? string.Empty;

            if (!File.Exists(jsonPath))
            {
                var errorMsg = "FREEAXEZ_MERGE_DATA_PATH not set or file not found.";
                ed.WriteMessage($"\n{errorMsg}");
                return;
            }

            MergeLayersRequest spec;
            try
            {
                var jsonContent = File.ReadAllText(jsonPath);

                spec = JsonConvert.DeserializeObject<MergeLayersRequest>(jsonContent)!
                       ?? throw new InvalidOperationException("Deserialisation returned null");
            }
            catch (Autodesk.AutoCAD.Runtime.Exception ex)
            {
                var errorMsg = $"Invalid merge JSON: {ex.Message}";
                ed.WriteMessage($"\n{errorMsg}\n");
                return;
            }

            if (spec.DryRun)
            {
                var dryRunMsg = "*** DRY‑RUN: JSON parsed OK, no changes applied ***";
                ed.WriteMessage($"\n{dryRunMsg}\n");
                return;
            }

            using (DocumentLock docLock = doc.LockDocument())
            {
                using Transaction tr = db.TransactionManager.StartTransaction();
                try
                {
                    EnsureLinetypes(db, tr, spec.Linetypes);
                    EnsureLayers   (db, tr, spec.FreeAxezLayers);
                    PerformMerge   (db, tr, spec.Mappings);

                    tr.Commit();

                    try
                    {
                        db.SaveAs(doc.Name, true, DwgVersion.Current, db.SecurityParameters);
                        ed.WriteMessage($"\nDocument saved: {Path.GetFileName(doc.Name)}\n");
                    }
                    catch (Autodesk.AutoCAD.Runtime.Exception saveEx)
                    {
                        ed.WriteMessage($"\nWarning: Changes applied but save failed: {saveEx.Message}\n");
                        ed.WriteMessage("Please save the document manually (Ctrl+S)\n");
                    }

                    ed.WriteMessage("\nMerge layers – completed successfully.\n");
                }
                catch (Autodesk.AutoCAD.Runtime.Exception ex)
                {
                    tr.Abort();
                    ed.WriteMessage($"\nMerge layers FAILED: {ex.Message}\n");
                }
            }
        }

        private static void EnsureLinetypes(Database db, Transaction tr, IEnumerable<LinetypeDto> list)
        {
            if (list == null)
            {
                return;
            }

            var ltTable = (LinetypeTable)tr.GetObject(db.LinetypeTableId, OpenMode.ForRead);

            foreach (var dto in list)
            {
                try
                {
                    if (ltTable.Has(dto.Name))
                    {
                        continue;
                    }

                    if (!ltTable.IsWriteEnabled)
                    {
                        ltTable.UpgradeOpen();
                    }

                    var rec = new LinetypeTableRecord
                    {
                        Name     = dto.Name,
                        Comments = string.IsNullOrWhiteSpace(dto.Description) ? dto.Name : dto.Description
                    };

                    if (TryParsePattern(dto.PatternRaw, out double[] pattern))
                    {
                        rec.NumDashes = pattern.Length;
                        for (int i = 0; i < pattern.Length; i++)
                            rec.SetDashLengthAt(i, pattern[i]);
                    }
                    else
                    {
                        rec.NumDashes = 0;
                    }

                    ltTable.Add(rec);
                    tr.AddNewlyCreatedDBObject(rec, true);
                }
                catch
                {
                }
            }
        }

        private static bool TryParsePattern(string raw, out double[] pattern)
        {
            pattern = Array.Empty<double>();

            if (string.IsNullOrWhiteSpace(raw))
            {
                return false;
            }

            string cleanPattern = raw;
            if (raw.StartsWith("A,", StringComparison.OrdinalIgnoreCase))
            {
                cleanPattern = raw.Substring(2);
            }

            string[] parts = cleanPattern.Split(new[] { ',', ';' }, StringSplitOptions.RemoveEmptyEntries);

            var vals = new List<double>(parts.Length);
            foreach (string p in parts)
            {
                if (double.TryParse(p.Trim(), NumberStyles.Float, CultureInfo.InvariantCulture, out double v))
                {
                    vals.Add(v);
                }
            }

            if (vals.Count == 0)
            {
                return false;
            }

            pattern = vals.ToArray();
            return true;
        }

        private static void EnsureLayers(Database db, Transaction tr, IEnumerable<FreeAxezLayer> list)
        {
            if (list == null)
            {
                return;
            }

            var layerTbl = (LayerTable)tr.GetObject(db.LayerTableId, OpenMode.ForRead);
            var ltypeTbl = (LinetypeTable)tr.GetObject(db.LinetypeTableId, OpenMode.ForRead);

            foreach (var dto in list)
            {
                try
                {
                    LayerTableRecord rec;

                    if (layerTbl.Has(dto.Name))
                    {
                        rec = (LayerTableRecord)tr.GetObject(layerTbl[dto.Name], OpenMode.ForWrite);
                    }
                    else
                    {
                        if (!layerTbl.IsWriteEnabled)
                        {
                            layerTbl.UpgradeOpen();
                        }

                        rec = new LayerTableRecord { Name = dto.Name };
                        layerTbl.Add(rec);
                        tr.AddNewlyCreatedDBObject(rec, true);
                    }

                    rec.Color        = ParseColor(dto.Color);
                    rec.LineWeight   = MapLineweight(dto.Lineweight);
                    rec.Transparency = ToTransparency(dto.Transparency);
                    rec.LinetypeObjectId = ltypeTbl.Has(dto.Linetype) ? ltypeTbl[dto.Linetype] : ltypeTbl["Continuous"];
                }
                catch
                {
                }
            }
        }

        private static void PerformMerge(Database db, Transaction tr, IEnumerable<LayerMapping> maps)
        {
            if (maps == null)
            {
                return;
            }

            var ltbl = (LayerTable)tr.GetObject(db.LayerTableId, OpenMode.ForRead);
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;

            foreach (var m in maps)
            {
                try
                {
                    if (!ltbl.Has(m.SourceLayer))
                    {
                        continue;
                    }

                    if (!ltbl.Has(m.TargetLayer))
                    {
                        continue;
                    }

                    try
                    {
                        var srcId = ltbl[m.SourceLayer];
                        var dstId = ltbl[m.TargetLayer];

                        MergeLayers(db, tr, srcId, dstId);

                        ed.WriteMessage($"\nMerged layer '{m.SourceLayer}' to '{m.TargetLayer}'\n");
                    }
                    catch (Autodesk.AutoCAD.Runtime.Exception mergeEx)
                    {
                        ed.WriteMessage($"\nWarning: Failed to merge layer '{m.SourceLayer}' to '{m.TargetLayer}': {mergeEx.Message}\n");
                    }
                }
                catch
                {
                }
            }
        }

        private static void MergeLayers(Database db, Transaction tr, ObjectId srcId, ObjectId dstId)
        {
            var blockTable = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);

            foreach (ObjectId btrId in blockTable)
            {
                var btr = (BlockTableRecord)tr.GetObject(btrId, OpenMode.ForRead);

                foreach (ObjectId entId in btr)
                {
                    var ent = tr.GetObject(entId, OpenMode.ForRead) as Entity;
                    if (ent != null && ent.LayerId == srcId)
                    {
                        ent.UpgradeOpen();
                        ent.LayerId = dstId;
                    }
                }
            }

            // Only delete source layer if it's not protected (0 or Defpoints)
            var srcLayer = (LayerTableRecord)tr.GetObject(srcId, OpenMode.ForWrite, true);
            if (!IsProtectedLayer(srcLayer.Name))
            {
                srcLayer.Erase(true);
            }
        }

        private static bool IsProtectedLayer(string layerName)
        {
            if (string.IsNullOrEmpty(layerName))
                return false;

            return layerName.Equals("0", StringComparison.OrdinalIgnoreCase) ||
                   layerName.Equals("Defpoints", StringComparison.OrdinalIgnoreCase);
        }

        private static Color ParseColor(string spec)
        {
            if (string.IsNullOrWhiteSpace(spec) || spec.Equals("ByLayer", StringComparison.OrdinalIgnoreCase))
            {
                return Color.FromColorIndex(ColorMethod.ByLayer, 256);
            }

            if (spec.StartsWith("#") && spec.Length == 7)
            {
                try
                {
                    byte r = Convert.ToByte(spec.Substring(1, 2), 16);
                    byte g = Convert.ToByte(spec.Substring(3, 2), 16);
                    byte b = Convert.ToByte(spec.Substring(5, 2), 16);
                    return Color.FromRgb(r, g, b);
                }
                catch
                {
                    return Color.FromColorIndex(ColorMethod.ByAci, 7);
                }
            }

            return Color.FromColorIndex(ColorMethod.ByAci, 7);
        }

        private static LineWeight MapLineweight(double mm)
        {
            if (mm <= 0)
            {
                return LineWeight.ByLayer;
            }

            int h = (int)Math.Round(mm * 100);

            var result = h switch
            {
                <= 5   => LineWeight.LineWeight005,
                <= 9   => LineWeight.LineWeight009,
                <= 13  => LineWeight.LineWeight013,
                <= 15  => LineWeight.LineWeight015,
                <= 18  => LineWeight.LineWeight018,
                <= 20  => LineWeight.LineWeight020,
                <= 25  => LineWeight.LineWeight025,
                <= 30  => LineWeight.LineWeight030,
                <= 35  => LineWeight.LineWeight035,
                <= 40  => LineWeight.LineWeight040,
                <= 50  => LineWeight.LineWeight050,
                <= 53  => LineWeight.LineWeight053,
                <= 60  => LineWeight.LineWeight060,
                <= 70  => LineWeight.LineWeight070,
                <= 80  => LineWeight.LineWeight080,
                <= 90  => LineWeight.LineWeight090,
                <= 100 => LineWeight.LineWeight100,
                <= 106 => LineWeight.LineWeight106,
                <= 120 => LineWeight.LineWeight120,
                <= 140 => LineWeight.LineWeight140,
                <= 158 => LineWeight.LineWeight158,
                <= 200 => LineWeight.LineWeight200,
                _      => LineWeight.LineWeight211
            };

            return result;
        }

        private static Transparency ToTransparency(int val)
        {
            if (val < 0)
            {
                return new Transparency();
            }

            if (val > 90)
            {
                val = 90;
            }

            return new Transparency((byte)val);
        }
    }
}
