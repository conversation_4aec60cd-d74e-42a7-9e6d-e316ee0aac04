﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using FreeAxez.Addin.Infrastructure.UI.Views;
using System.Windows.Interop;

namespace FreeAxez.Addin.Infrastructure.UI.Services
{
    public static class SelectLevelWindow
    {
        public static bool? ShowDialog(string title, out List<Level> selectedLevels)
        {
            var selectLevelWindow = new SelectLevelView(title);
            var handler = new WindowInteropHelper(selectLevelWindow);
            handler.Owner = RevitManager.UIApplication.MainWindowHandle;
            var result = selectLevelWindow.ShowDialog();

            if (result == true) 
            {
                selectedLevels = (selectLevelWindow.DataContext as SelectLevelViewModel).Levels
                    .Where(l => l.IsCheck)
                    .Select(l => l.Level)
                    .Cast<Level>()
                    .ToList();
            }
            else
            {
                selectedLevels = new List<Level>();
            }

            return result;
        }
    }
}
