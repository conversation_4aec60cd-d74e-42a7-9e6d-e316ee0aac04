﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.3" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.Authentication" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.Authentication.Abstractions" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.Authentication.Cookies" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.Authentication.Core" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.Cryptography.Internal" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.Cryptography.KeyDerivation" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.DataProtection" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.DataProtection.Abstractions" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.Hosting.Abstractions" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.Hosting.Server.Abstractions" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.Http" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.Http.Abstractions" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.Http.Extensions" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.Http.Features" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.Identity" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.AspNetCore.WebUtilities" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.Azure.KeyVault.Core" version="1.0.0" targetFramework="net462" />
  <package id="Microsoft.Azure.Storage.Blob" version="11.1.2" targetFramework="net462" />
  <package id="Microsoft.Azure.Storage.Common" version="11.1.2" targetFramework="net462" />
  <package id="Microsoft.Azure.Storage.Queue" version="11.1.2" targetFramework="net462" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.1.0" targetFramework="net462" />
  <package id="Microsoft.Bcl.HashCode" version="1.1.0" targetFramework="net462" />
  <package id="Microsoft.EntityFrameworkCore.Abstractions" version="3.1.3" targetFramework="net462" />
  <package id="Microsoft.EntityFrameworkCore.Analyzers" version="3.1.3" targetFramework="net462" />
  <package id="Microsoft.Extensions.Caching.Abstractions" version="3.1.3" targetFramework="net462" />
  <package id="Microsoft.Extensions.Caching.Memory" version="3.1.3" targetFramework="net462" />
  <package id="Microsoft.Extensions.Configuration" version="3.1.3" targetFramework="net462" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="3.1.3" targetFramework="net462" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="3.1.3" targetFramework="net462" />
  <package id="Microsoft.Extensions.DependencyInjection" version="3.1.3" targetFramework="net462" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="3.1.3" targetFramework="net462" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.Extensions.Identity.Core" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.Extensions.Logging" version="3.1.3" targetFramework="net462" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="3.1.3" targetFramework="net462" />
  <package id="Microsoft.Extensions.ObjectPool" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.Extensions.Options" version="3.1.3" targetFramework="net462" />
  <package id="Microsoft.Extensions.Primitives" version="3.1.3" targetFramework="net462" />
  <package id="Microsoft.Extensions.WebEncoders" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.Net.Http.Headers" version="2.2.0" targetFramework="net462" />
  <package id="Microsoft.Win32.Registry" version="4.5.0" targetFramework="net462" />
  <package id="Newtonsoft.Json" version="10.0.2" targetFramework="net462" />
  <package id="Serilog" version="2.9.0" targetFramework="net462" />
  <package id="Serilog.Sinks.File" version="4.1.0" targetFramework="net462" />
  <package id="System.Buffers" version="4.5.0" targetFramework="net462" />
  <package id="System.Collections.Immutable" version="1.7.0" targetFramework="net462" />
  <package id="System.ComponentModel.Annotations" version="4.7.0" targetFramework="net462" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.7.0" targetFramework="net462" />
  <package id="System.Memory" version="4.5.3" targetFramework="net462" />
  <package id="System.Numerics.Vectors" version="4.4.0" targetFramework="net462" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.7.1" targetFramework="net462" />
  <package id="System.Security.AccessControl" version="4.5.0" targetFramework="net462" />
  <package id="System.Security.Cryptography.Xml" version="4.5.0" targetFramework="net462" />
  <package id="System.Security.Permissions" version="4.5.0" targetFramework="net462" />
  <package id="System.Security.Principal.Windows" version="4.5.0" targetFramework="net462" />
  <package id="System.Text.Encodings.Web" version="4.5.0" targetFramework="net462" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.2" targetFramework="net462" />
  <package id="WindowsAzure.Storage" version="9.3.3" targetFramework="net462" />
</packages>