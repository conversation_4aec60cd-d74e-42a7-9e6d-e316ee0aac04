<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls.SideBar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="100"
             Background="Transparent">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Border Grid.Column="0" 
                Background="{StaticResource Blue500}" 
                BorderBrush="{StaticResource Gray500}" 
                BorderThickness="1" 
                CornerRadius="10 0 0 10">
            <StackPanel>
                <StackPanel Margin="10">
                    <Image Source="/FreeAxez.Addin;component/Infrastructure/UI/Images/ft-logo.png"
                           Width="80" 
                           Height="12" 
                           HorizontalAlignment="Left"></Image>
                    <TextBlock Text="{Binding SidebarTitle}" 
                               Style="{StaticResource TextBase}"
                               Foreground="White"
                               FontWeight="Bold"
                               TextWrapping="Wrap"
                               TextAlignment="Center"
                               Margin="0 10 0 0"/>
                </StackPanel>
                <ContentPresenter Content="{Binding InnerContent}"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
