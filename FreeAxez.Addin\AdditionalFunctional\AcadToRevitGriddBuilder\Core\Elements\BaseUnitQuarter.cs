using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using NetTopologySuite.Geometries;
using LineSegment = NetTopologySuite.Geometries.LineSegment;
using Point = FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data.Point;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Core.Elements;

public class BaseUnitQuarter : BaseFaElement
{
    private static readonly BaseUnitQuarterConfiguration _config = new();

    private BaseUnitQuarter(int id, List<LineSegmentData> segments) : base(id, segments)
    {
    }

    public BaseUnitQuarter() : base()
    {
    }

    public override ElementTypeConfiguration Configuration => _config;

    protected override void CalculateCenter(List<LineSegmentData> segments)
    {
        // Find two sides with quarter length
        var quarterSides = segments.Where(d => Configuration.IsValidLength(d.Segment.Length))
            .ToList();
        if (quarterSides.Count < 2)
            throw new InvalidOperationException("BaseUnitQuarter requires at least 2 valid quarter-length segments");

        var seg1 = quarterSides[0].Segment;
        var seg2 = quarterSides[1].Segment;

        var hypotenuseEnd = BuildHypotenuseEnd(seg1, seg2);
        Center = new Point(hypotenuseEnd.X, hypotenuseEnd.Y, 0);
    }

    protected override void CalculateRotationAngle(List<LineSegmentData> segments)
    {
        // Find two sides with quarter length
        var quarterSides = segments.Where(d => Configuration.IsValidLength(d.Segment.Length))
            .ToList();
        if (quarterSides.Count < 2)
        {
            RotationAngle = 0; // Default rotation if not enough quarter sides
            return;
        }

        // Base position: one side up, one side right
        // Calculate rotation to achieve this
        var angles = quarterSides.Select(s => s.Data.angle).ToArray();

        // Additional safety check
        if (angles.Length < 2)
        {
            RotationAngle = 0;
            return;
        }

        // Normalize angles
        for (var i = 0; i < angles.Length; i++)
        {
            while (angles[i] < 0) angles[i] += 360;
            while (angles[i] >= 360) angles[i] -= 360;
        }

        // Find the rotation needed to align one side to 90° (up) and another to 0° (right)
        double targetRotation = 0;

        // Check if we can align to cardinal directions
        foreach (var angle in angles)
        {
            var rotationToUp = 90 - angle;
            while (rotationToUp < 0) rotationToUp += 360;
            while (rotationToUp >= 360) rotationToUp -= 360;

            // Check if this rotation would put the other angle close to 0° (right)
            var otherAngles = angles.Where(a => Math.Abs(a - angle) > 10).ToArray();
            if (otherAngles.Length == 0) continue; // Skip if no other distinct angle found

            var otherAngle = otherAngles[0];
            var otherAfterRotation = otherAngle + rotationToUp;
            while (otherAfterRotation < 0) otherAfterRotation += 360;
            while (otherAfterRotation >= 360) otherAfterRotation -= 360;

            if (Math.Abs(otherAfterRotation) < 10 || Math.Abs(otherAfterRotation - 360) < 10)
            {
                targetRotation = rotationToUp;
                break;
            }
        }

        RotationAngle = SnapToCardinal(targetRotation);
    }

    protected override bool IsValidComponentForThisType(List<LineSegmentData> component, HashSet<LineSegmentData> usedSegments)
    {
        if (component.All(d => usedSegments.Contains(d)))
            return false;

        // Find segments with quarter length that are not used
        var quarterSides = component.Where(d => !usedSegments.Contains(d) &&
                                                Configuration.IsValidLength(d.Segment.Length))
            .ToList();

        return quarterSides.Count >= 2;
    }

    private static Coordinate BuildHypotenuseEnd(LineSegment seg1, LineSegment seg2)
    {
        // Find intersection of infinite lines
        var intersection = seg1.LineIntersection(seg2);
        if (intersection == null)
            throw new InvalidOperationException("Lines are parallel or coincident");

        // Calculate unit direction vectors
        var v1 = new Coordinate(seg1.P1.X - seg1.P0.X, seg1.P1.Y - seg1.P0.Y);
        var v2 = new Coordinate(seg2.P1.X - seg2.P0.X, seg2.P1.Y - seg2.P0.Y);
        Normalize(v1);
        Normalize(v2);

        // Assume both segments have the same length L
        var L = seg1.Length;

        // Calculate bisector direction (v1 + v2) - already at 45°
        var bis = new Coordinate(v1.X + v2.X, v1.Y + v2.Y);
        Normalize(bis);
        var hypoLen = L * Math.Sqrt(2);

        // Calculate end point of hypotenuse
        return new Coordinate(
            intersection.X + bis.X * hypoLen,
            intersection.Y + bis.Y * hypoLen
        );
    }

    private static void Normalize(Coordinate v)
    {
        var len = Math.Sqrt(v.X * v.X + v.Y * v.Y);
        if (len > 0)
        {
            v.X /= len;
            v.Y /= len;
        }
    }
}