<Window x:Class="FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Views.ProgressView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:viewModels="clr-namespace:FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.ViewModels"
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen"
        Title="Прогресс создания элементов" SizeToContent="Height" Width="400"
        ResizeMode="NoResize">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Window.DataContext>
        <viewModels:ProgressViewModel/>
    </Window.DataContext>
    <Grid>
        <StackPanel Margin="20">
            <TextBlock Style="{StaticResource TextBase}"
                       Margin="0 10 0 15"
                       TextWrapping="Wrap"
                       HorizontalAlignment="Center"
                       Text="Создание элементов из AutoCAD может занять некоторое время."/>
            
            <ProgressBar Height="12" 
                         Value="{Binding ProgressValue}" 
                         Maximum="100"
                         Foreground="{StaticResource Blue500}" 
                         Margin="0 10"/>
            
            <TextBlock Margin="0 5 0 5"
                       Style="{StaticResource TextBase}"
                       FontWeight="SemiBold"
                       HorizontalAlignment="Center"
                       TextWrapping="Wrap"
                       Text="{Binding StepStatus}"/>
            
            <TextBlock Margin="0 0 0 15"
                       Style="{StaticResource TextBase}"
                       FontWeight="Normal"
                       HorizontalAlignment="Center"
                       TextWrapping="Wrap"
                       Text="{Binding TaskStatus}"/>
            
            <Button Content="Отменить" 
                    Margin="0 5"
                    Style="{StaticResource ButtonOutlinedRed}"
                    IsEnabled="{Binding IsCancelEnabled}"
                    Command="{Binding CancelCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}" />
        </StackPanel>
    </Grid>
</Window>
