﻿namespace FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Models
{
    public class CurbTagOptions
    {
        public CurbTagOptions(double lengthToCenterOfTag, bool tagCurbsVisibleInView)
        {
            LengthToCenterOfTag = lengthToCenterOfTag;
            TagCurbsVisibleInView = tagCurbsVisibleInView;
        }

        public double LengthToCenterOfTag { get; }
        public bool TagCurbsVisibleInView { get; }
    }
}
