﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.ExportCutsheet.Views.ExportCutsheetView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:viewModels="clr-namespace:FreeAxez.Addin.AdditionalFunctional.ExportCutsheet.ViewModels"
        Height="150" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Title="Export Cutsheets"
        Closing="Window_Closing">

    <Window.DataContext>
        <viewModels:ExportCutsheetViewModel/>
    </Window.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto"/>
            <RowDefinition Height="auto"/>
            <RowDefinition Height="auto"/>
            <RowDefinition Height="auto"/>
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="auto"/>
        </Grid.ColumnDefinitions>

        <ProgressBar IsIndeterminate="{Binding IsDownloading}"
                     VerticalAlignment="Bottom"
                     BorderBrush="White"
                     Grid.Row="0"
                     Grid.ColumnSpan="2"
                     Margin="10 5 10 0"/>

        <TextBox Text="{Binding FolderPath,
                       UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                 Grid.Row="1"
                 Grid.ColumnSpan="2"
                 Width="305"
                 Height="25"
                 VerticalContentAlignment="Center"
                 HorizontalAlignment="Left"
                 Margin="10 10 0 5"/>

        <Button Content="Browse"
                Command="{Binding BrowseFolderCommand}"
                Grid.Row="1"
                Grid.Column="1"
                Width="50" Height="25"
                Padding="3"
                Margin="5 10 10 5"
                HorizontalAlignment="Right"/>

        <CheckBox IsChecked="{Binding IsOpenDirectory}"
                  Content="Open Folder"
                  Grid.Row="2"
                  Grid.Column="0"
                  Margin="10 5 5 5"/>

        <Button Content="Export"
                Command="{Binding ExportCutsheetCommand}"
                CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor,
                                    AncestorType={x:Type Window}}}"
                Grid.Row="3"
                Grid.Column="0"
                Margin="0 5 5 0"
                HorizontalAlignment="Right"
                Height="25" Width="100"/>

        <Button Content="Cancel"
                Command="{Binding CancelCommand}"
                CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor,
                                    AncestorType={x:Type Window}}}"
                Grid.Row="3"
                Grid.Column="2"
                Margin="5 5 10 0"
                HorizontalAlignment="Right"
                Height="25" Width="100"/>

    </Grid>
</Window>