using System;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Dialog;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.RevitHandlers
{
    public class FamilyEditHandler : BaseExternalEventHandler
    {
        private LibraryItemVm _libraryItemVm;
        private object _viewModel;
        private readonly DialogManager _dialogManager;

        public FamilyEditHandler()
        {
            _dialogManager = new DialogManager();
        }

        public void SetData(LibraryItemVm libraryItemVm, object viewModel)
        {
            _libraryItemVm = libraryItemVm ?? throw new ArgumentNullException(nameof(libraryItemVm));
            _viewModel = viewModel;
        }

        public override void ExecuteInternal(UIApplication app)
        {
            try
            {
                // Show dialog with callback that handles the result
                _dialogManager.ShowEditFamilyDialog(_libraryItemVm, result =>
                {
                    try
                    {
                        // Update ViewModel if dialog was successful
                        if (result == true)
                        {
                            UpdateViewModel(false);
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"Error updating ViewModel after dialog: {ex.Message}");
                        UpdateViewModel(true);
                    }
                    finally
                    {
                        _libraryItemVm = null;
                        _viewModel = null;
                    }
                });
            }
            catch (Exception ex)
            {
                ErrorHandler.HandleError("Error in family edit operation", ex, onError: () => UpdateViewModel(true));
                _libraryItemVm = null;
                _viewModel = null;
            }
        }

        private void UpdateViewModel(bool isError)
        {
            if (_viewModel == null)
                return;

            try
            {
                if (_viewModel is FamiliesPageVm familiesVm)
                {
                    if (!isError)
                        _ = familiesVm.LoadData();
                    familiesVm.IsLoading = false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error updating ViewModel: {ex.Message}");
            }
        }
    }
}
