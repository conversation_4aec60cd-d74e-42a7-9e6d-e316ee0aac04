﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Utils;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Models
{
    public class CurbFamily
    {
        private const string FamilyNameKey = "Curb";
        private const string CornerFamilyNameKey = "Corner";

        public CurbFamily()
        {
            Symbols = GetFamilySymbols();
            InstancesOnView = GetInstancesOnView(RevitManager.Document.ActiveView);
        }

        public List<FamilySymbol> Symbols { get; set; }
        public List<FamilyInstance> InstancesOnView { get; set; }

        public static bool IsCorner(FamilyInstance instance)
        {
            return instance.Symbol.FamilyName.Contains(CornerFamilyNameKey);
        }

        public bool IsFamilyNotExist(out string message)
        {
            if (Symbols.Count == 0)
            {
                message = $"There is no curb family in the project with a name that includes {<PERSON><PERSON>ame<PERSON>ey}.";
                return true;
            }

            message = "";
            return false;
        }

        public bool IsNoPlacedInstancesOnView(out string message)
        {
            if (InstancesOnView.Count == 0)
            {
                message = $"There are no instances of the curb family in the current view.";
                return true;
            }

            message = "";
            return false;
        }

        public List<FamilyInstance> SelectInstances()
        {
            var output = new List<FamilyInstance>();

            try
            {
                var selectedCurbIds = RevitManager.UIDocument.Selection.PickObjects(
                    ObjectType.Element, new CurbsSelectionFilter(InstancesOnView), "Select the curbs to tag.");
                output = selectedCurbIds.Select(r => RevitManager.Document.GetElement(r) as FamilyInstance).ToList();
            }
            catch { }

            return output;
        }

        private List<FamilySymbol> GetFamilySymbols()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsElementType()
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .Cast<FamilySymbol>()
                .Where(s => s.FamilyName.Contains(FamilyNameKey))
                .ToList();
        }

        private List<FamilyInstance> GetInstancesOnView(View view)
        {
            return new FilteredElementCollector(RevitManager.Document, view.Id)
                .WhereElementIsNotElementType()
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .Cast<FamilyInstance>()
                .Where(s => s.Symbol.FamilyName.Contains(FamilyNameKey))
                .ToList();
        }
    }
}
