using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel
{
    /// <summary>
    /// ViewModel for unified progress window
    /// </summary>
    public class ProgressWindowVm : BaseViewModel
    {
        private string _title = "Processing...";
        private string _statusMessage = "Please wait...";
        private bool _isIndeterminate = true;
        private int _progressValue = 0;

        /// <summary>
        /// Window title
        /// </summary>
        public string Title
        {
            get => _title;
            set
            {
                if (_title != value)
                {
                    _title = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Current status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                if (_statusMessage != value)
                {
                    _statusMessage = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Whether to show indeterminate progress (spinner) or determinate (percentage)
        /// </summary>
        public bool IsIndeterminate
        {
            get => _isIndeterminate;
            set
            {
                if (_isIndeterminate != value)
                {
                    _isIndeterminate = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Progress value (0-100) for determinate progress
        /// </summary>
        public int ProgressValue
        {
            get => _progressValue;
            set
            {
                if (_progressValue != value)
                {
                    _progressValue = value;
                    OnPropertyChanged();
                }
            }
        }
    }
}
