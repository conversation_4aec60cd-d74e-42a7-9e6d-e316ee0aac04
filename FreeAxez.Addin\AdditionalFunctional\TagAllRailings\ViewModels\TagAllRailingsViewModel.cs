﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using FreeAxez.Addin.AdditionalFunctional.TagAllRailings.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllRailings.ViewModels
{
    public class TagAllRailingsViewModel : BaseViewModel
    {
        private double _lengthToCenterOfTag;
        public TagAllRailingsViewModel()
        {
            _lengthToCenterOfTag = Properties.Settings.Default.TagAllRailingsLengthToCenterOfTag;

            RailingTagTypes = new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_StairsRailingTags)
                .WhereElementIsElementType()
                .ToElements()
                .OrderBy(t => t.Name)
                .ToList();

            SelectedRailingTagType = RailingTagTypes.FirstOrDefault(t => t.Name == Properties.Settings.Default.TagAllRailingsSelectedRailingTagTypeName);
            if (SelectedRailingTagType == null)
            {
                SelectedRailingTagType = RailingTagTypes.First();
            }

            TagElementsWithEmptyParameter = Properties.Settings.Default.TagAllRailingsTagElementsWithEmptyParameter;
            StartTag = Properties.Settings.Default.TagAllRailingsStartTag;
            EndTag = Properties.Settings.Default.TagAllRailingsEndTag;
            
            TagVisibleInView = Properties.Settings.Default.TagAllRailingsTagVisibleInView;
            // In order for the radio buttons to be displayed correctly,
            // need to explicitly set the value of true for the active option if TagVisibleInView is false
            TagSelected = !TagVisibleInView;

            CreateCommand = new RelayCommand(OnCreateCommandExecute);
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
        }


        public List<Element> RailingTagTypes { get; set; }

        public Element SelectedRailingTagType { get; set; }

        public string LengthToCenterOfTag
        {
            get
            {
                return ProjectUnitsConverter.FormatLength(_lengthToCenterOfTag);
            }
            set
            {
                if (ProjectUnitsConverter.TryParseLength(value, out _lengthToCenterOfTag))
                {
                    OnPropertyChanged();
                }
            }
        }

        public bool StartTag { get; set; }

        public bool EndTag { get; set; }

        public bool TagElementsWithEmptyParameter { get; set; }

        public bool TagVisibleInView { get; set; }

        public bool TagSelected { get; set; }

        public ICommand CreateCommand { get; set; }
        private void OnCreateCommandExecute(object p)
        {
            (p as Window).Close();

            var railings = new FilteredElementCollector(RevitManager.Document, RevitManager.UIDocument.ActiveView.Id)
                .OfCategory(BuiltInCategory.OST_StairsRailing)
                .WhereElementIsNotElementType()
                .Cast<Railing>()
                .ToList();

            if (TagSelected)
            {
                try
                {
                    railings = RevitManager.UIDocument.Selection
                        .PickObjects(Autodesk.Revit.UI.Selection.ObjectType.Element, new RailingSelectionFilter(railings), "Select railings to tag.")
                        .Select(r => RevitManager.Document.GetElement(r) as Railing)
                        .ToList();
                }
                catch
                {
                    // When user cancel selection an exception is throw.
                    // This means that the user does not want to tag the railings.
                    railings.Clear();
                }
            }

            if (!TagElementsWithEmptyParameter)
            {
                railings = railings.Where(r => TagParameter.HasValue(r)).ToList();
            }

            var tagCreator = new TagCreator(railings, SelectedRailingTagType.Id, _lengthToCenterOfTag, StartTag, EndTag);
            var createdTags = tagCreator.CreateTags();

            SaveSettings();

            InfoDialog.ShowDialog("Report", $"Created {createdTags.Count} tags.");
        }

        public ICommand CancelCommand { get; set; }
        private void OnCancelCommandExecute(object p)
        {
            (p as Window).Close();
        }


        private void SaveSettings()
        {
            Properties.Settings.Default.TagAllRailingsLengthToCenterOfTag = _lengthToCenterOfTag;
            Properties.Settings.Default.TagAllRailingsStartTag = StartTag;
            Properties.Settings.Default.TagAllRailingsEndTag = EndTag;
            Properties.Settings.Default.TagAllRailingsSelectedRailingTagTypeName = SelectedRailingTagType.Name;
            Properties.Settings.Default.TagAllRailingsTagElementsWithEmptyParameter = TagElementsWithEmptyParameter;
            Properties.Settings.Default.TagAllRailingsTagVisibleInView = TagVisibleInView;

            Properties.Settings.Default.Save();
        }
    }
}
