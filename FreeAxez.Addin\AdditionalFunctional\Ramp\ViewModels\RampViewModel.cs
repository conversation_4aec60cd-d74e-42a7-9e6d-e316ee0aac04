﻿using System;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Collections.Generic;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.AdditionalFunctional.Ramp.Enums;
using FreeAxez.Addin.AdditionalFunctional.Ramp.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.AdditionalFunctional.Ramp.Models;
using FreeAxez.Addin.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.Ramp.ViewModels
{
    public class RampViewModel : BaseViewModel
    {
        private GriddType _griddType;
        private RampSlope _rampSlope;
        private bool _isLeftSideSlopeChecked;
        private bool _isRightSideSlopeChecked;

        public RampViewModel()
        {
            _griddType = Properties.Settings.Default.RampGriddType ? GriddType.Gridd40 : GriddType.Gridd70;

            if (Enum.TryParse($"Slope{Properties.Settings.Default.RampRampSlope}",
                out RampSlope parsedRampSlope))
            {
                _rampSlope = parsedRampSlope;
            }
            else
            {
                _rampSlope = RampSlope.Slope12;
            }
            _isLeftSideSlopeChecked = Properties.Settings.Default.RampIsLeftSideSlopeChecked;
            _isRightSideSlopeChecked = Properties.Settings.Default.RampIsRightSideSlopeChecked;

            SelectLinesCommand = new RelayCommand(OnSelectLinesCommandExecute);
        }

        public GriddType GriddType
        {
            get => _griddType;
            set
            {
                if (_griddType != value)
                {
                    _griddType = value;
                    OnPropertyChanged(nameof(GriddType));
                }
            }
        }

        public RampSlope RampSlope
        {
            get => _rampSlope;
            set
            {
                if (value != _rampSlope)
                {
                    _rampSlope = value;
                    OnPropertyChanged(nameof(RampSlope));
                }
            }
        }

        public bool IsLeftSideSlopeChecked
        {
            get { return _isLeftSideSlopeChecked; }
            set
            {
                if (_isLeftSideSlopeChecked != value)
                {
                    _isLeftSideSlopeChecked = value;
                    OnPropertyChanged(nameof(IsLeftSideSlopeChecked));
                }
            }
        }

        public bool IsRightSideSlopeChecked
        {
            get { return _isRightSideSlopeChecked; }
            set
            {
                if (_isRightSideSlopeChecked != value)
                {
                    _isRightSideSlopeChecked = value;
                    OnPropertyChanged(nameof(IsRightSideSlopeChecked));
                }
            }
        }

        public ICommand SelectLinesCommand { get; }

        private void OnSelectLinesCommandExecute(object p)
        {
            SaveSettings();

            (p as Window).Close();

            List<CurveElement> selectedLines = PickLines();

            var rampBuilder = new RampBuilder(selectedLines,
                                              _griddType,
                                              _rampSlope,
                                              _isLeftSideSlopeChecked,
                                              _isRightSideSlopeChecked);

            rampBuilder.Build();
        }

        private List<CurveElement> PickLines()
        {
            List<CurveElement> selectedLines;

            if (!(RevitManager.Document.ActiveView is ViewPlan))
            {
                throw new NullReferenceException("Plan view wasn't selected. Select plan view.");
            }

            selectedLines = RevitManager.UIDocument.Selection
                .PickObjects(ObjectType.Element, new LineSelectionFilter())
                .Select(r => RevitManager.Document.GetElement(r.ElementId))
                .Cast<CurveElement>()
                .ToList();

            if (selectedLines.Count == 0)
            {
                throw new NullReferenceException("No line was selected.");
            }

            return selectedLines;
        }

        private void SaveSettings()
        {
            Properties.Settings.Default.RampGriddType = _griddType == GriddType.Gridd40;
            Properties.Settings.Default.RampRampSlope = int.Parse(Enum.GetName(
                typeof(RampSlope), _rampSlope).Replace("Slope", ""));
            Properties.Settings.Default.RampIsLeftSideSlopeChecked = _isLeftSideSlopeChecked;
            Properties.Settings.Default.RampIsRightSideSlopeChecked = _isRightSideSlopeChecked;

            Properties.Settings.Default.Save();
        }
    }
}