﻿using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.CountBaseUnits;
using FreeAxez.Addin.AdditionalFunctional.DeleteView;
using FreeAxez.Addin.AdditionalFunctional.DeleteViewBySize;
using FreeAxez.Addin.AdditionalFunctional.ExportCAD;
using FreeAxez.Addin.AdditionalFunctional.ExportForClient;
using FreeAxez.Addin.AdditionalFunctional.ExportForVR;
using FreeAxez.Addin.AdditionalFunctional.LineToFlexPipe;
using FreeAxez.Addin.AdditionalFunctional.FlexPipeToLine;
using FreeAxez.Addin.AdditionalFunctional.LineToRailing;
using FreeAxez.Addin.AdditionalFunctional.RailingToLine;
using FreeAxez.Addin.AdditionalFunctional.RoomToRegion;
using FreeAxez.Addin.AdditionalFunctional.TagAll;
using FreeAxez.Addin.AdditionalFunctional.TagAllRailings;
using FreeAxez.Addin.AdditionalFunctional.TagRepeatingSupercomponent;
using FreeAxez.Addin.AdditionalFunctional.TransferParameterValue;
using FreeAxez.Addin.Commands;
using FreeAxez.Addin.Services;
using System.IO;
using System.Reflection;
using System.Windows.Media.Imaging;
using FreeAxez.Addin.AdditionalFunctional.Pallets;
using FreeAxez.Addin.AdditionalFunctional.FloorBoxNumbering;
using FreeAxez.Addin.AdditionalFunctional.Undersheet;
using FreeAxez.Addin.AdditionalFunctional.LineLength;
using FreeAxez.Addin.AdditionalFunctional.ElementNumbering;
using FreeAxez.Addin.AdditionalFunctional.CutUnitByVoid;
using FreeAxez.Addin.AdditionalFunctional.Frame;
using FreeAxez.Addin.AdditionalFunctional.WhipExport;
using FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement;
using FreeAxez.Addin.AdditionalFunctional.Ramp;
using FreeAxez.Addin.AdditionalFunctional.WhipTypeReplace;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM;
using FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuitUpdate;
using FreeAxez.Addin.AdditionalFunctional.PanelScheduleCreation;
using FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates;
using FreeAxez.Addin.AdditionalFunctional.TagAllCurbs;
using FreeAxez.Addin.AdditionalFunctional.TagAllFrames;
using FreeAxez.Addin.AdditionalFunctional.TagRamp;
using FreeAxez.Addin.AdditionalFunctional.ExportCutsheet;
using Autodesk.Windows;
using UIFramework;
using RibbonItem = Autodesk.Revit.UI.RibbonItem;
using RibbonPanel = Autodesk.Revit.UI.RibbonPanel;
using System.Collections.Generic;
using FreeAxez.Addin.Infrastructure;
using System.Threading.Tasks;
using System;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Commands;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Utils;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Model;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Services;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.View;
using System.Linq;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Dialog;
using FreeAxez.Addin.AdditionalFunctional.GrommetGriddReplacement;
using FreeAxez.Addin.AdditionalFunctional.CreateTapOff;
using FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel;
using FreeAxez.Addin.AdditionalFunctional.FamilyCleanupTool;
using FreeAxez.Addin.AdditionalFunctional.ScopeInstructions;
using FreeAxez.Addin.AdditionalFunctional.LegendManagement;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent;
using FreeAxez.Addin.AdditionalFunctional.LevelViewManager;
using FreeAxez.Addin.AdditionalFunctional.FindAndReplaceViewName;
using FreeAxez.Addin.AdditionalFunctional.SheetTitleEditor;
using FreeAxez.Addin.AdditionalFunctional.ViewScale;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager;

namespace FreeAxez.Addin
{
    public class StartupApp : IExternalApplication
    {
        private readonly List<RibbonPanel> _panels = new();
        private readonly List<RibbonPanel> _toolTipPanels = new();
        private readonly List<PushButton> _managementButtons = new();
        private PushButton _libraryAdminButton;
        private IDialogService _dialogService;

        private const string FreeAxezTabName = "FreeAxez";
        private const string RaisedFloorPanelName = "Raised Floor";
        private const string CablePanelName = "Cable";
        private const string TagPanelName = "Tag";
        private const string ParameterPanelName = "Parameter";
        private const string ViewPanelName = "View";
        private const string ExportPanelName = "Export";
        private const string ManagementPanelName = "Management";
        private const string CreateButtonName = "Create New";
        private const string OpenButtonName = "Open";
        private const string AddRegionButtonName = "Add Region";
        private const string EditRegionButtonName = "Update Region";
        private const string LineToFlexPipeButtonName = "Line To\nFlex Pipe";
        private const string FlexPipeToLineButtonName = "Flex Pipe\nTo Line";
        private const string TagAllButtonName = "Tag All";
        private const string TagRepeatingButtonName = "Tag BC";
        private const string RailingToLineButtonName = "Railing\nTo Line";
        private const string LineToRailingButtonName = "Line To\nRailing";
        private const string RoomToRegionButtonName = "Room To\nRegion";
        private const string TransferParameterValueButtonName = "Transfer\nValues";
        private const string TransferViewTemplatesButtonName = "Transfer\nView Templates";
        private const string DeleteViewBySizeButtonName = "Delete\nSize";
        private const string DeleteViewButtonName = "Delete\nView";
        private const string TagAllRailingsButtonName = "Tag All\nRailings";
        private const string CountBaseUnitsButtonName = "Count Base\nUnits";
        private const string ExportForClientButtonName = "Export\nFor Client";
        private const string ExportForVRButtonName = "Export\nFor VR";
        private const string ExportToCADButtonName = "Export\nTo CAD";
        private const string StageButtonName = "Stage";
        private const string FloorBoxesMarkingButtonName = "Floor Box\nNumbering";
        private const string UndersheetButtonName = "Undersheet";
        private const string LineLengthButtonName = "Line\nLength";
        private const string ElementNumberingButtonName = "Element\nNumbering";
        private const string CutUnitByVoidButtonName = "Cut\nElement";
        private const string FrameButtonName = "Frame";
        private const string BaseUnitReplacementButtonName = "Base Unit\nReplacement";
        private const string GrommetGriddReplacementButtonName = "Grommet Gridd\nReplacement";
        private const string RampButtonName = "Ramp";
        private const string WhipTypeReplaceButtonName = "Whip Type\nReplace";
        private const string WhipExportButtonName = "Check\nWhip";
        private const string ElectricalCircuitButtonName = "Electrical\nCircuit";
        private const string ExportGriddBOMButtonName = "Export\nGridd BOM";
        private const string ExportPowerBomButtonName = "Export\nPower BOM";
        private const string ElectricalCircuitUpdateButtonName = "Circuit\nUpdate";
        private const string PanelSchedulePlacementButtonName = "Place\nSchedules";
        private const string PanelScheduleCreationButtonName = "Create\nSchedules";
        private const string TagAllCurbsButtonName = "Tag\nCurbs";
        private const string TagAllFramesButtonName = "Tag\nFrames";
        private const string TagRampsButtonName = "Tag\nRamps";
        private const string ExportCutsheetsButtonName = "Export\nCutsheets";
        private const string CreateTapOffButtonName = "Create\nTap Off";
        private const string TemplatesToExcelButtonName = "Export View\nTemplates";
        private const string TemplatesFromExcelButtonName = "Import View\nTemplates Updates";

        private const string ScopeInstructionsButtonName = $"Scope\nInstructions";
        private const string LegendsButtonName = "Legends";
        private const string LevelViewManagerButtonName = "Level View\nManager";
        private const string RenameViewButtonName = "Rename\nView";
        private const string SheetTitleEditorButtonName = "Sheet Title\nEditor";
        private const string ViewScaleButtonName = "View\nScale";
        private const string FamilyCleanupButtonName = "Family\nCleanup";
        private const string TaskManagerButtonName = "Task Manager";
        private const string DwgLayerManagerButtonName = "DWG Layer\nManager";
        private const string LibraryUserButtonName = "Family\nLibrary";
        private const string LibraryAdminButtonName = "Library\nAdmin";
        private const string RebaseProjectButtonName = "Rebase\nProject";
        private const string UserSettingsButtonName = "Settings";

        private readonly string ContentPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "Anguleris Technologies", "FreeAxez", "VideoInstructions");

        public Result OnStartup(UIControlledApplication application)
        {
            RevitManager.UiControlledApplication = application;

            CreateRibbonTab(application);
            IsAuthenticated();

            if (!Directory.Exists(ContentPath))
            {
                Directory.CreateDirectory(ContentPath);
            }

            var instructionsDownloader = new InstructionsDownloader();
            Task.Run(async () => await instructionsDownloader.DownloadAsync()).Wait();

            var ribbonItems = new List<RibbonItem>();
            foreach (RibbonPanel panel in _toolTipPanels)
            {
                foreach (RibbonItem ribbonItem in panel.GetItems())
                {
                    ribbonItems.Add(ribbonItem);
                }
            }

            var ribbonItemToolTipResolver = new RibbonItemInstructionResolver(
                ribbonItems, instructionsDownloader.Instructions);
            ribbonItemToolTipResolver.Resolve();

            ApplicationExecutor.Create();
            LogHelper.Information("Starting addin");
            return Result.Succeeded;
        }

        public Result OnShutdown(UIControlledApplication application)
        {
            LogHelper.Information("Addin stop");
            ApplicationExecutor.Stop();
            return Result.Succeeded;
        }

        private async void IsAuthenticated()
        {
            var (username, token) = UserAuthManager.GetCredentials();

            if (!string.IsNullOrEmpty(token))
            {
                try
                {
                    CurrentUser.Info = await UserAuthApiService.Instance.GetUserInfoAsync(token);
                    if (CurrentUser.Info != null)
                    {
                        bool isAdmin = CurrentUser.Info.Roles.Any(role => role == "Admin" || role == "BIM Manager");
                        UserAuthManager.SetRibbonElementsEnabled(true, isAdmin);
                    }
                    else
                    {
                        HandleInvalidToken();
                    }
                }
                catch (Exception ex)
                {
                    HandleInvalidToken();
                }
            }
            else
            {
                HandleInvalidToken();
            }
        }

        private void ShowLoginDialog()
        {
            var loginWindow = new LoginView();
            RevitManager.SetRevitAsWindowOwner(loginWindow);
            loginWindow.Show();
        }

        private void HandleInvalidToken()
        {
            UserAuthManager.RemoveCredentials();
            UserAuthManager.SetRibbonElementsEnabled(false);
            ShowLoginDialog();
        }

        private void CreateRibbonTab(UIControlledApplication application)
        {
            RibbonControl ribbonControl = RevitRibbonControl.RibbonControl;
            RibbonTab ribbonTab = ribbonControl.FindTab(FreeAxezTabName);

            var currentDll = typeof(StartupApp).Assembly.Location;

            if (ribbonTab is null)
            {
                application.CreateRibbonTab(FreeAxezTabName);
            }

            RibbonPanel raisedFloorPanel = application.CreateRibbonPanel(FreeAxezTabName, RaisedFloorPanelName);
            RibbonPanel cablePanel = application.CreateRibbonPanel(FreeAxezTabName, CablePanelName);
            RibbonPanel tagPanel = application.CreateRibbonPanel(FreeAxezTabName, TagPanelName);
            RibbonPanel viewPanel = application.CreateRibbonPanel(FreeAxezTabName, ViewPanelName);
            RibbonPanel parameterPanel = application.CreateRibbonPanel(FreeAxezTabName, ParameterPanelName);
            RibbonPanel exportPanel = application.CreateRibbonPanel(FreeAxezTabName, ExportPanelName);
            RibbonPanel managementPanel = application.CreateRibbonPanel(FreeAxezTabName, ManagementPanelName);

            _panels.Add(raisedFloorPanel);
            _panels.Add(cablePanel);
            _panels.Add(tagPanel);
            _panels.Add(viewPanel);
            _panels.Add(parameterPanel);
            _panels.Add(exportPanel);

            _toolTipPanels.AddRange(_panels);
            _toolTipPanels.Add(managementPanel);

            var createButton = new PushButtonData(CreateButtonName, CreateButtonName, currentDll,
                typeof(CreateCommand).FullName);
            createButton.Image = GetImage("fcreate16.png");
            createButton.LargeImage = GetImage("fcreate32.png");
            raisedFloorPanel.AddItem(createButton);

            var openButton = new PushButtonData(OpenButtonName, OpenButtonName, currentDll,
                typeof(OpenCommand).FullName);
            openButton.Image = GetImage("fopen16.png");
            openButton.LargeImage = GetImage("fopen32.png");
            raisedFloorPanel.AddItem(openButton);

            var addButton = new PushButtonData(AddRegionButtonName, AddRegionButtonName, currentDll,
                typeof(AddRegionCommand).FullName);
            addButton.Image = GetImage("fadd16.png");
            addButton.LargeImage = GetImage("fadd32.png");
            raisedFloorPanel.AddItem(addButton);

            var editRegion = new PushButtonData(EditRegionButtonName, EditRegionButtonName, currentDll,
                typeof(EditRegionCommand).FullName);
            editRegion.Image = GetImage("fedit16.png");
            editRegion.LargeImage = GetImage("fedit32.png");
            raisedFloorPanel.AddItem(editRegion);

            var roomToRegion = new PushButtonData(RoomToRegionButtonName, RoomToRegionButtonName, currentDll,
                typeof(RoomToRegionCommand).FullName);
            roomToRegion.Image = GetImage("roomToRegion16.png");
            roomToRegion.LargeImage = GetImage("roomToRegion32.png");
            roomToRegion.ToolTip = "Convert room to region.";
            raisedFloorPanel.AddItem(roomToRegion);

            var pallet = new PushButtonData(StageButtonName, StageButtonName, currentDll,
                typeof(PalletsCommand).FullName);
            pallet.Image = GetImage("pallet16.png");
            pallet.LargeImage = GetImage("pallet32.png");
            pallet.ToolTip = "Place pallets on the staging plan.";
            raisedFloorPanel.AddItem(pallet);

            var undersheet = new PushButtonData(UndersheetButtonName, UndersheetButtonName, currentDll,
                typeof(UndersheetCommand).FullName);
            undersheet.Image = GetImage("undersheet16.png");
            undersheet.LargeImage = GetImage("undersheet32.png");
            undersheet.ToolTip = "Place undersheet on the staging plan.";
            raisedFloorPanel.AddItem(undersheet);

            var cutUnitByVoid = new PushButtonData(CutUnitByVoidButtonName, CutUnitByVoidButtonName, currentDll,
                typeof(CutUnitByVoidCommand).FullName);
            cutUnitByVoid.Image = GetImage("cutVoid16.png");
            cutUnitByVoid.LargeImage = GetImage("cutVoid32.png");
            cutUnitByVoid.ToolTip = "The tool cuts all unit instances on the plane that intersect with the selected void geometry family instance.";
            raisedFloorPanel.AddItem(cutUnitByVoid);

            var frame = new PushButtonData(FrameButtonName, FrameButtonName, currentDll,
                typeof(FrameCommand).FullName);
            frame.Image = GetImage("frame16.png");
            frame.LargeImage = GetImage("frame32.png");
            frame.ToolTip = "Create frame family instances based on selected lines.";
            raisedFloorPanel.AddItem(frame);

            var baseUnitReplacement = new PushButtonData(BaseUnitReplacementButtonName, BaseUnitReplacementButtonName, currentDll,
                typeof(BaseUnitReplacementCommand).FullName);
            baseUnitReplacement.Image = GetImage("baseUnitReplacement16.png");
            baseUnitReplacement.LargeImage = GetImage("baseUnitReplacement32.png");
            baseUnitReplacement.ToolTip = "Tool for replacing units under floor boxes with pre-cut units.";
            raisedFloorPanel.AddItem(baseUnitReplacement);

            var grommetGriddReplacement = new PushButtonData(GrommetGriddReplacementButtonName, GrommetGriddReplacementButtonName, currentDll,
                typeof(GrommetGriddReplacementCommand).FullName);
            grommetGriddReplacement.Image = GetImage("BasePlateReplacement16.png");
            grommetGriddReplacement.LargeImage = GetImage("BasePlateReplacement32.png");
            grommetGriddReplacement.ToolTip = "Tool for replacing corner plates with pre-wiring plates.";
            raisedFloorPanel.AddItem(grommetGriddReplacement);

            var ramp = new PushButtonData(RampButtonName, RampButtonName, currentDll,
                typeof(RampCommand).FullName);
            ramp.Image = GetImage("ramp16.png");
            ramp.LargeImage = GetImage("ramp32.png");
            ramp.ToolTip = "Create ramp family instances based on selected lines.";
            raisedFloorPanel.AddItem(ramp);

            var exportGriddBOM = new PushButtonData(ExportGriddBOMButtonName, ExportGriddBOMButtonName, currentDll,
                typeof(ExportGriddBOMCommand).FullName);
            exportGriddBOM.Image = GetImage("exportGriddBOM16.png");
            exportGriddBOM.LargeImage = GetImage("exportGriddBOM32.png");
            exportGriddBOM.ToolTip = "Export the gridd bill of material to the selected Excel document.";
            raisedFloorPanel.AddItem(exportGriddBOM);            

            var lineToFlexPipe = new PushButtonData(LineToFlexPipeButtonName, LineToFlexPipeButtonName, currentDll,
               typeof(LineToFlexPipeCommand).FullName);
            lineToFlexPipe.Image = GetImage("lineToFlexPipe16.png");
            lineToFlexPipe.LargeImage = GetImage("lineToFlexPipe32.png");
            lineToFlexPipe.ToolTip = "Convert detail line to flex pipe.";
            cablePanel.AddItem(lineToFlexPipe);

            var flexPipeToLine = new PushButtonData(FlexPipeToLineButtonName, FlexPipeToLineButtonName, currentDll,
                typeof(FlexPipeToLineCommand).FullName);
            flexPipeToLine.Image = GetImage("flexPipeToLine16.png");
            flexPipeToLine.LargeImage = GetImage("flexPipeToLine32.png");
            flexPipeToLine.ToolTip = "Convert flex pipe to detail line.";
            cablePanel.AddItem(flexPipeToLine);

            var lineToRailing = new PushButtonData(LineToRailingButtonName, LineToRailingButtonName, currentDll,
                typeof(LineToRailingCommand).FullName);
            lineToRailing.Image = GetImage("lineToRailing16.png");
            lineToRailing.LargeImage = GetImage("lineToRailing32.png");
            lineToRailing.ToolTip = "Convert line to railing.";
            cablePanel.AddItem(lineToRailing);

            var railingToLine = new PushButtonData(RailingToLineButtonName, RailingToLineButtonName, currentDll,
                typeof(RailingToLineCommand).FullName);
            railingToLine.Image = GetImage("railingToLine16.png");
            railingToLine.LargeImage = GetImage("railingToLine32.png");
            railingToLine.ToolTip = "Convert railing to detail line.";
            cablePanel.AddItem(railingToLine);

            var lineLength = new PushButtonData(LineLengthButtonName, LineLengthButtonName, currentDll,
                typeof(LineLengthCommand).FullName);
            lineLength.Image = GetImage("lineLength16.png");
            lineLength.LargeImage = GetImage("lineLength32.png");
            lineLength.ToolTip = "Calculate the sum length of selected lines.";
            cablePanel.AddItem(lineLength);

            var whipTypeReplace = new PushButtonData(WhipTypeReplaceButtonName, WhipTypeReplaceButtonName, currentDll,
                typeof(WhipTypeReplaceCommand).FullName);
            whipTypeReplace.Image = GetImage("whipTypeReplace16.png");
            whipTypeReplace.LargeImage = GetImage("whipTypeReplace32.png");
            whipTypeReplace.ToolTip = "Apply the correct type of flex pipe for the whip.";
            cablePanel.AddItem(whipTypeReplace);

            var whipExport = new PushButtonData(WhipExportButtonName, WhipExportButtonName, currentDll,
                typeof(WhipExportCommand).FullName);
            whipExport.Image = GetImage("whipExport16.png");
            whipExport.LargeImage = GetImage("whipExport32.png");
            whipExport.ToolTip = "Flex Pipes type checking and data export in CSV format.";
            cablePanel.AddItem(whipExport);

            var electricalCircuit = new PushButtonData(ElectricalCircuitButtonName, ElectricalCircuitButtonName, currentDll,
                typeof(ElectricalCircuitCommand).FullName);
            electricalCircuit.Image = GetImage("electricalCircuit16.png");
            electricalCircuit.LargeImage = GetImage("electricalCircuit32.png");
            electricalCircuit.ToolTip = 
                "1. Creates electrical systems for boxes.\n" +
                "2. Sets the track as a panel for these systems.\n" +
                "3. Transfers parameters from the track to the whip and the box.\n" +
                "4. Performs analysis of electrical circuits and displays a report.";
            cablePanel.AddItem(electricalCircuit);

            var electricalCircuitUpdate = new PushButtonData(ElectricalCircuitUpdateButtonName, ElectricalCircuitUpdateButtonName, currentDll,
                typeof(ElectricalCircuitUpdateCommand).FullName);
            electricalCircuitUpdate.Image = GetImage("electricalCircuitUpdate16.png");
            electricalCircuitUpdate.LargeImage = GetImage("electricalCircuitUpdate32.png");
            electricalCircuitUpdate.ToolTip = "Update the parameters of elements in the valid electrical systems.";
            cablePanel.AddItem(electricalCircuitUpdate);

            var createTapOff = new PushButtonData(CreateTapOffButtonName, CreateTapOffButtonName, currentDll,
                typeof(CreateTapOffCommand).FullName);
            createTapOff.Image = GetImage("createTapOff16.png");
            createTapOff.LargeImage = GetImage("createTapOff32.png");
            createTapOff.ToolTip = "Create Tap Off elements for valid circuits.";
            cablePanel.AddItem(createTapOff);

            var panelScheduleCreation = new PushButtonData(PanelScheduleCreationButtonName, PanelScheduleCreationButtonName, currentDll,
                typeof(PanelScheduleCreationCommand).FullName);
            panelScheduleCreation.Image = GetImage("panelScheduleCreation16.png");
            panelScheduleCreation.LargeImage = GetImage("panelScheduleCreation32.png");
            panelScheduleCreation.ToolTip = "Create track schedules.";
            cablePanel.AddItem(panelScheduleCreation);

            var panelSchedulePlacement = new PushButtonData(PanelSchedulePlacementButtonName, PanelSchedulePlacementButtonName, currentDll,
                typeof(PanelSchedulePlacementCommand).FullName);
            panelSchedulePlacement.Image = GetImage("trackSchedule16.png");
            panelSchedulePlacement.LargeImage = GetImage("trackSchedule32.png");
            panelSchedulePlacement.ToolTip = "Places the existing track specifications on sheets.";
            cablePanel.AddItem(panelSchedulePlacement);

            var exportPowerBom = new PushButtonData(ExportPowerBomButtonName, ExportPowerBomButtonName, currentDll,
                typeof(ExportPowerBomCommand).FullName);
            exportPowerBom.Image = GetImage("exportPowerBOM16.png");
            exportPowerBom.LargeImage = GetImage("exportPowerBOM32.png");
            exportPowerBom.ToolTip = "Export the power bill of material to the selected Excel document.";
            cablePanel.AddItem(exportPowerBom);

            var exportCutsheets = new PushButtonData(ExportCutsheetsButtonName, ExportCutsheetsButtonName, currentDll,
                typeof(ExportCutsheetCommand).FullName);
            exportCutsheets.Image = GetImage("exportCutsheets16.png");
            exportCutsheets.LargeImage = GetImage("exportCutsheets32.png");
            exportCutsheets.ToolTip = "Export the product cutsheets.";
            cablePanel.AddItem(exportCutsheets);

            var tagAll = new PushButtonData(TagAllButtonName, TagAllButtonName, currentDll,
              typeof(TagAllCommand).FullName);
            tagAll.Image = GetImage("tag.png");
            tagAll.LargeImage = GetImage("tag.png");
            tagPanel.AddItem(tagAll);

            var tagAllRailings = new PushButtonData(TagAllRailingsButtonName, TagAllRailingsButtonName, currentDll,
                typeof(TagAllRailingsCommand).FullName);
            tagAllRailings.Image = GetImage("tagAllRailings16.png");
            tagAllRailings.LargeImage = GetImage("tagAllRailings32.png");
            tagAllRailings.ToolTip = "Add tags to the start and end for all railings in the active view.";
            tagPanel.AddItem(tagAllRailings);

            var tagRepeating = new PushButtonData(TagRepeatingButtonName, TagRepeatingButtonName, currentDll,
              typeof(TagRepeatingSupercomponentCommand).FullName);
            tagRepeating.Image = GetImage("all_tag16.png");
            tagRepeating.LargeImage = GetImage("all_tag32.png");
            tagPanel.AddItem(tagRepeating);

            var tagAllCurbs = new PushButtonData(TagAllCurbsButtonName, TagAllCurbsButtonName, currentDll,
                typeof(TagAllCurbsCommand).FullName);
            tagAllCurbs.Image = GetImage("tagAllCurbs16.png");
            tagAllCurbs.LargeImage = GetImage("tagAllCurbs32.png");
            tagAllCurbs.ToolTip = "Add tags to curbs in the active view.";
            tagPanel.AddItem(tagAllCurbs);

            var tagAllFrames = new PushButtonData(TagAllFramesButtonName, TagAllFramesButtonName, currentDll,
                typeof(TagAllFrameCommand).FullName);
            tagAllFrames.Image = GetImage("tagAllFrames16.png");
            tagAllFrames.LargeImage = GetImage("tagAllFrames32.png");
            tagAllFrames.ToolTip = "Add tags to frames in the active view.";
            tagPanel.AddItem(tagAllFrames);

            var tagRamps = new PushButtonData(TagRampsButtonName, TagRampsButtonName, currentDll,
                typeof(TagRampCommand).FullName);
            tagRamps.Image = GetImage("tagRamps16.png");
            tagRamps.LargeImage = GetImage("tagRamps32.png");
            tagRamps.ToolTip = "Add tags to ramps in the active view.";
            tagPanel.AddItem(tagRamps);

            var countBaseUnits = new PushButtonData(CountBaseUnitsButtonName, CountBaseUnitsButtonName, currentDll,
                typeof(CountBaseUnitsCommand).FullName);
            countBaseUnits.Image = GetImage("countBaseUnits16.png");
            countBaseUnits.LargeImage = GetImage("countBaseUnits32.png");
            countBaseUnits.ToolTip = "Count the base units between the selected and write the value below to the dimension.";
            tagPanel.AddItem(countBaseUnits);

            var floorBoxesMarking = new PushButtonData(FloorBoxesMarkingButtonName, FloorBoxesMarkingButtonName, currentDll,
                typeof(FloorBoxNumberingCommand).FullName);
            floorBoxesMarking.Image = GetImage("floorBoxesMarking16.png");
            floorBoxesMarking.LargeImage = GetImage("floorBoxesMarking32.png");
            floorBoxesMarking.ToolTip = "Set numbers for floor boxes.";
            tagPanel.AddItem(floorBoxesMarking);

            var elementNumbering = new PushButtonData(ElementNumberingButtonName, ElementNumberingButtonName, currentDll,
                typeof(ElementNumberingCommand).FullName);
            elementNumbering.Image = GetImage("elementNumbering16.png");
            elementNumbering.LargeImage = GetImage("elementNumbering32.png");
            elementNumbering.ToolTip = "Set numbers for elements.";
            tagPanel.AddItem(elementNumbering);

            var deleteViewBySize = new PushButtonData(DeleteViewBySizeButtonName, DeleteViewBySizeButtonName, currentDll,
                typeof(DeleteViewBySizeCommand).FullName);
            deleteViewBySize.Image = GetImage("deleteViewBySize16.png");
            deleteViewBySize.LargeImage = GetImage("deleteViewBySize32.png");
            deleteViewBySize.ToolTip = "Delete views and sheets associated with selected size.";
            viewPanel.AddItem(deleteViewBySize);

            var deleteView = new PushButtonData(DeleteViewButtonName, DeleteViewButtonName, currentDll,
                typeof(DeleteViewCommand).FullName);
            deleteView.Image = GetImage("deleteView16.png");
            deleteView.LargeImage = GetImage("deleteView32.png");
            deleteView.ToolTip = "Delete selected views and sheets.";
            viewPanel.AddItem(deleteView);

            var legendsButtonData = new PushButtonData(LegendsButtonName, LegendsButtonName, currentDll,
                typeof(LegendManagementCommand).FullName);
            legendsButtonData.Image = GetImage("legends16.png");
            legendsButtonData.LargeImage = GetImage("legends32.png");
            legendsButtonData.ToolTip = "Legends";
            viewPanel.AddItem(legendsButtonData);

            var levelViewManager = new PushButtonData(LevelViewManagerButtonName, LevelViewManagerButtonName, currentDll,
                typeof(LevelViewManagerCommand).FullName);
            levelViewManager.Image = GetImage("levelViewManager16.png");
            levelViewManager.LargeImage = GetImage("levelViewManager32.png");
            levelViewManager.ToolTip = 
                "Finds all views, specifications, and sheets in the project that relate to levels and duplicates them for the selected levels. " +
                "Before running the tool, the user manually creates the necessary levels and sets the correct elevation.";
            viewPanel.AddItem(levelViewManager);

            var renameView = new PushButtonData(RenameViewButtonName, RenameViewButtonName, currentDll,
                typeof(FindAndReplaceViewNameCommand).FullName);
            renameView.Image = GetImage("renameView16.png");
            renameView.LargeImage = GetImage("renameView32.png");
            renameView.ToolTip = "Search and replace text in view names.";
            viewPanel.AddItem(renameView);

            var sheetTitleEditor = new PushButtonData(SheetTitleEditorButtonName, SheetTitleEditorButtonName, currentDll,
                typeof(SheetTitleEditorCommand).FullName);
            sheetTitleEditor.Image = GetImage("sheetTitleEditor16.png");
            sheetTitleEditor.LargeImage = GetImage("sheetTitleEditor32.png");
            sheetTitleEditor.ToolTip = 
                "Management of sheet titles in the project. " +
                "Allows user to copy the title from the name of the placed view.";
            viewPanel.AddItem(sheetTitleEditor);

            var viewScale = new PushButtonData(ViewScaleButtonName, ViewScaleButtonName, currentDll,
                typeof(ViewScaleCommand).FullName);
            viewScale.Image = GetImage("viewScale16.png");
            viewScale.LargeImage = GetImage("viewScale32.png");
            viewScale.ToolTip = "Tool allows batch scaling of both view templates and individual views.";
            viewPanel.AddItem(viewScale);

            var transferParameterValue = new PushButtonData(TransferParameterValueButtonName, TransferParameterValueButtonName, currentDll,
                typeof(TransferParameterValueCommand).FullName);
            transferParameterValue.Image = GetImage("transferParameterValue16.png");
            transferParameterValue.LargeImage = GetImage("transferParameterValue32.png");
            transferParameterValue.ToolTip = "Transferring parameter values from the source element to selected elements with the same type for system elements and the same family for loadable families.";
            parameterPanel.AddItem(transferParameterValue);

            var transferViewTemplates = new PushButtonData(TransferViewTemplatesButtonName, TransferViewTemplatesButtonName, currentDll,
                typeof(TransferViewTemplatesCommand).FullName);
            transferViewTemplates.Image = GetImage("transferViewTemplates16.png");
            transferViewTemplates.LargeImage = GetImage("transferViewTemplates32.png");
            transferViewTemplates.ToolTip = "Transfer view templates from the Revit source file to the current document.";
            parameterPanel.AddItem(transferViewTemplates);

            var exportViewTemplatesToExcel = new PushButtonData(TemplatesToExcelButtonName, TemplatesToExcelButtonName, currentDll,
                typeof(ExportViewTemplatesToExcelCommand).FullName);
            exportViewTemplatesToExcel.Image = GetImage("TemplatesToExcel16.png");
            exportViewTemplatesToExcel.LargeImage = GetImage("TemplatesToExcel32.png");
            exportViewTemplatesToExcel.ToolTip = "Export view templates to Excel";
            parameterPanel.AddItem(exportViewTemplatesToExcel);

            var importViewTemplatesFromExcel = new PushButtonData(TemplatesFromExcelButtonName, TemplatesFromExcelButtonName, currentDll,
                typeof(ImportViewTemplatesFromExcelCommand).FullName);
            importViewTemplatesFromExcel.Image = GetImage("ImportViewTemplates16.png");
            importViewTemplatesFromExcel.LargeImage = GetImage("ImportViewTemplates32.png");
            importViewTemplatesFromExcel.ToolTip = "Import view templates from Excel";
            parameterPanel.AddItem(importViewTemplatesFromExcel);

            var exportForClient = new PushButtonData(ExportForClientButtonName, ExportForClientButtonName, currentDll,
                typeof(ExportForClientCommand).FullName);
            exportForClient.Image = GetImage("exportForClient16.png");
            exportForClient.LargeImage = GetImage("exportForClient32.png");
            exportForClient.ToolTip = "Purge unused items. Delete view templates. Save as an exported revit file.";
            exportPanel.AddItem(exportForClient);

            var exportForVR = new PushButtonData(ExportForVRButtonName, ExportForVRButtonName, currentDll,
                typeof(ExportForVRCommand).FullName);
            exportForVR.Image = GetImage("exportForVR16.png");
            exportForVR.LargeImage = GetImage("exportForVR32.png");
            exportForVR.ToolTip = "Delete views. Delete elements without geometry. Purge unused items. Save as an exported revit file.";
            exportPanel.AddItem(exportForVR);

            var exportToCAD = new PushButtonData(ExportToCADButtonName, ExportToCADButtonName, currentDll,
                typeof(ExportCADCommand).FullName);
            exportToCAD.Image = GetImage("exportToCAD16.png");
            exportToCAD.LargeImage = GetImage("exportToCAD32.png");
            exportToCAD.ToolTip = "Export sheets to DWG format with FreeAxez export setup.";
            exportPanel.AddItem(exportToCAD);

            var taskManagerButtonData = new PushButtonData(TaskManagerButtonName, TaskManagerButtonName, currentDll,
                typeof(TaskManagerCommand).FullName);
            taskManagerButtonData.Image = GetImage("taskManager16.png");
            taskManagerButtonData.LargeImage = GetImage("taskManager32.png");
            taskManagerButtonData.ToolTip = "Smartsheet task manager.";
            var taskManagerButton = managementPanel.AddItem(taskManagerButtonData) as PushButton;
            _managementButtons.Add(taskManagerButton);

            var familyCleanupButtonData = new PushButtonData(FamilyCleanupButtonName, FamilyCleanupButtonName, currentDll,
                typeof(FamilyCleanupToolCommand).FullName);
            familyCleanupButtonData.Image = GetImage("Cleanup16.png");
            familyCleanupButtonData.LargeImage = GetImage("Cleanup32.png");
            familyCleanupButtonData.ToolTip = "Family Cleanup Tool.";
            var familyCleanupButton = managementPanel.AddItem(familyCleanupButtonData) as PushButton;
            _managementButtons.Add(familyCleanupButton);

            var scopeInstructionsButtonData = new PushButtonData(ScopeInstructionsButtonName, ScopeInstructionsButtonName, currentDll,
                typeof(ScopeInstructionsCommand).FullName);
            scopeInstructionsButtonData.Image = GetImage("scopeInstructions16.png");
            scopeInstructionsButtonData.LargeImage = GetImage("scopeInstructions32.png");
            scopeInstructionsButtonData.ToolTip = "Scope Instructions.";
            var scopeInstructionsButton = managementPanel.AddItem(scopeInstructionsButtonData) as PushButton;
            _managementButtons.Add(scopeInstructionsButton);

            var dwgLayerManagerButtonData = new PushButtonData(DwgLayerManagerButtonName, DwgLayerManagerButtonName, currentDll,
                typeof(DwgLayerManagerCommand).FullName);
            dwgLayerManagerButtonData.Image = GetImage("DwgLayerManager16.png");
            dwgLayerManagerButtonData.LargeImage = GetImage("DwgLayerManager32.png");
            dwgLayerManagerButtonData.ToolTip = "DWG Layer Manager - Manage AutoCAD layers and map them to FreeAxez layers.";
            var dwgLayerManagerButton = managementPanel.AddItem(dwgLayerManagerButtonData) as PushButton;
            _managementButtons.Add(dwgLayerManagerButton);

            var libraryUserButtonData = new PushButtonData(LibraryUserButtonName, LibraryUserButtonName, currentDll,
                typeof(LibraryUserCommand).FullName);
            libraryUserButtonData.Image = GetImage("library16.png");
            libraryUserButtonData.LargeImage = GetImage("library32.png");
            var libraryUserButton = managementPanel.AddItem(libraryUserButtonData) as PushButton;
            _managementButtons.Add(libraryUserButton);

            var libraryAdminButtonData = new PushButtonData(LibraryAdminButtonName, LibraryAdminButtonName, currentDll,
                typeof(LibraryAdminCommand).FullName);
            libraryAdminButtonData.Image = GetImage("admin16.png");
            libraryAdminButtonData.LargeImage = GetImage("admin32.png");
            var libraryAdminButton= managementPanel.AddItem(libraryAdminButtonData) as PushButton;
            _libraryAdminButton = libraryAdminButton;

            var rebaseProjectButtonData = new PushButtonData(RebaseProjectButtonName, RebaseProjectButtonName, currentDll,
                typeof(TransferProjectContent).FullName);
            rebaseProjectButtonData.Image = GetImage("rebaseProject16.png");
            rebaseProjectButtonData.LargeImage = GetImage("rebaseProject32.png");
            managementPanel.AddItem(rebaseProjectButtonData);

            var userSettingsButton = new PushButtonData(UserSettingsButtonName, UserSettingsButtonName, currentDll,
                typeof(UserSettingsCommand).FullName);
            userSettingsButton.Image = GetImage("settings16.png");
            userSettingsButton.LargeImage = GetImage("settings32.png");
            managementPanel.AddItem(userSettingsButton);

            UserAuthManager.SetRibbonElementsList(_panels, _managementButtons, _libraryAdminButton);
        }

        private BitmapSource GetImage(string name)
        {
            string imagePath = typeof(StartupApp).Namespace + ".Content." + name;
            Stream s = Assembly.GetExecutingAssembly().GetManifestResourceStream(imagePath);
            var img = new BitmapImage();
            img.BeginInit();
            img.StreamSource = s;
            img.EndInit();
            return img;
        }

    }
}