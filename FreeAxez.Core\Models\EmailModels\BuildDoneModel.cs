﻿using System;
using System.Collections.Generic;
using FreeAxez.Core.Dto;

namespace FreeAxez.Core.Models
{
    public class BuildDoneModel
    {
        public string OptionLink { get; set; }
        public string BlobLink { get; set; }
        public string OptionName { get; set; }
        //-----------------------
        public string TotalAreaStandardUnits { get; set; }
        public string TotalArea { get; set; }
        public string HighTrafficArea { get; set; }
        public string TotalAreaBorderComponent { get; set; }
        public string HalfAndQuarterBaseBorderComponent { get; set; }
        public string RelativeCost { get; set; }
        public string RelativeTime { get; set; }
        public string NumberOfCutPieces { get; set; }
        //-----------------
        public string SubTotalFullBaseUnits { get; set; }
        public string TotalFullBaseUnits { get; set; }
        public string FullBaseUnits { get; set; }
        public string HalfBase { get; set; }
        public string QuarterBase { get; set; }
        public string Corner { get; set; }
        public string Channel { get; set; }
        public string HalfChannel { get; set; }

        public string UserEmail { get; set; }
        public List<KeyValueModel> DataSet { get; set; }



        public BuildDoneModel()
        { }

        public BuildDoneModel(OptionDto option, string blobUrl, string projectId, string revitName, string userEmail)
        {
            OptionName = string.IsNullOrEmpty(option.Name) ? (option.OrderIndex + 1).ToString() : option.Name;
            BlobLink = new Uri(blobUrl).AbsoluteUri;
            OptionLink = new Uri($"https://freeaxez.bimsmith.com/preview/{projectId}/{revitName}").AbsoluteUri;
            Channel = option.ChannelsCount.ToString();
            Corner = option.CornersCount.ToString();
            FullBaseUnits = option.BaseUnitsCount.ToString();
            HalfAndQuarterBaseBorderComponent = Math.Round((decimal)option.NonBorderPerc,2).ToString();
            HalfBase = option.HalfBaseUnitsCount.ToString();
            HalfChannel = option.HalfChannelsCount.ToString();
            HighTrafficArea = "N/A";
            NumberOfCutPieces = option.CutPieces.ToString();
            QuarterBase = option.QuarterBaseUnitsCount.ToString();
            TotalAreaBorderComponent = Math.Round((decimal)option.NonBorderPerc,2).ToString();
            TotalArea = option.TotalArea.ToString();
            TotalAreaStandardUnits = (option.ChannelsCount + option.CornersCount + option.BaseUnitsCount + option.HalfBaseUnitsCount + option.HalfChannelsCount + option.QuarterBaseUnitsCount).ToString();
            RelativeCost = Math.Round((decimal)option.Price,2).ToString();

            if (option.TimeToInstall.HasValue)
            {
                var time = TimeSpan.FromMinutes(option.TimeToInstall.Value);
                RelativeTime = string.Format("{0}:{1}", (int)time.TotalHours, time.Minutes);
            }
            else
            {
                RelativeTime = "N/A";
            }

            SubTotalFullBaseUnits = option.BaseUnitsCount.ToString();
            TotalFullBaseUnits = option.TotalBaseUnitsCount.ToString();


            UserEmail = userEmail ?? "<EMAIL>";
            DataSet = new List<KeyValueModel>();
        }
    }
}
