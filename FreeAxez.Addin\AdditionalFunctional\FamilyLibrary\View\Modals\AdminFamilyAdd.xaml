<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals.AdminFamilyAdd"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             mc:Ignorable="d"
             d:DesignHeight="610" 
             d:DesignWidth="600" 
             MinWidth="600">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
            <converters:BooleanToLoadingTextConverter x:Key="BooleanToLoadingTextConverter"/>
            <converters:BooleanAndConverter x:Key="BooleanAndConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="200"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0" 
                    VerticalAlignment="Center">
            <Grid>
                <Rectangle AllowDrop="True" 
                           Fill="White" 
                           RadiusX="10" 
                           RadiusY="10" 
                           StrokeDashArray="8" 
                           StrokeThickness="1" 
                           StrokeDashCap="Round"
                           DragEnter="Rectangle_DragEnter"
                           DragLeave="Rectangle_DragLeave"
                           Drop="Rectangle_Drop"
                           Stroke="Black"
                           Height="180"
                           Width="Auto"/>
                <StackPanel HorizontalAlignment="Center" 
                            VerticalAlignment="Center">
                    <StackPanel Orientation="Horizontal"
                                VerticalAlignment="Center">
                        <Viewbox Width="25" Height="25" Margin="0 0 5 0">
                            <Canvas Width="640" Height="512">
                                <Path Data="M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128H144zm79-217c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39V392c0 13.3 10.7 24 24 24s24-10.7 24-24V257.9l39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0l-80 80z"
                                      Fill="{StaticResource Green500}"/>
                            </Canvas>
                        </Viewbox>
                        <TextBlock Style="{StaticResource TextBase}" 
                                   Text="Drop Revit Family to upload or"></TextBlock>
                    </StackPanel>
                    <Button Style="{StaticResource ButtonSimpleGreen}" 
                            Content="Choose File" 
                            Command="{Binding ChooseFileCommand}">

                    </Button>
                </StackPanel>
            </Grid>
        </StackPanel>
        <StackPanel Grid.Row="1">
            <TextBlock Style="{StaticResource TextLarge}" 
                       Foreground="Black" 
                       Text="Uploading Files" 
                       FontWeight="Bold" 
                       Margin="0 0 0 5"></TextBlock>
            <ScrollViewer HorizontalScrollBarVisibility="Disabled" 
                          VerticalScrollBarVisibility="Hidden" 
                          MinHeight="50" 
                          MaxHeight="350">
                <ItemsControl 
                    Background="White"
                    ItemsSource="{Binding UploadFileInfo}">
                </ItemsControl>
            </ScrollViewer>
            <ProgressBar Height="8" 
                         Visibility="{Binding IsUploading, 
                            Converter={StaticResource BooleanToVisibilityConverter}}" 
                         IsIndeterminate="True" 
                         Foreground="{StaticResource Blue500}" 
                         Margin="0 10"/>
            </StackPanel>
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    Height="30"
                    Margin="0 2"
                    HorizontalAlignment="Right">
            <Button Style="{StaticResource ButtonOutlinedBlue}" 
                    Margin="0 0 20 0"
                    Width="180"
                    Command="{Binding CancelCommand}">
                Cancel
            </Button>
            <Button Width="180"
                    Command="{Binding ApplyCommand}" 
                    Style="{StaticResource ButtonSimpleBlue}">
                <Button.IsEnabled>
                    <MultiBinding Converter="{StaticResource BooleanAndConverter}">
                        <Binding Path="CanApply"/>
                        <Binding Path="IsUploading" 
                                 Converter="{StaticResource InverseBooleanConverter}"/>
                    </MultiBinding>
                </Button.IsEnabled>
                <StackPanel Orientation="Horizontal">
                    <ContentControl Template="{StaticResource CloudUploadIcon}" HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding IsUploading, Converter={StaticResource BooleanToLoadingTextConverter}, 
                                ConverterParameter='Upload To Library'}" 
                               Margin="5 0"
                               Foreground="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </StackPanel>
    </Grid>
</UserControl>
