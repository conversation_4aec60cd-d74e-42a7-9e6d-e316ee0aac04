﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllRailings.Utils
{
    public class TagCreator
    {
        private ElementId _tagTypeId;
        private double _leaderLength;
        private bool _startTag;
        private bool _endTag;
        private List<Railing> _railings;


        public TagCreator(List<Railing> railings, ElementId tagTypeId, double leaderLength, bool startTag, bool endTag)
        {
            _railings = railings;
            _tagTypeId = tagTypeId;
            _leaderLength = leaderLength;
            _startTag = startTag;
            _endTag = endTag;
        }


        public List<Element> CreateTags()
        {
            var tags = new List<Element>();

            using (var t = new Transaction(RevitManager.Document))
            {
                t.Start("Tag All Railings");

                var activeViewId = RevitManager.UIDocument.ActiveView.Id;

                foreach (var railing in _railings)
                {
                    var railingReference = new Reference(railing);

                    if (_startTag)
                    {
                        var startTag = IndependentTag.Create(RevitManager.Document, activeViewId, railingReference, true, TagMode.TM_ADDBY_CATEGORY, TagOrientation.Horizontal, XYZ.Zero);
                        startTag.ChangeTypeId(_tagTypeId);
                        startTag.LeaderEndCondition = LeaderEndCondition.Free;

                        var startPoint = railing.GetPath().First().GetEndPoint(0);
#if revit2018 || revit2019 || revit2020 || revit2021
                        startTag.LeaderEnd = startPoint;
#else
                        startTag.SetLeaderEnd(railingReference, startPoint);
#endif

                        var tagPoint = GetPointForTag(railing.GetPath().First(), startPoint, _leaderLength);
                        startTag.TagHeadPosition = tagPoint;

                        tags.Add(startTag);
                    }

                    if (_endTag)
                    {
                        var endTag = IndependentTag.Create(RevitManager.Document, activeViewId, railingReference, true, TagMode.TM_ADDBY_CATEGORY, TagOrientation.Horizontal, XYZ.Zero);
                        endTag.ChangeTypeId(_tagTypeId);
                        endTag.LeaderEndCondition = LeaderEndCondition.Free;

                        var endPoint = railing.GetPath().Last().GetEndPoint(1);
#if revit2018 || revit2019 || revit2020 || revit2021
                        endTag.LeaderEnd = endPoint;
#else
                        endTag.SetLeaderEnd(railingReference, endPoint);
#endif

                        var tagPoint = GetPointForTag(railing.GetPath().Last(), endPoint, _leaderLength);
                        endTag.TagHeadPosition = tagPoint;

                        tags.Add(endTag);
                    }
                }

                t.Commit();
            }

            return tags;
        }

        private XYZ GetPointForTag(Curve curve, XYZ point, double distance)
        {
            var offsetDirection = GetOffsetDirectionVector(curve, point);
            var offsetVector = offsetDirection.Multiply(distance);
            var pointForTag = point.Add(offsetVector);

            return pointForTag;
        }
        
        private XYZ GetOffsetDirectionVector(Curve curve, XYZ endPoint)
        {
            XYZ offsetDirection = null;

            if (curve is Arc)
            {
                offsetDirection = endPoint.Subtract((curve as Arc).Center).Normalize();
            }
            else
            {
                var curveDirection = (curve as Line).Direction;

                // Rotate vector up
                var angle = 90;
                if (curveDirection.AngleOnPlaneTo(XYZ.BasisX, XYZ.BasisZ) >= Math.PI / 2 &&
                    curveDirection.AngleOnPlaneTo(XYZ.BasisX, XYZ.BasisZ) <= 2 * Math.PI * 3 / 4)
                {
                    angle = -90;
                }

                offsetDirection = RotateVector2(curveDirection, angle).Normalize();

            }

            return offsetDirection;
        }

        private XYZ RotateVector2(XYZ vector, double degrees)
        {
            var radians = Math.PI * degrees / 180.0;

            var newX = vector.X * Math.Cos(radians) - vector.Y * Math.Sin(radians);
            var newY = vector.X * Math.Sin(radians) + vector.Y * Math.Cos(radians);
            var newZ = vector.Z;

            return new XYZ(newX, newY, newZ);
        }
    }
}
