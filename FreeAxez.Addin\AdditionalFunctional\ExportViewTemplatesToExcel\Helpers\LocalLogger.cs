﻿using Serilog;

namespace FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Helpers
{

    public class LocalLogger : IDisposable
    {
        private readonly ILogger _logger;
        private bool _disposed;

        public LocalLogger(string logPath)
        {
            _logger = new LoggerConfiguration()
#if DEBUG
                .MinimumLevel.Debug()
#else
                .MinimumLevel.Information()
#endif
                .WriteTo.RollingFile(logPath)
                .CreateLogger();
        }

        public void Information(string message) => _logger.Information(message);

        public void Error(string message) => _logger.Error(message);

        public void Warning(string message) => _logger.Warning(message);

        public void Debug(string message) => _logger.Debug(message);

        public void Dispose()
        {
            if (!_disposed)
            {
                if (_logger is IDisposable disposable)
                    disposable.Dispose();

                _disposed = true;
            }
        }
    }
}