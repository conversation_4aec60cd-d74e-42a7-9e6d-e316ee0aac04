﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Models;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Utils;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils
{
    public class ParameterSetter
    {
        public const string TrackAssignmentParameterName = "Track Assignment";
        public const string ComponentNumberParameterName = "Component Number";
        public const string TrackPanelParameterName = "Track Panel";
        public const string PanelNameParameterName = "Panel Name";
        public const string FBTypeParameterName = "FB Type";
        public const string FloorLevelParameterName = "Floor Level";
        public const string WhipLevelParameterName = "Level";
        public const string SpinLockParameterName = "Spin Lock";

        private readonly List<CircuitAssembly> _circuitModels;
        private readonly LevelHelper _levelHelper;
        private readonly SmartLookupParameter _smartLookup;


        public ParameterSetter(List<CircuitAssembly> circuitModels)
        {
            _circuitModels = circuitModels;

            _levelHelper = new LevelHelper();
            _smartLookup = new SmartLookupParameter();
        }


        public void ClearParameters()
        {
            ClearWhipsParameters();
            ClearBoxesParameters();
            ClearTrackParameters();
        }

        public void SetParameters()
        {
            SetWhipsParameters();
            SetBoxesParameters();
            SetTrackParameters();
            SetPanelParameters();
        }

        private void ClearWhipsParameters()
        {
            var whips = _circuitModels.Where(c => c.Whip != null).Select(c => c.Whip).ToList();
            foreach (var whip in whips)
            {
                _smartLookup.LookupParameter(whip, TrackAssignmentParameterName)?.Set("");
                _smartLookup.LookupParameter(whip, ComponentNumberParameterName)?.Set("");
                _smartLookup.LookupParameter(whip, FBTypeParameterName)?.Set("");
                _smartLookup.LookupParameter(whip, WhipLevelParameterName)?.Set("");
                _smartLookup.LookupParameter(whip, FloorLevelParameterName)?.Set("");
            }
        }

        private void ClearBoxesParameters()
        {
            var boxes = _circuitModels.Where(c => c.Box != null).Select(c => c.Box).ToList();
            foreach (var box in boxes)
            {
                _smartLookup.LookupParameter(box, TrackAssignmentParameterName)?.Set("");
                _smartLookup.LookupParameter(box, FloorLevelParameterName)?.Set("");
                _smartLookup.LookupParameter(box, SpinLockParameterName)?.Set(0);

                foreach (Parameter p in box.Parameters)
                {
                    var name = p.Definition.Name;
                    if (Regex.IsMatch(name, @"^\d+ft Whip$") 
                        || Regex.IsMatch(name, @"^Source \d \d+ft Whip$") 
                        || Regex.IsMatch(name, @"^L[1-3]$") 
                        || Regex.IsMatch(name, @"^Source \d L[1-3]$"))
                    {
                        p.Set(0);
                    }
                }
            }
        }

        private void ClearTrackParameters()
        {
            var tracks = _circuitModels.Where(c => c.Track != null).Select(c => c.Track).ToList();
            foreach (var track in tracks)
            {
                _smartLookup.LookupParameter(track, FloorLevelParameterName)?.Set("");
            }
        }

        private void SetWhipsParameters()
        {
            foreach (var circuit in _circuitModels)
            {
                if (circuit.Whip == null) continue;

                _smartLookup.LookupParameter(circuit.Whip, FloorLevelParameterName)?.Set(circuit.Level);
                _smartLookup.LookupParameter(circuit.Whip, WhipLevelParameterName)?.Set(circuit.Level);

                if (circuit.Track != null)
                {
                    _smartLookup.LookupParameter(circuit.Whip, TrackAssignmentParameterName)?
                        .Set(_smartLookup.LookupParameter(circuit.Track, TrackAssignmentParameterName)?.AsString());
                }

                if (circuit.Box != null)
                {
                    _smartLookup.LookupParameter(circuit.Whip, ComponentNumberParameterName)?
                        .Set(_smartLookup.LookupParameter(circuit.Box, ComponentNumberParameterName)?.AsString());

                    _smartLookup.LookupParameter(circuit.Whip, FBTypeParameterName)?
                        .Set(_smartLookup.LookupParameter(circuit.Box, FBTypeParameterName)?.AsString());
                }
            }
        }

        private void SetBoxesParameters()
        {
            var boxesCircuits = _circuitModels.Where(c => c.Box != null).GroupBy(c => c.Box);

            foreach (var boxCircuits in boxesCircuits)
            {
                var box = boxCircuits.Key;
                var circuits = boxCircuits.ToList();

                foreach (var circuit in circuits)
                {
                    var sourcePrefix = GetSourcePrefix(circuits.IndexOf(circuit)); // "Source 2 " => "Source 2 Track Assignment" OR "Track Assignment"

                    _smartLookup.LookupParameter(circuit.Box, FloorLevelParameterName)?.Set(circuit.Level);

                    // Set Component Number and Track Assignment (Source 2 Track Assignment)
                    if (circuit.Track != null)
                    {
                        _smartLookup.LookupParameter(circuit.Box, TrackAssignmentParameterName)?
                            .Set(_smartLookup.LookupParameter(circuit.Track, TrackAssignmentParameterName)?.AsString());
                    }

                    // Set whip length (Source 2 12ft Whip)
                    if (circuit.Whip != null)
                    {
                        if (Regex.IsMatch(circuit.Whip.Name, @"\d+")) // Access_Flooring-FreeAxez-GriddPower-Whip-08'
                        {
                            var length = int.Parse(Regex.Match(circuit.Whip.Name, @"\d+").Value); 
                            _smartLookup.LookupParameter(circuit.Box, sourcePrefix + length + "ft Whip")?.Set(1);
                        }
                        else // Whip exists but does not match the type of whip means spin lock
                        {
                            _smartLookup.LookupParameter(circuit.Box, SpinLockParameterName)?.Set(1);
                        }
                    }

                    // Set load phase (Source 2 L1)
                    var electricalSystems = GetElectricalSystems(box);
                    if (circuit.Panel != null && electricalSystems.Count > 0)
                    {
                        var curentElectricalSystem = electricalSystems.FirstOrDefault(
                            s => s.BaseEquipment?.Id.GetIntegerValue() == circuit.Panel.Id.GetIntegerValue());
                        if (curentElectricalSystem == null)
                        {
                            continue;
                        }
                                                                               
                        if (curentElectricalSystem.get_Parameter(BuiltInParameter.RBS_ELEC_APPARENT_LOAD_PHASEA)?.AsDouble() > 0)
                        {
                            _smartLookup.LookupParameter(circuit.Box, sourcePrefix + "L1")?.Set(1);
                        }
                        if (curentElectricalSystem.get_Parameter(BuiltInParameter.RBS_ELEC_APPARENT_LOAD_PHASEB)?.AsDouble() > 0)
                        {
                            _smartLookup.LookupParameter(circuit.Box, sourcePrefix + "L2")?.Set(1);
                        }
                        if (curentElectricalSystem.get_Parameter(BuiltInParameter.RBS_ELEC_APPARENT_LOAD_PHASEC)?.AsDouble() > 0)
                        {
                            _smartLookup.LookupParameter(circuit.Box, sourcePrefix + "L3")?.Set(1);
                        }
                    }
                }
            }
        }

        private void SetTrackParameters()
        {
            foreach (var circuit in _circuitModels)
            {
                if (circuit.Track == null) continue;

                _smartLookup.LookupParameter(circuit.Track, FloorLevelParameterName)?.Set(circuit.Level);
            }
        }

        private void SetPanelParameters()
        {
            var panels = _circuitModels
                .Where(c => c.Panel != null)
                .Select(c => c.Panel.Id.GetIntegerValue())
                .Distinct()
                .Select(id => RevitManager.Document.GetElement(new ElementId(id)))
                .ToList();

            foreach (var panel in panels)
            {
                var level = _levelHelper.GetLevel((panel.Location as LocationPoint).Point.Z);
                _smartLookup.LookupParameter(panel, PanelNameParameterName)?.Set(level?.Name);
            }
        }

        private List<ElectricalSystem> GetElectricalSystems(FamilyInstance box)
        {
            var output = new List<ElectricalSystem>();

#if (revit2020)
            if (box.MEPModel.ElectricalSystems != null)
            {
                foreach (ElectricalSystem electricalSystem in box.MEPModel.ElectricalSystems)
                {
                    output.Add(electricalSystem);
                }
            }
#else
            output.AddRange(box.MEPModel.GetElectricalSystems());
#endif

            return output;
        }

        private string GetSourcePrefix(int index)
        {
            if (index == 0)
            {
                return "";
            }
            return $"Source {index + 1} ";
        }
    }
}
