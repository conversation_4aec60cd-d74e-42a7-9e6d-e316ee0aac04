﻿using System;
using System.Collections.Generic;
using System.Linq;
using OfficeOpenXml;
using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Helpers
{
    public struct FilterResult
    {
        public ElementId FilterId { get; set; }
        public bool WasAdded { get; set; }
    }

    public class PageFiltersParser
    {
        private const string WorksheetName = "Filters";
        public void ParseSheetAndUpdateFilters(ExcelPackage package, Document doc, Dictionary<string, View> viewTemplates, LocalLogger localLogger)
        {
            var worksheet = package.Workbook.Worksheets[WorksheetName];
            if (worksheet == null)
                throw new InvalidOperationException($"Worksheet '{WorksheetName}' not found.");

            // Statistics tracking
            int processedRows = 0;
            int addedFilters = 0;
            int updatedFilters = 0;
            var errors = new List<string>();

            // Get all filters in the document once
            var filterGroups = new FilteredElementCollector(doc)
                .OfClass(typeof(ParameterFilterElement))
                .Cast<ParameterFilterElement>()
                .GroupBy(f => f.Name, StringComparer.OrdinalIgnoreCase)
                .ToList();

            var allFilters = new Dictionary<string, ElementId>(StringComparer.OrdinalIgnoreCase);
            foreach (var group in filterGroups)
            {
                if (group.Count() > 1)
                    localLogger?.Warning($"Found {group.Count()} filters with name '{group.Key}', using the first one");
                allFilters[group.Key] = group.First().Id;
            }

            // Get all line patterns in the document once
            var linePatternGroups = new FilteredElementCollector(doc)
                .OfClass(typeof(LinePatternElement))
                .Cast<LinePatternElement>()
                .GroupBy(lp => lp.Name, StringComparer.OrdinalIgnoreCase)
                .ToList();

            var allLinePatterns = new Dictionary<string, ElementId>(StringComparer.OrdinalIgnoreCase);
            foreach (var group in linePatternGroups)
            {
                if (group.Count() > 1)
                    localLogger?.Warning($"Found {group.Count()} line patterns with name '{group.Key}', using the first one");
                allLinePatterns[group.Key] = group.First().Id;
            }

            // Get all fill patterns in the document once
            var fillPatternGroups = new FilteredElementCollector(doc)
                .OfClass(typeof(FillPatternElement))
                .Cast<FillPatternElement>()
                .GroupBy(fp => fp.Name, StringComparer.OrdinalIgnoreCase)
                .ToList();

            var allFillPatterns = new Dictionary<string, ElementId>(StringComparer.OrdinalIgnoreCase);
            foreach (var group in fillPatternGroups)
            {
                if (group.Count() > 1)
                    localLogger?.Warning($"Found {group.Count()} fill patterns with name '{group.Key}', using the first one");
                allFillPatterns[group.Key] = group.First().Id;
            }

            int startRow = 2;
            int endRow = worksheet.Dimension.End.Row;
            for (int row = startRow; row <= endRow; row++)
            {
                try
                {
                    string vtName = worksheet.Cells[row, 1].Text.Trim();
                    if (string.IsNullOrEmpty(vtName)) continue;
                    if (!viewTemplates.TryGetValue(vtName, out View viewTemplate)) continue;
                    string filterName = worksheet.Cells[row, 2].Text.Trim();
                    if (string.IsNullOrEmpty(filterName)) continue;
                    var filterResult = GetFilterIdByName(viewTemplate, filterName, allFilters);
                    if (filterResult.FilterId == null || filterResult.FilterId == ElementId.InvalidElementId)
                    {
                        errors.Add($"Row {row}: Filter '{filterName}' not found in document");
                        continue;
                    }

                    if (filterResult.WasAdded)
                        addedFilters++;

                    processedRows++;
                    updatedFilters++;
                    var filterId = filterResult.FilterId;

                    string enabledText = worksheet.Cells[row, 3].Text.Trim();
                    bool isEnabled = !string.IsNullOrEmpty(enabledText) && enabledText.Equals("Yes", StringComparison.OrdinalIgnoreCase);
                    string visibleText = worksheet.Cells[row, 4].Text.Trim();
                    bool isVisible = !string.IsNullOrEmpty(visibleText) && visibleText.Equals("Yes", StringComparison.OrdinalIgnoreCase);

#if revit2020
                    try
                    {
                        if (!isEnabled)
                        {
                            if (viewTemplate.GetFilters().Contains(filterId))
                                viewTemplate.RemoveFilter(filterId);
                        }
                        // Note: Filter is already added in GetFilterIdByName if isEnabled is true
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"Row {row}: Error setting filter enabled for '{vtName}', filter '{filterName}': {ex.Message}");
                    }
#else
                    try { viewTemplate.SetIsFilterEnabled(filterId, isEnabled); }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting filter enabled for '{vtName}', filter '{filterName}': {ex.Message}"); }
#endif
                    try { viewTemplate.SetFilterVisibility(filterId, isVisible); }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting filter visibility for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    OverrideGraphicSettings ogs = null;
                    try { ogs = viewTemplate.GetFilterOverrides(filterId); }
                    catch (Exception ex) { errors.Add($"Row {row}: Error getting filter overrides for '{vtName}', filter '{filterName}': {ex.Message}"); continue; }

                    try
                    {
                        var projLineColor = GetRevitColorFromCell(worksheet.Cells[row, 5]);
                        if (projLineColor != null)
                            ogs.SetProjectionLineColor(projLineColor);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting projection line color for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try
                    {
                        string projLinePatternName = worksheet.Cells[row, 6].Text.Trim();
                        if (!string.IsNullOrEmpty(projLinePatternName))
                            ogs.SetProjectionLinePatternId(GetLinePatternIdByName(projLinePatternName, allLinePatterns));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting projection line pattern for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try { if (int.TryParse(worksheet.Cells[row, 7].Text.Trim(), out int projLineWeight)) ogs.SetProjectionLineWeight(projLineWeight); }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting projection line weight for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try
                    {
                        var surfForeColor = GetRevitColorFromCell(worksheet.Cells[row, 8]);
                        if (surfForeColor != null)
                            ogs.SetSurfaceForegroundPatternColor(surfForeColor);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting surface foreground color for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try
                    {
                        string surfForePatternName = worksheet.Cells[row, 9].Text.Trim();
                        if (!string.IsNullOrEmpty(surfForePatternName))
                            ogs.SetSurfaceForegroundPatternId(GetFillPatternIdByName(surfForePatternName, allFillPatterns));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting surface foreground pattern for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try
                    {
                        var surfBackColor = GetRevitColorFromCell(worksheet.Cells[row, 10]);
                        if (surfBackColor != null)
                            ogs.SetSurfaceBackgroundPatternColor(surfBackColor);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting surface background color for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try
                    {
                        string surfBackPatternName = worksheet.Cells[row, 11].Text.Trim();
                        if (!string.IsNullOrEmpty(surfBackPatternName))
                            ogs.SetSurfaceBackgroundPatternId(GetFillPatternIdByName(surfBackPatternName, allFillPatterns));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting surface background pattern for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try { if (int.TryParse(worksheet.Cells[row, 12].Text.Trim(), out int transparency)) ogs.SetSurfaceTransparency(transparency); }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting transparency for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try
                    {
                        var cutLineColor = GetRevitColorFromCell(worksheet.Cells[row, 13]);
                        if (cutLineColor != null)
                            ogs.SetCutLineColor(cutLineColor);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut line color for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try
                    {
                        string cutLinePatternName = worksheet.Cells[row, 14].Text.Trim();
                        if (!string.IsNullOrEmpty(cutLinePatternName))
                            ogs.SetCutLinePatternId(GetLinePatternIdByName(cutLinePatternName, allLinePatterns));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut line pattern for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try { if (int.TryParse(worksheet.Cells[row, 15].Text.Trim(), out int cutLineWeight)) ogs.SetCutLineWeight(cutLineWeight); }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut line weight for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try
                    {
                        var cutForeColor = GetRevitColorFromCell(worksheet.Cells[row, 16]);
                        if (cutForeColor != null)
                            ogs.SetCutForegroundPatternColor(cutForeColor);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut foreground color for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try
                    {
                        string cutForePatternName = worksheet.Cells[row, 17].Text.Trim();
                        if (!string.IsNullOrEmpty(cutForePatternName))
                            ogs.SetCutForegroundPatternId(GetFillPatternIdByName(cutForePatternName, allFillPatterns));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut foreground pattern for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try
                    {
                        var cutBackColor = GetRevitColorFromCell(worksheet.Cells[row, 18]);
                        if (cutBackColor != null)
                            ogs.SetCutBackgroundPatternColor(cutBackColor);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut background color for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try
                    {
                        string cutBackPatternName = worksheet.Cells[row, 19].Text.Trim();
                        if (!string.IsNullOrEmpty(cutBackPatternName))
                            ogs.SetCutBackgroundPatternId(GetFillPatternIdByName(cutBackPatternName, allFillPatterns));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting cut background pattern for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try
                    {
                        string halftoneText = worksheet.Cells[row, 20].Text.Trim();
                        if (!string.IsNullOrEmpty(halftoneText))
                            ogs.SetHalftone(halftoneText.Equals("Yes", StringComparison.OrdinalIgnoreCase));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting halftone for '{vtName}', filter '{filterName}': {ex.Message}"); }

                    try { viewTemplate.SetFilterOverrides(filterId, ogs); }
                    catch (Exception ex) { errors.Add($"Row {row}: Error updating filter overrides for '{vtName}', filter '{filterName}': {ex.Message}"); }
                }
                catch (Exception ex)
                {
                    errors.Add($"Row {row}: Error processing row in Filters: {ex.Message}");
                }
            }

            // Log summary
            if (errors.Any())
            {
                localLogger?.Error($"Filters processing completed with {errors.Count} errors:");
                foreach (var error in errors.Take(20)) // Limit to first 20 errors
                {
                    localLogger?.Error($"  {error}");
                }
                if (errors.Count > 20)
                    localLogger?.Error($"  ... and {errors.Count - 20} more errors");
            }

            localLogger?.Information($"Filters processed: {processedRows} rows, {addedFilters} filters added, {updatedFilters} filters updated");
        }

        private FilterResult GetFilterIdByName(View viewTemplate, string filterName, Dictionary<string, ElementId> allFilters)
        {
            // Try to find the filter in the pre-built dictionary
            if (!allFilters.TryGetValue(filterName, out ElementId filterId))
            {
                return new FilterResult { FilterId = ElementId.InvalidElementId, WasAdded = false };
            }

            bool wasAdded = false;
            // If the filter is not yet added to the view template, add it
            if (!viewTemplate.GetFilters().Contains(filterId))
            {
                try
                {
                    viewTemplate.AddFilter(filterId);
                    wasAdded = true;
                }
                catch (Exception)
                {
                    return new FilterResult { FilterId = ElementId.InvalidElementId, WasAdded = false };
                }
            }

            return new FilterResult { FilterId = filterId, WasAdded = wasAdded };
        }

        private Autodesk.Revit.DB.Color GetRevitColorFromCell(ExcelRange cell)
        {
            var rgb = cell.Style.Fill.BackgroundColor.Rgb;
            if (string.IsNullOrEmpty(rgb) || rgb.Length != 8) return null;
            int r = int.Parse(rgb.Substring(2, 2), System.Globalization.NumberStyles.HexNumber);
            int g = int.Parse(rgb.Substring(4, 2), System.Globalization.NumberStyles.HexNumber);
            int b = int.Parse(rgb.Substring(6, 2), System.Globalization.NumberStyles.HexNumber);
            return new Autodesk.Revit.DB.Color((byte)r, (byte)g, (byte)b);
        }

        private ElementId GetLinePatternIdByName(string patternName, Dictionary<string, ElementId> allLinePatterns)
        {
            if (string.IsNullOrEmpty(patternName)) return ElementId.InvalidElementId;
            return allLinePatterns.TryGetValue(patternName, out ElementId id) ? id : ElementId.InvalidElementId;
        }

        private ElementId GetFillPatternIdByName(string patternName, Dictionary<string, ElementId> allFillPatterns)
        {
            if (string.IsNullOrEmpty(patternName)) return ElementId.InvalidElementId;
            return allFillPatterns.TryGetValue(patternName, out ElementId id) ? id : ElementId.InvalidElementId;
        }
    }
}
