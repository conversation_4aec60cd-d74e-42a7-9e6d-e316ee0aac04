﻿using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using NetTopologySuite.Geometries;
using Point = FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data.Point;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Core.Elements;

public class BaseUnitHalf : BaseFaElement
{
    private static readonly BaseUnitHalfConfiguration _config = new();

    private BaseUnitHalf(int id, List<LineSegmentData> segments) : base(id, segments)
    {
    }

    public BaseUnitHalf() : base()
    {
    }

    public override ElementTypeConfiguration Configuration => _config;

    public double CutLength { get; private set; }

    protected override void CalculateCenter(List<LineSegmentData> segments)
    {
        var primary = segments.FirstOrDefault(d => Configuration.IsValidLength(d.Segment.Length));
        if (primary == null) return;

        var pm = primary.Segment.MidPoint;
        var sides = segments.Where(d => d != primary)
            .Where(d => d.Segment.Length >= primary.Segment.Length * _config.HalfSideMinRatio &&
                        d.Segment.Length <= primary.Segment.Length * _config.HalfOffset &&
                        IsPerpendicular(primary.Data.angle, d.Data.angle))
            .ToList();
        if (sides.Count != 2) return;

        var dirs = sides.Select(s =>
        {
            var m = s.Segment.MidPoint;
            double vx = m.X - pm.X, vy = m.Y - pm.Y;
            var len = Math.Sqrt(vx * vx + vy * vy);
            return new Coordinate(vx / len, vy / len);
        }).ToList();
        var avg = new Coordinate(dirs.Average(d => d.X), dirs.Average(d => d.Y));
        var mag = Math.Sqrt(avg.X * avg.X + avg.Y * avg.Y);
        if (mag > 0)
        {
            avg.X /= mag;
            avg.Y /= mag;
        }

        Center = new Point(pm.X + avg.X * _config.HalfOffset, pm.Y + avg.Y * _config.HalfOffset, 0);
        CutLength = primary.Segment.Length / 2 - sides.Max(s => s.Segment.Length);
    }

    protected override void CalculateRotationAngle(List<LineSegmentData> segments)
    {
        var primary = segments.FirstOrDefault(d => Configuration.IsValidLength(d.Segment.Length));
        if (primary == null) return;

        var pm = primary.Segment.MidPoint;
        var sides = segments.Where(d => d != primary)
            .Where(d => d.Segment.Length >= primary.Segment.Length * _config.HalfSideMinRatio &&
                        d.Segment.Length <= primary.Segment.Length * _config.HalfOffset &&
                        IsPerpendicular(primary.Data.angle, d.Data.angle))
            .ToList();
        if (sides.Count != 2) return;

        var dirs = sides.Select(s =>
        {
            var m = s.Segment.MidPoint;
            double vx = m.X - pm.X, vy = m.Y - pm.Y;
            var len = Math.Sqrt(vx * vx + vy * vy);
            return new Coordinate(vx / len, vy / len);
        }).ToList();
        var avg = new Coordinate(dirs.Average(d => d.X), dirs.Average(d => d.Y));
        var mag = Math.Sqrt(avg.X * avg.X + avg.Y * avg.Y);
        if (mag > 0)
        {
            avg.X /= mag;
            avg.Y /= mag;
        }

        var rawAngle = Math.Atan2(avg.Y, avg.X) * 180.0 / Math.PI;
        RotationAngle = SnapToCardinal(NormalizeAngle(rawAngle));
    }

    protected override bool IsValidComponentForThisType(List<LineSegmentData> component, HashSet<LineSegmentData> usedSegments)
    {
        if (component.All(d => usedSegments.Contains(d)))
            return false;

        // Find primary line for BaseUnitHalf
        var primary = component.FirstOrDefault(d => !usedSegments.Contains(d) &&
                                                    Configuration.IsValidLength(d.Segment.Length));
        if (primary == null) return false;

        var pLen = primary.Segment.Length;
        var sides = component.Where(d => !usedSegments.Contains(d) && d != primary)
            .Where(d => d.Segment.Length >= pLen * _config.HalfSideMinRatio &&
                        d.Segment.Length <= pLen * _config.HalfOffset &&
                        IsPerpendicular(primary.Data.angle, d.Data.angle))
            .ToList();

        return sides.Count == 2;
    }

    public override void SetElementSpecificParameters(FamilyInstance instance)
    {
        var cutParam = instance.LookupParameter("Cut Length 1");
        if (cutParam != null && !cutParam.IsReadOnly)
        {
            var feet = CutLength / 12.0;
            cutParam.Set(feet);
        }
    }

    protected override void MarkSegmentsAsUsed(List<LineSegmentData> component, HashSet<LineSegmentData> usedSegments)
    {
        var primary = component.FirstOrDefault(d => !usedSegments.Contains(d) &&
                                                   _config.IsValidLength(d.Segment.Length));
        if (primary != null)
        {
            var pLen = primary.Segment.Length;
            var sides = component.Where(d => !usedSegments.Contains(d) && d != primary)
                .Where(d => d.Segment.Length >= pLen * _config.HalfSideMinRatio &&
                            d.Segment.Length <= pLen * _config.HalfOffset &&
                            IsPerpendicular(primary.Data.angle, d.Data.angle))
                .ToList();

            foreach (var d in sides.Append(primary))
                usedSegments.Add(d);
        }
    }

    private static bool IsPerpendicular(double angle1, double angle2)
    {
        var diff = Math.Abs(angle1 - angle2);
        return Math.Abs(diff - 90) < 10 || Math.Abs(diff - 270) < 10;
    }
}