﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>netstandard2.0</TargetFramework>
		<Configurations>Debug;Release</Configurations>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Accord" Version="3.8.0" />
		<PackageReference Include="Accord.Math" Version="3.8.0" />
		<PackageReference Include="AspNetCore.Reporting" Version="2.1.0" />
		<PackageReference Include="Microsoft.Azure.Storage.Blob" Version="11.2.2" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.21.0" />
		<PackageReference Include="Microsoft.Azure.Storage.Queue" Version="11.2.2" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="3.1.3" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="3.1.1" />
		<PackageReference Include="Newtonsoft.Json" Version="12.0.2" />
		<PackageReference Include="SendGrid" Version="9.22.0" />

		<PackageReference Include="System.CodeDom" Version="5.0.0" />
		<PackageReference Include="System.Data.SqlClient" Version="4.8.2" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Text.Encoding.CodePages" Version="5.0.0" />
		<PackageReference Include="System.ValueTuple" Version="4.5.0" />
	</ItemGroup>
	
	<ItemGroup>
		<!--These Serilog versions are used by Revit and should not be updated-->
		<PackageReference Include="Serilog" Version="2.3.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="3.2.0" />
		<PackageReference Include="Serilog.Sinks.RollingFile" Version="3.3.0" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Update="Properties\Resources.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>Resources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>

</Project>
