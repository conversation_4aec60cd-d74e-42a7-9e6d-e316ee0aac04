﻿using Autodesk.Revit.DB;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.LineToFlexPipe.Utils
{
    public class LinesToHermiteSpline
    {
        public static List<XYZ> GetPointsWithOffset(List<Curve> groupedSortedCurves, bool roundCorner, double levelOffset)
        {
            List<XYZ> pts = new List<XYZ>();

            double endOffset = 0.02;
            double curvedCornerOffsetA = 1.0 / 12.0;
            double curvedCornerOffsetB = 4.0 / 12.0;

            foreach (Curve c in groupedSortedCurves)
            {
                if (!roundCorner || IsFreeEnd(c.GetEndPoint(0), groupedSortedCurves))
                {
                    XYZ end0 = c.GetEndPoint(0);
                    if (pts.FirstOrDefault(q => q.DistanceTo(end0) < 0.01) == null)
                        pts.Add(end0);
                }

                if (roundCorner && c.Length > 0.5 && !IsFreeEnd(c.GetEndPoint(0), groupedSortedCurves))
                {
                    pts.Add(c.Evaluate(c.GetEndParameter(0) + curvedCornerOffsetA, false));
                    pts.Add(c.Evaluate(c.GetEndParameter(0) + curvedCornerOffsetB - endOffset, false));
                    pts.Add(c.Evaluate(c.GetEndParameter(0) + curvedCornerOffsetB + endOffset, false));
                }
                else
                {
                    // 3 prior to turn, 2 after turn.
                    //         pts.Add(c.Evaluate(c.GetEndParameter(0) + endOffset * 2, false));
                    pts.Add(c.Evaluate(c.GetEndParameter(0) + endOffset, false));
                    pts.Add(c.Evaluate(c.GetEndParameter(0) + endOffset * 2, false));
                }

                // 2’ spacing for anything under 15’ then 4’ spacing for anything over 15’

                double increment = 2;
                if (c.Length > 15)
                    increment = 4;

                for (double x = increment; x < c.Length - endOffset; x += increment)
                {
                    XYZ thisPt = c.Evaluate(c.GetEndParameter(0) + x, false);
                    // if the vertex is within 5 inches from the corner to skip the vertex and just do the corner vertex layout.
                    // That way we wont have additional vertex getting in the way of the corner layout
                    if (roundCorner)
                    {
                        double dist0 = thisPt.DistanceTo(c.GetEndPoint(0));
                        double dist1 = thisPt.DistanceTo(c.GetEndPoint(1));
                        double minDistToCorner = 5.0 / 12.0;
                        if (dist0 > minDistToCorner && dist1 > minDistToCorner)
                        {
                            pts.Add(thisPt);
                        }
                    }
                    else
                    {
                        pts.Add(thisPt);
                    }

                }

                if (roundCorner && c.Length > 0.5 && !IsFreeEnd(c.GetEndPoint(1), groupedSortedCurves))
                {
                    pts.Add(c.Evaluate(c.GetEndParameter(1) - (curvedCornerOffsetB + endOffset), false));
                    pts.Add(c.Evaluate(c.GetEndParameter(1) - (curvedCornerOffsetB - endOffset), false));
                    pts.Add(c.Evaluate(c.GetEndParameter(1) - curvedCornerOffsetA, false));
                }
                else
                {
                    //        pts.Add(c.Evaluate(c.GetEndParameter(1) - endOffset * 2, false));
                    pts.Add(c.Evaluate(c.GetEndParameter(1) - endOffset * 3, false));
                    pts.Add(c.Evaluate(c.GetEndParameter(1) - endOffset * 2, false));
                    pts.Add(c.Evaluate(c.GetEndParameter(1) - endOffset, false));

                    XYZ end1 = c.GetEndPoint(1);
                    if (pts.FirstOrDefault(q => q.DistanceTo(end1) < 0.01) == null)
                        pts.Add(end1);

                }
            }

            List<XYZ> ptsWithOffset = pts.Select(q => q.Add(XYZ.BasisZ.Multiply(levelOffset))).ToList();

            return ptsWithOffset;
        }

        private static bool IsFreeEnd(XYZ pt, List<Curve> curves)
        {
            List<XYZ> ends = curves.Select(q => q.GetEndPoint(0)).ToList();
            ends.AddRange(curves.Select(q => q.GetEndPoint(1)));

            if (ends.Count(q => q.DistanceTo(pt) < 0.01) == 1)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }
}
