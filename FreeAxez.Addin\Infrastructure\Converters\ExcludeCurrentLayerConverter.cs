using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows.Data;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;

namespace FreeAxez.Addin.Infrastructure.Converters
{
    public class ExcludeCurrentLayerConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length != 2)
                return values[0];

            var allLayers = values[0] as IEnumerable<LayerModel>;
            var currentLayerName = values[1] as string;

            if (allLayers == null || string.IsNullOrEmpty(currentLayerName))
                return allLayers;

            // Filter out the layer with the same name as current layer
            return allLayers.Where(layer => !string.Equals(layer.Name, currentLayerName, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
