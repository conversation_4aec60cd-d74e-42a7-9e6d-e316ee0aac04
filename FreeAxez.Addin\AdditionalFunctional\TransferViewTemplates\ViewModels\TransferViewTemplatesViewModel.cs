﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Models;
using FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using Settings = FreeAxez.Addin.Properties.Settings;
using TransferViewTemplatesRevitManager =
    FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Utils.TransferViewTemplatesRevitManager;

namespace FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.ViewModels;

public class TransferViewTemplatesViewModel : BaseViewModel
{
    private readonly List<View> _viewsFromTargetDocument;

    public TransferViewTemplatesViewModel()
    {
        SourceFileViewModel = new SourceFileViewModel();

        CheckAllCommand = new RelayCommand(OnCheckAllCommandExecute);
        UncheckAllCommand = new RelayCommand(OnUncheckAllCommandExecute);
        CopyViewTemplates = new RelayCommand(OnCopyViewTemplatesCommandExecute);
        CancelCommand = new RelayCommand(OnCancelCommandExecute);
        SelectSourceFileCommand = new RelayCommand(OnSelectSourceFileCommandExecute);

        _viewsFromTargetDocument = GetAllViewsFromTargetDocument();
    }

    public SourceFileViewModel SourceFileViewModel { get; }

    public ICommand CheckAllCommand { get; set; }

    public ICommand UncheckAllCommand { get; set; }

    public ICommand CopyViewTemplates { get; set; }

    public ICommand CancelCommand { get; set; }

    public ICommand SelectSourceFileCommand { get; set; }

    public ObservableCollection<RevitViewTemplate> RevitViewTemplates => SourceFileViewModel.RevitViewTemplates;

    private void OnSelectSourceFileCommandExecute(object p)
    {
        SourceFileViewModel.OnSelectSourceFileCommandExecute(p);
    }

    private void OnCheckAllCommandExecute(object p)
    {
        var view = CollectionViewSource.GetDefaultView(RevitViewTemplates);
        foreach (var item in view)
        {
            if (item is RevitViewTemplate templateView)
            {
                templateView.IsChecked = true;
            }
        }
    }

    private void OnUncheckAllCommandExecute(object p)
    {
        var view = CollectionViewSource.GetDefaultView(RevitViewTemplates);
        foreach (var item in view)
        {
            if (item is RevitViewTemplate templateView)
            {
                templateView.IsChecked = false;
            }
        }
    }

    private void OnCancelCommandExecute(object p)
    {
        Settings.Default.TransferViewTemplatesDeleteTemplates =
            SourceFileViewModel.DeleteCurrentViewTemplate;
        Settings.Default.Save();

        (p as Window).Close();
    }

    private List<View> GetAllViewsFromTargetDocument()
    {
        return new FilteredElementCollector(TransferViewTemplatesRevitManager.TargetDocument)
            .OfClass(typeof(View))
            .Cast<View>()
            .ToList();
    }

    private void OnCopyViewTemplatesCommandExecute(object p)
    {
        try
        {
            using (var t = new Transaction(TransferViewTemplatesRevitManager.TargetDocument, "Copy view templates"))
            {
                t.Start();
                var cpOpts = new CopyPasteOptions();
                cpOpts.SetDuplicateTypeNamesHandler(new CopyHandler());

                var selectedViewTemplates = RevitViewTemplates
                    .Where(v => v.IsChecked)
                    .ToList();

                foreach (var selectedViewTemplate in selectedViewTemplates)
                {
                    var targetViewTemplate = FindTargetViewTemplateByName(selectedViewTemplate.Name);

                    if (targetViewTemplate != null) RenameViewTemplate(targetViewTemplate);

                    var newViewTemplateId = ElementTransformUtils.CopyElements(selectedViewTemplate.RevitDocument,
                        new List<ElementId> { selectedViewTemplate.Id },
                        TransferViewTemplatesRevitManager.TargetDocument,
                        Transform.Identity, cpOpts).FirstOrDefault();

                    if (targetViewTemplate != null)
                    {
                        var renamedViewTemplateId = targetViewTemplate.Id;

                        var viewsWithOldTemplate = _viewsFromTargetDocument
                            .Where(v => !v.IsTemplate && v.ViewTemplateId == renamedViewTemplateId).ToList();
                        foreach (var viewWithOldTemplate in viewsWithOldTemplate)
                            viewWithOldTemplate.ViewTemplateId = newViewTemplateId;
                    }
                }

                if (SourceFileViewModel.DeleteCurrentViewTemplate)
                {
                    var oldViewTemplatesIds =
                        new FilteredElementCollector(TransferViewTemplatesRevitManager.TargetDocument)
                            .OfClass(typeof(View))
                            .Cast<View>()
                            .Where(v => v.IsTemplate && v.Name.Contains("Old"))
                            .Select(v => v.Id)
                            .ToList();
                    TransferViewTemplatesRevitManager.TargetDocument.Delete(oldViewTemplatesIds);
                }

                (p as Window).Close();

                MessageWindow.ShowDialog("Transfering View Templates",
                    "View Templates successfully transferred to the current Document", MessageType.Success);

                Settings.Default.TransferViewTemplatesDeleteTemplates =
                    SourceFileViewModel.DeleteCurrentViewTemplate;
                Settings.Default.Save();

                t.Commit();
            }
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog("Error", "Failed to copy view templates: " + ex.Message, MessageType.Error);
        }
    }

    private View FindTargetViewTemplateByName(string name)
    {
        return _viewsFromTargetDocument.FirstOrDefault(v => v.IsTemplate && v.Name == name);
    }

    private void RenameViewTemplate(View targetViewTemplate)
    {
        var counter = 1;
        var originalName = targetViewTemplate.Name;
        var newName = originalName + " Old" + counter.ToString("D3");

        while (ViewNameExists(newName))
        {
            counter++;
            newName = originalName + " Old" + counter.ToString("D3");
        }

        targetViewTemplate.Name = newName;
    }

    private bool ViewNameExists(string name)
    {
        var existingTemplates = new FilteredElementCollector(TransferViewTemplatesRevitManager.TargetDocument)
            .OfClass(typeof(View))
            .Cast<View>()
            .Where(v => v.IsTemplate && v.Name == name)
            .ToList();

        return existingTemplates.Any();
    }
}