﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Models;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Models.Base;
using FreeAxez.Addin.Utils;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.Models
{
    public class FloorBox : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_ElectricalFixtures,
            FamilyNamesContains = new List<string>()
            {
                "-FB-",
                "-DeskMount-",
                "-Desk_Mount-"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        private const double WhipLengthToTypeTolerance = 1.0;

        private readonly Whip _whip;
        private double _length = double.NaN;
        private string _floorBoxType;
        private string _componentNumber;
        private string _phase;


        public FloorBox(Element floorBox, Whip whip) : base(floorBox)
        {
            _whip = whip;
        }


        public static List<FloorBox> Collect()
        {
            var output = new List<FloorBox>();

            var floorBoxes = FamilyCollector.Instances;
            
            var whips = Whip.CollectFloorBoxWhips();
            whips.AddRange(Whip.CollectSpinLocks());

            var circuitDevices = floorBoxes.Select(e => new CircuitDevice(e)).ToList();
            var circuitWhips = whips.Select(e => new CircuitWhip(e.Element as FlexPipe)).ToList();

            foreach (var circuitDevice in circuitDevices) 
            {
                Whip connectedWhip = null;
                foreach (var circuitWhip in circuitWhips)
                {
                    if (circuitDevice.IsConnected(circuitWhip))
                    {
                        connectedWhip = new Whip(circuitWhip.FlexPipe);
                        break;
                    }
                }

                output.Add(new FloorBox(circuitDevice.FamilyInstance, connectedWhip));
            }

            return output;
        }

        public static List<FamilyInstance> CollectInstances()
        {
            return FamilyCollector.Instances;
        }

        public static List<FamilyInstance> CollectFloorBoxInstances()
        {
            return FamilyCollector.Instances.Where(i => i.Symbol.FamilyName.Contains("-FB-")).ToList(); // Filter out -DeskMount-
        }

        public static ISelectionFilter CreateSelectionFilter()
        {
            return new FloorBoxSelectionFilter();
        }

        // TODO: Another product?
        public static List<FloorBox> CollectFieldWired()
        {
            return Collect()
                .Where(fb => (fb.Element as FamilyInstance).Symbol.FamilyName.Contains("Field_Wired"))
                .ToList();
        }

        public string FloorBoxType
        {
            get
            {
                if (_floorBoxType == null)
                {
                    _floorBoxType = Element.get_Parameter(new Guid(Constants.ParameterGuidFBType))?.AsString();
                    if (_floorBoxType == null)
                    {
                        LogHelper.Warning($"Element {Element.Id} does not contain parameter FB Type with Guid \"{Constants.ParameterGuidFBType}\"");
                        _floorBoxType = "";
                    }
                }

                return _floorBoxType;
            }
        }

        public override double Length
        {
            get
            {
                if (double.IsNaN(_length))
                {
                    if (_whip != null) 
                    {
                        if (_whip.IsSpinLock) // SpinLock is specified separately from the box
                        {
                            _length = 0;
                        }
                        else
                        {
                            // Access_Flooring-FreeAxez-GriddPower-Whip-08'
                            double.TryParse(Regex.Match(_whip.Element.Name, @"\d+")?.Value, out _length);

                            var diff = _length - _whip.Length;
                            if (diff < 0 || diff > WhipLengthToTypeTolerance)
                            {
                                _length = -1.0; // Invalid whip length
                            }
                        }
                    }
                    else
                    {
                        _length = 20; // If a box whip length is null, use the default lenght
                    }
                }

                return _length;
            }
        }

        public string ComponentNumber
        {
            get
            {
                if (_componentNumber == null)
                {
                    _componentNumber = Element.get_Parameter(new Guid(Constants.ParameterGuidComponentNumber))?.AsString();
                    if (_componentNumber == null)
                    {
                        LogHelper.Warning($"Element {Element.Id} does not contain parameter Component Number with Guid \"{Constants.ParameterGuidComponentNumber}\"");
                    }
                }

                return _componentNumber;
            }
        }

        public string Phase
        {
            get
            {
                if (_phase == null)
                {
                    _phase = Element.get_Parameter(new Guid(Constants.ParameterGuidPhase))?.AsString();
                    if (_phase == null)
                    {
                        LogHelper.Warning($"Element {Element.Id} does not contain parameter Phase with Guid \"{Constants.ParameterGuidPhase}\"");
                    }
                }

                return _phase;
            }
        }

        public override string Id
        {
            get
            {
                if (_whip != null) return $"{base.Id}, {_whip.Id}";
                return $"{base.Id}";
            }
        }

        private class FloorBoxSelectionFilter : ISelectionFilter
        {
            private readonly List<ElementId> _symbolIds;

            public FloorBoxSelectionFilter()
            {
                _symbolIds = FamilyCollector.Symbols.Select(s => s.Id).ToList();
            }

            public bool AllowElement(Element elem)
            {
                return elem is FamilyInstance familyInstance && _symbolIds.Any(s => familyInstance.Symbol.Id.Equals(s));
            }

            public bool AllowReference(Reference reference, XYZ position)
            {
                return true;
            }
        }
    }
}
