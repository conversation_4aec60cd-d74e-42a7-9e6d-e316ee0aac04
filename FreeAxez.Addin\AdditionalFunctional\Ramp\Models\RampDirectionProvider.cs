﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.Ramp.Models
{
    public class RampDirectionProvider
    {
        private const double Tolerance = 0.3;
        private const double Multiplayer = Tolerance * 25;

        public static Curve Curve { get; set; }
        public static XYZ CurveDirection { get; set; }
        public static bool IsReversed { get; set; }
        public static XYZ PerpVector { get; set; }
        public static BoundingBoxXYZ Bbox { get; set; }
        public static Outline LeftOutline { get; set; }
        public static Outline RightOutline { get; set; }

        public XYZ GetRampComponentsDirection(CurveElement line)
        {
            var crv = line.GeometryCurve as Line;
            Curve = crv;
            CurveDirection = crv.Direction;
            PerpVector = line.SketchPlane.GetPlane().Normal.CrossProduct(CurveDirection);
            RevitManager.Document.Regenerate();
            Bbox = line.get_BoundingBox(RevitManager.Document.ActiveView);
            GetBboxMinMaxPoints();
            if (LeftOutline.IsEmpty)
            {
                GetBboxMinMaxPointsReversed();
            }
            GetEndPoint();
            return ConstructDirection();
        }

        private void GetEndPoint()
        {
            LeftOutline.Scale(0.95);
            RightOutline.Scale(0.95);

            var leftbboxfilter = new BoundingBoxIntersectsFilter(LeftOutline);
            var rightbboxfilter = new BoundingBoxIntersectsFilter(RightOutline);

            var leftCollector = new FilteredElementCollector(RevitManager.Document);
            var rightCollector = new FilteredElementCollector(RevitManager.Document);

            var LeftCount = leftCollector.WherePasses(leftbboxfilter).ToElements().Count;
            var RightCount = rightCollector.WherePasses(rightbboxfilter).ToElements().Count;

            if (LeftCount < RightCount)
            {
                IsReversed = false;
            }
            else if (RightCount < LeftCount)
            {
                IsReversed = true;
            }
            else
            {
                IsReversed = true;
            }
        }

        private void GetBboxMinMaxPoints()
        {
            LeftOutline = ConstructOutline(Bbox, Tolerance, Multiplayer, 1);
            RightOutline = ConstructOutline(Bbox, Multiplayer, Tolerance, -1);
        }

        private void GetBboxMinMaxPointsReversed()
        {
            LeftOutline = ConstructOutline(Bbox, Multiplayer, Tolerance, 1);
            RightOutline = ConstructOutline(Bbox, Tolerance, Multiplayer, -1);
        }

        private Outline ConstructOutline(BoundingBoxXYZ bbox, double firstoffset, double secondoffset, double side)
        {
            var LeftMin = ConstructPoint(bbox.Min, firstoffset * side);
            var LeftMax = ConstructPoint(bbox.Max, secondoffset * side);
            return new Outline(LeftMin, LeftMax);
        }

        private XYZ ConstructPoint(XYZ point, double tolerance)
        {
            var pt = new XYZ(point.X, point.Y, point.Z);
            return pt + PerpVector.Multiply(tolerance);
        }

        private XYZ ConstructDirection()
        {
            return PerpVector;
        }
    }
}