﻿using FreeAxez.Core.Enums;
using System;
using System.Collections.Generic;

namespace FreeAxez.Core.Dto
{
    public class OptionDto
    {
        public Guid Id { get; set; }
        public Guid ProjectId { get; set; }
        public string Name { get; set; }
        public List<RegionDto> Regions { get; set; }

        public bool IsCompleted { get; set; }
        public double? Price { get; set; }
        public int OrderIndex { get; set; }
        public int BaseUnitsCount { get; set; }
        public int HalfBaseUnitsCount { get; set; }
        public int QuarterBaseUnitsCount { get; set; }
        public int ChannelsCount { get; set; }
        public int HalfChannelsCount { get; set; }
        public int CornersCount { get; set; }
        public int CutPieces { get; set; }
        public string BlobPath { get; set; }
        public Guid? CompareId { get; set; }
        public OptionBuildStatus BuildStatus { get; set; }
        public double CutBorderArea { get; set; }
        public double BorderArea { get; set; }
        public double StandardUnitsArea { get; set; }
        public double NonBorderPerc { get; set; }
        public double? TimeToInstall { get; set; }
        public double TotalArea { get; set; }
        public int TotalBaseUnitsCount { get; set; }
        public OptionDto()
        {
            ChannelsCount = 0;
            BaseUnitsCount = 0;
            HalfBaseUnitsCount = 0;
            QuarterBaseUnitsCount = 0;
            ChannelsCount = 0;
            HalfChannelsCount = 0;
            CornersCount = 0;
            NonBorderPerc = 0;
        }
    }
}