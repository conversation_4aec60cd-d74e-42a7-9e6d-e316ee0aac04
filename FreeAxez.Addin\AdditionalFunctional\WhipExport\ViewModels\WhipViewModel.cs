﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.AdditionalFunctional.WhipTypeReplace.Utils;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.WhipExport.ViewModels
{
    public class WhipViewModel
    {
        public WhipViewModel(Element element)
        {
            Id = element.Id.GetIntegerValue();
            TypeName = element.Name;
            Level = (element as MEPCurve)?.ReferenceLevel?.Name;
            FullLength = GetLength(element);
            ProjectionLength = element.get_Parameter(BuiltInParameter.CURVE_ELEM_LENGTH)?.AsValueString();
            TrackAssignment = element.LookupParameter("Track Assignment")?.AsString();
            CorrectTypeLength = GetCorrectTypeState(element);
            TypeLength = GetTypeLength(element);
        }


        public int Id { get; set; }
        public string FullLength { get; set; }
        public string ProjectionLength { get; set; }
        public string TypeLength { get; set; }
        public string TypeName { get; set; }
        public string CorrectTypeLength { get; set; }
        public string TrackAssignment { get; set; }
        public string Level { get; set; }


        private string GetLength(Element element)
        {
            var length = WhipFullLength.GetRiseLength(element as FlexPipe, false);
#if revit2020 || revit2021
            return UnitFormatUtils.Format(RevitManager.Document.GetUnits(), UnitType.UT_Length, length, false, false);
#else
            return UnitFormatUtils.Format(RevitManager.Document.GetUnits(), SpecTypeId.Length, length, false);
#endif
        }

        private string GetCorrectTypeState(Element element)
        {
            var length = WhipFullLength.GetRiseLength(element as FlexPipe);
            var typeLengthString = element.Name.Split('-').Last().Replace("'", "");  // -08' 10' 12' 14' 16' 20'
            var typeLengthDouble = 0.0;
            double.TryParse(typeLengthString, out typeLengthDouble);

            if (typeLengthDouble == 0.0)
            {
                return "False";
            }
            else if (typeLengthDouble - 1.0 < length && length <= typeLengthDouble)
            {
                // Correct range (typeLength - 1, typeLength]
                return "True";
            }
            else
            {
                return "False";
            }
        }

        private string GetTypeLength(Element element)
        {
            var typeLengthString = element.Name.Split('-').Last().Replace("'", "");  // -08' 10' 12' 14' 16' 20'
            var typeLengthInt = 0;
            int.TryParse(typeLengthString, out typeLengthInt);
            return $"{typeLengthInt}'";
        }
    }
}
