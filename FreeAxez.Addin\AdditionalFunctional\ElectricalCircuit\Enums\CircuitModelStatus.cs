﻿using System.ComponentModel;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Enums
{
    public enum CircuitModelStatus
    {
        // Common
        [Description("Unknown")]
        Unknown = 0,
        [Description("Valid")]
        Valid = 1,

        // Floor Box Circuit Assembly Type
        [Description("No Panel")]
        NoPanel,
        [Description("No Track")]
        NoTrack,
        [Description("No Whip")]
        NoWhip,
        [Description("No Box")]
        NoBox,
        [Description("Single Whip")]
        SingleWhip,
        [Description("Single Box")]
        SingleBox,

        // Track Circuit Assembly Type
        [Description("No Feed Module")]
        NoFeedModule,
        [Description("Single Feed Module")]
        SingleFeedModule,
    }
}
