﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using System.Collections.Generic;

namespace FreeAxez.Addin.AdditionalFunctional
{
    public static class CommonUtils
    {
        #region TagRepeatingSupercomponent
        public static Solid makeCylinder(XYZ point, double radius)
        {
            CurveLoop halfCircle = new CurveLoop();
            Arc arc = Arc.Create(
                    point.Subtract(XYZ.BasisY.Multiply(radius)),
                    point.Add(XYZ.BasisY.Multiply(radius)),
                    point.Add(XYZ.BasisX.Multiply(radius)));
            halfCircle.Append(arc);
            halfCircle.Append(Line.CreateBound(arc.GetEndPoint(1), arc.GetEndPoint(0)));

            List<CurveLoop> loops = new List<CurveLoop>(1)
            {
                halfCircle
            };
            Solid solidUp = GeometryCreationUtilities.CreateExtrusionGeometry(loops, XYZ.BasisZ, 10);
            Solid solidDown = GeometryCreationUtilities.CreateExtrusionGeometry(loops, XYZ.BasisZ.Negate(), 10);
            Solid solid = BooleanOperationsUtils.ExecuteBooleanOperation(solidUp, solidDown, BooleanOperationsType.Union);
            return solid;
        }

        public static XYZ ProjectOnto(Plane plane, XYZ p)
        {
            double d = SignedDistanceTo(plane, p);
            if (d == 0)
                return p;
            XYZ q = p - d * plane.Normal;
            return q;
        }

        private static double SignedDistanceTo(Plane plane, XYZ p)
        {
            XYZ v = p - plane.Origin;
            return plane.Normal.DotProduct(v);
        }
        #endregion


        #region Units from text block for FormFlexPipe2 and FormTagAll
        public static double GetInternalUnitsFromTextBox(Document doc, Units units, System.Windows.Forms.TextBox textbox)
        {
            double ret = 0;
#if revit2018 || revit2019 || revit2020 || revit2021
            UnitFormatUtils.TryParse(units, UnitType.UT_Length, textbox.Text, out ret);
            UnitUtils.ConvertToInternalUnits(ret, units.GetFormatOptions(UnitType.UT_Length).DisplayUnits);
#else
            UnitFormatUtils.TryParse(units, SpecTypeId.Length, textbox.Text, out ret);
            UnitUtils.ConvertToInternalUnits(ret, units.GetFormatOptions(SpecTypeId.Length).GetUnitTypeId());
#endif
            return ret;
        }

        public static bool checkLengthTextBox(Document doc, Units units, System.Windows.Forms.TextBox textbox, AllowedValues allowed)
        {
            if (doc == null || textbox == null)
                return false;

            double newDouble = 0;
            string message = "";
            ValueParsingOptions vpo = new ValueParsingOptions();
            vpo.AllowedValues = allowed;
#if revit2018 || revit2019 || revit2020 || revit2021
            if (UnitFormatUtils.TryParse(units, UnitType.UT_Length, textbox.Text, vpo, out newDouble, out message))
            {
                FormatValueOptions formatValue = new FormatValueOptions() { AppendUnitSymbol = true };
                string formatted = UnitFormatUtils.Format(units, UnitType.UT_Length, newDouble, false, true, formatValue);
                textbox.Text = formatted;
            }
#else
            if (UnitFormatUtils.TryParse(units, SpecTypeId.Length, textbox.Text, vpo, out newDouble, out message))
            {
                FormatValueOptions formatValue = new FormatValueOptions() { AppendUnitSymbol = true };
                string formatted = UnitFormatUtils.Format(units, SpecTypeId.Length, newDouble, true, formatValue);
                textbox.Text = formatted;
            }
#endif
            
            else
            {
                if (textbox.Visible && textbox.Enabled)
                {
                    TaskDialog.Show("Error", textbox.AccessibleName + ": " + message);
                    return false;
                }
            }
            return true;
        }
        #endregion

        public class NameIDObject
        {
            public NameIDObject(string name, int idValue)
            {
                Name = name;
                IdValue = idValue;
            }
            public string Name { get; set; }
            public int IdValue { get; set; }
        }
    }
}