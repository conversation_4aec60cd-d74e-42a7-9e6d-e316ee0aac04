﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.ExportCutsheet.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System;

namespace FreeAxez.Addin.AdditionalFunctional.ExportCutsheet
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class ExportCutsheetCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var exportCutsheetView = new ExportCutsheetView();

            try
            {
                exportCutsheetView.ShowDialog();
            }
            catch (Exception exception)
            {
                InfoDialog.ShowDialog("Error", exception.Message);
                LogHelper.Error($"{exception.Message} at {exception.StackTrace}");
                return Result.Failed;
            }
            finally
            {
                exportCutsheetView.Close();
            }

            return Result.Succeeded;
        }
    }
}
