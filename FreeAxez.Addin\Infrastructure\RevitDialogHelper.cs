﻿using Autodesk.Revit.UI;

namespace FreeAxez.Addin.Infrastucture
{
    public static class RevitDialogHelper
    {
        public static void ShowNotification(string message)
        {
            TaskDialog mainDialog = new TaskDialog("FreeAxez");
            mainDialog.MainInstruction = message;

            mainDialog.Show();
        }

        public static bool ShowConfirmation(string message, string confirmText, string cancelText)
        {
            TaskDialog mainDialog = new TaskDialog("FreeAxez");
            mainDialog.MainInstruction = message;

            mainDialog.AddCommandLink(TaskDialogCommandLinkId.CommandLink1,
                          confirmText);
            mainDialog.AddCommandLink(TaskDialogCommandLinkId.CommandLink2,
                          cancelText);

            mainDialog.CommonButtons = TaskDialogCommonButtons.None;
            var result = mainDialog.Show();

            return result == TaskDialogResult.CommandLink1;
        }
    }
}