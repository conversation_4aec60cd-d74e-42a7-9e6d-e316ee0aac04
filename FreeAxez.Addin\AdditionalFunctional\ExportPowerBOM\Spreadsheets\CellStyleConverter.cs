﻿using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using SixLabors.ImageSharp;
using System.Collections.Generic;
using HorizontalAlignment = NPOI.SS.UserModel.HorizontalAlignment;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Spreadsheets
{
    public class CellStyleConverter
    {
        private Dictionary<OrderCellStyle, ICellStyle> _cellStylePairs;
        private readonly XSSFWorkbook _workbook;


        public CellStyleConverter(XSSFWorkbook workbook)
        {
            _cellStylePairs = new Dictionary<OrderCellStyle, ICellStyle>();
            _workbook = workbook;
        }


        public ICellStyle GetCellStyle(OrderCellStyle orderCellStyle)
        {
            if (!_cellStylePairs.TryGetValue(orderCellStyle, out ICellStyle output))
            {
                output = CreateCellStyles(orderCellStyle);
                _cellStylePairs.Add(orderCellStyle, output);
            }
            return output;
        }

        public ICellStyle CreateCellStyles(OrderCellStyle orderCellStyle)
        {
            var cellStyle = _workbook.CreateCellStyle() as XSSFCellStyle;

            if (orderCellStyle.Alignment != null)
            {
                cellStyle.Alignment = (HorizontalAlignment)orderCellStyle.Alignment;
            }
            if (orderCellStyle.Foreground != null)
            {
                cellStyle.FillPattern = FillPattern.SolidForeground;
                // https://stackoverflow.com/questions/31159724/npoi-setting-background-color-isnt-working
                cellStyle.SetFillForegroundColor(new XSSFColor((Color)orderCellStyle.Foreground));
            }
            if (orderCellStyle.FontBold)
            {
                var font = (XSSFFont)_workbook.CreateFont();
                font.IsBold = true;
                cellStyle.SetFont(font);
            }
            if (orderCellStyle.DataFormat != null)
            {
                short dataFormat = _workbook.CreateDataFormat().GetFormat(orderCellStyle.DataFormat);
                cellStyle.DataFormat = dataFormat;
            }
            if (orderCellStyle.Borders.Count > 0)
            {
                if (orderCellStyle.Borders.Contains(Border.Left))
                {
                    cellStyle.BorderLeft = BorderStyle.Thin;
                }
                if (orderCellStyle.Borders.Contains(Border.Top))
                {
                    cellStyle.BorderTop = BorderStyle.Thin;
                }
                if (orderCellStyle.Borders.Contains(Border.Right))
                {
                    cellStyle.BorderRight = BorderStyle.Thin;
                }
                if (orderCellStyle.Borders.Contains(Border.Bottom))
                {
                    cellStyle.BorderBottom = BorderStyle.Thin;
                }
            }

            return cellStyle;
        }
    }
}
