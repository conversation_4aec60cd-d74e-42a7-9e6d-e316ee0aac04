﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.Ramp.Enums;
using FreeAxez.Addin.AdditionalFunctional.Ramp.Utils;
using FreeAxez.Addin.Enums;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.Ramp.Models
{
    public class RampComponentProvider
    {
        private const string FamilySymbolGridd40 = "Gridd-40";
        private const string FamilySymbolGridd70 = "Gridd-70";
        private const double Corner45WidthSlope12 = 0.**********;
        private const double Corner45WidthSlope20 = 0.**********;
        public const double MiddleRampWidth = 1.**********;
        private readonly string _symbolName;
        private readonly int _rampSlopeInt;
        private RampFamilyProvider _familyProvider;
        private FamilySymbol _middleSymbol;
        private FamilySymbol _sideSymbol;
        private FamilySymbol _cornerSymbol;

        public RampComponentProvider(GriddType griddType,
                                     RampSlope rampSlope)
        {
            _symbolName = griddType == GriddType.Gridd40 ? FamilySymbolGridd40 : FamilySymbolGridd70;
            _rampSlopeInt = int.Parse(Enum.GetName(
                typeof(RampSlope), rampSlope).Replace("Slope", ""));

            _familyProvider = new RampFamilyProvider(griddType, rampSlope);
        }

        public int RampSlope => _rampSlopeInt;

        public FamilySymbol GetMiddleSymbol()
        {
            // Example: Access-Flooring-FreeAxez-Gridd-Ramp_1_12
            if (_middleSymbol == null)
            {
                _middleSymbol = _familyProvider.GetSymbol(new RampFamilyOptions()
                {
                    RampAngleType = RampAngleType.None,
                    RampSide = RampSide.None,
                    RampAngle = RampAngle.None
                });

                if (_middleSymbol == null)
                {
                    throw new NullReferenceException($"Failed to get family symbol for middle ramp element.");
                }
            }

            return _middleSymbol;
        }

        public FamilySymbol GetSideSymbol(bool isLeftSideSloped)
        {
            // Example: Access-Flooring-FreeAxez-Gridd-Ramp_External_Angle_1_12
            // Example: Access-Flooring-FreeAxez-Gridd-Ramp_External_Angle_Left_1_12_1_20
            // Example: Access-Flooring-FreeAxez-Gridd-Ramp_External_Angle_Left_1_12_1_30
            // Example: Access-Flooring-FreeAxez-Gridd-Ramp_External_Angle_1_40
            // Example: Access-Flooring-FreeAxez-Gridd-Ramp_External_Angle_Left_1_20_1_50
            var rampSide = isLeftSideSloped ? RampSide.Left : RampSide.Right;
            if (_rampSlopeInt == 12 || _rampSlopeInt == 40) rampSide = RampSide.None;

            _sideSymbol = _familyProvider.GetSymbol(new RampFamilyOptions()
            {
                RampAngleType = RampAngleType.External,
                RampSide = rampSide,
                RampAngle = RampAngle.None
            });

            if (_sideSymbol == null)
            {
                throw new NullReferenceException($"Failed to get family symbol for side ramp element.");
            }

            return _sideSymbol;
        }

        public FamilySymbol GetCornerSymbol(double angle, RampAngleType angleType)
        {
            // Example: Access-Flooring-FreeAxez-Gridd-Ramp_External_Angle_1_20
            // Example: Access-Flooring-FreeAxez-Gridd-Ramp_Internal_Angle_45_1_20
            var isStraight = Math.Round(angle, 2) == Math.Round(ProjectUnitsConverter.ToRadians(90d), 2);
            _cornerSymbol = _familyProvider.GetSymbol(new RampFamilyOptions()
            {
                RampAngleType = angleType,
                RampSide = RampSide.None,
                RampAngle = isStraight ? RampAngle.Straight : RampAngle.Diagonal
            });

            if (_cornerSymbol == null)
            {
                throw new NullReferenceException($"Failed to get family symbol for corner ramp element.");
            }

            return _cornerSymbol;
        }

        public double GetMiddleWidth()
        {
            return MiddleRampWidth;
        }

        public double GetSideWidth(bool isLeftSideSloped = true)
        {
            var familySymbol = GetSideSymbol(isLeftSideSloped);

            return familySymbol.LookupParameter("C Length").AsDouble();
        }

        public double GetCornerWidth(double angle, RampAngleType angleType)
        {
            var familySymbol = GetCornerSymbol(angle, angleType);

            double width;

            if (Math.Round(angle, 2) == Math.Round(ProjectUnitsConverter.ToRadians(90d), 2))
            {
                width = familySymbol.LookupParameter("C Width").AsDouble();
            }
            else
            {
                if (angleType == RampAngleType.Internal)
                {
                    width = familySymbol.LookupParameter("C Width/2").AsDouble();
                }
                else
                {
                    width = _rampSlopeInt == 12 ? Corner45WidthSlope12 : Corner45WidthSlope20;
                }
            }

            return width;
        }
    }
}