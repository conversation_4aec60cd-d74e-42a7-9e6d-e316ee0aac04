﻿using System;
using System.Collections.Generic;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;

public class LibraryItemDto : BaseDto
{
    public string Name { get; set; }
    public string MatchingName { get; set; }
    public string Version { get; set; }
    public string ProductName { get; set; }
    public string Description { get; set; }
    public string CreatedBy { get; set; }
    public string UpdatedBy { get; set; }
    public DateTime DateCreated { get; set; }
    public DateTime LastDateUpdated { get; set; }
    public string ChangesDescription { get; set; }
    public string FamilyFilePath { get; set; }
    public string FamilyImagePath { get; set; }
    public Guid OriginalItemId { get; set; }
    public bool IsDeleted { get; set; }
    public Guid CategoryId { get; set; }
    public string RevitVersion { get; set; }
    public LibraryCategoryDto Category { get; set; }
    public List<LibraryItemHistoryEntryDto> HistoryEntries { get; set; }
    public bool IsDetailsVisible { get; set; }
    public FamilyStatus Status { get; set; }
}