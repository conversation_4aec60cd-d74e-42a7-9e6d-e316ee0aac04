﻿<?xml version="1.0" encoding="utf-8"?>

<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>WinExe</OutputType>
		<UseWPF>true</UseWPF>
		<LangVersion>latest</LangVersion>
		<ImplicitUsings>true</ImplicitUsings>
		<Nullable>enable</Nullable>
		<AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
		<GenerateAssemblyInfo>true</GenerateAssemblyInfo>
		<AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
		<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
		<ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
		<Configurations>Debug 2020;Debug 2021;Debug 2022;Debug 2023;Debug 2024;Debug 2025;Debug 2026</Configurations>
		<Configurations>$(Configurations);Release 2020;Release 2021;Release 2022;Release 2023;Release 2024;Release 2025;Release 2026</Configurations>
		<PlatformTarget>x64</PlatformTarget>
	</PropertyGroup>

	<PropertyGroup>
		<NoWarn>CS8618;</NoWarn>
	</PropertyGroup>

	<PropertyGroup Condition="$(Configuration.Contains('2020'))">
		<RevitVersion>2020</RevitVersion>
		<DefineConstants>REVIT2020</DefineConstants>
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
	<PropertyGroup Condition="$(Configuration.Contains('2021'))">
		<RevitVersion>2021</RevitVersion>
		<DefineConstants>REVIT2021</DefineConstants>
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
	<PropertyGroup Condition="$(Configuration.Contains('2022'))">
		<RevitVersion>2022</RevitVersion>
		<DefineConstants>REVIT2022</DefineConstants>
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
	<PropertyGroup Condition="$(Configuration.Contains('2023'))">
		<RevitVersion>2023</RevitVersion>
		<DefineConstants>REVIT2023</DefineConstants>
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
	<PropertyGroup Condition="$(Configuration.Contains('2024'))">
		<RevitVersion>2024</RevitVersion>
		<DefineConstants>REVIT2024</DefineConstants>
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
	<PropertyGroup Condition="$(Configuration.Contains('2025'))">
		<RevitVersion>2025</RevitVersion>
		<DefineConstants>REVIT2025</DefineConstants>
		<TargetFramework>net8.0-windows</TargetFramework>
	</PropertyGroup>
		<PropertyGroup Condition="$(Configuration.Contains('2026'))">
		<RevitVersion>2026</RevitVersion>
		<DefineConstants>REVIT2026</DefineConstants>
		<TargetFramework>net8.0-windows</TargetFramework>
	</PropertyGroup>

	<PropertyGroup Condition="!$(TargetFramework.StartsWith('net4'))">
		<EnableDynamicLoading>true</EnableDynamicLoading>
		<GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
		<GenerateDependencyFiles>false</GenerateDependencyFiles>
	</PropertyGroup>

	<ItemGroup Condition="$(TargetFramework.StartsWith('net4'))">
		<Reference Include="Microsoft.CSharp" />
		<Reference Include="PresentationCore" />
		<Reference Include="System.Net.Http" />
		<Reference Include="System.Windows.Forms" />
		<Reference Include="System.Web" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Web.WebView2" Version="1.0.3124.44" />
		<PackageReference Include="Serilog" Version="4.0.1" />
		<PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
	</ItemGroup>

	<ItemGroup>
		<Resource Include="Resources\bim-logo.png" />
	</ItemGroup>

</Project>