using System.IO;
using System.Net;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Details;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.RevitHandlers
{
    public class DetailsDownloadToRevitHandler : BaseExternalEventHandler
    {
        private LibraryItemDetailsDto _detailsItem;
        private object _viewModel;

        public void SetData(LibraryItemDetailsDto detailsItem, object viewModel = null)
        {
            _detailsItem = detailsItem ?? throw new ArgumentNullException(nameof(detailsItem));
            _viewModel = viewModel;
        }

        public override void ExecuteInternal(UIApplication app)
        {
            string localFilePath = null;

            try
            {
                if (app.ActiveUIDocument.Document.IsFamilyDocument)
                    throw new InvalidOperationException("Cannot load details into a family document.");

                if (!IsRevitVersionCompatible(_detailsItem.RevitVersion))
                    throw new InvalidOperationException(
                        $"Details version {_detailsItem.RevitVersion} is not compatible with current Revit version.");

                localFilePath = DownloadDetailsFile(_detailsItem.FilePath, _detailsItem.Name);
                
                var fileExtension = Path.GetExtension(_detailsItem.FilePath).ToUpperInvariant().Replace(".", "");
                
                if (fileExtension.Equals("RFA", StringComparison.OrdinalIgnoreCase))
                {
                    ProcessRfaFile(app, localFilePath);
                }
                else
                {
                    ProcessRvtFile(app, localFilePath);
                }

                UpdateViewModel(false);
            }
            catch (Exception ex)
            {
                ErrorHandler.HandleError("Error processing details download", ex, onError: () => UpdateViewModel(true));
            }
            finally
            {
                CleanupTempFile(localFilePath);
                _detailsItem = null;
                _viewModel = null;
            }
        }

        private void ProcessRfaFile(UIApplication app, string localFilePath)
        {
            try
            {
                using var trans = new Transaction(app.ActiveUIDocument.Document, "Load Family");
                trans.Start();
                app.ActiveUIDocument.Document.LoadFamily(localFilePath, new OverrideFamilyLoadOptions(), out _);
                trans.Commit();
            }
            catch (Exception ex)
            {
                ErrorHandler.HandleError("Error loading family", ex);
                throw;
            }
        }

        private void ProcessRvtFile(UIApplication app, string localFilePath)
        {
            Document sourceDocument = null;

            try
            {
                var currentDocument = app.ActiveUIDocument.Document;
                sourceDocument = app.Application.OpenDocumentFile(localFilePath);

                var draftingViews = DetailSelectionHelper.GetDraftingViews(sourceDocument);
                var nonEmptyDraftingViews = draftingViews
                    .Where(view => DetailSelectionHelper.IsDraftingViewNonEmpty(sourceDocument, view))
                    .ToList();

                if (nonEmptyDraftingViews.Count == 0)
                {
                    FamilyLibraryCore.ShowMessage("No Views Found", 
                        "No non-empty drafting views found in this file.", MessageType.Info);
                    return;
                }

                var detailSelectionResult = DetailSelectionDialogHelper.ShowModalDetailSelectionDialog(
                    sourceDocument, nonEmptyDraftingViews);

                if (!detailSelectionResult.DialogResult || detailSelectionResult.SelectedViewIds.Count == 0)
                    return;

                var copySuccess = true;
                var copiedViews = DetailsCopyUtility.CopySelectedDraftingViewsWithProgress(
                    sourceDocument, currentDocument, detailSelectionResult.SelectedViewIds.ToList(), ref copySuccess);

                if (copiedViews.Count > 0)
                {
                    RevitDialogUtility.ShowCopiedItemsDialog(copiedViews, currentDocument);
                }
                // Don't show any message if no views were copied - this is normal when user cancels or selects nothing
            }
            catch (Exception ex)
            {
                ErrorHandler.HandleError("Error processing RVT file", ex);
                throw;
            }
            finally
            {
                if (sourceDocument?.IsValidObject == true)
                {
                    try
                    {
                        sourceDocument.Close(false);
                    }
                    catch (Exception closeEx)
                    {
                        LogHelper.Warning($"Failed to close source document: {closeEx.Message}");
                    }
                }
            }
        }

        private string DownloadDetailsFile(string detailsFileUrl, string fileName)
        {
            var uniqueTempPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
            Directory.CreateDirectory(uniqueTempPath);
            var localFilePath = Path.Combine(uniqueTempPath, fileName);

            using var client = new WebClient();
            client.DownloadFile(new Uri(detailsFileUrl), localFilePath);
            return localFilePath;
        }

        private bool IsRevitVersionCompatible(string detailsVersion)
        {
            int.TryParse(RevitManager.RevitVersion, out var projectVer);
            int.TryParse(detailsVersion, out var detailsVer);
            return detailsVer <= projectVer;
        }

        private void CleanupTempFile(string tempFilePath)
        {
            if (string.IsNullOrEmpty(tempFilePath))
                return;

            try
            {
                var tempDirectory = Path.GetDirectoryName(tempFilePath);
                if (!string.IsNullOrEmpty(tempDirectory) && Directory.Exists(tempDirectory))
                    Directory.Delete(tempDirectory, true);
            }
            catch (Exception ex)
            {
                LogHelper.Warning($"Failed to delete temp directory: {ex.Message}");
            }
        }

        private void UpdateViewModel(bool isError)
        {
            if (_viewModel == null)
                return;

            try
            {
                // Add specific ViewModel updates here if needed
                // For now, we don't have specific ViewModels that need updating for Details
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error updating ViewModel: {ex.Message}");
            }
        }
    }
}
