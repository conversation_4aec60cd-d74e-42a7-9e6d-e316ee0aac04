﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.DeleteView.Models;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.DeleteView.ViewModels
{
    public class DeleteViewViewModel : BaseViewModel
    {
        public DeleteViewViewModel()
        {
            RevitViews = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(View))
                .WhereElementIsNotElementType()
                .ToElements()
                .Cast<View>()
                .Where(v => v.CanBePrinted)
                .Select(v => new RevitView()
                {
                    Name = v.Title,
                    View = v
                })
                .OrderBy(s => s.Name)
                .ToList();

            CheckAllCommand = new RelayCommand(OnCheckAllCommandExecute);
            UncheckAllCommand = new RelayCommand(OnUncheckAllCommandExecute);
            DeleteViewsCommand = new RelayCommand(OnDeleteViewsCommandExecute);
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
        }


        public List<RevitView> RevitViews { get; set; }

        public ICommand CheckAllCommand { get; set; }
        private void OnCheckAllCommandExecute(object p)
        {
            foreach (var view in RevitViews)
            {
                view.IsCheck = true;
            }
        }

        public ICommand UncheckAllCommand { get; set; }
        private void OnUncheckAllCommandExecute(object p)
        {
            foreach (var view in RevitViews)
            {
                view.IsCheck = false;
            }
        }

        public ICommand DeleteViewsCommand { get; set; }
        private void OnDeleteViewsCommandExecute(object p)
        {
            if (RevitViews.Count(v => v.IsCheck == true) == 0)
            {
                InfoDialog.ShowDialog("Warning", "No views selected for deletion.");
                return;
            }

            (p as Window).Close();

            ChangeActiveViewIfNecessary();

            using (var t = new Transaction(RevitManager.Document))
            {
                t.Start("Delete Selected Views");

                foreach (var revitView in RevitViews)
                {
                    if (!revitView.IsCheck)
                    {
                        continue;
                    }

                    try
                    {
                        RevitManager.Document.Delete(revitView.View.Id);
                    }
                    catch { }
                }

                t.Commit();
            }

            InfoDialog.ShowDialog("Report", $"Deleted {RevitViews.Count(v => v.IsCheck)} views.");
        }

        public ICommand CancelCommand { get; set; }
        private void OnCancelCommandExecute(object p)
        {
            (p as Window).Close();
        }


        private void ChangeActiveViewIfNecessary()
        {
            var viewsForDelete = RevitViews.Where(v => v.IsCheck).Select(v => v.View.Id.GetIntegerValue()).ToHashSet<int>();
            if (!viewsForDelete.Contains(RevitManager.UIDocument.ActiveView.Id.GetIntegerValue()))
            {
                return;
            }

            var openedViews = RevitManager.UIDocument.GetOpenUIViews().Select(v => v.ViewId.GetIntegerValue()).ToHashSet<int>();
            var availableViewsToChange = openedViews.Except(viewsForDelete);
            if (availableViewsToChange.Count() > 0)
            {
                RevitManager.UIDocument.ActiveView = RevitManager.Document.GetElement(new ElementId(availableViewsToChange.First())) as View;
            }
            else
            {
                View default3DView = null;

                using (var transaction = new Transaction(RevitManager.Document))
                {
                    transaction.Start("Default 3D View");

                    var viewFamilyTypeId = new FilteredElementCollector(RevitManager.Document)
                        .OfClass(typeof(ViewFamilyType))
                        .Where(t => (t as ViewFamilyType).ViewFamily == ViewFamily.ThreeDimensional)
                        .Select(t => t.Id)
                        .First();

                    default3DView = View3D.CreateIsometric(RevitManager.Document, viewFamilyTypeId);

                    transaction.Commit();
                }

                RevitManager.UIDocument.ActiveView = default3DView;
            }
        }
    }
}
