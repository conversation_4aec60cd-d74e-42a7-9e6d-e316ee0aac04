﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.Helpers;
using FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.Models;
using FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using System.Windows.Interop;

namespace FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class BaseUnitReplacementCommand : BaseExternalCommand
    {
        private static BaseUnitReplacementWindow _reportWindow;

        public override Result Execute()
        {
            var selectLevelResult = SelectLevelWindow.ShowDialog("Base Unit Replacement", out List<Level> selectedLevels);
            if (selectLevelResult != true)
            {
                return Result.Cancelled;
            }
            else if (selectedLevels.Count() == 0)
            {
                MessageWindow.ShowDialog("No level has been selected.", MessageType.Notify);
                return Result.Cancelled;
            }

            var familyCollector = new BaseUnitReplacementFamilyCollector(selectedLevels);
            if (!familyCollector.IsFamiliesExist(out string validationMessage))
            {
                MessageWindow.ShowDialog(validationMessage, MessageType.Notify);
                return Result.Cancelled;
            }

            var intersectionHelper = new BoxUnitIntersectionHelper(familyCollector);
            var intersections = intersectionHelper.GetIntersections();

            var replaceHelper = new ReplaceUnitHelper(familyCollector);
            replaceHelper.ReplaceBaseUnits(ref intersections, out string report);
            
            MessageWindow.ShowDialog(report, MessageType.Success);
            ShowReportWindow(intersections.Where(i => i.Box != null).ToList()); // Show only intersections with unit

            return Result.Succeeded;
        }

        private void ShowReportWindow(List<BoxUnitsIntersection> intersections)
        {
            if (_reportWindow != null && _reportWindow.IsVisible) _reportWindow.Close();

            _reportWindow = new BaseUnitReplacementWindow();
            var viewModel = new BaseUnitReplacementViewModel(intersections);
            _reportWindow.DataContext = viewModel;
            var handler = new WindowInteropHelper(_reportWindow);
            handler.Owner = RevitManager.UIApplication.MainWindowHandle;
            _reportWindow.Show();
        }
    }
}