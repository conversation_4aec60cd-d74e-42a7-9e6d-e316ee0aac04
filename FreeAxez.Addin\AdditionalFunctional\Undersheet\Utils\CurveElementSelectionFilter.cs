﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;

namespace FreeAxez.Addin.AdditionalFunctional.Undersheet.Utils
{
    public class CurveElementSelectionFilter : ISelectionFilter
    {
        public bool AllowElement(Element elem)
        {
            if (!(elem is CurveElement))
            {
                return false;
            }

            if ((elem as CurveElement).GeometryCurve is Line) 
            { 
                return true;
            }

            return false;
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            return false;
        }
    }
}
