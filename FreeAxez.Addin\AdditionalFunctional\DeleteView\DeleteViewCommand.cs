﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.DeleteView.Views;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.DeleteView
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    internal class DeleteViewCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var window = new DeleteViewView();
            window.ShowDialog();

            return Result.Succeeded;
        }
    }
}
