﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="ColorsPalette.xaml" />
    </ResourceDictionary.MergedDictionaries>
    <Style x:Key="CheckBoxStyle"
           TargetType="CheckBox">
        <Setter Property="Foreground"
                Value="{StaticResource Gray800}" />
        <Setter Property="FontSize"
                Value="13" />
        <Setter Property="FontFamily"
                Value="Segoe UI" />
        <Setter Property="FontWeight"
                Value="Normal" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0"
                                Width="15"
                                Height="15"
                                Margin="0,0,5,0"
                                BorderBrush="{StaticResource Gray600}"
                                Opacity="0.5"
                                BorderThickness="1"
                                Background="Transparent"
                                x:Name="CheckBorder"
                                CornerRadius="2">
                            <!-- IndeterminateMark Rectangle -->
                            <Rectangle x:Name="IndeterminateMark"
                                       Width="9"
                                       Height="9"
                                       Fill="{StaticResource Blue500}"
                                       Visibility="Collapsed"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center" />
                        </Border>
                        <ContentPresenter Grid.Column="1"
                                          VerticalAlignment="Center"
                                          HorizontalAlignment="Left"
                                          TextElement.Foreground="{TemplateBinding Foreground}"
                                          TextElement.FontSize="{TemplateBinding FontSize}" />
                        <Path x:Name="CheckMark"
                              Grid.Column="0"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              Data="M1,5 L4,8 L9,1"
                              Stroke="White"
                              StrokeThickness="3"
                              Margin="0,0,4,0"
                              Opacity="0" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <!-- Checked State -->
                        <Trigger Property="IsChecked"
                                 Value="True">
                            <Setter TargetName="CheckMark"
                                    Property="Opacity"
                                    Value="1" />
                            <Setter TargetName="CheckBorder"
                                    Property="Background"
                                    Value="{StaticResource Blue500}" />
                            <Setter TargetName="CheckBorder"
                                    Property="Opacity"
                                    Value="1" />
                            <Setter TargetName="CheckBorder"
                                    Property="BorderBrush"
                                    Value="{StaticResource Blue500}" />
                        </Trigger>
                        <!-- Indeterminate State -->
                        <Trigger Property="IsChecked"
                                 Value="{x:Null}">
                            <Setter TargetName="IndeterminateMark"
                                    Property="Visibility"
                                    Value="Visible" />
                            <Setter TargetName="CheckBorder"
                                    Property="Background"
                                    Value="Transparent" />
                            <Setter TargetName="CheckBorder"
                                    Property="BorderBrush"
                                    Value="{StaticResource Gray600}" />
                            <Setter TargetName="CheckBorder"
                                    Property="Opacity"
                                    Value="1" />
                        </Trigger>
                        <!-- Disabled State -->
                        <Trigger Property="IsEnabled"
                                 Value="False">
                            <Setter Property="Foreground"
                                    Value="#FFBDBD" />
                            <Setter TargetName="CheckBorder"
                                    Property="BorderBrush"
                                    Value="{StaticResource Gray400}" />
                            <Setter TargetName="CheckBorder"
                                    Property="Background"
                                    Value="{StaticResource Gray200}" />
                            <Setter TargetName="CheckMark"
                                    Property="Stroke"
                                    Value="{StaticResource Gray400}" />
                            <Setter TargetName="IndeterminateMark"
                                    Property="Fill"
                                    Value="{StaticResource Gray400}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>