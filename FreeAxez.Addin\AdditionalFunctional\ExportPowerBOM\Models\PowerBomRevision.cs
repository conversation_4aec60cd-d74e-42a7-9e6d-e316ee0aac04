﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBOM.Utils;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Models
{
    public class PowerBomRevision
    {
        private readonly RevisionViewModel _revision;
        private readonly List<string> _selectedAccessoryNames;

        public PowerBomRevision(RevisionViewModel revision, List<string> selectedAccessoryNames)
        {
            _revision = revision;
            _selectedAccessoryNames = selectedAccessoryNames;
        }


        public string ProjectName { get; private set; }
        public string RevisionDate { get; private set; }
        public string RevisionNumber { get; private set; }
        public List<string> ModelLevels { get; private set; }
        public List<ProductGroup> ProductGroups { get; private set; }
        public List<ProductGroup> FloorBoxCountGroups { get; private set; }


        public void Calculate()
        {
            var products = PowerProductCollector.Collect(_selectedAccessoryNames);
            ProductGroups = ProductGroup.GroupProducts(products);
            FloorBoxCountGroups = ProductGroup.GroupFloorBoxes(products);

            ModelLevels = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Level))
                .Select(l => l.Name)
                .OrderBy(l => l)
                .ToList();

            RevisionDate = _revision.RevisionDate;
            RevisionNumber = _revision.RevisionNumber;
            ProjectName = RevitManager.Document.ProjectInformation.Name;
        }
    }
}
