﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.Ramp.Views.RampView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.Ramp.Converters"
        xmlns:enums="clr-namespace:FreeAxez.Addin.AdditionalFunctional.Ramp.Enums"
        xmlns:globalEnums="clr-namespace:FreeAxez.Addin.Enums"
        xmlns:viewModels="clr-namespace:FreeAxez.Addin.AdditionalFunctional.Ramp.ViewModels"
        mc:Ignorable="d"
        d:DesignHeight="320"
        d:DesignWidth="250"
        Height="320"
        Width="250"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Title="Frame">

    <Window.DataContext>
        <viewModels:RampViewModel />
    </Window.DataContext>

    <Window.Resources>
        <converters:EnumToBoolConverter x:Key="EnumToBoolConverter" />
    </Window.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition />
        </Grid.ColumnDefinitions>

        <Grid.RowDefinitions>
            <RowDefinition Height="60" />
            <RowDefinition Height="115" />
            <RowDefinition Height="55" />
            <RowDefinition Height="50" />
        </Grid.RowDefinitions>

        <GroupBox Header="Grid Type"
                  Grid.Row="0"
                  Grid.Column="0"
                  Margin="5 0 5 0">
            <StackPanel Margin="3">
                <RadioButton Content="Grid 40"
                             IsChecked="{Binding GriddType,Converter={StaticResource EnumToBoolConverter},
                                 ConverterParameter={x:Static globalEnums:GriddType.Gridd40}}" />
                <RadioButton Content="Grid 70"
                             Margin="0 1 0 0"
                             IsChecked="{Binding GriddType,Converter={StaticResource EnumToBoolConverter},
                                 ConverterParameter={x:Static globalEnums:GriddType.Gridd70}}" />
            </StackPanel>
        </GroupBox>

        <GroupBox Header="Ramp Slope"
                  Grid.Row="1"
                  Grid.Column="0"
                  Margin="5 5 5 0">
            <StackPanel Margin="3">
                <RadioButton Content="1 : 12"
                             IsChecked="{Binding RampSlope,Converter={StaticResource EnumToBoolConverter},
                                 ConverterParameter={x:Static enums:RampSlope.Slope12}}" />
                <RadioButton Content="1 : 20"
                             Margin="0 1 0 0"
                             IsChecked="{Binding RampSlope,Converter={StaticResource EnumToBoolConverter},
                                 ConverterParameter={x:Static enums:RampSlope.Slope20}}" />
                <RadioButton Content="1 : 30"
                             Margin="0 1 0 0"
                             IsChecked="{Binding RampSlope,Converter={StaticResource EnumToBoolConverter},
                                 ConverterParameter={x:Static enums:RampSlope.Slope30}}" />
                <RadioButton Content="1 : 40"
                             Margin="0 1 0 0"
                             IsChecked="{Binding RampSlope,Converter={StaticResource EnumToBoolConverter},
                                 ConverterParameter={x:Static enums:RampSlope.Slope40}}" />
                <RadioButton Content="1 : 50"
                             Margin="0 1 0 0"
                             IsChecked="{Binding RampSlope,Converter={StaticResource EnumToBoolConverter},
                                 ConverterParameter={x:Static enums:RampSlope.Slope50}}" />
            </StackPanel>
        </GroupBox>

        <GroupBox Header="Side Slope"
                  Grid.Row="2"
                  Grid.Column="0"
                  Margin="5 5 5 0">
            <StackPanel Margin="3"
                        Orientation="Horizontal">
                <CheckBox Content="Left"
                          IsChecked="{Binding IsLeftSideSlopeChecked}" />
                <CheckBox Content="Right"
                          Margin="30 0 0 0"
                          IsChecked="{Binding IsRightSideSlopeChecked}" />
            </StackPanel>
        </GroupBox>

        <Button Content="Select Lines"
                Command="{Binding SelectLinesCommand}"
                CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor,
                          AncestorType={x:Type Window}}}"
                Grid.Row="3"
                Grid.Column="0"
                Margin="0 5 0 0"
                Height="25"
                Width="200" />
    </Grid>
</Window>
