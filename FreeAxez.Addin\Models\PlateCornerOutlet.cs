﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class PlateCornerOutlet : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
                "Outlet_Corner_Plate"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public PlateCornerOutlet(Element element) : base(element)
        {
        }

        public static List<PlateCornerOutlet> Collect()
        {
            return FamilyCollector.Instances.Select(f => new PlateCornerOutlet(f)).ToList();
        }

        public static Family GetFamily(out string missedFamilyMessage)
        {
            var family = FamilyCollector.Families.FirstOrDefault();

            missedFamilyMessage = string.Empty;
            if (family == null)
            {
                missedFamilyMessage =
                    "No family with 'Outlet_Corner_Plate' in its name.";
            }

            return family;
        }
    }
}
