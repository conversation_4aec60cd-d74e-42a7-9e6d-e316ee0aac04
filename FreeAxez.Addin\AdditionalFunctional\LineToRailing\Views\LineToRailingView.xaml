﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.LineToRailing.Views.LineToRailingView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.LineToRailing.Views"
        xmlns:vm="clr-namespace:FreeAxez.Addin.AdditionalFunctional.LineToRailing.ViewModels"
        SizeToContent="WidthAndHeight"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterScreen"
        Title="Lines To Railings">
    <Window.DataContext>
        <vm:LineToRailingViewModel/>
    </Window.DataContext>
    <Grid Margin="5">
        <StackPanel>
            <Label Content="Railing Type:" />
            <ComboBox ItemsSource="{Binding RailingTypes}" SelectedItem="{Binding SelectedRailingType}" Width="350">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Name}"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
            <CheckBox Margin="0,10,0,5" IsChecked="{Binding DeleteSelectedLines}" Content="Delete Selected Lines"/>
            <CheckBox Margin="0,0,0,5" IsChecked="{Binding CreateRailingForEachLine}" Content="Create Railing For Each Line"/>
            <StackPanel Orientation="Horizontal" >
                <Label Content="Base Offset" />
                <TextBox Margin="0,2" Text="{Binding BaseOffset}" Width="80"/>
            </StackPanel>
            <Button Margin="0,20,0,0" Padding="5" Content="Create Railings" Height="30" Command="{Binding CreateRailingsCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
        </StackPanel>
    </Grid>
</Window>
