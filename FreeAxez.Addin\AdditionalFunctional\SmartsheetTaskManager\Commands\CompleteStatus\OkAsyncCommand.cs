﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Abstractions;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Commands.CompleteStatus;

public class OkAsyncCommand : AsyncCommandBase
{
    private readonly CompleteStatusViewModel _completeStatusViewModel;
    private readonly TaskManagerViewModel _taskManagerViewModel;
    private readonly TaskManagerHttpClientBase _taskManagerHttpClientService;

    public OkAsyncCommand(CompleteStatusViewModel completeStatusViewModel,
                          TaskManagerViewModel taskManagerViewModel,
                          TaskManagerHttpClientBase taskManagerHttpClientService)
    {
        _completeStatusViewModel = completeStatusViewModel;
        _taskManagerViewModel = taskManagerViewModel;
        _taskManagerHttpClientService = taskManagerHttpClientService;
    }

    public async override Task ExecuteAsync(object @object)
    {
        Window? window = @object as Window;
        window.Owner = null;
        window.Visibility = Visibility.Hidden;

        try
        {
            _taskManagerViewModel.IsExecuting = true;

            HttpResponseMessage httpResponseMessage = await _taskManagerHttpClientService
                .SetCompleteStatusAsync(_taskManagerViewModel.SheetId,
                                        _taskManagerViewModel.RecentRow.Id,
                                        _completeStatusViewModel.AttachedFilePaths,
                                        _completeStatusViewModel.Comment,
                                        _completeStatusViewModel.CancellationTokenSource.Token);

            await _taskManagerViewModel.InitializeAsync();

            _taskManagerViewModel.IsExecuting = false;
        }
        catch (TaskCanceledException)
        {
            _taskManagerViewModel.ErrorResponse = string.Empty;
        }
        catch (Exception exception)
        {
            _taskManagerViewModel.ErrorResponse = exception.Message;
        }
        finally
        {
            _taskManagerViewModel.IsExecuting = false;
            window?.Close();
        }
    }
}
