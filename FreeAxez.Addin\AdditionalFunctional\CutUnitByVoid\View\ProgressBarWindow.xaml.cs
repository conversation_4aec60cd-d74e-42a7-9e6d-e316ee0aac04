﻿using System.Windows;

namespace FreeAxez.Addin.AdditionalFunctional.CutUnitByVoid.View
{
    public partial class ProgressBarWindow : Window
    {
        public ProgressBarWindow()
        {
            InitializeComponent();
        }


        public void SetMaxValue(int maxValue)
        {
            progressBar.IsIndeterminate = false;
            progressBar.Maximum = maxValue;
        }

        public void UpdateProgress(int value)
        {
            progressBar.Value = value;
        }

        public void SetStatus(string message)
        {
            progressText.Text = message;
        }
    }
}
