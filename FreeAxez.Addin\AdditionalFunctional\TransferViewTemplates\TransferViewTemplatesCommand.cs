﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Utils;
using FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates.Views;
using FreeAxez.Addin.Infrastructure;
using RevitManager = FreeAxez.Addin.Infrastructure.RevitManager;

namespace FreeAxez.Addin.AdditionalFunctional.TransferViewTemplates
{
    [Transaction(TransactionMode.Manual)]
    public class TransferViewTemplatesCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            TransferViewTemplatesRevitManager.CommandData = RevitManager.CommandData;

            var window = new TransferViewTemplatesView();
            window.ShowDialog();

            return Result.Succeeded;
        }
    }
}