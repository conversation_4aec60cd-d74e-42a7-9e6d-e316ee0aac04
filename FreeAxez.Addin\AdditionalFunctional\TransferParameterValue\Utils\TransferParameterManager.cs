﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;

namespace FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.Utils
{
    class TransferParameterManager
    {
        private readonly Element _sourceElement;
        private readonly List<Element> _targetElements;
        private readonly List<string> _parameterNames;


        public TransferParameterManager(Element sourceElement, List<Element> targetElements, List<string> parameterNames)
        {
            _sourceElement = sourceElement;
            _targetElements = targetElements;
            _parameterNames = parameterNames;
        }


        public static List<string> GetAvailableParameterNames(Element element)
        {
            var parameterNames = new List<string>();

            var instanceParameters = element.ParametersMap;
            foreach (Parameter parameter in instanceParameters)
            {
                if (parameter.StorageType == StorageType.ElementId)
                {
                    continue;
                }

                if (parameter.IsReadOnly)
                {
                    continue;
                }

                parameterNames.Add(parameter.Definition.Name);
            }

            var typeParameters = RevitManager.Document.GetElement(element.GetTypeId())?.ParametersMap;

            if (typeParameters != null)
            {
                foreach (Parameter parameter in typeParameters)
                {
                    if (parameter.StorageType == StorageType.ElementId)
                    {
                        continue;
                    }

                    if (parameter.IsReadOnly)
                    {
                        continue;
                    }

                    parameterNames.Add(parameter.Definition.Name);
                }
            }

            return parameterNames;
        }

        public void SetValues()
        {
            using (var t = new Transaction(RevitManager.Document))
            {
                t.Start("Transfer Parameter Values");

                foreach (var targetElement in _targetElements)
                {
                    foreach (var parameterName in _parameterNames)
                    {
                        var sourceParameter = GetParameterFromInstanceOrType(_sourceElement, parameterName);
                        var targetParameter = GetParameterFromInstanceOrType(targetElement, parameterName);

                        if (sourceParameter == null || targetParameter == null)
                        {
                            continue;
                        }

                        try
                        {
                            switch (sourceParameter.StorageType)
                            {
                                case StorageType.Double:
                                    targetParameter.Set(sourceParameter.AsDouble());
                                    break;
                                case StorageType.Integer:
                                    targetParameter.Set(sourceParameter.AsInteger());
                                    break;
                                case StorageType.String:
                                    targetParameter.Set(sourceParameter.AsString());
                                    break;
                                case StorageType.ElementId:
                                    break;
                            }
                        }
                        catch
                        {

                        }
                    }
                }

                t.Commit();
            }
        }

        private Parameter GetParameterFromInstanceOrType(Element element, string parameterName)
        {
            Parameter parameter = null;

            parameter = element.LookupParameter(parameterName);
            if (parameter == null)
            {
                parameter = RevitManager.Document.GetElement(element.GetTypeId()).LookupParameter(parameterName);
            }

            return parameter;
        }
    }
}
