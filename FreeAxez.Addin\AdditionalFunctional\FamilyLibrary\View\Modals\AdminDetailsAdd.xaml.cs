using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals;

public partial class AdminDetailsAdd : UserControl
{
    public AdminDetailsAdd()
    {
        InitializeComponent();
    }

    private void Rectangle_DragEnter(object sender, DragEventArgs e)
    {
        var rect = sender as System.Windows.Shapes.Rectangle;
        if (rect != null)
        {
            rect.Fill = new SolidColorBrush(Colors.WhiteSmoke);
        }
    }

    private void Rectangle_DragLeave(object sender, DragEventArgs e)
    {
        var rect = sender as System.Windows.Shapes.Rectangle;
        if (rect != null)
        {
            rect.Fill = new SolidColorBrush(Colors.White);
        }
    }

    private void Rectangle_Drop(object sender, DragEventArgs e)
    {
        if (DataContext is AdminDetailsAddVm viewModel)
        {
            viewModel.FileDropCommand.Execute(e);
        }
    }
}
