﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class BorderL : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
            },
            FamilyNamesEndWith = new List<string>
            {
                "Nested_Border_L_Type_v2"
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public BorderL(Element element) : base(element)
        {
        }

        public static List<BorderL> Collect()
        {
            return FamilyCollector.Instances.Select(g => new BorderL(g)).ToList();
        }
    }
}
