using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using Microsoft.Win32;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels;

public class LoadOrReloadLinetypesViewModel : WindowViewModel
{
    private readonly DwgLayerManagerApiService _apiService;
    private readonly LayerDialogService _dialogService;
    private string _selectedFilePath;
    private LinetypeModel _selectedLinetype;

    public LoadOrReloadLinetypesViewModel(DwgLayerManagerApiService apiService)
    {
        _dialogService = new LayerDialogService(apiService);
        _apiService = apiService ?? throw new ArgumentNullException(nameof(apiService));

        AvailableLinetypes = new ObservableCollection<LinetypeModel>();

        // Try to find default acad.lin path
        var defaultPath = _dialogService.GetDefaultLinetypeFilePath();
        SelectedFilePath = defaultPath ?? "acad.lin"; // Placeholder if not found

        LoadLinetypesFromFile();

        BrowseFileCommand = new RelayCommand(_ => BrowseFile());
        OkCommand = new RelayCommand(param => Ok(param as Window));
        CancelCommand = new RelayCommand(param => Cancel(param as Window));
    }

    public ObservableCollection<LinetypeModel> AvailableLinetypes { get; }

    public string SelectedFilePath
    {
        get => _selectedFilePath;
        set
        {
            if (Set(ref _selectedFilePath, value)) LoadLinetypesFromFile();
        }
    }

    public LinetypeModel SelectedLinetype
    {
        get => _selectedLinetype;
        set => Set(ref _selectedLinetype, value);
    }

    public ICommand BrowseFileCommand { get; }
    public ICommand OkCommand { get; }
    public ICommand CancelCommand { get; }

    private void BrowseFile()
    {
        var dialog = new OpenFileDialog
        {
            Title = "Select Linetype File",
            Filter = "Linetype Files (*.lin)|*.lin|All Files (*.*)|*.*",
            DefaultExt = ".lin"
        };

        // Set initial directory based on current path
        if (!string.IsNullOrEmpty(SelectedFilePath))
        {
            var directory = Path.GetDirectoryName(SelectedFilePath);
            if (!string.IsNullOrEmpty(directory) && Directory.Exists(directory))
            {
                dialog.InitialDirectory = directory;

                // If the current file exists, pre-select it
                if (File.Exists(SelectedFilePath)) dialog.FileName = Path.GetFileName(SelectedFilePath);
            }
        }

        if (dialog.ShowDialog() == true) SelectedFilePath = dialog.FileName;
    }

    private void LoadLinetypesFromFile()
    {
        AvailableLinetypes.Clear();

        if (string.IsNullOrEmpty(SelectedFilePath))
            return;

        try
        {
            if (File.Exists(SelectedFilePath))
            {
                var linetypes = _dialogService.ParseLinetypeFile(SelectedFilePath);
                foreach (var linetype in linetypes) AvailableLinetypes.Add(linetype);

                // Select first item if available
                if (AvailableLinetypes.Any()) SelectedLinetype = AvailableLinetypes.First();
            }
            // File doesn't exist - AvailableLinetypes will remain empty
        }
        catch
        {
            // File can't be loaded - AvailableLinetypes will remain empty
        }
    }


    private async void Ok(Window window)
    {
        if (SelectedLinetype == null)
        {
            MessageWindow.ShowDialog("Error", "Please select a linetype to add", MessageType.Error);
            return;
        }

        try
        {
            // Check if linetype already exists
            var existingLinetypes = await _apiService.GetAllLinetypesAsync();
            var existingLinetype = existingLinetypes.FirstOrDefault(l =>
                l.Name.Equals(SelectedLinetype.Name, StringComparison.OrdinalIgnoreCase));

            LinetypeModel resultLinetype = null;

            if (existingLinetype != null)
            {
                // Update existing linetype
                existingLinetype.Name = SelectedLinetype.Name;
                existingLinetype.Description = SelectedLinetype.Description;
                existingLinetype.PatternRaw = SelectedLinetype.PatternRaw;
                existingLinetype.UpdatedUtc = DateTime.UtcNow;

                resultLinetype = await _apiService.UpdateLinetypeAsync(existingLinetype);
            }
            else
            {
                // Create new linetype
                var newLinetype = new LinetypeModel
                {
                    Id = Guid.NewGuid(),
                    Name = SelectedLinetype.Name,
                    Description = SelectedLinetype.Description,
                    PatternRaw = SelectedLinetype.PatternRaw,
                    UpdatedUtc = DateTime.UtcNow
                };

                resultLinetype = await _apiService.CreateLinetypeAsync(newLinetype);
            }

            if (resultLinetype != null)
            {
                // Close window without success message
                window.DialogResult = true;
                window.Close();
            }
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog("Error", $"Failed to save linetype: {ex.Message}", MessageType.Error);
        }
    }

    private void Cancel(Window window)
    {
        window.DialogResult = false;
        window.Close();
    }
}