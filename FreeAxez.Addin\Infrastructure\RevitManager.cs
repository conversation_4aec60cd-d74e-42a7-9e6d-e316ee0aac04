﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using System.Windows.Interop;
using System.Windows;
using Application = Autodesk.Revit.ApplicationServices.Application;

namespace FreeAxez.Addin.Infrastructure;

internal class RevitManager
{
    public static UIControlledApplication UiControlledApplication { get; internal set; }
    public static ExternalCommandData CommandData { get; internal set; }
    public static UIApplication UIApplication => CommandData.Application;
    public static Application Application => CommandData.Application.Application;
    public static UIDocument UIDocument => CommandData.Application.ActiveUIDocument;
    public static Document Document => CommandData.Application.ActiveUIDocument.Document;
    public static string RevitVersion => CommandData.Application.Application.VersionNumber;


    public static void SetRevitAsWindowOwner(Window window)
    {
        var helper = new WindowInteropHelper(window);

#if revit2018
        helper.Owner = System.Diagnostics.Process.GetCurrentProcess().MainWindowHandle;
#else
        if (CommandData == null)
        {
            helper.Owner = UiControlledApplication.MainWindowHandle;
        }
        else
        {
            helper.Owner = UIApplication.MainWindowHandle;
        }
#endif
    }
}