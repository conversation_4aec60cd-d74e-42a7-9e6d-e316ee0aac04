﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement.Views.PanelSchedulePlacementView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement.ViewModels"
        SizeToContent="WidthAndHeight"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterScreen"
        Title="Track Schedule Placement"
        Topmost="True">
    <Window.DataContext>
        <vm:PanelSchedulePlacementViewModel/>
    </Window.DataContext>
    <StackPanel Margin="10">
        <Label Content="Sheet size:" />
        <ComboBox ItemsSource="{Binding SheetSizes}" SelectedItem="{Binding SelectedSheetSize}" Width="300"/>
        <Label Content="Levels for creating sheets:"/>
        <ListBox ItemsSource="{Binding Levels}" MinHeight="70" MaxHeight="400">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal">
                        <CheckBox IsChecked="{Binding IsChecked}"/>
                        <TextBlock Margin="5,0,0,0" Text="{Binding Name}"/>
                    </StackPanel>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
        <StackPanel Margin="0,10,0,0" Orientation="Horizontal">
            <Label Content="Revision number:" />
            <TextBox Width="100" Height="20" Text="{Binding Revision}"/>
        </StackPanel>
        <CheckBox Margin="0,10" Content="Delete unused sheets" IsChecked="{Binding DeleteUnusedSheets}"/>
        <Button Content="Place" Height="30" Command="{Binding PlaceCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
    </StackPanel>
</Window>
