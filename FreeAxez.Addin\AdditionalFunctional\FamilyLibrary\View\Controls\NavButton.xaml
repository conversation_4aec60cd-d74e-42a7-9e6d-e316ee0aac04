﻿<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls.NavButton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <RadioButton Style="{StaticResource NavButtonStyle}"
                     GroupName="{Binding GroupName, RelativeSource={RelativeSource AncestorType=UserControl}}"
                     Command="{Binding Command, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                     IsChecked="{Binding IsChecked, RelativeSource={RelativeSource AncestorType=UserControl}}"
                     Margin="2">
            <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="2">
                <ContentControl Template="{Binding IconTemplate, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                                Margin="0 0 0 2"
                                VerticalAlignment="Center"/>
                <TextBlock Text="{Binding ButtonText, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                           Margin="0 2 0 0"
                           Style="{StaticResource TextBase}" 
                           FontWeight="SemiBold" 
                           TextWrapping="Wrap"
                           TextAlignment="Center"
                           Foreground="White"/>
            </StackPanel>
        </RadioButton>
    </Grid>
</UserControl>
