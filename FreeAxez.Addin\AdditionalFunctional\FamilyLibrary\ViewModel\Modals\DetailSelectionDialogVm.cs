using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows.Data;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Dialog;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Details;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals
{
    public class DetailSelectionDialogVm : ModalDialogVm
    {
        private ObservableCollection<DetailViewModel> _detailViews = new();
        private bool _isBulkUpdating;
        private string _searchText = string.Empty;

        public DetailSelectionDialogVm(List<DetailViewModel> detailViews)
        {
            Title = "Select Detail Views to Import";

            CmdSelectAll = new RelayCommand(ExecuteSelectAll);
            CmdSelectNone = new RelayCommand(ExecuteSelectNone);
            CmdImport = new RelayCommand(ExecuteImport, CanExecuteImport);
            CmdShowPreview = new RelayCommand(ExecuteShowPreview);

            // Initialize detail views
            SetDetailViews(detailViews);
            FilterViews(); // Initialize filter
        }

        public ObservableCollection<DetailViewModel> DetailViews
        {
            get => _detailViews;
            set
            {
                if (_detailViews != value)
                {
                    _detailViews = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (_searchText != value)
                {
                    _searchText = value;
                    OnPropertyChanged();
                    FilterViews();
                }
            }
        }

        public ICommand CmdSelectAll { get; }
        public ICommand CmdSelectNone { get; }
        public ICommand CmdImport { get; }
        public ICommand CmdShowPreview { get; }

        public List<Autodesk.Revit.DB.ElementId> SelectedViewIds { get; private set; } = new();

        private void SetDetailViews(List<DetailViewModel> views)
        {
            DetailViews.Clear();
            foreach (var view in views)
            {
                DetailViews.Add(view);
            }
        }

        private void FilterViews()
        {
            var view = CollectionViewSource.GetDefaultView(DetailViews);
            view.Filter = item =>
            {
                var detailView = item as DetailViewModel;
                if (detailView == null) return false;

                if (!string.IsNullOrEmpty(SearchText))
                {
                    return detailView.Name.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0;
                }

                return true;
            };

            view.Refresh();
        }

        private void ExecuteSelectAll(object parameter)
        {
            _isBulkUpdating = true;
            var view = CollectionViewSource.GetDefaultView(DetailViews);
            foreach (DetailViewModel detailView in view)
            {
                if (detailView.IsEnabled)
                {
                    detailView.IsSelected = true;
                }
            }
            _isBulkUpdating = false;
            OnPropertyChanged(nameof(DetailViews));
        }

        private void ExecuteSelectNone(object parameter)
        {
            _isBulkUpdating = true;
            var view = CollectionViewSource.GetDefaultView(DetailViews);
            foreach (DetailViewModel detailView in view)
            {
                detailView.IsSelected = false;
            }
            _isBulkUpdating = false;
            OnPropertyChanged(nameof(DetailViews));
        }

        private void ExecuteImport(object parameter)
        {
            SelectedViewIds = DetailViews
                .Where(v => v.IsSelected)
                .Select(v => v.ElementId)
                .ToList();

            CloseModal(true);
        }

        private bool CanExecuteImport(object parameter)
        {
            return DetailViews.Any(v => v.IsSelected);
        }

        private void ExecuteShowPreview(object parameter)
        {
            if (parameter is DetailViewModel viewModel && viewModel?.PreviewImage != null)
            {
                LogHelper.Information($"Showing preview for: {viewModel.Name}");

                var dialogManager = new DialogManager();
                dialogManager.ShowDetailPreviewDialog(viewModel.PreviewImage, viewModel.Name);
            }
        }
    }
}
