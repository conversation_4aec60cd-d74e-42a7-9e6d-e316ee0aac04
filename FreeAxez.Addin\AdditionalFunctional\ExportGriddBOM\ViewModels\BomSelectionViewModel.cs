﻿using System.Windows;
using System.Windows.Input;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Enums;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Views;
using FreeAxez.Addin.Infrastructure;
using System.Windows.Interop;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.ViewModels
{
    public class BomSelectionViewModel : BaseViewModel
    {
        private SelectionOption _selectedOption = SelectionOption.EntireProject;

        public BomSelectionViewModel()
        {
            ConfirmCommand = new RelayCommand(ExecuteConfirm);
            CancelCommand = new RelayCommand(ExecuteCancel);
        }

        public SelectionOption SelectedOption
        {
            get => _selectedOption;
            set
            {
                if (_selectedOption != value)
                {
                    _selectedOption = value;
                    OnPropertyChanged();
                }
            }
        }

        public ICommand ConfirmCommand { get; }
        public ICommand CancelCommand { get; }

        private void ExecuteConfirm(object obj)
        {
            if (obj is not Window currentWindow) return;

            currentWindow.Close();

            Element selectedElement = null;

            switch (_selectedOption)
            {
                case SelectionOption.SelectedRoom:
                    selectedElement = SelectElement(currentWindow, new RoomSelectionFilter());
                    if (selectedElement != null)
                    {
                        OpenExportWindowForRoomOrRegion(currentWindow, selectedElement);
                    }
                    break;
                case SelectionOption.SelectedRegion:
                    selectedElement = SelectElement(currentWindow, new FilledRegionSelectionFilter());
                    if (selectedElement != null)
                    {
                        OpenExportWindowForRoomOrRegion(currentWindow, selectedElement);
                    }
                    break;
                case SelectionOption.EntireProject:
                default:
                    OpenExportWindowForEntireProject(currentWindow);
                    break;
            }
        }

        private void OpenExportWindowForEntireProject(Window currentWindow)
        {
            var exportView = new ExportGriddBomView();
            var exportViewModel = new ExportGriddBomViewModel();
            exportView.DataContext = exportViewModel;
            var handler = new WindowInteropHelper(exportView);
            handler.Owner = RevitManager.UIApplication.MainWindowHandle;
            exportView.ShowDialog();
        }

        private void OpenExportWindowForRoomOrRegion(Window currentWindow, Element selectedElement)
        {
            var exportView = new ExportGriddBomRoomRegionView();
            var exportViewModel = new ExportGriddBomRoomRegionViewModel(_selectedOption, selectedElement);
            exportView.DataContext = exportViewModel;
            var handler = new WindowInteropHelper(exportView);
            handler.Owner = RevitManager.UIApplication.MainWindowHandle;
            exportView.ShowDialog();
        }

        private Element SelectElement(Window currentWindow, ISelectionFilter filter)
        {
            currentWindow.Close();

            try
            {
                Reference reference = RevitManager.UIApplication.ActiveUIDocument.Selection.PickObject(ObjectType.Element, filter);
                return RevitManager.Document.GetElement(reference);
            }
            catch (Autodesk.Revit.Exceptions.OperationCanceledException)
            {
                return null;
            }
        }

        private void ExecuteCancel(object obj)
        {
            if (obj is Window window)
            {
                window.Close();
            }
        }
    }

    public class RoomSelectionFilter : ISelectionFilter
    {
        public bool AllowElement(Element elem) => elem is Room;
        public bool AllowReference(Reference reference, XYZ position) => false;
    }

    public class FilledRegionSelectionFilter : ISelectionFilter
    {
        public bool AllowElement(Element elem) => elem is FilledRegion;
        public bool AllowReference(Reference reference, XYZ position) => false;
    }
}