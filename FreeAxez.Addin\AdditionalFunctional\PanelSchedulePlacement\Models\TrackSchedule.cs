﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement.Models
{
    internal class TrackSchedule
    {
        private readonly List<TrackSchedulePart> _trackScheduleParts;


        /// <summary>
        /// Combines parts of the specification into one element for ease of placement.
        /// </summary>
        public TrackSchedule(List<TrackSchedulePart> trackScheduleParts) 
        {
            _trackScheduleParts = trackScheduleParts.OrderBy(p => p.Name).ToList();
        }


        public string TrackName => _trackScheduleParts.First().TrackName;
        public string Level => _trackScheduleParts.First().Level;
        public double Height => _trackScheduleParts.Sum(p => p.Height);
        public double Width => _trackScheduleParts.Max(p => p.Width);


        public static void CalculateDimensions(List<TrackSchedule> trackSchedules)
        {
            using (var t = new Transaction(RevitManager.Document, "Calculate Schedule Size"))
            {
                t.Start();

                var tempSheet = new FilteredElementCollector(RevitManager.Document)
                    .OfClass(typeof(ViewSheet))
                    .Cast<ViewSheet>()
                    .FirstOrDefault(s => s.SheetNumber == "TEMP TRACK SCHEDULES");
                if (tempSheet != null)
                {
                    RevitManager.Document.Delete(tempSheet.Id);
                }

                // Temporary sheet for placing specifications (will be rolled back)
                var sheet = ViewSheet.Create(RevitManager.Document, ElementId.InvalidElementId);
                sheet.SheetNumber = "TEMP TRACK SCHEDULES";
                sheet.Name = "Formation of specifications";

                // Placement of all specifications on one sheet
                var scheduleInstances = new List<ScheduleSheetInstance>();
                var schedules = trackSchedules.SelectMany(s => s._trackScheduleParts).ToList();
                foreach (var schedule in schedules)
                {
                    scheduleInstances.Add(schedule.Place(sheet.Id, new XYZ()));
                }

                // One-time generation of a sheet to obtain dimensions for schedules
                for (int i = 0; i < scheduleInstances.Count; i++)
                {
                    // At the first call, the sheet is regenerated
                    schedules[i].SetDimensionsFromInstance(scheduleInstances[i]);
                }

                t.RollBack();
            }
        }

        public void Place(ElementId viewSheetId, XYZ origin)
        {
            foreach (var trackSchedulePart in _trackScheduleParts)
            {
                trackSchedulePart.Place(viewSheetId, origin);
                origin = new XYZ(origin.X, origin.Y - trackSchedulePart.Height, origin.Z);
            }
        }
    }
}
