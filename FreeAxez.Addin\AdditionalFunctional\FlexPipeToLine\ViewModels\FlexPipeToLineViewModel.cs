﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.AdditionalFunctional.FlexPipeToLine.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.FlexPipeToLine.ViewModels
{
    public class FlexPipeToLineViewModel : BaseViewModel
    {
        private List<int> _specialLineStyles = new List<int>()
        {
            (int)BuiltInCategory.OST_AreaSchemeLines,
            (int)BuiltInCategory.OST_AxisOfRotation,
            (int)BuiltInCategory.OST_FabricAreaSketchEnvelopeLines,
            (int)BuiltInCategory.OST_FabricAreaSketchSheetsLines,
            (int)BuiltInCategory.OST_InsulationLines,
            (int)BuiltInCategory.OST_RoomSeparationLines,
            (int)BuiltInCategory.OST_SketchLines,
            (int)BuiltInCategory.OST_MEPSpaceSeparationLines
        };


        public FlexPipeToLineViewModel()
        {
            var linesCategory = RevitManager.Document.Settings.Categories.get_Item(BuiltInCategory.OST_Lines);

            var graphicStyles = new List<Element>();

            foreach (Category category in linesCategory.SubCategories)
            {
                if (_specialLineStyles.Contains(category.Id.GetIntegerValue()))
                {
                    continue;
                }

                graphicStyles.Add(category.GetGraphicsStyle(GraphicsStyleType.Projection));
            }


            LineStyles = graphicStyles.OrderBy(s => s.Name).ToList();

            var selectedLineStyle = LineStyles.FirstOrDefault(s => s.Name == Properties.Settings.Default.FlexPipeToLineSelectedLineStyleName);
            SelectedLineStyle = selectedLineStyle == null ? LineStyles.First() : selectedLineStyle;

            ConvertCommand = new RelayCommand(OnCenvertCommandExecute);
        }


        public List<Element> LineStyles { get; set; }

        public Element SelectedLineStyle { get; set; }

        public ICommand ConvertCommand { get; set; }
        private void OnCenvertCommandExecute(object p)
        {
            (p as Window).Close();

            SaveSettings();

            var flexPipes = PickFlexPipes();

            if (flexPipes.Count == 0)
            {
                InfoDialog.ShowDialog("Warning", "Flex pipes have not been selected.");

                return;
            }

            var converter = new FlexPipeToLineConverter(SelectedLineStyle, flexPipes);
            var countOfCreatedDetailLines = converter.Convert();

            InfoDialog.ShowDialog("Report", $"Created {countOfCreatedDetailLines} detail lines.");
        }


        private List<FlexPipe> PickFlexPipes()
        {
            var flexPipes = new List<FlexPipe>();

            try
            {
                var flexPipeReferences = RevitManager.UIDocument.Selection
                .PickObjects(Autodesk.Revit.UI.Selection.ObjectType.Element, new FlexPipeSelectionFilter(), "Select flex pipes to convert to lines.");

                flexPipes = flexPipeReferences.Select(r => RevitManager.Document.GetElement(r)).Cast<FlexPipe>().ToList();
            }
            catch (Autodesk.Revit.Exceptions.OperationCanceledException)
            {

            }

            return flexPipes;
        }

        private void SaveSettings()
        {
            Properties.Settings.Default.FlexPipeToLineSelectedLineStyleName = SelectedLineStyle.Name;
            Properties.Settings.Default.Save();
        }
    }
}
