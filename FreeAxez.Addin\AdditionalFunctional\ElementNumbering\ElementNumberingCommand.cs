﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.ElementNumbering.Views;
using FreeAxez.Addin.AdditionalFunctional.NumberingElements.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.ElementNumbering
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    public class ElementNumberingCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            if (!(RevitManager.UIDocument.ActiveView is ViewPlan))
            {
                InfoDialog.ShowDialog("Warning", "The plugin only works with plans.");
                return Result.Cancelled;
            }
            
            if (NoFloorElementsOnView())
            {
                InfoDialog.ShowDialog("Warning", "There are no valid elements on the current view.");
                return Result.Cancelled;
            }

            var settingWindow = new ElementNumberingView();
            settingWindow.ShowDialog();
            if (settingWindow.DialogResult != true)
            {
                return Result.Cancelled;
            }

            var selectElementsForNambering = SelectElementsOfCategory();
            if (selectElementsForNambering.Count == 0)
            {
                InfoDialog.ShowDialog("Elements Marking", "No elements selected.");
                return Result.Cancelled;
            }
            
            selectElementsForNambering = SortElements(selectElementsForNambering);

            MarkElements(selectElementsForNambering, out int lastNumber);
            SaveNewStartNumberToProperties(lastNumber);

            InfoDialog.ShowDialog("Report", $"The mark was set for {selectElementsForNambering.Count} elements.");            

            return Result.Succeeded;
        }

        private bool NoFloorElementsOnView()
        {
            var multicategoryFilter = new ElementMulticategoryFilter(new List<BuiltInCategory>()
                {
                    BuiltInCategory.OST_FlexPipeCurves,
                    BuiltInCategory.OST_ElectricalFixtures,
                    BuiltInCategory.OST_ElectricalEquipment
                });

            var floorElementsCount = new FilteredElementCollector(RevitManager.Document, RevitManager.UIDocument.ActiveView.Id)
                .WherePasses(multicategoryFilter)
                .WhereElementIsNotElementType()
                .Count();

            return floorElementsCount == 0;
        }

        private List<Element> SelectElementsOfCategory()
        {
            var elementsForNambering = new List<Element>();

            try
            {
                var referencesFilterFlexPipes = RevitManager.UIDocument.Selection.PickObjects(
                    Autodesk.Revit.UI.Selection.ObjectType.Element,
                    new SelectionFilter(),                    
                    "Select elements for marking.");

                elementsForNambering = referencesFilterFlexPipes.Select(r => RevitManager.Document.GetElement(r)).ToList();
            }
            catch { }

            return elementsForNambering;
        }

        private List<Element> SortElements(List<Element> elements)
        {

            if (Properties.Settings.Default.ElementNumberingHorizontalDirection)
            {
                elements = elements.OrderBy(e => GetMinXFromLocation(e)).ThenByDescending(e => GetMaxYFromLocation(e)).ToList();
            }
            else
            {
                elements = elements.OrderByDescending(e => GetMaxYFromLocation(e)).ThenBy(e => GetMinXFromLocation(e)).ToList();                
            }

            return elements;
        }

        private double GetMinXFromLocation(Element element)
        {
            var location = element.Location;
            if (location is LocationPoint locationPoint)
            {
                return locationPoint.Point.X;                
            }

            var locationCurve = location as LocationCurve;
            return locationCurve.Curve.Tessellate().Min(p => p.X);
        }
                
        private double GetMaxYFromLocation(Element element)
        {
            var location = element.Location;      
            if (location is LocationPoint locationPoint)
            {
                return locationPoint.Point.Y;
            }

            var locationCurve = location as LocationCurve; 
            return locationCurve.Curve.Tessellate().Max(p => p.Y);
        }

        private void MarkElements(List<Element> elements, out int lastNumber)
        {
            var startNumber = Properties.Settings.Default.ElementNumberingStartNumber;
            var prefix = Properties.Settings.Default.ElementNumberingPrefix;
            
            using (var t = new Transaction(RevitManager.Document, "Elements Marking"))

            {
                t.Start();

                foreach (var element in elements)
                {
                    var markValue = $"{prefix}{startNumber}";
                    element.LookupParameter(Properties.Settings.Default.ElementNumberingParameterName)?.Set(markValue);
                    startNumber++;
                }

                t.Commit();
            }

            lastNumber = startNumber;
        }

        private void SaveNewStartNumberToProperties(int startNumber)
        {
            Properties.Settings.Default.ElementNumberingStartNumber = startNumber;
            Properties.Settings.Default.Save();
        }
    }
}
