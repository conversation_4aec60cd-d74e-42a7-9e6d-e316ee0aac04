﻿<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.UserAccountControl.View.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             mc:Ignorable="d" 
             Width="500" Loaded="UserControl_Loaded">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
            <converters:ListToStringConverter x:Key="ListToStringConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid Background="White">
        <StackPanel Visibility="{Binding IsDataLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel Visibility="{Binding IsLoggedIn, Converter={StaticResource BooleanToVisibilityConverter}}">
                <TextBlock Text="Account Data" 
                           Style="{StaticResource TextLarge}" 
                           Margin=" 0 10 0 0"
                           FontWeight="SemiBold"/>
                <StackPanel Orientation="Horizontal" Margin="0 10 0 10">
                    <Viewbox Width="60" Height="60">
                        <Canvas Width="512" Height="512">
                            <Path Data="M399 384.2C376.9 345.8 335.4 320 288 320H224c-47.4 0-88.9 25.8-111 64.2c35.2 39.2 86.2 63.8 143 63.8s107.8-24.7 143-63.8zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zm256 16a72 72 0 1 0 0-144 72 72 0 1 0 0 144z"
                            Fill="{StaticResource Gray300}"/>
                        </Canvas>
                    </Viewbox>
                    <Grid Margin="10 0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="20"></RowDefinition>
                            <RowDefinition Height="20"></RowDefinition>
                            <RowDefinition Height="20"></RowDefinition>
                            <RowDefinition Height="20"></RowDefinition>
                        </Grid.RowDefinitions>
                        <TextBlock Grid.Row="0" 
                                   Text="{Binding FirstName, StringFormat='First Name: {0}'}" 
                                   Style="{StaticResource TextBase}" 
                                   FontWeight="SemiBold"
                                   />
                        <TextBlock Grid.Row="1" 
                                   Text="{Binding LastName, StringFormat='Last Name: {0}'}" 
                                   Style="{StaticResource TextBase}" 
                                   FontWeight="SemiBold"
                                   />
                        <TextBlock Grid.Row="2" 
                                   Text="{Binding Email, StringFormat='Email: {0}'}" 
                                   Style="{StaticResource TextBase}" 
                                   FontWeight="SemiBold"
                                   />
                        <StackPanel Grid.Row="3" Orientation="Horizontal">
                            <TextBlock Text="Roles: " 
                                       Style="{StaticResource TextBase}" 
                                       />
                            <TextBlock Text="{Binding Roles, Converter={StaticResource ListToStringConverter}}" 
                                       Style="{StaticResource TextBase}"
                                       FontWeight="SemiBold"
                                       />
                        </StackPanel>

                    </Grid>
                </StackPanel>
                <Button Content="Log Out" 
                        Command="{Binding LogoutCommand}" 
                        Margin="0 10 0 0"
                        Style="{StaticResource ButtonOutlinedRed}" 
                        HorizontalAlignment="Right"/>
            </StackPanel>
            <StackPanel Visibility="{Binding IsLoggedIn, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                <TextBlock Style="{StaticResource TextBase}" 
                           Text="Email" 
                           Margin="0 10 0 5">
                </TextBlock>
                <TextBox Style="{StaticResource UiTextBox}" 
                         Tag="Enter Email" 
                         Name="EmailTextBox" 
                         VerticalContentAlignment="Center"
                         Margin="0,0,0,10" 
                         Text="{Binding UserEmail, UpdateSourceTrigger=PropertyChanged}"
                         />
                <TextBlock Style="{StaticResource TextBase}" Text="Password" 
                           Margin="0 10 0 5">
                </TextBlock>
                <Grid>
                    <PasswordBox Style="{StaticResource PasswordBox}"
                                 x:Name="PasswordBox"
                                 PasswordChanged="PasswordBox_PasswordChanged"
                                 />
                    <TextBlock x:Name="PlaceholderText"
                               Text="Enter password"
                               Margin="9,0,0,0"
                               Foreground="{StaticResource Gray600}"
                               Panel.ZIndex="2"
                               IsHitTestVisible="False"
                               Visibility="Visible" />
                </Grid>
                <TextBlock Text="{Binding Error}" 
                           Foreground="Red" 
                           Visibility="{Binding Error, Converter={StaticResource StringToVisibilityConverter}}"/>
                <ProgressBar Height="8" 
                             Visibility="{Binding IsLoggingIn, Converter={StaticResource BooleanToVisibilityConverter}}" 
                             IsIndeterminate="True"
                             Foreground="{StaticResource Blue500}" 
                             Margin="0 20"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0 15">
                    <Button Style="{StaticResource ButtonOutlinedGreen}" 
                            Margin="0 0 20 0"
                            Content="Forgot Password"
                            Command="{Binding ShowResetPasswordCommand}"/>
                    <Button Content="Log In" Command="{Binding LoginCommand}" IsEnabled="{Binding CanApply}"
                            Style="{StaticResource ButtonSimpleBlue}" />
                </StackPanel>
            </StackPanel>
            <StackPanel Orientation="Horizontal" Margin="0 10 0 0">
                <TextBlock Text="Current Version: "
                           Style="{StaticResource TextBase}"
                           FontWeight="SemiBold"/>
                <TextBlock Text="{Binding AssemblyVersion}"
                           FontWeight="SemiBold"
                           Style="{StaticResource TextBase}"/>
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
