﻿using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using SixLabors.ImageSharp;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBOM.Spreadsheets
{
    public class ExcelSheet
    {
        private readonly ISheet _sheet;
        private IFormulaEvaluator _formulaEvaluator;


        public ExcelSheet(ISheet sheet)
        {
            _sheet = sheet;
            _formulaEvaluator = _sheet.Workbook.GetCreationHelper().CreateFormulaEvaluator();
        }


        public string Name => _sheet.SheetName;


        public void UpdateSheetName(string newName)
        {
            var sheetId = _sheet.Workbook.GetSheetIndex(_sheet);
            _sheet.Workbook.SetSheetName(sheetId, newName);
        }

        public void ClearRows(int startRowForCleaning)
        {
            var rowIterator = _sheet.GetRowEnumerator();
            while (rowIterator.MoveNext())
            {
                var row = (IRow)rowIterator.Current;
                if (row.RowNum < startRowForCleaning) continue;
                foreach (ICell cell in row.Cells)
                {
                    cell.SetBlank();
                    cell.CellStyle = _sheet.Workbook.CreateCellStyle();
                }
            }
        }

        public void SetCellValue(int row, int column, string value)
        {
            var cell = GetCell(row, column);
            if (int.TryParse(value, out int intResult))
            {
                cell.SetCellType(CellType.Numeric);
                cell.SetCellValue(intResult);
            }
            else if (double.TryParse(value, out double doubleResult))
            {
                cell.SetCellType(CellType.Numeric);
                cell.SetCellValue(doubleResult);
            }
            else
            {
                cell.SetCellValue(value);
            }
        }

        public void SetFormula(int row, int column, string formula)
        {
            var cell = GetCell(row, column);
            cell.SetCellType(CellType.Formula);
            cell.SetCellFormula(formula);
            try
            {
                _formulaEvaluator.EvaluateFormulaCell(cell);
            }
            catch { }
        }

        public void SetMoneyFormat(int row, int column)
        {
            var cell = GetCell(row, column);
            var style = GetClonedCellStyle(cell);
            string format = "$#,##0.00";
            short dataFormat = _sheet.Workbook.CreateDataFormat().GetFormat(format);
            style.DataFormat = dataFormat;
            cell.CellStyle = style;
        }

        public void SetLeftAlignment(int row, int column)
        {
            var cell = GetCell(row, column);
            var style = GetClonedCellStyle(cell);
            style.Alignment = HorizontalAlignment.Left;
            cell.CellStyle = style;
        }

        public void SetCenterAlignment(int row, int column)
        {
            var cell = GetCell(row, column);
            var style = GetClonedCellStyle(cell);
            style.Alignment = HorizontalAlignment.Center;
            cell.CellStyle = style;
        }

        public void SetRightAlignment(int row, int column)
        {
            var cell = GetCell(row, column);
            var style = GetClonedCellStyle(cell);
            style.Alignment = HorizontalAlignment.Right;
            cell.CellStyle = style;
        }

        public void AddBorders(int row, int column)
        {
            var cell = GetCell(row, column);
            var style = GetClonedCellStyle(cell);
            style.BorderTop = BorderStyle.Thin;
            style.BorderBottom = BorderStyle.Thin;
            style.BorderLeft = BorderStyle.Thin;
            style.BorderRight = BorderStyle.Thin;
            cell.CellStyle = style;
        }

        public void ShadeForeground(int row, int column)
        {
            var cell = GetCell(row, column);
            var style = GetClonedCellStyle(cell);
            style.FillPattern = FillPattern.SolidForeground;
            // https://stackoverflow.com/questions/31159724/npoi-setting-background-color-isnt-working
            style.SetFillForegroundColor(new XSSFColor(Color.LightGray));
            cell.CellStyle = style;
        }

        public void SetFontBold(int row, int column)
        {
            var cell = GetCell(row, column);
            var style = GetClonedCellStyle(cell);
            XSSFFont font = (XSSFFont)_sheet.Workbook.CreateFont();
            font.IsBold = true;
            style.SetFont(font);
            cell.CellStyle = style;
        }

        private ICell GetCell(int row, int column)
        {
            var sheetRow = _sheet.GetRow(row);
            if (sheetRow == null) sheetRow = _sheet.CreateRow(row);
            var cell = sheetRow.GetCell(column);
            if (cell == null) cell = sheetRow.CreateCell(column);
            return cell;
        }

        private XSSFCellStyle GetClonedCellStyle(ICell cell)
        {
            var style = (XSSFCellStyle)_sheet.Workbook.CreateCellStyle();
            style.CloneStyleFrom(cell.CellStyle);
            return style;
        }
    }
}
