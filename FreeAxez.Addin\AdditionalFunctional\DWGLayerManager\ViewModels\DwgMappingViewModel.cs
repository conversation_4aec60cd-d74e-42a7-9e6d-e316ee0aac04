using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Windows.Data;
using System.Windows.Forms;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using OpenFileDialog = Microsoft.Win32.OpenFileDialog;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels;

public class DwgMappingViewModel : WindowViewModel
{
    private readonly DwgFileOperationService _dwgFileOperations;
    private readonly LayerMappingOperations _layerMappingOperations;
    private readonly DwgLayerManagerApiService _apiService;
    private bool _canExecuteMapping;
    private ObservableCollection<DwgLayerInfo> _dwgLayers;
    private ObservableCollection<LayerModel> _freeAxezLayers;
    private bool _isProcessing;
    private List<LinetypeModel> _linetypes;
    private string _pendingSuccessMessage;
    private string _progressText;
    private string _progressTitle;
    private string _searchText;
    private string _selectedDwgFilePath;

    public DwgMappingViewModel(DwgLayerManagerApiService apiService)
    {
        var dwgLayerService = new DwgLayerService(new AutoCADInvoker());
        _dwgFileOperations = new DwgFileOperationService(dwgLayerService);
        _layerMappingOperations = new LayerMappingOperations(dwgLayerService, apiService);
        _apiService = apiService;

        DwgLayers = new ObservableCollection<DwgLayerInfo>();
        FreeAxezLayers = new ObservableCollection<LayerModel>();
        _linetypes = new List<LinetypeModel>();

        // Initialize filtered collection
        FilteredDwgLayers = CollectionViewSource.GetDefaultView(DwgLayers);
        FilteredDwgLayers.Filter = layer => ((DwgLayerInfo)layer).IsVisible;

        SelectDwgFileCommand = new RelayCommand(_ => SelectDwgFile(), _ => !IsProcessing);
        ExecuteMappingCommand = new RelayCommand(_ => ExecuteMapping(), _ => CanExecuteMapping);
        MarkForDeletionCommand = new RelayCommand(param => MarkForDeletion(param as DwgLayerInfo));

        LoadFreeAxezLayersAsync();
    }

    public ObservableCollection<DwgLayerInfo> DwgLayers
    {
        get => _dwgLayers;
        set => Set(ref _dwgLayers, value);
    }

    public ICollectionView FilteredDwgLayers { get; }

    public ObservableCollection<LayerModel> FreeAxezLayers
    {
        get => _freeAxezLayers;
        set => Set(ref _freeAxezLayers, value);
    }

    public List<LinetypeModel> FreeAxezLinetypes => _linetypes;

    public string SelectedDwgFilePath
    {
        get => _selectedDwgFilePath;
        set
        {
            if (Set(ref _selectedDwgFilePath, value)) LoadDwgLayersAsync();
        }
    }

    public string SearchText
    {
        get => _searchText;
        set
        {
            if (Set(ref _searchText, value)) FilterDwgLayers();
        }
    }


    public bool CanExecuteMapping
    {
        get => !IsProcessing && DwgLayers?.Any(l => l.MappedLayerId.HasValue) == true;
        set
        {
            if (Set(ref _canExecuteMapping, value)) ((RelayCommand)ExecuteMappingCommand).RaiseCanExecuteChanged();
        }
    }


    public bool IsProcessing
    {
        get => _isProcessing;
        set
        {
            if (Set(ref _isProcessing, value))
            {
                OnPropertyChanged(nameof(CanExecuteMapping));
                ((RelayCommand)ExecuteMappingCommand).RaiseCanExecuteChanged();
                ((RelayCommand)SelectDwgFileCommand).RaiseCanExecuteChanged();
            }
        }
    }


    public string ProgressText
    {
        get => _progressText;
        set => Set(ref _progressText, value);
    }

    public string ProgressTitle
    {
        get => _progressTitle;
        set => Set(ref _progressTitle, value);
    }

    public ICommand SelectDwgFileCommand { get; }
    public ICommand ExecuteMappingCommand { get; }
    public ICommand MarkForDeletionCommand { get; }

    public void SetPendingSuccessMessage(string message)
    {
        _pendingSuccessMessage = message;
    }

    public void RefreshFreeAxezLayers()
    {
        LoadFreeAxezLayersAsync();
    }

    public void RefreshFreeAxezLayersPreservingMappings()
    {
        // Save current mappings
        var currentMappings = DwgLayers?.ToDictionary(
            layer => layer.Name,
            layer => layer.MappedLayerId
        ) ?? new Dictionary<string, Guid?>();

        // Reload FreeAxez layers
        LoadFreeAxezLayersAsync();

        // Restore mappings after a short delay to allow UI to update
        System.Windows.Threading.Dispatcher.CurrentDispatcher.BeginInvoke(
            System.Windows.Threading.DispatcherPriority.Background,
            new Action(() =>
            {
                if (DwgLayers != null)
                {
                    foreach (var layer in DwgLayers)
                    {
                        if (currentMappings.TryGetValue(layer.Name, out var mappedId))
                        {
                            layer.MappedLayerId = mappedId;
                        }
                    }
                }
            })
        );
    }

    private async void LoadFreeAxezLayersAsync()
    {
        try
        {
            var layers = await _apiService.GetAllLayersAsync();

            FreeAxezLayers.Clear();

            // Add empty option for clearing selection
            FreeAxezLayers.Add(new LayerModel
            {
                Id = Guid.Empty,
                Name = ""
            });

            foreach (var layer in layers) FreeAxezLayers.Add(layer);

            // Notify UI that the collection has changed
            OnPropertyChanged(nameof(FreeAxezLayers));

            // Load linetypes for pattern information
            _linetypes = await _apiService.GetAllLinetypesAsync();
        }
        catch (Exception)
        {
            // ignored
        }
    }


    private void SelectDwgFile()
    {
        var selectedFile = _dwgFileOperations.SelectDwgFile();
        if (!string.IsNullOrEmpty(selectedFile))
        {
            // Clear search text when opening file
            SearchText = string.Empty;

            // Always refresh the file, even if it's the same path
            // This handles cases where the file was previously failed to load
            var previousPath = SelectedDwgFilePath;
            SelectedDwgFilePath = selectedFile;

            // If it's the same file, force reload
            if (string.Equals(previousPath, selectedFile, StringComparison.OrdinalIgnoreCase))
            {
                LoadDwgLayersAsync();
            }
        }
    }

    public async void LoadDwgLayersAsync()
    {
        if (string.IsNullOrEmpty(SelectedDwgFilePath) || !File.Exists(SelectedDwgFilePath))
        {
            DwgLayers.Clear();
            CanExecuteMapping = false;
            return;
        }

        // Check if file is available for reading before starting
        if (!_dwgFileOperations.IsFileAvailableForWriting(SelectedDwgFilePath))
        {
            MessageWindow.ShowDialog("File In Use",
                "The DWG file is currently open in AutoCAD or another application.\n\n" +
                "Please close the file and try again.",
                MessageType.Warning);
            DwgLayers.Clear();
            CanExecuteMapping = false;
            return;
        }

        IsProcessing = true;
        ProgressTitle = "Loading DWG Layers";
        ProgressText = "Extracting layer information from DWG file...";

        bool loadSuccessful = false;
        List<DwgLayerInfo> layers = null;

        try
        {
            var progress = new Progress<string>(message => ProgressText = message);
            layers = await _dwgFileOperations.LoadDwgLayersAsync(SelectedDwgFilePath, progress);

            DwgLayers.Clear();
            foreach (var layer in layers)
            {
                // Subscribe to property changes to update CanExecuteMapping
                layer.PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(layer.MappedLayerId))
                    {
                        OnPropertyChanged(nameof(CanExecuteMapping));
                        ((RelayCommand)ExecuteMappingCommand).RaiseCanExecuteChanged();
                    }
                };
                DwgLayers.Add(layer);
            }

            ProgressText = "Completed successfully";
            CanExecuteMapping = DwgLayers.Any();
            loadSuccessful = true;

            // Notify that DwgLayers collection has changed for CanPurgeUnused
            OnPropertyChanged(nameof(DwgLayers));

            // Show pending success message if any
            if (!string.IsNullOrEmpty(_pendingSuccessMessage))
            {
                MessageWindow.ShowDialog("Success", _pendingSuccessMessage, MessageType.Success);
                _pendingSuccessMessage = null;
            }

            Application.DoEvents();
        }
        catch (Exception)
        {
            // Error handling is done in DwgFileOperations
            CanExecuteMapping = false;
            loadSuccessful = false;
        }

        finally
        {
            // Always reset processing state when loading completes
            IsProcessing = false;
            ProgressText = "";
            ProgressTitle = "";
        }

        // Only show success messages if loading was successful
        if (loadSuccessful)
        {
            // Show pending success message if available (from merge operation)
            if (!string.IsNullOrEmpty(_pendingSuccessMessage))
            {
                var message = _pendingSuccessMessage;
                _pendingSuccessMessage = null; // Clear the message

                // Delay to ensure UI updates are complete
                await Task.Delay(100);
                MessageWindow.ShowDialog("Success", message, MessageType.Success);
            }
            else
            {
                MessageWindow.ShowDialog("Success", $"Loaded {layers.Count} layers from DWG file", MessageType.Success);
            }
        }
    }

    private async void ExecuteMapping()
    {
        try
        {
            // Check if file is available for writing before starting
            if (!_dwgFileOperations.IsFileAvailableForWriting(SelectedDwgFilePath))
            {
                MessageWindow.ShowDialog("File In Use",
                    "The DWG file is currently open in AutoCAD or another application.\n\n" +
                    "Please close the file and try again.",
                    MessageType.Warning);
                return;
            }

            IsProcessing = true;
            ProgressTitle = "Merging Layers";

            var progress = new Progress<string>(message => ProgressText = message);
            var success = await _layerMappingOperations.ExecuteMappingAsync(
                SelectedDwgFilePath, DwgLayers, FreeAxezLayers, _linetypes, progress);

            if (success)
            {
                // Store success message to show after reload
                _pendingSuccessMessage = $"Successfully merged {DwgLayers.Count(l => l.MappedLayerId.HasValue)} layers";
                LoadDwgLayersAsync(); // Reload to see changes
            }
            else
            {
                IsProcessing = false;
            }
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog("Error", $"Error during layer merging: {ex.Message}", MessageType.Error);
            IsProcessing = false;
            ProgressText = "";
            ProgressTitle = "";
        }
    }



    private void MarkForDeletion(DwgLayerInfo layer)
    {
        if (layer == null) return;

        layer.IsMarkedForDeletion = !layer.IsMarkedForDeletion;

        // Clear mapping if marked for deletion
        if (layer.IsMarkedForDeletion) layer.MappedLayerId = null;

        OnPropertyChanged(nameof(DwgLayers));
    }



    private void FilterDwgLayers()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            // Show all layers
            foreach (var layer in DwgLayers) layer.IsVisible = true;
        }
        else
        {
            // Filter by name
            var searchLower = SearchText.ToLower();
            foreach (var layer in DwgLayers) layer.IsVisible = layer.Name.ToLower().Contains(searchLower);
        }

        // Refresh the filtered view
        FilteredDwgLayers?.Refresh();
    }
}