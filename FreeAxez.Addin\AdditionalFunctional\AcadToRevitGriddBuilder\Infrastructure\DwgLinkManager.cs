using System;
using System.IO;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Infrastructure
{
    /// <summary>
    /// Manages DWG link selection and validation
    /// </summary>
    public class DwgLinkManager
    {
        public DwgLinkInfo SelectDwgLink(UIDocument uidoc)
        {
            try
            {
                var selection = uidoc.Selection;
                var reference = selection.PickObject(ObjectType.Element, new DwgLinkSelectionFilter(),
                    "Select a DWG link to process");

                var element = uidoc.Document.GetElement(reference);
                if (element is ImportInstance importInstance)
                {
                    return CreateDwgLinkInfo(uidoc.Document, importInstance);
                }

                throw new InvalidOperationException("Selected element is not a DWG link");
            }
            catch (Autodesk.Revit.Exceptions.OperationCanceledException)
            {
                return null; // User cancelled selection
            }
        }

        private DwgLinkInfo CreateDwgLinkInfo(Document document, ImportInstance importInstance)
        {
            var linkInfo = new DwgLinkInfo
            {
                ImportInstance = importInstance,
                ElementId = importInstance.Id
            };

            // Get level
            var levelId = importInstance.LevelId;
            if (levelId != ElementId.InvalidElementId)
            {
                linkInfo.Level = document.GetElement(levelId) as Level;
            }

            // Get DWG file path
            var cadLinkType = document.GetElement(importInstance.GetTypeId()) as CADLinkType;
            if (cadLinkType != null)
            {
                linkInfo.DwgFilePath = GetDwgFilePath(cadLinkType);
                linkInfo.LinkName = cadLinkType.Name;
            }

            // Get transformation
            linkInfo.Transform = importInstance.GetTransform();

            return linkInfo;
        }

        private string GetDwgFilePath(CADLinkType cadLinkType)
        {
            try
            {
                var externalFileRef = cadLinkType.GetExternalFileReference();
                if (externalFileRef == null) return null;

                // Get the ModelPath and convert to user-visible path
                var modelPath = externalFileRef.GetAbsolutePath();
                if (modelPath != null)
                {
                    return ModelPathUtils.ConvertModelPathToUserVisiblePath(modelPath);
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        public bool ValidateDwgFileAccess(DwgLinkInfo linkInfo)
        {
            if (linkInfo == null || string.IsNullOrEmpty(linkInfo.DwgFilePath))
                return false;

            return File.Exists(linkInfo.DwgFilePath);
        }

        public void ShowUpdateLinkMessage(DwgLinkInfo linkInfo)
        {
            var message = $"DWG file is not accessible at the expected location:\n\n" +
                         $"Link: {linkInfo.LinkName}\n" +
                         $"Expected path: {linkInfo.DwgFilePath ?? "Unknown"}\n\n" +
                         $"Please update the link location in Revit and try again.";

            TaskDialog.Show("DWG Link Not Found", message, TaskDialogCommonButtons.Ok);
        }
    }

    public class DwgLinkSelectionFilter : ISelectionFilter
    {
        public bool AllowElement(Element elem)
        {
            return elem is ImportInstance importInstance &&
                   importInstance.IsLinked &&
                   IsDwgLink(importInstance);
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            return false;
        }

        private bool IsDwgLink(ImportInstance importInstance)
        {
            try
            {
                var doc = importInstance.Document;
                var linkType = doc.GetElement(importInstance.GetTypeId()) as CADLinkType;

                if (linkType?.GetExternalFileReference() != null)
                {
                    var modelPath = linkType.GetExternalFileReference().GetAbsolutePath();
                    if (modelPath != null)
                    {
                        var pathString = ModelPathUtils.ConvertModelPathToUserVisiblePath(modelPath);
                        if (!string.IsNullOrEmpty(pathString))
                        {
                            var extension = Path.GetExtension(pathString).ToLowerInvariant();
                            return extension == ".dwg";
                        }
                    }
                }
            }
            catch
            {
                // If we can't get the path, assume it's not a DWG
            }

            return false;
        }
    }
}
