﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.Services
{
    public static class GeometryHelper
    {
        public static Outline GetSolidsOutline(FamilyInstance instance)
        {
            var solids = GetAllSolids(instance);

            if (solids.Count == 0)
            {
                LogHelper.Warning($"There is no solid in family instance {instance.Id.GetIntegerValue()}");
                return new Outline(new XYZ(), new XYZ(1, 1, 1));
            }

            var points = new List<XYZ>();
            foreach (var solid in solids)
            {
                var bb = solid.GetBoundingBox();
                var centroid = solid.ComputeCentroid();
                points.Add(bb.Min.Add(solid.ComputeCentroid()));
                points.Add(bb.Max.Add(solid.ComputeCentroid()));
            }

            var min = new XYZ(points.Min(p => p.X), points.Min(p => p.Y), points.Min(p => p.Z));
            var max = new XYZ(points.Max(p => p.X), points.Max(p => p.Y), points.Max(p => p.Z));

            return new Outline(min, max);
        }

        public static List<Solid> GetAllSolids(FamilyInstance instance)
        {
            var solids = new List<Solid>();

            foreach (var geomObject in instance.get_Geometry(new Options()))
            {
                if (geomObject is Solid)
                {
                    solids.Add(geomObject as Solid);
                }

                if (geomObject is GeometryInstance geometryInstance)
                {
                    foreach (var instanceGeomObject in geometryInstance.GetInstanceGeometry())
                    {
                        if (instanceGeomObject is Solid)
                        {
                            solids.Add(instanceGeomObject as Solid);
                        }
                    }
                }
            }

            var subComponetIds = instance.GetSubComponentIds();
            if (subComponetIds.Count > 0)
            {
                foreach (var id in subComponetIds)
                {
                    var subInstance = RevitManager.Document.GetElement(id) as FamilyInstance;
                    solids.AddRange(GetAllSolids(subInstance));
                }
            }

            return solids.Where(s => s.Volume > 0).ToList();
        }

        public static Solid GetVoidSolid(Element element)
        {
            return element
                .get_Geometry(new Options() { IncludeNonVisibleObjects = true })
                .Where(x => x is Solid)
                .Cast<Solid>()
                .First(x => x.Volume < 0);
        }

        public static Solid GetSolid(Element element)
        {
            return element
                .get_Geometry(new Options() { IncludeNonVisibleObjects = true })
                .Where(x => x is Solid)
                .Cast<Solid>()
                .First(x => x.Volume > 0);
        }

        public static PlanarFace GetFaceWithNormal(Solid solid, XYZ normal)
        {
            foreach (Face face in solid.Faces)
            {
                if (face is PlanarFace planarFace)
                {
                    XYZ faceNormal = planarFace.FaceNormal;
                    if (faceNormal.IsAlmostEqualTo(normal))
                    {
                        return planarFace;
                    }
                }
            }
            return null;
        }

        public static IList<CurveLoop> GetOffsetCurveLoopsFromFace(PlanarFace face, double offsetDistance)
        {
            IList<CurveLoop> faceLoops = face.GetEdgesAsCurveLoops();
            List<CurveLoop> offsetLoops = new List<CurveLoop>();

            foreach (CurveLoop loop in faceLoops)
            {
                try
                {
                    CurveLoop offsetLoop = CurveLoop.CreateViaOffset(loop, -offsetDistance, face.FaceNormal);
                    offsetLoops.Add(offsetLoop);
                }
                catch
                {

                }
            }

            return offsetLoops;
        }

        public static Solid CreateExtrusionGeometry(IList<CurveLoop> loops, XYZ extrusionDirection, double height)
        {
            try
            {
                return GeometryCreationUtilities.CreateExtrusionGeometry(loops, extrusionDirection, height);
            }
            catch
            {
                return null;
            }
        }
    }
}
