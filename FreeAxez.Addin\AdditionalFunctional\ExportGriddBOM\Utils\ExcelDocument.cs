﻿using NPOI.XSSF.UserModel;
using System.IO;
using FreeAxez.Addin.Infrastructure;
using Microsoft.Win32;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using NPOI.SS.UserModel;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Utils
{
    internal class ExcelDocument
    {
        private XSSFWorkbook _workbook;
        private string _filePath;


        private ExcelDocument(string filePath)
        {
            using (FileStream file = new FileStream(filePath, FileMode.Open, FileAccess.ReadWrite, FileShare.ReadWrite))
            {
                _workbook = new XSSFWorkbook(file);
                _filePath = filePath;
            }
        }
         

        public static ExcelDocument Open(string filePath)
        {
            return new ExcelDocument(filePath);
        }

        public void Save()
        {
            string tempFilePath = Path.GetTempFileName();  
            using (MemoryStream ms = new MemoryStream())
            {
                _workbook.Write(ms);
                ms.Position = 0; 

                using (FileStream fs = new FileStream(tempFilePath, FileMode.Create, FileAccess.Write))
                {
                    ms.CopyTo(fs);  
                }
            }

            bool fileDeleted = FileOperationHelper.TryDeleteFileWithRetries(_filePath, 5, 200);
            if (fileDeleted)
            {
                File.Move(tempFilePath, _filePath); 
            }
            else
            {
                bool? result = MessageWindow.ShowDialog(
                    "The original file could not be overwritten due to access restrictions or active synchronization.",
                    "Would you like to save the file under a different name in the same folder?",
                    MessageType.Warning
                );

                if (result == true) 
                {
                    string newPath = ShowSaveAsDialog();
                    if (!string.IsNullOrEmpty(newPath))
                    {
                        File.Move(tempFilePath, newPath);
                        MessageWindow.Notification("File Saved", $"The file was successfully saved as: '{newPath}'", MessageType.Success);
                    }
                    else
                    {
                        MessageWindow.Notification("Save Cancelled", "File save operation was cancelled by the user.", MessageType.Info);
                    }
                }
                else
                {
                    File.Delete(tempFilePath); 
                    MessageWindow.Notification("Save Not Performed", "The file save operation was not performed.", MessageType.Error);
                }
            }
        }

        private string ShowSaveAsDialog()
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.InitialDirectory = Path.GetDirectoryName(_filePath); 
            saveFileDialog.Filter = "Excel files (*.xlsx)|*.xlsx|All files (*.*)|*.*";
            if (saveFileDialog.ShowDialog() == true)
            {
                return saveFileDialog.FileName;
            }
            return null;
        }

        public List<string> GetSheetNames()
        {
            var sheetNames = new List<string>();
            for (int i = 0; i < _workbook.NumberOfSheets; i++)
            {
                sheetNames.Add(_workbook.GetSheetName(i));
            }
            return sheetNames;
        }

        public void CopySheet(string sourceSheetName, string newSheetName)
        {
            // TODO: Use the library method after unifying work with Excel.
            // Custom implementation of the copy method is necessary
            // due to a conflict between the NPOI and EPPlus libraries.

            // Get source sheet
            var sourceSheet = _workbook.GetSheet(sourceSheetName);
            if (sourceSheet == null)
                throw new ArgumentException($"Sheet '{sourceSheetName}' not found.");

            // Copy sheet
            var newSheet = _workbook.CreateSheet(newSheetName);

            // Copy sheet settings
            newSheet.PrintSetup.Landscape = sourceSheet.PrintSetup.Landscape;
            newSheet.PrintSetup.PaperSize = sourceSheet.PrintSetup.PaperSize;
            newSheet.PrintSetup.Scale = sourceSheet.PrintSetup.Scale;

            // Copy the height and weight for the row and column
            for (int i = 0; i <= sourceSheet.LastRowNum; i++)
            {
                var sourceRow = sourceSheet.GetRow(i);
                if (sourceRow == null) continue;

                var newRow = newSheet.CreateRow(i);
                newRow.Height = sourceRow.Height;

                for (int j = 0; j < sourceRow.LastCellNum; j++)
                {
                    var sourceCell = sourceRow.GetCell(j);
                    if (sourceCell == null) continue;

                    var newCell = newRow.CreateCell(j);
                    newSheet.SetColumnWidth(j, sourceSheet.GetColumnWidth(j));

                    // Copy style and value
                    newCell.CellStyle = sourceCell.CellStyle;

                    switch (sourceCell.CellType)
                    {
                        case NPOI.SS.UserModel.CellType.String:
                            newCell.SetCellValue(sourceCell.StringCellValue);
                            break;
                        case NPOI.SS.UserModel.CellType.Numeric:
                            newCell.SetCellValue(sourceCell.NumericCellValue);
                            break;
                        case NPOI.SS.UserModel.CellType.Boolean:
                            newCell.SetCellValue(sourceCell.BooleanCellValue);
                            break;
                        case NPOI.SS.UserModel.CellType.Formula:
                            newCell.SetCellFormula(sourceCell.CellFormula);
                            break;
                            // Add another types
                    }
                }
            }

            // Copy merged cells
            foreach (var region in sourceSheet.MergedRegions)
            {
                newSheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(
                    region.FirstRow, region.LastRow, region.FirstColumn, region.LastColumn));
            }
        }

        public void UpdateCells(string sheetName, int startRowIndex, int startColumnIndex, List<List<string>> data)
        {
            var sheet = _workbook.GetSheet(sheetName);
            for (int dataRowIndex = 0; dataRowIndex < data.Count; dataRowIndex++)
            {
                for (int dataColumnIndex = 0; dataColumnIndex < data[dataRowIndex].Count; dataColumnIndex++)
                {
                    var row = sheet.GetRow(dataRowIndex + startRowIndex);
                    if (row == null) row = sheet.CreateRow(dataRowIndex + startRowIndex);
                    var cell = row.GetCell(dataColumnIndex + startColumnIndex);
                    if (cell == null) cell = row.CreateCell(dataColumnIndex + startColumnIndex);

                    var value = data[dataRowIndex][dataColumnIndex];
                    if (int.TryParse(value, out int number))
                    {
                        cell.SetCellValue(number);
                    }
                    else
                    {
                        cell.SetCellValue(value);
                    }
                }
            }
        }

        public void UpdateCell(string sheetName, int rowIndex, int columnIndex, string data)
        {
            var sheet = _workbook.GetSheet(sheetName);
            var row = sheet.GetRow(rowIndex);
            if (row == null) row = sheet.CreateRow(rowIndex);
            var cell = row.GetCell(columnIndex);
            if (cell == null) cell = row.CreateCell(columnIndex);
            cell.SetCellValue(data);
        }

        public void CleanRow(string sheetName, int rowIndex)
        {
            var sheet = _workbook.GetSheet(sheetName);
            var row = sheet.GetRow(rowIndex);
            if (row == null)
            {
                return;
            }

            foreach (var cell in row)
            {
                cell.SetCellValue(string.Empty);
            }
        }

        public void ActivateSheet(string sheetName)
        {
            _workbook.SetSelectedTab(_workbook.GetSheetIndex(sheetName));
            _workbook.SetActiveSheet(_workbook.GetSheetIndex(sheetName));
        }

        public int GetNumberOfRows(string sheetName)
        {
            var sheet = _workbook.GetSheet(sheetName);
            return sheet.PhysicalNumberOfRows;
        }

        public void ForceFormulaRecalculation(string sheetName)
        {
            var sheet = _workbook.GetSheet(sheetName);
            sheet.ForceFormulaRecalculation = true;
        }

        public static ExcelDocument CreateNew(string filePath, string sheetName)
        {
            var workbook = new XSSFWorkbook();
            workbook.CreateSheet(sheetName);
            using (var file = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(file);
            }
            return new ExcelDocument(filePath);
        }

        public void UpdateStyledCell(string sheetName, int row, int col, string value, HorizontalAlignment alignment, bool isBold = false, short fillColor = -1)
        {
            var sheet = _workbook.GetSheet(sheetName);
            var currentRow = sheet.GetRow(row) ?? sheet.CreateRow(row);
            var cell = currentRow.GetCell(col) ?? currentRow.CreateCell(col);

            cell.SetCellValue(value);

            var style = _workbook.CreateCellStyle();
            style.Alignment = alignment;

            if (isBold)
            {
                var font = _workbook.CreateFont();
                font.IsBold = true;
                style.SetFont(font);
            }

            if (fillColor != -1)
            {
                style.FillForegroundColor = fillColor;
                style.FillPattern = FillPattern.SolidForeground;
            }

            cell.CellStyle = style;
        }

        public void UpdateColumnWidth(string sheetName, int[] columnIndexes, int widthInChars)
        {
            var sheet = _workbook.GetSheet(sheetName);
            if (sheet == null)
                throw new ArgumentException($"Sheet '{sheetName}' not found in workbook.");

            int widthIn256 = widthInChars * 256 * 12;

            foreach (int colIndex in columnIndexes)
            {
                sheet.SetColumnWidth(colIndex, widthIn256);
            }
        }

        public void UpdateStyledCell(string sheetName, int row, int col, double value, HorizontalAlignment alignment, short fillColor = -1)
        {
            var sheet = _workbook.GetSheet(sheetName);
            var currentRow = sheet.GetRow(row) ?? sheet.CreateRow(row);
            var cell = currentRow.GetCell(col) ?? currentRow.CreateCell(col);

            cell.SetCellValue(value);

            var style = _workbook.CreateCellStyle();
            style.Alignment = alignment;

            if (fillColor != -1)
            {
                style.FillForegroundColor = fillColor;
                style.FillPattern = FillPattern.SolidForeground;
            }

            cell.CellStyle = style;
        }
    }
}
