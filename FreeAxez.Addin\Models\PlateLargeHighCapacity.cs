﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class PlateLargeHighCapacity : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
                "High_Capacity-Plate_Large",
                "Plate_High_Capacity_Oversized"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public PlateLargeHighCapacity(Element element) : base(element)
        {
        }

        public static List<PlateLargeHighCapacity> Collect()
        {
            return FamilyCollector.Instances.Select(f => new PlateLargeHighCapacity(f)).ToList();
        }
    }
}
