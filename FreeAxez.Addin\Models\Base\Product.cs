﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Utils;
using System;

namespace FreeAxez.Addin.Models.Base
{
    public abstract class Product
    {
        private readonly Element _element;
        private string _model;
        private string _productName;
        private string _description;
        private Level _level;


        protected Product(Element element)
        {
            _element = element;
        }


        public Element Element => _element;

        public virtual Level Level
        {
            get
            {
                if (_level == null)
                {
                    _level = RevitManager.Document.GetElement(Element.LevelId) as Level;
                    if (_level == null)
                    {
                        LogHelper.Warning($"Element {Element.Id} has not level associated with the element");
                    }
                }

                return _level;
            }
        }

        public virtual string Model
        {
            get
            {
                if (_model == null)
                {
                    _model = RevitManager.Document.GetElement(Element.GetTypeId())
                        .get_Parameter(BuiltInParameter.ALL_MODEL_MODEL)?
                        .AsString();

                    if (_model == null)
                    {
                        LogHelper.Warning($"Element {Element.Id} does not contain built in parameter of type \"Model\"");
                        _model = "";
                    }
                }

                return _model;
            }
        }

        public virtual string ProductName
        {
            get
            {
                if (_productName == null)
                {
                    _productName = RevitManager.Document.GetElement(Element.GetTypeId())
                        .get_Parameter(new Guid(Constants.ParameterGuidProductName))?
                        .AsString();

                    if (_productName == null)
                    {
                        LogHelper.Warning($"Element {Element.Id} does not contain parameter of type Product Name with Guid \"{Constants.ParameterGuidProductName}\"");
                        _productName = "";
                    }
                }

                return _productName;
            }
        }

        public virtual string Description
        {
            get
            {
                if (_description == null)
                {
                    _description = RevitManager.Document.GetElement(Element.GetTypeId())
                        .get_Parameter(BuiltInParameter.ALL_MODEL_DESCRIPTION)?
                        .AsString();

                    if (_description == null)
                    {
                        LogHelper.Warning($"Element {Element.Id} does not contain built in parameter of type \"Description\"");
                        _description = "";
                    }
                }

                return _description;
            }
        }

        public virtual string Id
        {
            get
            {
                return _element.Id.GetIntegerValue().ToString();
            }
        }

        public virtual double Length { get; }
    }
}
