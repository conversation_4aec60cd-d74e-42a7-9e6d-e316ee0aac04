using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Constants;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;
using FreeAxez.Addin.Infrastructure;
using NetTopologySuite.Geometries;
using System.Diagnostics;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services
{
    /// <summary>
    /// Main processor for Low Voltage Path generation
    /// </summary>
    public class LowVoltageProcessor
    {
        private readonly Document _document;
        private readonly LowVoltagePathSettings _settings;

        public LowVoltageProcessor(Document document, LowVoltagePathSettings settings)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        }

        /// <summary>
        /// Processes the low voltage path generation
        /// </summary>
        public ExecutionReport ProcessLowVoltagePath(List<CurveElement> lines, List<FamilyInstance> outlets)
        {
            var stopwatch = Stopwatch.StartNew();
            var report = new ExecutionReport
            {
                TotalLinesProcessed = lines.Count,
                TotalOutletsProcessed = outlets.Count
            };

            try
            {
                // Convert data to geometry models
                var dataCollector = new LowVoltageDataCollector(_document, _document.ActiveView);
                var outletModels = dataCollector.ConvertOutletsToModels(outlets);
                var lineGeometry = dataCollector.ConvertLinesToGeometry(lines);

                // Process geometry to create railing and annotation models
                var (railingModels, annotationModels, unconnectedOutlets) = ProcessGeometry(lineGeometry, outletModels);

                // Create Revit elements
                var createdRailings = CreateRailings(railingModels);
                var createdAnnotations = CreateAnnotations(annotationModels);

                // Update wire counts
                UpdateWireCounts(createdRailings, createdAnnotations);

                // Delete lines if requested
                if (_settings.DeleteLines)
                {
                    DeleteLines(lines);
                    report.LinesDeleted = lines.Count;
                }

                // Update report
                report.RailingsCreated = createdRailings.Count;
                report.AnnotationsCreated = createdAnnotations.Count;
                report.UnconnectedOutlets = unconnectedOutlets.Count;
                report.UnconnectedOutletIds = unconnectedOutlets.Select(o => o.Id).ToList();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error processing low voltage path: {ex.Message}", ex);
            }
            finally
            {
                stopwatch.Stop();
                report.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
            }

            return report;
        }

        /// <summary>
        /// Processes geometry to create railing and annotation models
        /// </summary>
        private (List<RailingModel> railings, List<AnnotationModel> annotations, List<OutletModel> unconnected) 
            ProcessGeometry(List<LineString> lines, List<OutletModel> outlets)
        {
            var allRailings = new List<RailingModel>();
            var allAnnotations = new List<AnnotationModel>();
            var allUnconnected = new List<OutletModel>();

            // Group lines and process each group
            var lineGroupService = new LineGroupService();
            var lineGroups = lineGroupService.GroupLines(lines);

            foreach (var group in lineGroups)
            {
                // Find outlets associated with this group
                var groupOutlets = outlets.Where(o => 
                    group.Any(l => l.Distance(o.Location) < LowVoltageConstants.OutletToLineToleranceFeet))
                    .ToList();

                if (!groupOutlets.Any())
                {
                    continue;
                }

                // Normalize lines
                var normalizationService = new LineNormalizationService();
                var minSegmentLength = RevitManager.Application.ShortCurveTolerance;
                var fixedLines = normalizationService.NormalizeLines(group, minSegmentLength);

                // Analyze geometry
                var analyzerService = new GeometryAnalyzerService();
                var geometryTree = analyzerService.AnalyzeGeometry(fixedLines, groupOutlets);

                // Generate railings
                var railingsGenerator = new RailingsGeneratorService();
                var railings = railingsGenerator.GenerateRailings(geometryTree);
                allRailings.AddRange(railings);

                // Place annotations
                var annotationPlacer = new AnnotationPlacerService();
                var annotations = annotationPlacer.PlaceAnnotations(railings, fixedLines, geometryTree.Root);
                allAnnotations.AddRange(annotations);

                // Track unconnected outlets
                var connectedOutlets = groupOutlets.Where(o => 
                    railings.Any(r => r.Path.Distance(o.Location) < LowVoltageConstants.OutletToLineToleranceFeet))
                    .ToList();
                
                var unconnectedInGroup = groupOutlets.Except(connectedOutlets).ToList();
                allUnconnected.AddRange(unconnectedInGroup);
            }

            return (allRailings, allAnnotations, allUnconnected);
        }

        /// <summary>
        /// Creates Revit railings from railing models
        /// </summary>
        private List<Railing> CreateRailings(List<RailingModel> railingModels)
        {
            var createdRailings = new List<Railing>();

            using (var transaction = new Transaction(_document, "Create Low Voltage Railings"))
            {
                transaction.Start();

                try
                {
                    var levelId = _document.ActiveView.GenLevel.Id;

                    foreach (var railingModel in railingModels)
                    {
                        var curveLoop = NTSConverter.LineStringToCurveLoop(railingModel.Path);
                        var railing = Railing.Create(_document, curveLoop, _settings.SelectedRailingType.Id, levelId);
                        
                        // Set wire count parameter
                        railing.LookupParameter(LowVoltageConstants.QuantityParameterName)?.Set(railingModel.WireCount);
                        
                        createdRailings.Add(railing);
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.RollBack();
                    throw;
                }
            }

            return createdRailings;
        }

        /// <summary>
        /// Creates Revit annotations from annotation models
        /// </summary>
        private List<FamilyInstance> CreateAnnotations(List<AnnotationModel> annotationModels)
        {
            var createdAnnotations = new List<FamilyInstance>();

            using (var transaction = new Transaction(_document, "Create Low Voltage Annotations"))
            {
                transaction.Start();

                try
                {
                    foreach (var annotationModel in annotationModels)
                    {
                        var origin = NTSConverter.PointToXYZ(annotationModel.Location);
                        var annotationSymbol = GetAnnotationSymbol(annotationModel.Orientation);
                        
                        if (annotationSymbol != null)
                        {
                            var annotation = _document.Create.NewFamilyInstance(
                                origin, annotationSymbol, _document.ActiveView);
                            createdAnnotations.Add(annotation);
                        }
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.RollBack();
                    throw;
                }
            }

            return createdAnnotations;
        }

        /// <summary>
        /// Updates wire counts in annotations based on nearby railings
        /// </summary>
        private void UpdateWireCounts(List<Railing> railings, List<FamilyInstance> annotations)
        {
            var wireCountingService = new WireCountingService(railings, annotations);
            wireCountingService.CountWires();
        }

        /// <summary>
        /// Deletes the original lines if requested
        /// </summary>
        private void DeleteLines(List<CurveElement> lines)
        {
            using (var transaction = new Transaction(_document, "Delete Low Voltage Lines"))
            {
                transaction.Start();

                try
                {
                    var elementIds = lines.Select(l => l.Id).ToList();
                    _document.Delete(elementIds);
                    transaction.Commit();
                }
                catch
                {
                    transaction.RollBack();
                    throw;
                }
            }
        }

        /// <summary>
        /// Gets annotation symbol for the specified direction
        /// </summary>
        private FamilySymbol GetAnnotationSymbol(Direction direction)
        {
            var annotationSymbols = new FilteredElementCollector(_document)
                .OfClass(typeof(FamilySymbol))
                .OfCategory(BuiltInCategory.OST_GenericAnnotation)
                .Cast<FamilySymbol>()
                .Where(s => s.FamilyName == LowVoltageConstants.AnnotationFamilyName)
                .ToList();

            return annotationSymbols.FirstOrDefault(s => s.Name.Equals(direction.ToString())) 
                   ?? annotationSymbols.FirstOrDefault();
        }
    }
}
