﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Enums;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Models;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils
{
    public class ProjectMapper
    {
        private readonly Document _sourceDoc;
        private readonly Document _targetDoc;

        public ProjectMapper(Document sourceDoc, Document targetDoc)
        {
            _sourceDoc = sourceDoc;
            _targetDoc = targetDoc;

            MappedTitleBlocks = MapTitleBlocks();
            MappedLegends = MapViews(ViewType.Legend);
            MappedDraftingViews = MapViews(ViewType.DraftingView);
            MappedSchedules = MapViews(ViewType.Schedule);
        }

        public List<TitleBlockMapping> MappedTitleBlocks { get; private set; }
        public List<ViewMapping> MappedLegends { get; private set; }
        public List<ViewMapping> MappedDraftingViews { get; private set; }
        public List<ViewMapping> MappedSchedules { get; private set; }

        private List<TitleBlockMapping> MapTitleBlocks()
        {
            var sourceTitleBlocks = new FilteredElementCollector(_sourceDoc)
                .OfCategory(BuiltInCategory.OST_TitleBlocks)
                .OfClass(typeof(FamilySymbol))
                .Cast<FamilySymbol>()
                .ToList();

            var targetTitleBlocks = new FilteredElementCollector(_targetDoc)
                .OfCategory(BuiltInCategory.OST_TitleBlocks)
                .OfClass(typeof(FamilySymbol))
                .Cast<FamilySymbol>()
                .ToList();

            var mappedTitleBlocks = new List<TitleBlockMapping>();

            foreach (var sourceSymbol in sourceTitleBlocks)
            {
                var sourceMappingKey = $"{sourceSymbol.Family.Name}:{sourceSymbol.Name}";

                var exactMatch = targetTitleBlocks.FirstOrDefault(targetSymbol =>
                    $"{targetSymbol.Family.Name}:{targetSymbol.Name}".Equals(sourceMappingKey, StringComparison.OrdinalIgnoreCase));

                if (exactMatch != null)
                {
                    mappedTitleBlocks.Add(new TitleBlockMapping(sourceSymbol, exactMatch));
                    continue;
                }

                var bestMatch = targetTitleBlocks.Select(targetSymbol => new
                {
                    TargetSymbol = targetSymbol,
                    Similarity = FuzzySimilarityCalculator.CalculateSimilarity(
                        sourceMappingKey,
                        $"{targetSymbol.Family.Name}:{targetSymbol.Name}")
                })
                .OrderByDescending(x => x.Similarity)
                .FirstOrDefault();

                if (bestMatch != null && bestMatch.Similarity > 0.8)
                {
                    mappedTitleBlocks.Add(new TitleBlockMapping(sourceSymbol, bestMatch.TargetSymbol));
                }
                else
                {
                    mappedTitleBlocks.Add(new TitleBlockMapping(sourceSymbol, null));
                }
            }

            return mappedTitleBlocks;
        }

        private List<ViewMapping> MapViews(ViewType viewType)
        {
            if (viewType == ViewType.Schedule)
                return MapSchedules();

            var sourceViews = new FilteredElementCollector(_sourceDoc)
                .OfClass(typeof(View))
                .Cast<View>()
                .Where(v => v.ViewType == viewType)
                .ToList();

            var targetViews = new FilteredElementCollector(_targetDoc)
                .OfClass(typeof(View))
                .Cast<View>()
                .Where(v => v.ViewType == viewType)
                .ToList();

            var mappedViews = new List<ViewMapping>();

            foreach (var sourceView in sourceViews)
            {
                var exactMatch = targetViews.FirstOrDefault(targetView =>
                    targetView.Name.Equals(sourceView.Name, StringComparison.OrdinalIgnoreCase));

                if (exactMatch != null)
                {
                    mappedViews.Add(new ViewMapping(sourceView, exactMatch));
                    continue;
                }

                var bestMatch = targetViews.Select(targetView => new
                    {
                        TargetView = targetView,
                        Similarity = FuzzySimilarityCalculator.CalculateSimilarity(
                            sourceView.Name,
                            targetView.Name)
                    })
                    .OrderByDescending(x => x.Similarity)
                    .FirstOrDefault();

                if (bestMatch != null && bestMatch.Similarity > 0.8)
                {
                    mappedViews.Add(new ViewMapping(sourceView, bestMatch.TargetView));
                }
                else
                {
                    mappedViews.Add(new ViewMapping(sourceView, null));
                }
            }

            return mappedViews;
        }

        private List<ViewMapping> MapSchedules()
        {
            var sourceSchedules = new FilteredElementCollector(_sourceDoc)
                .OfClass(typeof(View))
                .Cast<View>()
                .Where(v => v is ViewSchedule && IsViewScheduleRelevant(v as ViewSchedule))
                .Cast<ViewSchedule>()
                .ToList();

            var targetSchedules = new FilteredElementCollector(_targetDoc)
                .OfClass(typeof(View))
                .Cast<View>()
                .Where(v => v is ViewSchedule && IsViewScheduleRelevant(v as ViewSchedule))
                .Cast<ViewSchedule>()
                .ToList();

            var mappedSchedules = new List<ViewMapping>();

            var scheduleOption = OptionsManager.ScheduleHandlingOption;

            foreach (var sourceSchedule in sourceSchedules)
            {
                var sourceSubCategory = GetViewSubCategory(sourceSchedule);

                var exactMatch = targetSchedules.FirstOrDefault(targetSchedule =>
                    targetSchedule.Name.Equals(sourceSchedule.Name, StringComparison.OrdinalIgnoreCase) &&
                    GetViewSubCategory(targetSchedule) == sourceSubCategory);

                if (exactMatch != null)
                {
                    mappedSchedules.Add(new ViewMapping(sourceSchedule, exactMatch));
                    continue;
                }

                if (scheduleOption == ScheduleHandlingOption.UseSource || scheduleOption == ScheduleHandlingOption.Combine)
                {
                    mappedSchedules.Add(new ViewMapping(sourceSchedule, null));
                    continue;
                }

                if (scheduleOption == ScheduleHandlingOption.UseTarget)
                {
                    var bestMatch = targetSchedules
                        .Where(targetSchedule => GetViewSubCategory(targetSchedule) == sourceSubCategory)
                        .Select(targetSchedule => new
                        {
                            TargetSchedule = targetSchedule,
                            Similarity = FuzzySimilarityCalculator.CalculateSimilarity(
                                sourceSchedule.Name,
                                targetSchedule.Name)
                        })
                        .OrderByDescending(x => x.Similarity)
                        .FirstOrDefault();

                    if (bestMatch != null && bestMatch.Similarity > 0.9)
                    {
                        mappedSchedules.Add(new ViewMapping(sourceSchedule, bestMatch.TargetSchedule));
                    }
                    else
                    {
                        mappedSchedules.Add(new ViewMapping(sourceSchedule, null));
                    }
                }
            }

            return mappedSchedules;
        }

        private bool IsViewScheduleRelevant(ViewSchedule viewSchedule)
        {
            if (viewSchedule == null)
                return false;

            if (viewSchedule.IsTitleblockRevisionSchedule || viewSchedule.IsInternalKeynoteSchedule)
                return false;

            if (viewSchedule.IsTemplate)
                return false;

            return true;
        }

        private string GetViewSubCategory(View view)
        {
            if (view == null)
                return null;

            var subCategoryParam = view.LookupParameter("View Sub Category");

            return subCategoryParam != null && subCategoryParam.HasValue
                ? subCategoryParam.AsString()
                : null;
        }
    }
}