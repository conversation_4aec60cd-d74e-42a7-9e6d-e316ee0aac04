﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.Views.BaseUnitReplacementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.ViewModels"
        MinHeight="500"
        MinWidth="600"
        Height="690"
        SizeToContent="Width"
        WindowStartupLocation="CenterScreen"
        Title="Base Unit Replacement">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}"
               BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Window.DataContext>
        <vm:BaseUnitReplacementViewModel />
    </Window.DataContext>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <DataGrid Grid.Row="0"
                  Name="grid"
                  AutoGenerateColumns="False"
                  HeadersVisibility="Column"
                  CanUserDeleteRows="False"
                  CanUserAddRows="False"
                  EnableRowVirtualization="True"
                  IsReadOnly="True"
                  Style="{StaticResource DataGridWithBorders}"
                  ItemsSource="{Binding Intersections}">
            <DataGrid.Columns>
                <DataGridTextColumn Header="Correct"
                                    Binding="{Binding Correct}">
                    <DataGridTextColumn.CellStyle>
                        <Style TargetType="DataGridCell"
                               BasedOn="{StaticResource DataGridCellStyle}">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding Correct}"
                                             Value="True">
                                    <Setter Property="Foreground"
                                            Value="{StaticResource Green600}" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Correct}"
                                             Value="False">
                                    <Setter Property="Foreground"
                                            Value="{StaticResource Red600}" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGridTextColumn.CellStyle>
                </DataGridTextColumn>
                <DataGridTemplateColumn Header="Box Id"
                                        SortMemberPath="BoxId">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="{Binding BoxId}"
                                    Command="{Binding DataContext.IdCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                    CommandParameter="{Binding BoxId}"
                                    Style="{StaticResource ButtonOutlinedBlue}"
                                    Height="25" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="Box Family"
                                    Binding="{Binding BoxFamilyName}" />
                <DataGridTextColumn Header="Box Type"
                                    Binding="{Binding BoxTypeName}" />
                <DataGridTextColumn Header="Level"
                                    Binding="{Binding Level}" />
                <DataGridTextColumn Header="Unit Ids"
                                    Binding="{Binding UnitIds}" />
                <DataGridTextColumn Header="Unit Families"
                                    Binding="{Binding UnitFamilyNames}" />
                <DataGridTextColumn Header="Unit Types"
                                    Binding="{Binding UnitTypeNames}" />
            </DataGrid.Columns>
        </DataGrid>
        <StackPanel Grid.Row="1"
                    Margin="0,5,0,0"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right">
            <Button Content="Export"
                    Margin="5,0,0,0"
                    Height="35"
                    Width="100"
                    Style="{StaticResource ButtonOutlinedBlue}"
                    Command="{Binding ExportCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}" />
            <Button Content="Close"
                    Margin="5,0,0,0"
                    Height="35"
                    Width="100"
                    Style="{StaticResource ButtonOutlinedRed}"
                    Command="{Binding CloseCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}" />
        </StackPanel>
    </Grid>
</Window>