﻿using System.Windows;
using System.Windows.Controls;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.ViewModel;

namespace FreeAxez.Addin.AdditionalFunctional.UserAccountControl.View
{
    public partial class SettingsView : UserControl
    {
        public SettingsView()
        {
            InitializeComponent();
        }
        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (this.DataContext is SettingsVm viewModel)
            {
                viewModel.UserPassword = PasswordBox.Password;
                PlaceholderText.Visibility = PasswordBox.Password.Length > 0
                    ? Visibility.Collapsed
                    : Visibility.Visible;
            }
        }

        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {

        }
    }
}
