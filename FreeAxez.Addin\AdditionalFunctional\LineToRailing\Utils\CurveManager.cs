﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.LineToRailing.Utils
{
    public class CurveManager
    {
        private List<Reference> _references;
        private LevelManager _levelManager;


        public CurveManager()
        {
            _levelManager = new LevelManager();
        }


        public List<Curve> SelectedCurves { get; private set; } = new List<Curve>();

        public List<CurveLoop> CurveLoops { get; private set; } = new List<CurveLoop>();


        public void PickCurvesInUI()
        {
            try
            {
                _references = RevitManager.UIDocument.Selection.PickObjects(Autodesk.Revit.UI.Selection.ObjectType.Element,
                    new CurveSelectionFilter(), "Select the lines to convert to railings.").ToList();

                if (_references.Count == 0)
                {
                    return;
                }

                foreach (var reference in _references)
                {
                    SelectedCurves.Add((RevitManager.Document.GetElement(reference) as CurveElement).GeometryCurve);
                }
            }
            catch (Autodesk.Revit.Exceptions.OperationCanceledException)
            {

            }
        }

        public void CreateCurveLoops(bool getSplitedCurves)
        {
            if (SelectedCurves.Count == 0)
            {
                return;
            }
            
            if (getSplitedCurves)
            {
                CurveLoops = GetCurveLoopsWithoutCombine();
            }
            else
            {
                CurveLoops = GetCurveLoops(new List<List<Curve>>(), SelectedCurves).Select(l => CurveLoop.Create(l)).ToList();
            }
        }

        public ElementId GetLevelId(CurveLoop curveLoop)
        {
            var elevationOfStartPoint = curveLoop.First().GetEndPoint(0).Z;

            return _levelManager.GetNearestLevel(elevationOfStartPoint).Id;
        }

        public void DeleteSelectedLines()
        {
            using (var t = new Transaction(RevitManager.Document))
            {
                t.Start("Remove Selected Lines");

                RevitManager.Document.Delete(_references.Select(r => RevitManager.Document.GetElement(r).Id).ToList());

                t.Commit();
            }
        }

        private List<List<Curve>> GetCurveLoops(List<List<Curve>> curveLoops, List<Curve> curves)
        {
            var curveIndexesForRemove = new List<int>();

            // Try to add each curve to curve loops
            for (int i = 0; i < curves.Count; i++)
            {
                for (int j = 0; j < curveLoops.Count; j++)
                {
                    var currentCurveLoop = curveLoops[j];

                    if (TryAddCurve(ref currentCurveLoop, curves[i]))
                    {
                        curveIndexesForRemove.Insert(0, i);
                        continue;
                    }
                }
            }

            // Remove curves which was added to curve loops
            if (curveIndexesForRemove.Count > 0)
            {
                foreach (var index in curveIndexesForRemove)
                {
                    curves.RemoveAt(index);
                }
            }

            // No one curve was added so we create new curve loop for first curve
            if (curveIndexesForRemove.Count == 0)
            {
                curveLoops.Add(new List<Curve>() { curves[0] });
                curves.RemoveAt(0);
            }

            if (curves.Count == 0)
            {
                return curveLoops;
            }

            return GetCurveLoops(curveLoops, curves);
        }

        private bool TryAddCurve(ref List<Curve> curveLoop, Curve curve)
        {
            if (curveLoop.Count() == 0)
            {
                curveLoop.Append(curve);
                return true;
            }

            var curveStartPoint = curve.GetEndPoint(0);
            var curveEndPoint = curve.GetEndPoint(1);
            var loopStartPoint = curveLoop.First().GetEndPoint(0);
            var loopEndPoint = curveLoop.Last().GetEndPoint(1);

            if (loopStartPoint.IsAlmostEqualTo(curveStartPoint))
            {
                curveLoop.Insert(0, curve.CreateReversed());
                return true;
            }
            else if (loopStartPoint.IsAlmostEqualTo(curveEndPoint))
            {
                curveLoop.Insert(0, curve);
                return true;
            }
            else if (loopEndPoint.IsAlmostEqualTo(curveStartPoint))
            {
                curveLoop.Add(curve);
                return true;
            }
            else if (loopEndPoint.IsAlmostEqualTo(curveEndPoint))
            {
                curveLoop.Add(curve.CreateReversed());
                return true;
            }

            return false;
        }

        private List<CurveLoop> GetCurveLoopsWithoutCombine()
        {
            var curveLoops = new List<CurveLoop>();
            
            foreach (var curve in SelectedCurves)
            {
                var curveLoop = new CurveLoop();
                curveLoop.Append(curve);

                curveLoops.Add(curveLoop);
            }

            return curveLoops;
        }
    }
}
