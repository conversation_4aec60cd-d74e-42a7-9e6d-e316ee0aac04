﻿namespace FreeAxez.Core.Dto
{
    public class RateSetting
    {
        public double BaseUnitPriceRatio { get; set; }
        public double HalfBaseUnitPriceRatio { get; set; }
        public double QuarterBaseUnitPriceRatio { get; set; }
        public double CornerPriceRatio { get; set; }
        public double ChannelPriceRatio { get; set; }
        public double HalfChannelPriceRatio { get; set; }
        public double TrapezoidPriceRatio { get; set; }
        public double LTrapezoidPriceRatio { get; set; }
        public double LBorderPriceRatio { get; set; }
        public double BorderPriceRatio { get; set; }

        public double BaseUnitTimeRatio { get; set; }
        public double HalfBaseUnitTimeRatio { get; set; }
        public double QuarterBaseUnitTimeRatio { get; set; }
        public double CornerTimeRatio { get; set; }
        public double ChannelTimeRatio { get; set; }
        public double HalfChannelTimeRatio { get; set; }
        public double TrapezoidTimeRatio { get; set; }
        public double LTrapezoidTimeRatio { get; set; }
        public double LBorderTimeRatio { get; set; }
        public double BorderTimeRatio { get; set; }
    }
}