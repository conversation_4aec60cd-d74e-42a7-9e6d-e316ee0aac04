﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Models
{
    public class GriddBomLevel
    {
        private readonly Level _level;
        private readonly List<Element> _elements;
        private readonly SmartLookupParameter _smartLookupParameter;

        public GriddBomLevel(Level level, List<Element> elements, SmartLookupParameter smartLookupParameter)
        {
            _level = level;
            _elements = elements;
            _smartLookupParameter = smartLookupParameter;
        }


        public int Number { get; private set; }
        public double LevelArea { get; private set; }
        public double GriddArea { get; private set; }
        public double ReinforcedArea { get; private set; }
        public List<GriddBomProduct> Products { get; private set; } = new List<GriddBomProduct>();


        public void CalculateBom()
        {
            Number = GetNumber();
            LevelArea = GetArea(Constants.FloorTypeNameOverall);
            GriddArea = GetArea(Constants.FloorTypeNameGridd);
            ReinforcedArea = GetArea(Constants.FloorTypeNameReinforced);
            Products = GetProducts();
        }

        private int GetNumber()
        {
            // Level -4 => -4 | Level 12 => 12 | Level 1.2 => 2
            var match = Regex.Match(_level.Name, @"-?\d+$");
            if (match.Success) return int.Parse(match.Value);
            return -1;
        }

        private double GetArea(string floorTypeName)
        {
            var floors = _elements.Where(e => e is Floor floor && floor.Name == floorTypeName).ToList();
            return Math.Round(floors.Sum(f => f.get_Parameter(BuiltInParameter.HOST_AREA_COMPUTED).AsDouble()), 2);
        }

        private List<GriddBomProduct> GetProducts()
        {
            var groupsByProductKey = _elements
                .Where(e => e is FamilyInstance)
                .Cast<FamilyInstance>()
                .GroupBy(g => GriddBomProduct.GetProductKey(g, _smartLookupParameter));

            var products = new List<GriddBomProduct>();
            foreach (var group in groupsByProductKey)
            {
                if (string.IsNullOrEmpty(group.Key)) continue;
                products.Add(new GriddBomProduct(group.ToList(), _smartLookupParameter));
            }

            products.ForEach(p => p.CalculateBom());
            products = products.OrderBy(p => p.Model).ToList();

            return products;
        }
    }
}
