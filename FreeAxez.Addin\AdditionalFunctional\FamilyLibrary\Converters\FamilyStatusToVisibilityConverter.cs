﻿using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters;

public class FamilyStatusToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is FamilyStatus status)
            return status == FamilyStatus.NotPresentInProject || status == FamilyStatus.OutdatedVersionInProject
                ? Visibility.Visible
                : Visibility.Collapsed;
        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}