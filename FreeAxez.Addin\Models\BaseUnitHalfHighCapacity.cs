﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class BaseUnitHalfHighCapacity : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
                "Base_Unit_Half_High_Capacity",
                "Base_Unit_Half_High_Density",
                "High_Capacity-Base_Unit-Half"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public BaseUnitHalfHighCapacity(Element element) : base(element)
        {
        }

        public static List<BaseUnitHalfHighCapacity> Collect()
        {
            return FamilyCollector.Instances.Select(f => new BaseUnitHalfHighCapacity(f)).ToList();
        }
    }
}