﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ExportForVR.Models;
using FreeAxez.Addin.AdditionalFunctional.ExportForVR.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.ExportForVR.ViewModels
{
    public class ExportForVRViewModel : BaseViewModel
    {
        public ExportForVRViewModel()
        {
            FolderPath = Properties.Settings.Default.ExportForVRPath;
            LightweightFamiliesPath = Properties.Settings.Default.ExportForVRLightweightFamiliesPath;
            CopyFurniture = Properties.Settings.Default.ExportForVRCopyFurniture;

            if (!Directory.Exists(FolderPath))
            {
                FolderPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            }

            Levels = GetLevelModels();

            ExportCommand = new RelayCommand(OnExportCommandExecute);
            CancelCommand = new RelayCommand(OnCancelCommandExecute);
            BrowseCommand = new RelayCommand(OnBrowseCommandExecute);
            BrowseLightweightCommand = new RelayCommand(OnBrowseLightweightCommandExecute);
        }


        public string FolderPath { get; set; }
        public string LightweightFamiliesPath { get; set; }
        public bool CopyFurniture { get; set; }
        public List<LevelModel> Levels { get; set; }

        public ICommand ExportCommand { get; set; }
        private void OnExportCommandExecute(object p)
        {
            if (!Directory.Exists(FolderPath))
            {
                InfoDialog.ShowDialog("Warning", "The path doesn't exist.");
                return;
            }

            var selectedLevels = Levels.Where(rv => rv.IsCheck).Select(rv => rv.Level).Cast<Level>().ToList();
            if (selectedLevels.Count == 0)
            {
                InfoDialog.ShowDialog("Warning", "Levels are not selected.\nPlease select at least one level.");
                return;
            }

            SaveSettings();
            (p as Window).Close();

            var exporter = new VRExporter(FolderPath, LightweightFamiliesPath, CopyFurniture, selectedLevels);
            exporter.Export();
        }

        public ICommand CancelCommand { get; set; }
        private void OnCancelCommandExecute(object p)
        {
            (p as Window).Close();
        }

        public ICommand BrowseCommand { get; set; }
        private void OnBrowseCommandExecute(object p)
        {
            var browse = new FolderBrowserDialog();
            browse.Description = "Select the folder to export Revit files.";

            if (Directory.Exists(FolderPath))
            {
                browse.SelectedPath = FolderPath;
            }

            if (browse.ShowDialog() == DialogResult.OK)
            {
                FolderPath = browse.SelectedPath;
                OnPropertyChanged(nameof(FolderPath));
            }
        }

        public ICommand BrowseLightweightCommand { get; set; }
        private void OnBrowseLightweightCommandExecute(object p)
        {
            var browse = new FolderBrowserDialog();
            browse.Description = "Select the folder with lightweight families.";

            if (Directory.Exists(LightweightFamiliesPath))
            {
                browse.SelectedPath = LightweightFamiliesPath;
            }

            if (browse.ShowDialog() == DialogResult.OK)
            {
                LightweightFamiliesPath = browse.SelectedPath;
                OnPropertyChanged(nameof(LightweightFamiliesPath));
            }
        }


        private void SaveSettings()
        {
            Properties.Settings.Default.ExportForVRPath = FolderPath;
            Properties.Settings.Default.ExportForVRLightweightFamiliesPath = LightweightFamiliesPath;
            Properties.Settings.Default.ExportForVRCopyFurniture = CopyFurniture;
            Properties.Settings.Default.Save();
        }

        private List<LevelModel> GetLevelModels()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Level))
                .OrderBy(l => l.get_Parameter(BuiltInParameter.LEVEL_ELEV).AsDouble())
                .Select(l => new LevelModel() { Name = l.Name, Level = l })
                .ToList();
        }
    }
}
