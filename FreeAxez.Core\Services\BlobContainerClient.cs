﻿using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs;
using Azure;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Threading;

namespace FreeAxez.Core.Services
{
    public class BlobContainerClient : IBlobContainerClient
    {
        private readonly Azure.Storage.Blobs.BlobContainerClient _blobContainerClient;

        public BlobContainerClient(Azure.Storage.Blobs.BlobContainerClient blobContainerClient)
        {
            _blobContainerClient = blobContainerClient;
        }

        public async Task<Response<BlobContentInfo>> UploadFileAsync(
            string blobName, Stream stream, bool overwrite = true, CancellationToken cancellationToken = default)
        {
            BlobClient existedBlobClient = _blobContainerClient.GetBlobClient(blobName);

            Response<bool> isBlobClientExistsResponse = await existedBlobClient.ExistsAsync(cancellationToken);

            if (isBlobClientExistsResponse && overwrite is true)
            {
                return await existedBlobClient.UploadAsync(stream, overwrite, cancellationToken);
            }

            if (isBlobClientExistsResponse && overwrite is false)
            {
                string blobFileExtension = Path.GetExtension(blobName);
                string blobDirectoryName = Path.GetDirectoryName(blobName) ?? string.Empty;
                string blobNameWithoutExtension = Path.GetFileNameWithoutExtension(blobName);
                string timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff");

                string directoryNameWithBlobName = string.IsNullOrEmpty(blobDirectoryName)
                    ? blobNameWithoutExtension
                    : Path.Combine(blobDirectoryName, blobNameWithoutExtension);

                string newBlobName = $"{directoryNameWithBlobName}_{timestamp}{blobFileExtension}";

                return await _blobContainerClient.UploadBlobAsync(newBlobName, stream, cancellationToken);
            }

            return await _blobContainerClient.UploadBlobAsync(blobName, stream, cancellationToken);
        }

        public async Task<Response<bool>> DeleteFileIfExistsAsync(
            string blobName, CancellationToken cancellationToken = default)
        {
            Response<bool> existsResponse = await ExistsAsync(blobName, cancellationToken);

            if (existsResponse)
            {
                Response deleteResponse = await _blobContainerClient.DeleteBlobAsync(blobName, cancellationToken: cancellationToken);

                return Response.FromValue(existsResponse.Value, deleteResponse);
            }

            return existsResponse;
        }

        public async Task<Response<bool>> ExistsAsync(string blobName, CancellationToken cancellationToken = default)
        {
            BlobClient blobClient = _blobContainerClient.GetBlobClient(blobName);

            return await blobClient.ExistsAsync(cancellationToken);
        }

        public string GetBlobClientAbsolutePath(string blobName)
        {
            return _blobContainerClient.GetBlobClient(blobName).Uri.AbsoluteUri;
        }
    }
}
