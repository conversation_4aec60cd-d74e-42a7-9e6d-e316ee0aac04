﻿using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Utils;
using FreeAxez.Addin.Infrastructure;
using System.Threading.Tasks;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Services;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Model;
using Newtonsoft.Json;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.View;
using System.Linq;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System.Windows;

namespace FreeAxez.Addin.AdditionalFunctional.UserAccountControl.ViewModel
{
    public class LoginVm : WindowViewModel
    {
        private string _userEmail;
        private string _userPassword;
        private bool _isLoggingIn;

        public LoginVm()
        {
            LoginCommand = new AsyncRelayCommand(ExecuteLogin);
            ShowResetPasswordCommand = new RelayCommand(ShowResetPasswordView);
        }

        public ICommand LoginCommand { get; private set; }
        public ICommand ShowResetPasswordCommand { get; private set; }

        private async Task ExecuteLogin(object p)
        {
            IsLoggingIn = true;
            var response = await UserAuthApiService.Instance.GetTokenAsync(UserEmail, UserPassword);
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(responseContent);
                var token = tokenResponse.Token;

                UserAuthManager.SaveCredentials(UserEmail, token);
                CurrentUser.Info = await UserAuthApiService.Instance.GetUserInfoAsync(token);

                bool isAdmin = CurrentUser.Info.Roles.Contains("Admin");
                UserAuthManager.SetRibbonElementsEnabled(true, isAdmin);
                (p as Window)!.Close();
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Error = errorContent;
            }
            IsLoggingIn = false;
        }

        private void ShowResetPasswordView(object p)
        {
            (p as Window)!.Close();

            var resetPasswordView = new ResetPasswordView();
            RevitManager.SetRevitAsWindowOwner(resetPasswordView);
            resetPasswordView.Show();
        }

        public bool CanApply => !_isLoggingIn && !string.IsNullOrWhiteSpace(UserEmail) && !string.IsNullOrWhiteSpace(UserPassword);

        public string UserEmail
        {
            get => _userEmail;
            set
            {
                _userEmail = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CanApply));
            }
        }

        public string UserPassword
        {
            get => _userPassword;
            set
            {
                _userPassword = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CanApply));
            }
        }

        public bool IsLoggingIn
        {
            get => _isLoggingIn;
            set
            {
                _isLoggingIn = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CanApply));
            }
        }
    }
   
}
