﻿﻿using System;
using System.Threading.Tasks;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Utils;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals
{
    public class AdminDetailsDeleteVm : ModalDialogVm
    {
        private readonly LibraryItemDetailsDto _selectedDetails;

        public AdminDetailsDeleteVm(LibraryItemDetailsDto selectedDetails)
        {
            _selectedDetails = selectedDetails;
            ApplyCommand = new RelayCommand(ExecuteApply);
        }

        public ICommand ApplyCommand { get; }

        public string DetailsName => _selectedDetails.Name;

        private async void ExecuteApply(object parameter)
        {
            try
            {
                var userId = Properties.Settings.Default.UserEmail;
                var response = await ApiService.Instance.DeleteDetailsAsync(_selectedDetails.Id, userId);
                if (response.IsSuccessStatusCode)
                {
                    CloseModal(true);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Error = $"Failed to delete details: {errorContent}";
                    LogHelper.Error($"Failed to delete details: {errorContent}");
                }
            }
            catch (Exception ex)
            {
                Error = $"Error: {ex.Message}";
                LogHelper.Error($"An error occurred: {ex.Message}");
            }
        }
    }
}
