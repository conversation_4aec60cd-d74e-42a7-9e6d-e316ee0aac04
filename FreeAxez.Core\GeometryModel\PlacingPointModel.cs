﻿namespace FreeAxez.Core.GeometryModel
{
    public class PlacingPointModel : PointModel
    {
        public double Angle { get; set; }
        public double Length { get; set; }
        public string BorderRule { get; set; }
        public bool IsHalf { get; set; }
        public double BorderWidth { get; set; }
        public double BorderMaxWidth { get; set; }
        public double RadialRadius { get; set; }
        public double StartX { get; set; }
        public double StartY { get; set; }
        public bool MirrorX { get; set; }
        public bool MirrorY { get; set; }
        public double EndX { get; set; }
        public double EndY { get; set; }
        public bool IsCounterclock { get; set; }
    }
}