using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Details;

/// <summary>
///     Utility class for showing Revit-related dialogs
/// </summary>
public static class RevitDialogUtility
{
    /// <summary>
    ///     Shows a dialog with information about copied items
    /// </summary>
    public static void ShowCopiedItemsDialog(List<ViewDrafting> copiedViews, Document targetDoc)
    {
        try
        {
            if (copiedViews == null || copiedViews.Count == 0)
            {
                // Don't show any message if no views were copied
                return;
            }

            var message = $"Successfully copied {copiedViews.Count} drafting view(s) to the current project:\n\n";

            foreach (var view in copiedViews) // Limit to first 10 for readability
                message += $"• {view.Name}\n";

            ShowMessage(message, "Copy Successful", MessageBoxImage.Information);

            LogHelper.Information($"Successfully copied {copiedViews.Count} views to {targetDoc.Title}");
        }
        catch (Exception ex)
        {
            LogHelper.Error($"Error showing copied items dialog: {ex.Message}");
            // Don't show fallback success message on error
        }
    }

    /// <summary>
    ///     Shows an error dialog
    /// </summary>
    public static void ShowErrorDialog(string title, string message)
    {
        ShowMessage(message, title, MessageBoxImage.Error);
    }

    /// <summary>
    ///     Shows a message dialog
    /// </summary>
    private static void ShowMessage(string message, string title, MessageBoxImage icon)
    {
        try
        {
            var messageType = ConvertToMessageType(icon);
            MessageWindow.ShowDialog(title, message, messageType);
        }
        catch (Exception ex)
        {
            LogHelper.Error($"Error showing message dialog: {ex.Message}");
        }
    }

    /// <summary>
    ///     Converts MessageBoxImage to MessageType
    /// </summary>
    private static MessageType ConvertToMessageType(MessageBoxImage icon)
    {
        return icon switch
        {
            MessageBoxImage.Information => MessageType.Info,
            MessageBoxImage.Warning => MessageType.Warning,
            MessageBoxImage.Error => MessageType.Error,
            MessageBoxImage.Question => MessageType.Info,
            _ => MessageType.Info
        };
    }
}