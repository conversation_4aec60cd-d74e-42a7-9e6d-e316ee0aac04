﻿using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;
using FreeAxez.Addin.Infrastructure;
using Microsoft.Win32;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;

public class LibraryItemDetailsNewVm : BaseViewModel
{
    private readonly AdminFamilyBaseVm _parentVm;
    private string _manufacturer;
    private BitmapSource _familyPreview;
    private string _fileSize;
    private LibraryItemDto _libraryItem;
    private byte[] _revitFileBytes;
    private Guid _selectedCategoryId;
    private int _uploadProgress;

    public LibraryItemDetailsNewVm(LibraryItemDto libraryItem, AdminFamilyBaseVm parentVm)
    {
        _libraryItem = libraryItem;
        _parentVm = parentVm;

        ChooseImageCommand = new RelayCommand(param => ChooseImage());
        RejectFileCommand = new RelayCommand(param => RejectFile());
    }

    public ICommand ChooseImageCommand { get; private set; }
    public ICommand RejectFileCommand { get; private set; }
    public ObservableCollection<LibraryCategoryDto> Categories => _parentVm.Categories;

    public Guid SelectedCategoryId
    {
        get => _selectedCategoryId;
        set
        {
            _selectedCategoryId = value;
            OnPropertyChanged();
            _libraryItem.CategoryId = value;
            OnPropertyChanged(nameof(BorderColor));
            OnPropertyChanged(nameof(BorderThickness));
            CommandManager.InvalidateRequerySuggested();
        }
    }

    public LibraryItemDto LibraryItem
    {
        get => _libraryItem;
        set
        {
            _libraryItem = value;
            OnPropertyChanged();
        }
    }

    public string FileName
    {
        get => _libraryItem.Name;
        set
        {
            _libraryItem.Name = value;
            OnPropertyChanged();
        }
    }

    public string Manufacturer
    {
        get => _manufacturer;
        set
        {
            _manufacturer = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(BorderColor));
            OnPropertyChanged(nameof(BorderThickness));
            CommandManager.InvalidateRequerySuggested();
        }
    }

    public string Version
    {
        get => _libraryItem.Version;
        set
        {
            _libraryItem.Version = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(BorderColor));
            OnPropertyChanged(nameof(BorderThickness));
            CommandManager.InvalidateRequerySuggested();
        }
    }

    public string RevitVersion
    {
        get => _libraryItem.RevitVersion;
        set
        {
            _libraryItem.RevitVersion = value;
            OnPropertyChanged();
        }
    }

    public string ProductName
    {
        get => _libraryItem.ProductName;
        set
        {
            _libraryItem.ProductName = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(BorderColor));
            OnPropertyChanged(nameof(BorderThickness));
            CommandManager.InvalidateRequerySuggested();
        }
    }

    public string Description
    {
        get => _libraryItem.Description;
        set
        {
            _libraryItem.Description = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(BorderColor));
            OnPropertyChanged(nameof(BorderThickness));
            CommandManager.InvalidateRequerySuggested();
        }
    }

    public string ChangesDescription
    {
        get => _libraryItem.ChangesDescription;
        set
        {
            if (_libraryItem.ChangesDescription != value)
            {
                _libraryItem.ChangesDescription = value;
                OnPropertyChanged();
            }
        }
    }

    public string FileSize
    {
        get => _fileSize;
        set
        {
            _fileSize = value;
            OnPropertyChanged();
        }
    }

    public int UploadProgress
    {
        get => _uploadProgress;
        set
        {
            _uploadProgress = value;
            OnPropertyChanged();
        }
    }

    public BitmapSource FamilyPreview
    {
        get => _familyPreview;
        set
        {
            if (_familyPreview != value)
            {
                _familyPreview = value;
                OnPropertyChanged();
            }
        }
    }

    public byte[] RevitFileBytes
    {
        get => _revitFileBytes;
        set
        {
            _revitFileBytes = value;
            OnPropertyChanged();
        }
    }

    public SolidColorBrush BorderColor
    {
        get
        {
            var isCategorySelected = SelectedCategoryId != Guid.Empty;
            var selectedCategory = Categories.FirstOrDefault(c => c.Id == SelectedCategoryId);

            if (selectedCategory != null && selectedCategory.IsFreeAxezCategory)
            {
                var isKnown = ProductName != "Unknown" &&
                              Version != "Unknown" &&
                              Manufacturer != "Unknown" &&
                              FamilyPropertiesComparer.IsManufacturerFreeAxez(Manufacturer);

                if (isKnown && isCategorySelected)
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#47a23f")!);
                }
                else
                {
                    return Brushes.Red;
                }
            }

            if (selectedCategory != null && !selectedCategory.IsFreeAxezCategory)
            {
                if (isCategorySelected)
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#47a23f")!);
                }

                return Brushes.LightGray;
            }

            return Brushes.Red;
        }
    }
    public Thickness BorderThickness
    {
        get
        {
            var selectedCategory = Categories.FirstOrDefault(c => c.Id == SelectedCategoryId);
            var isCategorySelected = SelectedCategoryId != Guid.Empty;

            if (selectedCategory != null && selectedCategory.IsFreeAxezCategory)
            {
                var isKnown = ProductName != "Unknown" && Version != "Unknown" &&
                              Manufacturer != "Unknown" && FamilyPropertiesComparer.IsManufacturerFreeAxez(Manufacturer);

                if (isKnown && isCategorySelected)
                    return new Thickness(2);

                return new Thickness(1);
            }
            else if (selectedCategory != null && !selectedCategory.IsFreeAxezCategory)
            {
                if (isCategorySelected)
                    return new Thickness(2); 

                return new Thickness(1);
            }

            return new Thickness(1);
        }
    }

    public void RejectFile()
    {
        if (_parentVm.RejectFileCommand.CanExecute(this)) _parentVm.RejectFileCommand.Execute(this);
    }

    private void ChooseImage()
    {
        var openFileDialog = new OpenFileDialog
        {
            Filter = "Image files (*.png;*.jpeg;*.jpg)|*.png;*.jpeg;*.jpg",
            Title = "Select an Image"
        };

        if (openFileDialog.ShowDialog() == true)
        {
            var imageUri = new Uri(openFileDialog.FileName, UriKind.Absolute);
            var image = new BitmapImage(imageUri);

            FamilyPreview = image;
        }
    }
}