﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Models
{
    public class CurbAnnatationFamily
    {
        private const string CurbAnnotationSymbolRegex = @"CURB[\s-_](CORNER[\s-_])?TYPICAL";

        public CurbAnnatationFamily()
        {
            var curbAnnotationSymbolNameRegex = new Regex(CurbAnnotationSymbolRegex, RegexOptions.IgnoreCase);

            var curbAnnotationSymbols = new FilteredElementCollector(RevitManager.Document)
                .WhereElementIsElementType()
                .OfCategory(BuiltInCategory.OST_GenericAnnotation)
                .Cast<FamilySymbol>()
                .Where(s => curbAnnotationSymbolNameRegex.IsMatch(s.Name))
                .ToList();

            TagSymbol = curbAnnotationSymbols.FirstOrDefault(s => !s.Name.Contains("CORNER"));
            CornerTagSymbol = curbAnnotationSymbols.FirstOrDefault(s => s.Name.Contains("CORNER"));
        }

        public FamilySymbol TagSymbol { get; private set; }
        public FamilySymbol CornerTagSymbol { get; private set; }

        public bool IsFamilyNotExist(out string message)
        {
            if (TagSymbol == null || CornerTagSymbol == null)
            {
                message = "There is no general annotation for curbs in the project.\n" +
                    "Please load the family 'FA-Annotation_Curb' with types 'GRIDD CURB TYPICAL' and 'GRIDD CURB CORNER TYPICAL'.";
                return true;
            }

            message = "";
            return false;
        }
    }
}
