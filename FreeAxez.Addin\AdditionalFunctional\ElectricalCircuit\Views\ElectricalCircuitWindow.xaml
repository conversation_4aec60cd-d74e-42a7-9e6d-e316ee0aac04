﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Views.ElectricalCircuitWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Views"
        MinHeight="500"
        MinWidth="500"
        Height="555"
        SizeToContent="Width"
        WindowStartupLocation="CenterScreen"
        Title="Electrical Circuit">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml" />
                <ResourceDictionary>
                    <local:EnumToStringConverter x:Key="EnumToStringConverter" />
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}"
               BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <DataGrid Grid.Row="0"
                  Style="{StaticResource DataGridWithBorders}"
                  ItemsSource="{Binding CircuitAssemblies}"
                  Name="grid"
                  AutoGenerateColumns="False"
                  HeadersVisibility="Column"
                  CanUserDeleteRows="False"
                  CanUserAddRows="False"
                  EnableRowVirtualization="True"
                  IsReadOnly="True"
                  MinColumnWidth="12">
            <DataGrid.Columns>
                <DataGridTextColumn Header="Circuit Type"
                                    Binding="{Binding CircuitAssemblyType, Converter={StaticResource EnumToStringConverter}}" />
                <DataGridTextColumn Header="Circuit Status"
                                    Binding="{Binding CircuitModelStatus, Converter={StaticResource EnumToStringConverter}}">
                    <DataGridTextColumn.CellStyle>
                        <Style TargetType="DataGridCell"
                               BasedOn="{StaticResource DataGridCellStyle}">
                            <Setter Property="Foreground"
                                    Value="{StaticResource Red600}" />
                            <Style.Triggers>
                                <Trigger Property="IsSelected"
                                         Value="True">
                                    <Setter Property="Foreground"
                                            Value="{StaticResource Red600}" />
                                </Trigger>
                                <DataTrigger Binding="{Binding CircuitModelStatus, Converter={StaticResource EnumToStringConverter}}"
                                             Value="Valid">
                                    <Setter Property="Foreground"
                                            Value="{StaticResource Green600}" />
                                </DataTrigger>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding CircuitModelStatus, Converter={StaticResource EnumToStringConverter}}"
                                                   Value="Valid" />
                                        <Condition Binding="{Binding RelativeSource={RelativeSource Self}, Path=IsSelected}"
                                                   Value="True" />
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="Foreground"
                                            Value="{StaticResource Green600}" />
                                </MultiDataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGridTextColumn.CellStyle>
                </DataGridTextColumn>
                <DataGridTextColumn Header="Electrical Circuit"
                                    Binding="{Binding CircuitElectricalStatus, Converter={StaticResource EnumToStringConverter}}">
                    <DataGridTextColumn.CellStyle>
                        <Style TargetType="DataGridCell"
                               BasedOn="{StaticResource DataGridCellStyle}">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding CircuitElectricalStatus, Converter={StaticResource EnumToStringConverter}}"
                                             Value="Valid">
                                    <Setter Property="Foreground"
                                            Value="{StaticResource Green600}" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding CircuitElectricalStatus, Converter={StaticResource EnumToStringConverter}}"
                                             Value="Invalid">
                                    <Setter Property="Foreground"
                                            Value="{StaticResource Red600}" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGridTextColumn.CellStyle>
                </DataGridTextColumn>
                <DataGridTextColumn Header="Track Assignment"
                                    Binding="{Binding TrackAssignment}" />
                <DataGridTextColumn Header="Component Number"
                                    Binding="{Binding ComponentNumber}" />
                <DataGridTextColumn Header="Level"
                                    Binding="{Binding Level}" />
                <DataGridTemplateColumn Header="Panel Id"
                                        SortMemberPath="PanelId">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="{Binding Panel.Id}"
                                    Command="{Binding DataContext.IdCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                    CommandParameter="{Binding Panel.Id}"
                                    Height="25">
                                <Button.Style>
                                    <Style TargetType="Button"
                                           BasedOn="{StaticResource ButtonOutlinedBlue}">
                                        <Setter Property="Visibility"
                                                Value="Visible" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Panel}"
                                                         Value="{x:Null}">
                                                <Setter Property="Visibility"
                                                        Value="Collapsed" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="Panel Name"
                                    Binding="{Binding PanelName}" />
                <DataGridTemplateColumn Header="Track Id"
                                        SortMemberPath="TrackId">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="{Binding Track.Id}"
                                    Command="{Binding DataContext.IdCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                    CommandParameter="{Binding Track.Id}"
                                    Height="25">
                                <Button.Style>
                                    <Style TargetType="Button"
                                           BasedOn="{StaticResource ButtonOutlinedBlue}">
                                        <Setter Property="Visibility"
                                                Value="Visible" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Track}"
                                                         Value="{x:Null}">
                                                <Setter Property="Visibility"
                                                        Value="Collapsed" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="TrackType"
                                    Binding="{Binding Track.Name}"
                                    Width="400" />
                <DataGridTemplateColumn Header="Whip Id"
                                        SortMemberPath="WhipId">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="{Binding Whip.Id}"
                                    Command="{Binding DataContext.IdCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                    CommandParameter="{Binding Whip.Id}"
                                    Height="25">
                                <Button.Style>
                                    <Style TargetType="Button"
                                           BasedOn="{StaticResource ButtonOutlinedBlue}">
                                        <Setter Property="Visibility"
                                                Value="Visible" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Whip}"
                                                         Value="{x:Null}">
                                                <Setter Property="Visibility"
                                                        Value="Collapsed" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="WhipType"
                                    Binding="{Binding Whip.Name}"
                                    Width="400" />
                <DataGridTemplateColumn Header="Box Id"
                                        SortMemberPath="BoxId">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="{Binding Box.Id}"
                                    Command="{Binding DataContext.IdCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                    CommandParameter="{Binding Box.Id}"
                                    Height="25">
                                <Button.Style>
                                    <Style TargetType="Button"
                                           BasedOn="{StaticResource ButtonOutlinedBlue}">
                                        <Setter Property="Visibility"
                                                Value="Visible" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Box}"
                                                         Value="{x:Null}">
                                                <Setter Property="Visibility"
                                                        Value="Collapsed" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="BoxType"
                                    Binding="{Binding Box.Name}" />
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</Window>

