using System.Windows.Media;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Interfaces;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;

public sealed class LayerModel : BaseViewModel, IDisplayableLayer
{
    private Color _color = Colors.White;
    private string _linetypeDescription = string.Empty;
    private Guid _linetypeId;
    private string _linetypeName = string.Empty;
    private short _lineweight01mm = -3;
    private string _name = string.Empty;
    private byte _transparencyPct;

    public Guid Id { get; set; }

    public string Name
    {
        get => _name;
        set => Set(ref _name, value);
    }

    /// <summary>WPF Color for UI binding</summary>
    public Color Color
    {
        get => _color;
        set => Set(ref _color, value);
    }

    /// <summary>24-bit RGB color value (0xRRGGBB) for AutoCAD compatibility</summary>
    public uint Color24bit
    {
        get => (uint)((Color.R << 16) | (Color.G << 8) | Color.B);
        set
        {
            var r = (byte)((value >> 16) & 0xFF);
            var g = (byte)((value >> 8) & 0xFF);
            var b = (byte)(value & 0xFF);
            Color = Color.FromRgb(r, g, b);
        }
    }

    /// <summary>Lineweight in hundredths of mm (-3 = Default, 0-211)</summary>
    public short Lineweight01mm
    {
        get => _lineweight01mm;
        set
        {
            if (Set(ref _lineweight01mm, value))
            {
                OnPropertyChanged(nameof(LineweightDisplay));
                OnPropertyChanged(nameof(LineweightThickness));
            }
        }
    }

    /// <summary>Transparency 0-90%, where 0 = opaque</summary>
    public byte TransparencyPct
    {
        get => _transparencyPct;
        set
        {
            if (Set(ref _transparencyPct, value)) OnPropertyChanged(nameof(TransparencyDisplay));
        }
    }

    /// <summary>FK to corporate linetype</summary>
    public Guid LinetypeId
    {
        get => _linetypeId;
        set => Set(ref _linetypeId, value);
    }

    public string LinetypeName
    {
        get => _linetypeName;
        set
        {
            if (Set(ref _linetypeName, value)) OnPropertyChanged(nameof(LinetypeDisplay));
        }
    }

    public string LinetypeDescription
    {
        get => _linetypeDescription;
        set
        {
            if (Set(ref _linetypeDescription, value)) OnPropertyChanged(nameof(LinetypeDescriptionDisplay));
        }
    }

    public DateTime UpdatedUtc { get; set; }

    // Display properties for UI
    public string LinetypeDisplay => LinetypeName ?? string.Empty;

    public string LinetypeDescriptionDisplay => LinetypeDescription ?? string.Empty;

    public string LineweightDisplay => LineweightService.GetDisplayName(Lineweight01mm);

    public double LineweightThickness =>
        Math.Max(1, Math.Min(8, Lineweight01mm == -3 ? 1 : Math.Abs(Lineweight01mm) / 25.0));

    public string TransparencyDisplay => $"{TransparencyPct}%";

    public string ColorRgbDisplay
    {
        get
        {
            var color = System.Drawing.Color.FromArgb((int)Color24bit);
            return $"RGB({color.R},{color.G},{color.B})";
        }
    }

    public bool IsVisible { get; set; } = true;
    public bool IsLocked { get; set; } = false;

    #region Interface Implementations

    // ILayerInfo implementation
    string ILayerInfo.LinetypeName => LinetypeName;
    int ILayerInfo.TransparencyPct => TransparencyPct;

    // IDisplayableLayer implementation
    public string DisplayName => Name;
    public string ColorDisplay => ColorRgbDisplay;

    #endregion
}