using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using Point = FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data.Point;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Core.Elements
{
    public class BorderElement : BaseFaElement
    {
        private static readonly BorderConfiguration _config = new();

        public double Length { get; private set; }
        public double Width { get; private set; }
        public int BorderNumber { get; set; }
        public string BorderType { get; private set; }

        private BorderElement(int id, List<LineSegmentData> segments) : base(id, segments)
        {
            CalculateDimensions(segments);
        }

        public BorderElement() : base()
        {
        }

        public override ElementTypeConfiguration Configuration => _config;



        public static BorderElement CreateWithPlacement(
            int id,
            List<LineSegmentData> segments,
            Transform dwgTransform,
            Point center,
            double rotation,
            double length,
            double width,
            int borderNumber)
        {
            var transformedSegments = BaseFaElement.ApplyTransformToSegments(segments, dwgTransform);
            var element = new BorderElement(id, transformedSegments);

            element.Center = center;
            element.RotationAngle = rotation;
            element.IsRotated = Math.Abs(rotation) > 0.1;
            element.Length = length;
            element.Width = width;
            element.BorderNumber = borderNumber;

            return element;
        }



        protected override bool IsValidComponentForThisType(List<LineSegmentData> component, HashSet<LineSegmentData> usedSegments)
        {
            return component.Count == 4; // Border must be a rectangle (4 lines)
        }

        protected override void CalculateCenter(List<LineSegmentData> segments)
        {
            // Calculate center as average of all line endpoints
            var allPoints = segments.SelectMany(s => new[]
            {
                new Point(s.Data.startPoint.x, s.Data.startPoint.y, 0),
                new Point(s.Data.endPoint.x, s.Data.endPoint.y, 0)
            }).ToList();
            var centerX = allPoints.Average(p => p.X);
            var centerY = allPoints.Average(p => p.Y);
            var centerZ = allPoints.Average(p => p.Z);

            Center = new Point(centerX, centerY, centerZ);
        }

        protected override void CalculateRotationAngle(List<LineSegmentData> segments)
        {
            // For borders, rotation will be calculated during placement based on orientation
            RotationAngle = 0.0;
        }

        private void CalculateDimensions(List<LineSegmentData> segments)
        {
            // Calculate lengths of all sides
            var sideLengths = segments.Select(s => s.Data.length).ToList();

            // For rectangle: take min and max lengths directly
            Width = sideLengths.Min(); // Shortest side
            Length = sideLengths.Max(); // Longest side

            // Determine border type based on width (in inches)
            BorderType = DetermineBorderType(Width);
        }

        private static string DetermineBorderType(double widthInches)
        {
            // Convert inches to approximate ranges based on your image
            if (widthInches >= 0.5 && widthInches <= 2.0)
                return "A"; // 1/2in to 2in (default)
            else if (widthInches > 2.0 && widthInches <= 4.125)
                return "B"; // 2in to 4 1/2in (default)
            else if (widthInches > 4.125 && widthInches <= 6.5)
                return "C"; // 4 1/2in to 6 1/2in (Half) (default)
            else if (widthInches > 6.5 && widthInches <= 9.25)
                return "D"; // 4 1/2in to 5 3/4in (Full) (default)
            else if (widthInches > 9.25 && widthInches <= 8.5)
                return "E"; // 5 3/4in to 8 1/2in (Full) (default)
            else if (widthInches > 8.5 && widthInches <= 10.125)
                return "F"; // 8 1/2in to 10 1/2in (Full) (default)
            else if (widthInches > 10.125 && widthInches <= 12.0)
                return "G"; // 10 1/2in to 12in (Full) (default)
            else
                return "A"; // Default fallback
        }

        public override void SetElementSpecificParameters(FamilyInstance instance)
        {
            // Set Length parameter
            var lengthParam = instance.LookupParameter("Length");
            if (lengthParam != null && !lengthParam.IsReadOnly)
            {
                lengthParam.Set(Length / 12.0); // Convert inches to feet
            }

            // Set Desired Gap Distance parameter (width)
            var gapParam = instance.LookupParameter("Desired Gap Distance");
            if (gapParam != null && !gapParam.IsReadOnly)
            {
                gapParam.Set(Width / 12.0); // Convert inches to feet
            }

            // Set Comments parameter with border number
            var commentsParam = instance.LookupParameter("Comments");
            if (commentsParam != null && !commentsParam.IsReadOnly)
            {
                commentsParam.Set($"Border #{BorderNumber}");
            }
        }

        public override string ToString()
        {
            return $"Border: Type={BorderType}, Center=({Center.X:F3}, {Center.Y:F3}), Length={Length:F3}, Width={Width:F3}, Rotation={RotationAngle:F3}°";
        }
    }
}
