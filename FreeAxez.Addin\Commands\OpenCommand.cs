﻿using System.Diagnostics;
using System.IO;
using System.Reflection;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastucture;
using FreeAxez.Addin.Services;
using FreeAxez.Addin.Utils;
using FreeAxez.Core.Services;

namespace FreeAxez.Addin.Commands
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class OpenCommand : BaseExternalCommand
    {
        private readonly FreeAxezWebApiService _webApiService;
        private readonly string _fileVersion;
        public OpenCommand()
        {
            _webApiService = new FreeAxezWebApiService();
            _fileVersion = FileVersionInfo.GetVersionInfo
                               (Assembly.GetExecutingAssembly().Location)?.FileVersion;
        }
        public override Result Execute()
        {
            if (!WebView2Helper.CheckWebView2Installed())
            {
                return Result.Cancelled;
            }

            if (!_webApiService.CheckCurrentVersion(_fileVersion))
            {
                RevitDialogHelper.ShowNotification("You use not actual version of Revit Plugin. " +
                    "Please, go to: https://freeaxez.bimsmith.com/download             and get actual version");

                ApplicationExecutor.RunUpdatePage();
                return Result.Failed;
            }

            LogHelper.Information("Open command.");
            var projectIdGP = GlobalParameterHelper.GetProjectId(RevitManager.Document);
            var fileName = Path.GetFileName(RevitManager.Document.PathName);
            if (projectIdGP != null && projectIdGP.HasValue)
            {
                ApplicationExecutor.Run(projectIdGP.Value, fileName);
            }
            else
            {
                var revitUniqueId = RevitManager.Document.ProjectInformation.UniqueId;
                var id = _webApiService.GetProjectId(revitUniqueId, fileName);
                if(id != null && id.HasValue)
                {
                    using (Transaction trans = new Transaction(RevitManager.Document, "Set global parameter"))
                    {
                        trans.Start();
                        GlobalParameterHelper.SetProjectId(id.Value, RevitManager.Document);
                        trans.Commit();
                    }
                    RevitManager.Document.Save();
                    ApplicationExecutor.Run(id.Value, fileName);
                }
                else
                {
                    RevitDialogHelper.ShowNotification("Please Create new project first.");
                }
            }

            LogHelper.Information("Open command completed.");
            return Result.Succeeded;
        }
    }
}