﻿using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages;

public class HistoryPageVm : BasePageVm
{
    private ObservableCollection<LibraryItemHistoryEntryDto> _familyHistoryEntries;
    private ObservableCollection<LibraryItemDetailsHistoryEntryDto> _detailsHistoryEntries;
    private bool _isFamiliesTabSelected = true;
    private bool _isDetailsTabSelected;
    private bool _isLoading;

    public HistoryPageVm()
    {
        LoadDataCommand = new AsyncRelayCommand(async () => await LoadData());
        SelectFamiliesTabCommand = new RelayCommand(ExecuteSelectFamiliesTab);
        SelectDetailsTabCommand = new RelayCommand(ExecuteSelectDetailsTab);
        Task.Run(LoadData);
    }

    public ICommand LoadDataCommand { get; }
    public ICommand SelectFamiliesTabCommand { get; }
    public ICommand SelectDetailsTabCommand { get; }

    public bool IsLoading
    {
        get => _isLoading;
        set
        {
            _isLoading = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<LibraryItemHistoryEntryDto> FamilyHistoryEntries
    {
        get => _familyHistoryEntries;
        set
        {
            _familyHistoryEntries = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<LibraryItemDetailsHistoryEntryDto> DetailsHistoryEntries
    {
        get => _detailsHistoryEntries;
        set
        {
            _detailsHistoryEntries = value;
            OnPropertyChanged();
        }
    }

    public bool IsFamiliesTabSelected
    {
        get => _isFamiliesTabSelected;
        set
        {
            _isFamiliesTabSelected = value;
            OnPropertyChanged();
        }
    }

    public bool IsDetailsTabSelected
    {
        get => _isDetailsTabSelected;
        set
        {
            _isDetailsTabSelected = value;
            OnPropertyChanged();
        }
    }

    public async Task LoadData()
    {
        try
        {
            IsLoading = true;
            var result = await DataLoader.LoadHistoryPageData();
            if (result.FamilyHistoryEntries != null)
            {
                FamilyHistoryEntries = new ObservableCollection<LibraryItemHistoryEntryDto>(
                    result.FamilyHistoryEntries.OrderByDescending(x => x.Timestamp));
            }
            else
            {
                FamilyHistoryEntries = new ObservableCollection<LibraryItemHistoryEntryDto>();
            }

            if (result.DetailsHistoryEntries != null)
            {
                DetailsHistoryEntries = new ObservableCollection<LibraryItemDetailsHistoryEntryDto>(
                    result.DetailsHistoryEntries.OrderByDescending(x => x.Timestamp));
            }
            else
            {
                DetailsHistoryEntries = new ObservableCollection<LibraryItemDetailsHistoryEntryDto>();
            }
        }
        catch (Exception ex)
        {
            LogHelper.Error($"An error occurred while trying load History data: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void ExecuteSelectFamiliesTab(object parameter)
    {
        IsFamiliesTabSelected = true;
        IsDetailsTabSelected = false;
    }

    private void ExecuteSelectDetailsTab(object parameter)
    {
        IsFamiliesTabSelected = false;
        IsDetailsTabSelected = true;
    }

    protected override void Filter()
    {
        if (IsFamiliesTabSelected)
        {
            FilterFamilyHistory();
        }
        else
        {
            FilterDetailsHistory();
        }
    }

    private void FilterFamilyHistory()
    {
        var view = CollectionViewSource.GetDefaultView(FamilyHistoryEntries);
        if (view != null)
        {
            view.Filter = item =>
            {
                var entry = item as LibraryItemHistoryEntryDto;
                if (entry == null) return false;

                var textMatch = ItemFilter.FilterHistoryEntryBySearchText(entry, SearchText);
                return textMatch;
            };
            view.Refresh();
        }
    }

    private void FilterDetailsHistory()
    {
        var view = CollectionViewSource.GetDefaultView(DetailsHistoryEntries);
        if (view != null)
        {
            view.Filter = item =>
            {
                var entry = item as LibraryItemDetailsHistoryEntryDto;
                if (entry == null) return false;

                if (string.IsNullOrEmpty(SearchText)) return true;

                return entry.Name.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                       (entry.Description != null && entry.Description.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0) ||
                       entry.Action.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                       entry.CreatedBy.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                       entry.UpdatedBy.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0;
            };
            view.Refresh();
        }
    }
}