﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;

namespace FreeAxez.Addin.Services
{
    public class TransactionLineSelectionFilter : ISelectionFilter
    {
        private List<int> _selectedRegionIds;


        public TransactionLineSelectionFilter(List<int> selectedRegionIds)
        {
            _selectedRegionIds = selectedRegionIds;
        }


        public bool AllowElement(Element elem)
        {
            return _selectedRegionIds.Contains(elem.Id.GetIntegerValue());
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            return true;
        }
    }
}
