using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Workflow
{
    /// <summary>
    /// Unified result class for all GriddBuilder operations
    /// </summary>
    public class BuildResult
    {
        public bool Success { get; set; } = true;
        public string ErrorMessage { get; set; }
        public string DwgLinkName { get; set; }

        // Element placement results
        public int PlacedBaseUnits { get; set; }
        public int PlacedBaseUnitHalves { get; set; }
        public int PlacedBaseUnitQuarters { get; set; }
        public int PlacedPlateCorners { get; set; }
        public int PlacedPlateChannels { get; set; }
        public int PlacedBorderElements { get; set; }

        // Area processing results
        public int CreatedOverallAreaFloors { get; set; }
        public int CreatedGriddAreaFloors { get; set; }
        public string AreaProcessingMessage { get; set; }
        public List<Floor> CreatedFloors { get; set; } = new List<Floor>();


        public List<string> Warnings { get; set; } = new List<string>();
        public List<string> Errors { get; set; } = new List<string>();

        public int TotalPlacedElements => PlacedBaseUnits + PlacedBaseUnitHalves +
                                         PlacedBaseUnitQuarters + PlacedPlateCorners + PlacedPlateChannels + PlacedBorderElements;

        public int TotalCreatedFloors => CreatedOverallAreaFloors + CreatedGriddAreaFloors;

        public bool HasWarnings => Warnings.Any();
        public bool HasErrors => Errors.Any() || !string.IsNullOrEmpty(ErrorMessage);

        public void AddWarning(string warning)
        {
            if (!string.IsNullOrEmpty(warning))
                Warnings.Add(warning);
        }

        public void AddError(string error)
        {
            if (!string.IsNullOrEmpty(error))
                Errors.Add(error);
        }

        public string GetSummaryMessage()
        {
            if (!Success)
            {
                return $"Build failed for {DwgLinkName}:\n{ErrorMessage}";
            }

            var message = $"Build completed for {DwgLinkName}\n\n";

            if (TotalPlacedElements > 0)
            {
                message += "Successfully placed:\n";
                if (PlacedBaseUnits > 0) message += $"• Base Units: {PlacedBaseUnits}\n";
                if (PlacedBaseUnitHalves > 0) message += $"• Base Unit Halves: {PlacedBaseUnitHalves}\n";
                if (PlacedBaseUnitQuarters > 0) message += $"• Base Unit Quarters: {PlacedBaseUnitQuarters}\n";
                if (PlacedPlateCorners > 0) message += $"• Plate Corners: {PlacedPlateCorners}\n";
                if (PlacedPlateChannels > 0) message += $"• Plate Channels: {PlacedPlateChannels}\n";
                if (PlacedBorderElements > 0) message += $"• Border Elements: {PlacedBorderElements}\n";
                message += $"\nTotal: {TotalPlacedElements} elements";
            }
            else
            {
                message += "No elements were placed.";
            }

            if (TotalCreatedFloors > 0)
            {
                message += "\n\nFloors created from area data:\n";
                if (CreatedOverallAreaFloors > 0) message += $"• Overall Area floors: {CreatedOverallAreaFloors}\n";
                if (CreatedGriddAreaFloors > 0) message += $"• Gridd Area floors: {CreatedGriddAreaFloors}\n";
                message += $"Total: {TotalCreatedFloors} floors";
            }
            else if (!string.IsNullOrEmpty(AreaProcessingMessage))
            {
                message += $"\n\nArea processing: {AreaProcessingMessage}";
            }


            if (HasWarnings)
            {
                message += $"\n\n{Warnings.Count} warning(s) occurred.";
            }

            if (HasErrors)
            {
                message += $"\n\n{Errors.Count} error(s) occurred.";
            }

            return message;
        }

        /// <summary>
        /// Updates element placement counts
        /// </summary>
        public void UpdateElementPlacement(int baseUnits, int baseUnitHalves, int baseUnitQuarters,
            int plateCorners, int plateChannels, int borderElements)
        {
            PlacedBaseUnits = baseUnits;
            PlacedBaseUnitHalves = baseUnitHalves;
            PlacedBaseUnitQuarters = baseUnitQuarters;
            PlacedPlateCorners = plateCorners;
            PlacedPlateChannels = plateChannels;
            PlacedBorderElements = borderElements;
        }

        /// <summary>
        /// Updates area processing results
        /// </summary>
        public void UpdateAreaProcessing(int overallAreaFloors, int griddAreaFloors,
            List<Floor> createdFloors, string message = null)
        {
            CreatedOverallAreaFloors = overallAreaFloors;
            CreatedGriddAreaFloors = griddAreaFloors;
            CreatedFloors.AddRange(createdFloors ?? new List<Floor>());
            if (!string.IsNullOrEmpty(message))
                AreaProcessingMessage = message;
        }


        public static BuildResult CreateSuccess(string dwgLinkName, string warningMessage = null)
        {
            var result = new BuildResult
            {
                Success = true,
                DwgLinkName = dwgLinkName
            };

            if (!string.IsNullOrEmpty(warningMessage))
                result.AddWarning(warningMessage);

            return result;
        }

        public static BuildResult CreateFailure(string dwgLinkName, string errorMessage)
        {
            return new BuildResult
            {
                Success = false,
                DwgLinkName = dwgLinkName,
                ErrorMessage = errorMessage
            };
        }
    }
}
