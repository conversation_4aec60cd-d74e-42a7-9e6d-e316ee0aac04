﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Services;

namespace FreeAxez.Addin.AdditionalFunctional.CreateTapOff.Utils
{
    public static class TapOffPositionLocator
    {
        private const double ReceptacleGeometrySurfaceArea = 0.1; // Volume of geometry that emulates the position of the socket
        private const double ReceptaclePositionOffset = 0.0971549068074893; // Offset from centroid to exact position
        private const double ReceptaclePlateSurfaceAreaTolerance = 0.005;

        public static XYZ GetPostition(
            FamilyInstance track, Element whip, List<XYZ> receptaclePositions)
        {
            var w0 = (whip.Location as LocationCurve).Curve.GetEndPoint(0);
            var w1 = (whip.Location as LocationCurve).Curve.GetEndPoint(1);

            XYZ nearestPosition = null;
            var nearestDistance = double.PositiveInfinity;

            foreach (var position in receptaclePositions)
            {
                var distance = w0.DistanceTo(position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestPosition = position;
                }

                distance = w1.DistanceTo(position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestPosition = position;
                }
            }

            return nearestPosition;
        }

        public static List<XYZ> GetReceptaclePositions(FamilyInstance track)
        {
            var output = new List<XYZ>();

            var trackIds = track.GetSubComponentIds();
            if (trackIds.Count != 0) 
            { 
                foreach (var trackId in trackIds)
                {
                    var nestedTrack = RevitManager.Document.GetElement(trackId) as FamilyInstance;
                    output.AddRange(GetReceptaclePositionsForSingleTrack(nestedTrack));
                }
            }
            else
            {
                output.AddRange(GetReceptaclePositionsForSingleTrack(track));
            }

            return output;
        }

        private static List<XYZ> GetReceptaclePositionsForSingleTrack(FamilyInstance track)
        {
            var output = new List<XYZ>();

            var receptacleSolids = GeometryHelper.GetAllSolids(track)
                .Where(s => Math.Abs(s.SurfaceArea - ReceptacleGeometrySurfaceArea) < ReceptaclePlateSurfaceAreaTolerance)
                .ToList();

            if (receptacleSolids.Count == 0)
            {
                LogHelper.Warning(
                    $"Failed to get receptacle position from track family " +
                    $"'{track.Symbol.FamilyName} : {track.Symbol.Name}'");

                return output;
            }

            var trackLocation = (track.Location as LocationPoint).Point;
            foreach (var receptacleSolid in receptacleSolids) 
            {
                var centroid = receptacleSolid.ComputeCentroid();
                var position = new XYZ(centroid.X, centroid.Y, trackLocation.Z);

                position = position.Add(
                    track.HandOrientation.Negate().Multiply(ReceptaclePositionOffset));

                output.Add(position);
            }

            return output;
        }
    }
}
