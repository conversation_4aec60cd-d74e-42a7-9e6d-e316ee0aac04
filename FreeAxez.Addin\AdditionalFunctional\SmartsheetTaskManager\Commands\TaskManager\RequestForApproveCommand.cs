﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Abstractions;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Views;
using FreeAxez.Addin.Infrastructure;
using System.Windows.Threading;

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Commands.TaskManager
{
    public class RequestForApproveCommand : CommandBase
    {
        private readonly TaskManagerViewModel _taskManagerViewModel;
        private readonly TaskManagerView _taskManagerView;
        private readonly Dispatcher _dispatcher;
        private readonly TaskManagerHttpClientBase _taskManagerHttpClientService;

        public RequestForApproveCommand(TaskManagerViewModel taskManagerViewModel,
                                        TaskManagerView taskManagerView,
                                        Dispatcher dispatcher,
                                        TaskManagerHttpClientBase taskManagerHttpClientService)
        {
            _taskManagerViewModel = taskManagerViewModel;
            _taskManagerView = taskManagerView;
            _dispatcher = dispatcher;
            _taskManagerHttpClientService = taskManagerHttpClientService;
        }

        public override void Execute(object parameter)
        {
            _taskManagerViewModel.ErrorResponse = string.Empty;

            var sendRequestView = new SendRequestView
            {
                DataContext = new SendRequestViewModel(_taskManagerViewModel,
                                                       _dispatcher,
                                                       _taskManagerHttpClientService),
                Owner = _taskManagerView
            };

            sendRequestView.Show();
        }
    }
}
