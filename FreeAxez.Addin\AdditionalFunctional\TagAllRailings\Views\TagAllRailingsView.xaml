﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.TagAllRailings.Views.TagAllRailingsView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.TagAllRailings.Views"
        xmlns:vm="clr-namespace:FreeAxez.Addin.AdditionalFunctional.TagAllRailings.ViewModels"
        mc:Ignorable="d" 
        SizeToContent="WidthAndHeight"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Title="Tag All Railings">
    <Window.DataContext>
        <vm:TagAllRailingsViewModel/>
    </Window.DataContext>
    <Grid Margin="10,0,10,10">
        <StackPanel>
            <Label Content="Railing Tag Type:"/>
            <ComboBox ItemsSource="{Binding RailingTagTypes}" SelectedItem="{Binding SelectedRailingTagType}">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Name}"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
            <DockPanel Margin="0,10,0,0">
                <Label DockPanel.Dock="Left" Content="Length To Center Of Tag"/>
                <TextBox VerticalContentAlignment="Center" Text="{Binding LengthToCenterOfTag}"/>
            </DockPanel>
            <StackPanel>
                <CheckBox Margin="0,5,0,0" Content="Tag Railings With Empty Parameter" IsChecked="{Binding TagElementsWithEmptyParameter}" VerticalAlignment="Center"/>
                <CheckBox Margin="0,5,0,0" Content="Start Tag" IsChecked="{Binding StartTag}" VerticalAlignment="Center"/>
                <CheckBox Margin="0,5,0,0" Content="End Tag" IsChecked="{Binding EndTag}" VerticalAlignment="Center"/>
            </StackPanel>
            <GroupBox Margin="0,5,0,5" Header="Railing For Creating Tags">
                <StackPanel Margin="0,5,0,2">
                    <RadioButton Content="Visible In View" IsChecked="{Binding TagVisibleInView}" GroupName="tagregRailings"/>
                    <RadioButton Margin="0,5,0,0" Content="Select Railings" IsChecked="{Binding TagSelected}" GroupName="tagregRailings"/>
                </StackPanel>
            </GroupBox>
            <StackPanel Margin="0,10,0,0" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Width="100" Height="30" Content="Create" Command="{Binding CreateCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
                <Button Width="100" Margin="10,0,0,0" Content="Cancel" Command="{Binding CancelCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
            </StackPanel>
        </StackPanel>
    </Grid>
</Window>
