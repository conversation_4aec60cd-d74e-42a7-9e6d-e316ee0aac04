namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;

public interface IAutoCADInvoker
{
    Task<string> ExtractLayersAsync(string dwgFilePath, string outputFilePath);
    Task<bool> ReplaceLayersAsync(string dwgFilePath, string mappingFilePath);
    Task<bool> DeleteEmptyLayersAsync(string dwgFilePath);
    Task<bool> MergeLayersAsync(string dwgFilePath, string mergeDataFilePath);
}