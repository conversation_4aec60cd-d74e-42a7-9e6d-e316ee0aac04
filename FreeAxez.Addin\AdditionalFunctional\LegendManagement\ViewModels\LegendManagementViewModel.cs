﻿using FreeAxez.Addin.AdditionalFunctional.LegendManagement.Models;
using FreeAxez.Addin.AdditionalFunctional.LegendManagement.Stores;
using FreeAxez.Addin.AdditionalFunctional.LegendManagement.Views;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows.Data;

namespace FreeAxez.Addin.AdditionalFunctional.LegendManagement.ViewModels
{
    public class LegendManagementViewModel : WindowViewModel
    {
        private string _searchFilter = string.Empty;
        private readonly ICollectionView _sheetsCollectionView;
        private readonly List<SheetViewModel> _sheets;
        private readonly LegendManagementView _legendManagementView;
        private readonly ProjectLegendMappingsStore _projectLegendMappingsStore;
        //private readonly CorrectLegendMappingsStore _correctLegendMappingsStore;

        public LegendManagementViewModel(LegendManagementView legendManagementView,
                                         ProjectLegendMappingsStore projectLegendMappingsStore
                                         /*CorrectLegendMappingsStore correctLegendMappingsStore*/)
        {
            _legendManagementView = legendManagementView;
            _projectLegendMappingsStore = projectLegendMappingsStore;
            //_correctLegendMappingsStore = correctLegendMappingsStore;

            _sheets = SeedSheets();
            OnPropertyChanged(nameof(Sheets));

            _sheetsCollectionView = CollectionViewSource.GetDefaultView(Sheets);
            _sheetsCollectionView.Filter = SheetsCollectionViewFilterPredicate;
        }

        public string SearchFilter
        {
            get => _searchFilter;
            set
            {
                _searchFilter = value;
                OnPropertyChanged(nameof(SearchFilter));

                _sheetsCollectionView?.Refresh();
                OnPropertyChanged(nameof(Sheets));
            }
        }
        public List<SheetViewModel> Sheets => _sheets;

        private List<SheetViewModel> SeedSheets()
        {
            var output = new List<SheetViewModel>();

            foreach (KeyValuePair<CompositeSheetKey, List<Legend>> projectLegendMapping in _projectLegendMappingsStore.Dictionary)
            {
                var emptyLegendCollection = Enumerable.Empty<Legend>().ToList();

                var sheetViewModel = new SheetViewModel(projectLegendMapping.Key.SheetSize,
                                                        projectLegendMapping.Key.SheetSorting,
                                                        projectLegendMapping.Key.Sheet,
                                                        projectLegendMapping.Value,
                                                        emptyLegendCollection,
                                                        emptyLegendCollection);

                output.Add(sheetViewModel);
            }

            //foreach (KeyValuePair<CompositeSheetKey, List<Legend>> projectLegendMapping in _projectLegendMappingsStore.Dictionary)
            //{
            //    if (!_correctLegendMappingsStore.Dictionary.TryGetValue(projectLegendMapping.Key, out List<Legend> correctLegends))
            //    {
            //        correctLegends = Enumerable.Empty<Legend>().ToList();
            //    }

            //    var presentLegends = projectLegendMapping.Value
            //        .Where(projectLegend => correctLegends.Contains(projectLegend))
            //        .ToList();

            //    var redundantLegends = projectLegendMapping.Value
            //        .Where(projectLegend => !correctLegends.Contains(projectLegend))
            //        .ToList();

            //    var missingLegends = correctLegends
            //        .Except(presentLegends)
            //        .Except(redundantLegends)
            //        .ToList();

            //    output.Add(new SheetViewModel(
            //        projectLegendMapping.Key.SheetSize,
            //        projectLegendMapping.Key.SheetSorting,
            //        projectLegendMapping.Key.Sheet,
            //        presentLegends,
            //        missingLegends,
            //        redundantLegends));
            //}

            return output
                .OrderBy(sheet => sheet.SheetSize.Name)
                .ThenBy(sheet => sheet.SheetSorting.Name)
                .ThenBy(sheet => sheet.Sheet.SheetNumber)
                .ToList();
        }

        private bool SheetsCollectionViewFilterPredicate(object @object)
        {
            if (@object is SheetViewModel sheetViewModel)
            {
                string searchFilterUpper = _searchFilter.ToUpper();

                string sheetSizeNameUpper = sheetViewModel.SheetSize.Name.ToUpper();
                string sheetSortingNameUpper = sheetViewModel.SheetSorting.Name.ToUpper();
                string sheetNumberUpper = sheetViewModel.Sheet.SheetNumber.ToUpper();

                return sheetSizeNameUpper.Contains(searchFilterUpper)
                    || sheetSortingNameUpper.Contains(searchFilterUpper)
                    || sheetNumberUpper.Contains(searchFilterUpper)
                    || sheetViewModel.PresentLegends.Any(legend => legend.Name.ToUpper().Contains(searchFilterUpper));
                    //|| sheetViewModel.MissingLegends.Any(legend => legend.Name.ToUpper().Contains(searchFilterUpper))
                    //|| sheetViewModel.RedundantLegends.Any(legend => legend.Name.ToUpper().Contains(searchFilterUpper));
            }

            return false;
        }

        protected override void OnCancelCommandExecute(object sender)
        {
            _legendManagementView?.Close();
        }
    }
}
