﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;

namespace FreeAxez.Addin.AdditionalFunctional.DeleteViewBySize.Models
{
    public class ViewSize : BaseViewModel
    {
        private bool _isCheck;


        public bool IsCheck
        {
            get
            {
                return _isCheck;
            }
            set
            {
                _isCheck = value;
                OnPropertyChanged();
            }
        }

        public string Name { get; set; }

        public List<Element> Views { get; set; }
    }
}
