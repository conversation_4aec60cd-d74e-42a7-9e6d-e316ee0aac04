﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="FreeAxez.Addin.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="browserAppName" Type="System.String" Scope="Application">
      <Value Profile="(Default)">FreeAxez.BrowserApp.exe</Value>
    </Setting>
    <Setting Name="selectedRailingTypeName" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="deleteSelectedLineOption" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="selectedLineStyleName" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="baseOffsetForRailing" Type="System.Double" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="createRailingForEachLine" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="userParameterSets" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="TagAllRailingsLengthToCenterOfTag" Type="System.Double" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="TagAllRailingsStartTag" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="TagAllRailingsEndTag" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="TagAllRailingsSelectedRailingTagTypeName" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ExportCADPrefix" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ExportCADPath" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="FlexPipeToLineSelectedLineStyleName" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="TagAllRailingsTagElementsWithEmptyParameter" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="FloorBoxNumberingPrefix" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="LowVoltageScopeType" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="LowVoltageDeleteLines" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="LowVoltageSelectedRailingType" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="FloorBoxNumberingStartNumber" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="FloorBoxNumberingHorizontalDirection" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="TagAllRailingsTagVisibleInView" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="ElementNumberingParameterName" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ElementNumberingStartNumber" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="ElementNumberingPrefix" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ElementNumberingHorizontalDirection" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="FrameSelectedGridd40" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="exportGriddBOMPath" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="exportGriddBOMOpenFile" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="panelSchedulePlacementSelectedLevels" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="panelSchedulePlacementDeleteUnused" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="FrameSelectedCornerLong" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="exportPowerBomPath" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="exportPowerBomOpenFile" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="panelSchedulePlacementSheetSize" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="panelSchedulePlacementRevision" Type="System.String" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="panelScheduleCreationConnectedComponents" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="TagAllCurbsLengthToCenterOfTag" Type="System.Double" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="TagCurbsOffset" Type="System.Double" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="TagCurbsVisibleInView" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="TagAllFramesLengthToCenterOfTag" Type="System.Double" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="TagFramesVisibleInView" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="TransferViewTemplatesDeleteTemplates" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="TransferViewTemplatesSourceFilePath" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="EхportCADColor" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="RampGriddType" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="RampRampSlope" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="RampIsLeftSideSlopeChecked" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="RampIsRightSideSlopeChecked" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="TagRampLengthToCenterOfTag" Type="System.Double" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="TagRampIsVisibleInView" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="TagRampIsSelectRampComponents" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="TagTypeRump" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="ExportCutsheetFolderPath" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ExportCutsheetIsOpenDirectory" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="ExportForVRPath" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ExportForVRCopyFurniture" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="ExportPowerBomAccessories" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ExportGriddBomAccessories" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ExportCADIncludeFloatingInformation" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="GriddBuilderBaseUnitHeight" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">40</Value>
    </Setting>
    <Setting Name="SelectedAutoCADVersion" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="ExportForVRLightweightFamiliesPath" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
  </Settings>
</SettingsFile>