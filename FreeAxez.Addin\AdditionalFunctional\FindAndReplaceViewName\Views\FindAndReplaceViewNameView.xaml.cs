﻿using FreeAxez.Addin.AdditionalFunctional.FindAndReplaceViewName.ViewModels;
using System.Windows;
using System.Windows.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.FindAndReplaceViewName.Views
{
    public partial class FindAndReplaceViewNameView : Window
    {
        private bool _isUpdatingCheckboxes = false;

        public FindAndReplaceViewNameView()
        {
            InitializeComponent();
        }

        private void CheckBox_CheckedChanged(object sender, RoutedEventArgs e)
        {
            // Prevent recursive calls
            if (_isUpdatingCheckboxes)
                return;

            _isUpdatingCheckboxes = true;

            try
            {
                // Get the checkbox that was changed
                var checkbox = sender as CheckBox;
                if (checkbox == null)
                    return;

                // Get the new state
                bool newState = checkbox.IsChecked ?? false;

                if (ItemsListView.SelectedItems.Contains(checkbox.DataContext))
                {
                    // Get all selected items in the ListView
                    var selectedItems = ItemsListView.SelectedItems;

                    // Update all selected items
                    foreach (ViewNameModel item in selectedItems)
                    {
                        item.IsSelected = newState;
                    }
                }

                // Notify ViewModel about selection changes
                var viewModel = DataContext as FindAndReplaceViewNameViewModel;
                if (viewModel != null)
                {
                    viewModel.UpdateSelectedCount();
                }
            }
            finally
            {
                _isUpdatingCheckboxes = false;
            }
        }
    }
}
