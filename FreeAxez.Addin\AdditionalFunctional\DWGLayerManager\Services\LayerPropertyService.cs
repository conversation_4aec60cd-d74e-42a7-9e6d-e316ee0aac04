using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows.Data;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services
{
    /// <summary>
    /// Service for layer property operations and business logic
    /// </summary>
    public class LayerPropertyService
    {
        private readonly LayerDialogService _dialogService;

        public LayerPropertyService(LayerDialogService dialogService)
        {
            _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
        }

        /// <summary>
        /// Updates layer color through dialog selection
        /// </summary>
        public bool UpdateLayerColor(LayerModel layer)
        {
            if (layer == null) return false;

            var newColor = _dialogService.SelectColor(layer.Color);
            if (newColor.HasValue)
            {
                layer.Color = newColor.Value;
                return true;
            }

            return false;
        }

        /// <summary>
        /// Updates layer linetype through dialog selection
        /// </summary>
        public bool UpdateLayerLinetype(LayerModel layer)
        {
            if (layer == null) return false;

            var result = _dialogService.SelectLinetype(layer.LinetypeId);
            if (result != null)
            {
                layer.LinetypeId = result.LinetypeId;
                layer.LinetypeName = result.LinetypeName;
                layer.LinetypeDescription = result.LinetypeDescription;
                return true;
            }

            return false;
        }

        /// <summary>
        /// Updates layer lineweight through dialog selection
        /// </summary>
        public bool UpdateLayerLineweight(LayerModel layer)
        {
            if (layer == null) return false;

            var newLineweight = _dialogService.SelectLineweight(layer.Lineweight01mm);
            if (newLineweight.HasValue)
            {
                layer.Lineweight01mm = newLineweight.Value;
                return true;
            }

            return false;
        }

        /// <summary>
        /// Updates layer transparency through dialog selection
        /// </summary>
        public bool UpdateLayerTransparency(LayerModel layer)
        {
            if (layer == null) return false;

            var newTransparency = _dialogService.SelectTransparency(layer.TransparencyPct);
            if (newTransparency.HasValue)
            {
                layer.TransparencyPct = (byte)newTransparency.Value;
                return true;
            }

            return false;
        }

        /// <summary>
        /// Loads linetypes from file and adds them to the collection
        /// </summary>
        public int LoadAndAddLinetypes(ObservableCollection<LinetypeModel> linetypes)
        {
            if (linetypes == null) return 0;

            var loadedLinetypes = _dialogService.LoadLinetypesFromFile();
            if (loadedLinetypes?.Any() == true)
            {
                var addedCount = 0;
                foreach (var linetype in loadedLinetypes)
                {
                    if (!linetypes.Any(l => l.Name == linetype.Name))
                    {
                        linetypes.Add(linetype);
                        addedCount++;
                    }
                }

                if (addedCount > 0)
                {
                    MessageWindow.ShowDialog("Success", $"Loaded {addedCount} linetypes", MessageType.Success);
                }

                return addedCount;
            }

            return 0;
        }

        /// <summary>
        /// Validates layer properties
        /// </summary>
        public bool ValidateLayer(LayerModel layer)
        {
            if (layer == null) return false;
            if (string.IsNullOrWhiteSpace(layer.Name)) return false;
            
            // Add more validation rules as needed
            return true;
        }

        /// <summary>
        /// Creates a new layer with default properties
        /// </summary>
        public LayerModel CreateDefaultLayer(string name = null)
        {
            return new LayerModel
            {
                Id = Guid.NewGuid(),
                Name = name ?? "New Layer",
                Color = System.Windows.Media.Colors.White,
                LinetypeName = "Continuous",
                LinetypeDescription = "Solid line",
                Lineweight01mm = -3, // Default
                TransparencyPct = 0
            };
        }

        #region Layer Search and Filtering (from LayerSearchService)

        /// <summary>
        /// Creates a filtered view for layer collection
        /// </summary>
        public ICollectionView CreateFilteredView(ObservableCollection<LayerModel> layers)
        {
            var filteredView = CollectionViewSource.GetDefaultView(layers);
            filteredView.Filter = layer => ((LayerModel)layer).IsVisible;
            return filteredView;
        }

        /// <summary>
        /// Filters layers based on search text
        /// </summary>
        public void FilterLayers(ObservableCollection<LayerModel> layers, string searchText, ICollectionView filteredView)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                foreach (var layer in layers)
                {
                    layer.IsVisible = true;
                }
            }
            else
            {
                var searchLower = searchText.ToLower();
                foreach (var layer in layers)
                {
                    layer.IsVisible = layer.Name.ToLower().Contains(searchLower);
                }
            }

            filteredView?.Refresh();
        }

        #endregion
    }
}
