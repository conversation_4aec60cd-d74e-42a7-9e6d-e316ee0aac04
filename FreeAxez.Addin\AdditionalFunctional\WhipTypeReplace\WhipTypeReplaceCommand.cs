﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using Autodesk.Revit.UI;
using Autodesk.Revit.Attributes;
using FreeAxez.Addin.Infrastructure.UI;
using FreeAxez.Addin.AdditionalFunctional.WhipTypeReplace.Utils;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.Models;

namespace FreeAxez.Addin.AdditionalFunctional.WhipTypeReplace
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class WhipTypeReplaceCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var whipTypeSelector = new WhipTypeSelector();
            if (whipTypeSelector.ProjectHasNotTypes)
            {
                InfoDialog.ShowDialog("Whip Type Replace",
                    "There are no flex pipe types in the project that matched\n" +
                    "the template name like \"Access_Flooring-FreeAxez-GriddPower-Whip-08'\"");
                return Result.Cancelled;
            }

            var whips = Whip.CollectFloorBoxWhips().Select(w => w.Element).ToList();

            var invalidTypeName = 0;
            var correctType = 0;
            var changedType = 0;
            var wrongLength = 0;
            using (var t = new Transaction(RevitManager.Document, "Whip Type Replace"))
            {
                t.Start();

                foreach (var whip in whips)
                {
                    var fullLength = WhipFullLength.GetRiseLength(whip as FlexPipe);

                    var typeLengthString = whip.Name.Split('-').Last().Replace("'", "");  // -08' -10' -12' -14' -16' -20'
                    var typeLength = 0.0;
                    double.TryParse(typeLengthString, out typeLength);

                    if (typeLength == 0)
                    {
                        // Invalid type name without length
                        invalidTypeName++;
                    }
                    else if (typeLength - 1 < fullLength && fullLength <= typeLength)
                    {
                        // Correct range (lengthType - 1, lengthType]
                        // lengthType = 14'
                        // Length => Round => Correct?
                        // 12' 3/6" => 13' => -
                        // 13' => 13' => -
                        // 13' 4/6" => 14' => +
                        // 14' => 14' => +
                        // 14' 1/8" => 15' => -
                        correctType++;
                    }
                    else if (whipTypeSelector.GetWhipType(fullLength) != null)
                    {
                        whip.ChangeTypeId(whipTypeSelector.GetWhipType(fullLength).Id);
                        changedType++;
                    }
                    else
                    {
                        wrongLength++;
                    }
                }

                t.Commit();
            }

            var report = $"Invalid type name - {invalidTypeName},\n" +
                         $"Correct type - {correctType},\n" +
                         $"Changed type - {changedType},\n" +
                         $"Invalid length - {wrongLength}.";

            InfoDialog.ShowDialog("Whip Type Replace", report);

            return Result.Succeeded;
        }
    }
}
