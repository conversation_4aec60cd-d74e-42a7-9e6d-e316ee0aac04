﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.GrommetGriddReplacement.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Services;
using NetTopologySuite.Geometries;
using NetTopologySuite.Index.Strtree;
using Point = NetTopologySuite.Geometries.Point;

namespace FreeAxez.Addin.AdditionalFunctional.GrommetGriddReplacement
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class GrommetGriddReplacementCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var selectLevelResult = SelectLevelWindow.ShowDialog("Grommet Gridd Replacement", out List<Level> selectedLevels);
            if (selectLevelResult != true)
            {
                return Result.Cancelled;
            }
            else if (selectedLevels.Count() == 0)
            {
                MessageWindow.ShowDialog("No level has been selected.", MessageType.Notify);
                return Result.Cancelled;
            }

            // Validation
            var collector = new GrommetGriddReplacementFamilyCollector(selectedLevels);
            if (!collector.ValidateFamiliesExist(out string validationMessage))
            {
                LogHelper.Warning(validationMessage);
                MessageWindow.ShowDialog(validationMessage, MessageType.Notify);
                return Result.Cancelled;
            }
            else
            {
                if (!string.IsNullOrEmpty(validationMessage))
                {
                    LogHelper.Warning(validationMessage);
                    if (MessageWindow.ShowDialog(validationMessage, MessageType.Warning) != true)
                    {
                        return Result.Cancelled;
                    }
                }
            }

            var report = "";
            using (var t = new Transaction(RevitManager.Document, "Grommet Gridd Replacement"))
            {
                t.Start();

                var baseUnitIntersections = GetIntersections(collector.BaseUnits, collector.Grommets);
                var updatedBaseUnits = UpdateTypes(baseUnitIntersections.Select(i => i.Unit).ToList(), collector);

                var cornerIntersections = GetIntersections(collector.CornerPlates, collector.Grommets);
                var updatedCorners = UpdateTypes(cornerIntersections.Select(i => i.Unit).ToList(), collector);

                var channelIntersections = GetIntersections(collector.ChannelPlates, collector.Grommets);
                var updatedChannels = UpdateTypes(channelIntersections.Select(i => i.Unit).ToList(), collector);

                report = $"The number of processed elements:\n" +
                    $"Base Units - {updatedBaseUnits.Count};\n" +
                    $"Channels - {updatedChannels.Count};\n" +
                    $"Corners - {updatedCorners.Count}.";

                t.Commit();
            }

            MessageWindow.ShowDialog(report, MessageType.Success);

            return Result.Succeeded;
        }

        private List<(FamilyInstance Unit, FamilyInstance Grommet)> GetIntersections(
            List<FamilyInstance> units, List<FamilyInstance> grommets)
        {
            var output = new List<(FamilyInstance Unit, FamilyInstance Grommet)>();

            var rTree = new STRtree<FamilyInstance>();
            foreach (var unit in units)
            {
                var bb = unit.get_BoundingBox(null);
                var envelope = new Envelope(
                    new Coordinate(bb.Min.X, bb.Min.Y),
                    new Coordinate(bb.Max.X, bb.Max.Y));
                rTree.Insert(envelope, unit);
            }
            rTree.Build();

            foreach (var grommet in grommets)
            {
                var grommetLocation = (grommet.Location as LocationPoint).Point;
                var grommetLocationEnvelope = new Envelope(
                    grommetLocation.X, grommetLocation.X, grommetLocation.Y, grommetLocation.Y);

                var possibleUnits = rTree.Query(grommetLocationEnvelope);

                foreach (var unit in possibleUnits)
                {
                    if (!grommet.LevelId.Equals(unit.LevelId)) continue;

                    var unitConvexHull = GetConvexHullFromSolids(unit);
                    var grommetLocationPoint = new Point(grommetLocation.X, grommetLocation.Y);
                    
                    if (unitConvexHull.Covers(grommetLocationPoint))
                    {
                        output.Add((unit, grommet));
                        break;
                    }
                }
            }

            return output;
        }

        private Geometry GetConvexHullFromSolids(FamilyInstance unit)
        {
            var points = new List<Point>();

            var solids = GeometryHelper.GetAllSolids(unit);
            foreach (var solid in solids) 
            {
                foreach (Edge edge in solid.Edges)
                {
                    foreach (var point in edge.Tessellate())
                    {
                        points.Add(new Point(point.X, point.Y));
                    }
                }
            }

            var geometryFactory = new GeometryFactory();
            var multiPoint = geometryFactory.CreateMultiPoint(points.ToArray());

            return multiPoint.ConvexHull();
        }

        private List<FamilyInstance> UpdateTypes(List<FamilyInstance> instances, GrommetGriddReplacementFamilyCollector collector)
        {
            var output = new List<FamilyInstance>();

            foreach (var instance in instances)
            {
                var symbolId = collector.GetOutletSymbolId(instance.Symbol.Id);
                if (symbolId == null) continue;

                instance.ChangeTypeId(symbolId);
                output.Add(instance);
            }

            return output;
        }
   }
}
