using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Media;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Interfaces;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;

public class DwgLayerInfo : INotifyPropertyChanged, IDisplayableLayer, IMappableLayer
{
    // Mapping
    private Guid? _mappedLayerId;
    public string Name { get; set; }
    public Color Color { get; set; }
    public string Linetype { get; set; }
    public string LinetypeDescription { get; set; }
    public double Lineweight { get; set; }
    public int Transparency { get; set; }

    // Statistics
    public int LineCount { get; set; }
    public int PolylineCount { get; set; }
    public int TotalObjects { get; set; }

    // New detailed object counts from FastExtractLayersOptimized
    public int ModelSpaceObjects { get; set; }
    public int PaperSpaceObjects { get; set; }
    public int BlockDefinitionObjects { get; set; }
    public bool IsUsed { get; set; }
    public bool Purgeable { get; set; }

    // Computed property: can delete based on Purgeable flag from AutoCAD
    public bool CanDelete => Purgeable;

    // Computed property: is layer unused (regardless of protection)
    public bool IsUnused => ModelSpaceObjects == 0 && PaperSpaceObjects == 0 && BlockDefinitionObjects == 0;

    // Computed property: is layer protected from deletion
    public bool IsProtected => IsProtectedLayer(Name);

    private static bool IsProtectedLayer(string layerName)
    {
        if (string.IsNullOrEmpty(layerName))
            return false;

        return layerName.Equals("0", StringComparison.OrdinalIgnoreCase) ||
               layerName.Equals("Defpoints", StringComparison.OrdinalIgnoreCase);
    }

    public Guid? MappedLayerId
    {
        get => _mappedLayerId;
        set
        {
            if (_mappedLayerId != value)
            {
                _mappedLayerId = value;
                OnPropertyChanged();
            }
        }
    }

    public bool IsMarkedForDeletion { get; set; }
    public bool IsVisible { get; set; } = true; // For filtering

    // Display properties
    public string LineweightDisplay
    {
        get
        {
            if (Lineweight == -3) return "Default";
            if (Lineweight == -1) return "ByLayer";
            if (Lineweight == -2) return "ByBlock";
            if (Lineweight == 0) return ""; // Show empty for unparsed values
            return $"{Lineweight / 100.0:F2} mm";
        }
    }

    public string TransparencyDisplay
    {
        get
        {
            if (Transparency == -1) return "ByLayer";
            if (Transparency == -2) return "ByBlock";
            if (Transparency == 0) return "0%"; // 0% transparency means opaque
            return $"{Transparency}%";
        }
    }

    public string LinetypeDisplayName
    {
        get
        {
            // Show description if available, otherwise show name, otherwise empty
            if (!string.IsNullOrEmpty(LinetypeDescription))
                return LinetypeDescription;
            if (!string.IsNullOrEmpty(Linetype))
                return Linetype;
            return "";
        }
    }

    #region Interface Implementations

    // ILayerInfo implementation
    string ILayerInfo.LinetypeName => Linetype;
    int ILayerInfo.TransparencyPct => Transparency;

    // IDisplayableLayer implementation
    public string DisplayName => Name;
    string IDisplayableLayer.ColorDisplay => $"RGB({Color.R},{Color.G},{Color.B})";
    string IDisplayableLayer.LineweightDisplay => LineweightDisplay;
    string IDisplayableLayer.TransparencyDisplay => TransparencyDisplay;

    // IMappableLayer implementation
    public bool CanMap => !IsProtected;
    public bool IsMapped => MappedLayerId.HasValue;

    #endregion

    // INotifyPropertyChanged implementation
    public event PropertyChangedEventHandler PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}