﻿using System;
using System.Reflection;

namespace FreeAxezTest.Animation
{
    public class InternalMethodUIRevitItemConverter
    {
        public Autodesk.Windows.RibbonItem GetRibbonItem(Autodesk.Revit.UI.RibbonItem item)
        {
            Type itemType = item.GetType();

            var methodInfo = itemType.GetMethod(
                "getRibbonItem", BindingFlags.NonPublic | BindingFlags.Instance);

            var windowRibbonItem = methodInfo.Invoke(item, null);

            return windowRibbonItem as Autodesk.Windows.RibbonItem;
        }
    }
}
