﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.DeleteViewBySize.Views.DeleteViewBySizeView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.DeleteViewBySize.Views"
        xmlns:vm="clr-namespace:FreeAxez.Addin.AdditionalFunctional.DeleteViewBySize.ViewModels"
        mc:Ignorable="d" 
        MinHeight="450" MinWidth="350"
        Height="450" Width="350"
        WindowStartupLocation="CenterScreen"
        Title="Delete Size">
    <Window.DataContext>
        <vm:DeleteViewBySizeViewModel/>
    </Window.DataContext>
    <Grid Margin="5">
        <Grid.RowDefinitions>
            <RowDefinition/>
            <RowDefinition Height="35"/>
        </Grid.RowDefinitions>
        <DataGrid x:Name="dataGrid" ItemsSource="{Binding ViewSizes}" AutoGenerateColumns="False" CanUserAddRows="False" HeadersVisibility="Column" SelectionUnit="FullRow" SelectionMode="Extended">
            <DataGrid.Columns>
                <DataGridTemplateColumn Header="Delete" SortMemberPath="IsCheck">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <CheckBox HorizontalAlignment="Center" IsChecked="{Binding IsCheck, UpdateSourceTrigger=PropertyChanged}" Checked="CheckBox_Checked" Unchecked="CheckBox_Checked"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Binding="{Binding Name}" Header="Size" MinWidth="200" IsReadOnly="True"/>
            </DataGrid.Columns>
        </DataGrid>
        <StackPanel Margin="0,5,0,0" Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="Delete" Width="100" Command="{Binding DeleteViewsCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
            <Button Margin="5,0,0,0" Content="Cancel" Width="100" Command="{Binding CancelCommand}" CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
        </StackPanel>
    </Grid>
</Window>
