﻿using System.Collections.ObjectModel;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Interfaces;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Models;
using System.Windows.Forms;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Enums;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils
{
    public class RebaseManager
    {
        private readonly IProgressReporter _progressReporter;
        private readonly Document _source;
        private readonly Document _target;
        private readonly ObservableCollection<ViewTemplateMapping> _viewTemplateMappings;

        public RebaseManager(Document source, Document target,
            IProgressReporter progressReporter, ObservableCollection<ViewTemplateMapping> viewTemplateMappings)
        {
            _source = source;
            _target = target;
            _viewTemplateMappings = viewTemplateMappings;
            _progressReporter = progressReporter;
        }

        public void Rebase(CancellationToken cancellationToken)
        {
            _progressReporter.ReportStatus("Starting project synchronization...");
            using (var t = new TransactionGroup(_target, "Rebase Project"))
            {
                t.Start();

                try
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    //Step 1: Cleanup Target Model
                    _progressReporter.ReportStepStatus("Step 1/9: Cleaning up the target model...");
                    CleanupTargetModel(cancellationToken);
                    _progressReporter.ReportStatus("Target model cleanup completed.");
                    Application.DoEvents();

                    // Step 2: Delete Existing Sheets and Views
                    _progressReporter.ReportStepStatus("Step 2/9: Deleting old sheets and views from the target model...");
                    DeleteTargetSheetsAndViews(cancellationToken);
                    _progressReporter.ReportStatus("Sheets and views deleted successfully.");
                    Application.DoEvents();

                    var mapper = new ProjectMapper(_source, _target);

                    if (OptionsManager.ViewHandlingOption == ViewHandlingOption.UseSource || OptionsManager.ViewHandlingOption == ViewHandlingOption.Combine)
                    {
                        _progressReporter.ReportStepStatus("Step 2/9: Copy Legends and Drafting Views from Source to Target...");
                        AnnotationManager.CopyLegendsAndDraftingViews(_source, _target, _progressReporter, cancellationToken);
                    }

                    if (OptionsManager.ScheduleHandlingOption == ScheduleHandlingOption.UseSource|| OptionsManager.ScheduleHandlingOption == ScheduleHandlingOption.Combine)
                    {
                        _progressReporter.ReportStepStatus("Step 2/9: Copy Schedules from Source to Target...");
                        AnnotationManager.CopySchedules(_source, _target,mapper, _progressReporter, cancellationToken);
                    }

                    // Step 3: Fit Levels and Elevations
                    _progressReporter.ReportStepStatus("Step 3/9: Adjusting levels and grids...");
                    LevelAndGridManager.FitLevelsAndElevations(_source, _target, _progressReporter, cancellationToken);
                    LevelAndGridManager.DeleteTargetGrids(_target, _progressReporter, cancellationToken);
                    _progressReporter.ReportStatus("Levels and grids adjustment completed.");
                    Application.DoEvents();

                    // Step 4: Transfer Project Information and Parameters
                    _progressReporter.ReportStepStatus("Step 4/9: Transferring project information and parameters...");
                    ProjectDataManager.TransferProjectInformation(_source, _target, _progressReporter, cancellationToken);
                    ProjectDataManager.CopyProjectParameters(_source, _target, _progressReporter, cancellationToken);
                    _progressReporter.ReportStatus("Project information transfer completed.");
                    Application.DoEvents();


                    if (OptionsManager.CopyRevisions)
                    {
                        // Step 5: Copy Revisions and Settings
                        _progressReporter.ReportStepStatus("Step 5/9: Copying revisions and project settings...");
                        RevisionUtils.CopyRevisionsAndSettings(_source, _target);
                        _progressReporter.ReportStatus("Revisions and settings copied successfully.");
                        Application.DoEvents();
                    }

                    // Step 7: Rebase Views
                    _progressReporter.ReportStepStatus("Step 6/9: Synchronizing views between projects...");
                    var rebasedViews=RebaseViews(cancellationToken);
                    _progressReporter.ReportStatus("Views synchronization completed.");
                    Application.DoEvents();

                    // Step 6: Copy Elements
                    _progressReporter.ReportStepStatus("Step 7/9: Copying model elements...");
                    ModelManager.CopyElements(_source, _target, _progressReporter, cancellationToken);
                    _progressReporter.ReportStatus("Model elements copied successfully.");
                    Application.DoEvents();

                    // Step 7: Rebase Views
                    _progressReporter.ReportStepStatus("Step 8/9: Copying annotations between views...");
                    CopyAnnotationsBetweenViews(rebasedViews, cancellationToken);
                    _progressReporter.ReportStatus("Views synchronization completed.");
                    Application.DoEvents();

                    // Step 8: Copy Sheets from Source to Target
                    _progressReporter.ReportStepStatus("Step 9/9: Copying sheets from the source model...");
                    CopySheets(cancellationToken, mapper);
                    _progressReporter.ReportStatus("Sheets copied successfully.");
                    Application.DoEvents();

                    _progressReporter.ReportStatus("Project synchronization completed successfully.");
                }
                catch (OperationCanceledException)
                {
                    LogHelper.Warning("The operation was canceled by the user.");
                    _progressReporter.ReportStatus("The synchronization process was canceled by the user.");
                    t.RollBack();
                    return;
                }

                t.Assimilate();
            }
        }



        private void CleanupTargetModel(CancellationToken cancellationToken)
        {
            _progressReporter.ReportStatus("Cleaning up Target Model...");
            cancellationToken.ThrowIfCancellationRequested();

            ModelManager.CleanupTargetModel(_target, _progressReporter, cancellationToken);
        }

        private void DeleteTargetSheetsAndViews(CancellationToken cancellationToken)
        {
            _progressReporter.ReportStatus("Deleting sheets and views from target document...");
            cancellationToken.ThrowIfCancellationRequested();

            var targetDocumentSheets = SheetManager.CollectSheetsBySheetSize(_target, true);
            SheetManager.DeleteSheetsFromDocument(_target, targetDocumentSheets, _progressReporter, cancellationToken);

            var targetDocumentViews = ViewManager.CollectViewsBySheetSize(_target, true);
            ViewManager.DeleteViewsFromDocument(_target, targetDocumentViews, _progressReporter, cancellationToken);
        }

        private List<ViewPlan> RebaseViews(CancellationToken cancellationToken)
        {
            ModelManager.CopyVolumeOfInterestElements(_source,_target);

            _progressReporter.ReportStatus("Rebasing Views Between Projects...");
            cancellationToken.ThrowIfCancellationRequested();

            var rebasedViews=ViewManager.RebaseViewsBetweenProjects(_source, _target, _viewTemplateMappings, _progressReporter,
                cancellationToken);
            return rebasedViews;
        }

        private void CopyAnnotationsBetweenViews(List<ViewPlan> rebasedViews, CancellationToken cancellationToken)
        {
            _progressReporter.ReportStatus("Copying Annotations Between Views...");
            cancellationToken.ThrowIfCancellationRequested();
            AnnotationManager.CopyAnnotationsAndAdjust(_source, _target, rebasedViews,_progressReporter, cancellationToken);

        }

        private void CopySheets(CancellationToken cancellationToken, ProjectMapper mapper)
        {
            _progressReporter.ReportStatus("Copying sheets from source document...");
            cancellationToken.ThrowIfCancellationRequested();

            var sourceDocumentSheets = SheetManager.CollectSheetsBySheetSize(_source, true);
            SheetManager.CopySheetsFromSourceToTarget(_source, _target, sourceDocumentSheets, mapper, _progressReporter,
                cancellationToken);
        }
    }
}