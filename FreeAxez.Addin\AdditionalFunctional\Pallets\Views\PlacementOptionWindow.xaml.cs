﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.Pallets.Models;
using FreeAxez.Addin.AdditionalFunctional.Pallets.Utils;
using FreeAxez.Addin.Enums;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System.Windows;

namespace FreeAxez.Addin.AdditionalFunctional.Pallets.Views
{
    public partial class PlacementOptionWindow : Window
    {
        private UnitCollector _unitCollector;
        private FilledRegion _startRegion;
        private FilledRegion _placementRegion;
        private FilledRegion _protectedRegion;
        private FilledRegionSelectionFilter _filledRegionSelectionFilter;

        private static readonly string _requiredAreaGridd40 = PalletConfigurator
                .GetRequiredStartRegionArea(GriddType.Gridd40)
                .ToString();

        private static readonly string _requiredAreaGridd70 = PalletConfigurator
                .GetRequiredStartRegionArea(GriddType.Gridd70)
                .ToString();

        private static readonly double _requiredAreaToCreateStagingPlane = 400d;

        public PlacementOptionWindow(UnitCollector unitCollector)
        {
            InitializeComponent();

            _unitCollector = unitCollector;
            _filledRegionSelectionFilter = new FilledRegionSelectionFilter();
        }

        public static bool ShowDialog(ref UnitCollector unitCollector, out PlacementOption placementOption)
        {
            placementOption = new PlacementOption();
            var window = new PlacementOptionWindow(unitCollector);

            window.requiredArea.Content = $"Gridd40 - {_requiredAreaGridd40} SF; Gridd70 - {_requiredAreaGridd70} SF";
            window.requiredStagingArea.Content = $"{_requiredAreaToCreateStagingPlane} SF";

            if (window.ShowDialog() != true)
            {
                return false;
            }
            placementOption.VerticalDirection = (bool)window.verticalDirection.IsChecked;
            placementOption.StartRegion = new StartRegion(window._startRegion);
            placementOption.SelectPlacementRegion = (bool)window.selectPlacementRegion.IsChecked;

            if (placementOption.SelectPlacementRegion)
            {
                placementOption.PlacementRegion = new PlacementRegion(window._placementRegion);
            }

            placementOption.SelectProtectedRegion = window._protectedRegion != null;

            if (placementOption.SelectProtectedRegion)
            {
                placementOption.ProtectedRegion = new PlacementRegion(window._protectedRegion);
            }

            return true;
        }

        private void placePallets_Click(object sender, RoutedEventArgs e)
        {
            if (_startRegion == null)
            {
                InfoDialog.ShowDialog("Warning", "The starting region has not been selected.");
                return;
            }

            if (selectPlacementRegion.IsChecked == true &&  _placementRegion == null)
            {
                InfoDialog.ShowDialog("Warning", "The placement region has not been selected.");
                return;
            }

            this.DialogResult = true;
            this.Close();
        }

        private void startArea_Click(object sender, RoutedEventArgs e)
        {
            this.Hide();
            FilledRegion filledRegion = null;

            try
            {
                var reference = RevitManager.UIDocument.Selection.PickObject(Autodesk.Revit.UI.Selection.ObjectType.Element, _filledRegionSelectionFilter);
                filledRegion = RevitManager.Document.GetElement(reference) as FilledRegion;
            }
            catch
            {
                
            }

            if (filledRegion == null)
            {
                InfoDialog.ShowDialog("Warning", "The starting region has not been selected.\n" +
                    "The start region must be rectangular, without holes and be orthogonal to the main axes.");
            }
            else
            {
                var selectedRegionArea = Math.Round(filledRegion.get_Parameter(BuiltInParameter.HOST_AREA_COMPUTED).AsDouble());
                var requiredArea = PalletConfigurator.GetRequiredStartRegionArea(_unitCollector.GriddType);
                var tolerance = Math.Round(requiredArea * 0.1);

                if (selectedRegionArea >= requiredArea * 2)
                {
                    InfoDialog.ShowDialog("Warning", 
                        $"The selected region is very large. The area is {selectedRegionArea} SF, twice the recommended area.\n" +
                        $"Recommended area for this grid type: {requiredArea} SF +-10%.");
                }
                else if (selectedRegionArea <= requiredArea / 2)
                {
                    InfoDialog.ShowDialog("Warning",
                        $"The selected region is very small. Area {selectedRegionArea} SF, half the recommended area.\n" +
                        $"Recommended area for this grid type: {requiredArea} SF +-10%.");
                }
                else if (selectedRegionArea <= requiredArea - tolerance || selectedRegionArea >= requiredArea + tolerance)
                {
                    InfoDialog.ShowDialog("Warning",
                        "Possibly incorrect placement of pallets.\n" +
                        $"The selected region area is {selectedRegionArea} SF differs significantly from the recommended area.\n" +
                        $"Recommended area for this grid type: {requiredArea} SF +-10%.");

                    _startRegion = filledRegion;
                    startArea.Content = $"Id: {_startRegion.Id.GetIntegerValue()}";
                }
                else if (selectedRegionArea >= requiredArea - tolerance && selectedRegionArea <= requiredArea + tolerance)
                {
                    _startRegion = filledRegion;
                    startArea.Content = $"Id: {_startRegion.Id.GetIntegerValue()}";
                }
            }

            this.ShowDialog();
        }

        private void placementArea_Click(object sender, RoutedEventArgs e)
        {
            this.Hide();
            FilledRegion filledRegion = null;

            try
            {
                var reference = RevitManager.UIDocument.Selection.PickObject(Autodesk.Revit.UI.Selection.ObjectType.Element, _filledRegionSelectionFilter);
                filledRegion = RevitManager.Document.GetElement(reference) as FilledRegion;
            }
            catch
            {

            }

            if (filledRegion == null)
            {
                _unitCollector.PlacementRegion = null;
                placementArea.Content = "Select";
                InfoDialog.ShowDialog("Warning", "The placement region has not been selected.");
            }
            else
            {
                _unitCollector.PlacementRegion = new PlacementRegion(filledRegion);

                var selectedRegionArea = Math.Round(filledRegion.get_Parameter(BuiltInParameter.HOST_AREA_COMPUTED).AsDouble());
                var requiredArea = PalletConfigurator.GetRequiredStartRegionArea(_unitCollector.GriddType);

                if (selectedRegionArea <= requiredArea)
                {
                    InfoDialog.ShowDialog("Warning",
                        $"The placement region cannot be less than the required area of {requiredArea} SF.\n" +
                        $"Area of the selected region is {selectedRegionArea} SF.");
                }
                else
                {
                    _placementRegion = filledRegion;
                    placementArea.Content = $"Id: {_placementRegion.Id.GetIntegerValue()}";
                }
            }

            this.ShowDialog();
        }

        private void selectPlacementRegion_Checked(object sender, RoutedEventArgs e)
        {
            if (selectPlacementRegion.IsChecked == true)
            {
                selectPlacementRegionButton.Visibility = System.Windows.Visibility.Visible;
            }
            else
            {
                selectPlacementRegionButton.Visibility = System.Windows.Visibility.Collapsed;

                _placementRegion = null;
                _unitCollector.PlacementRegion = null;
                placementArea.Content = "Select";
            }
        }

        private void protectedRegion_Click(object sender, RoutedEventArgs e)
        {
            this.Hide();
            FilledRegion filledRegion = null;

            try
            {
                var reference = RevitManager.UIDocument.Selection.PickObject(Autodesk.Revit.UI.Selection.ObjectType.Element, _filledRegionSelectionFilter);
                filledRegion = RevitManager.Document.GetElement(reference) as FilledRegion;
            }
            catch
            {

            }

            _protectedRegion = filledRegion;
            if (_protectedRegion == null)
            {
                // Since this property is optional, it should be possible to reset
                protectedRegion.Content = "Select";
                InfoDialog.ShowDialog("Warning", "The protected region has not been selected.");
            }
            else
            {
                protectedRegion.Content = $"Id: {_protectedRegion.Id.GetIntegerValue()}";
            }

            this.ShowDialog();
        }
    }
}
