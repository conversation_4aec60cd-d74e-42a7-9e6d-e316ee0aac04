﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class FrameSingle : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
            },
            FamilyNamesEndWith = new List<string>
            {
                "Frame-Single"
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public FrameSingle(Element element) : base(element)
        {
        }

        public static List<FrameSingle> Collect()
        {
            return FamilyCollector.Instances.Select(g => new FrameSingle(g)).ToList();
        }
    }
}
