﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls;

/// <summary>
///     Interaction logic for NavButton.xaml
/// </summary>
public partial class NavButton : UserControl
{
    public static readonly DependencyProperty CommandProperty = DependencyProperty.Register(
        "Command", typeof(ICommand), typeof(NavButton));

    public static readonly DependencyProperty IconTemplateProperty = DependencyProperty.Register(
        "IconTemplate", typeof(ControlTemplate), typeof(NavButton));

    public static readonly DependencyProperty ButtonTextProperty = DependencyProperty.Register(
        "ButtonText", typeof(string), typeof(NavButton));

    public static readonly DependencyProperty IsCheckedProperty = DependencyProperty.Register(
        "IsChecked", typeof(bool), typeof(NavButton), new PropertyMetadata(false));

    public static readonly DependencyProperty GroupNameProperty = DependencyProperty.Register(
        "GroupName", typeof(string), typeof(NavButton), new PropertyMetadata("DefaultGroup"));

    public NavButton()
    {
        InitializeComponent();
    }

    public string GroupName
    {
        get => (string)GetValue(GroupNameProperty);
        set => SetValue(GroupNameProperty, value);
    }

    public ICommand Command
    {
        get => (ICommand)GetValue(CommandProperty);
        set => SetValue(CommandProperty, value);
    }

    public ControlTemplate IconTemplate
    {
        get => (ControlTemplate)GetValue(IconTemplateProperty);
        set => SetValue(IconTemplateProperty, value);
    }

    public string ButtonText
    {
        get => (string)GetValue(ButtonTextProperty);
        set => SetValue(ButtonTextProperty, value);
    }

    public bool IsChecked
    {
        get => (bool)GetValue(IsCheckedProperty);
        set => SetValue(IsCheckedProperty, value);
    }
}