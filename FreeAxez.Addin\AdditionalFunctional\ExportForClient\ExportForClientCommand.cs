﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.ExportForClient.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System;

namespace FreeAxez.Addin.AdditionalFunctional.ExportForClient
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class ExportForClientCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var savedDocumentPath = string.Empty;
            Document exportDocument = null;
            Exception exception = null;

            try
            {
                if (!RevitFile.SaveAs(RevitManager.Document, out savedDocumentPath))
                {
                    return Result.Cancelled;
                }

                exportDocument = RevitFile.OpenDocumentInBackground(RevitManager.Application, savedDocumentPath);

                // TODO: Wait for families. Take to much time when big project!
                //var swapManager = new FamilySwapManager(exportDocument);
                //swapManager.ReplaceFamilies();

                var purgeManager = new PurgeManager(exportDocument);
                purgeManager.PurgeTypes();
                purgeManager.DeleteViewTemplates();
                // TODO: Take too much time. Need to create a list of material ids that won't try to delete
                //purgeManager.PurgeMaterials();
                //purgeManager.PurgeAssets();
            }
            catch (Exception ex)
            {
                exception = ex;
            }
            finally
            {
                if (exportDocument != null)
                {
                    RevitFile.SaveAndClose(exportDocument);
                }

                RevitFile.DeleteBackupFiles(savedDocumentPath);
            }

            if (exception != null)
            {
                InfoDialog.ShowDialog("Error", exception.Message);
            }
            else
            {
                InfoDialog.ShowDialog("Report", "Export completed.");
            }

            return Result.Succeeded;
        }
    }
}
