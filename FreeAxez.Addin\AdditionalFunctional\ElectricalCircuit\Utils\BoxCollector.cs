﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils
{
    public class BoxCollector
    {
        private readonly LevelHelper _levelHelper;


        public BoxCollector(LevelHelper levelHelper)
        {
            _levelHelper = levelHelper;
        }


        public List<FamilyInstance> GetBoxFamilyInstances()
        {
            var floorBoxAbbreviation = new List<string>() { "-FB-", "-DeskMount-", "-Desk_Mount-" };

            var floorBoxes = new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_ElectricalFixtures)
                .WhereElementIsNotElementType()
                .Cast<FamilyInstance>()
                .Where(i => i.SuperComponent == null)
                .Where(i => floorBoxAbbreviation.Any(v => i.Symbol.FamilyName.Contains(v)))
                .Where(_levelHelper.BelongsToTheSelectedLevel)
                .ToList();

            return floorBoxes;
        }
    }
}
