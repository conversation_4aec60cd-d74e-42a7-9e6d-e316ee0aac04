<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls.DetailsFileInfo"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             mc:Ignorable="d"
             d:DesignHeight="100" d:DesignWidth="400">
    <UserControl.Resources>
        <ResourceDictionary>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid Margin="0 0 0 5">
        <Border CornerRadius="5"
                BorderBrush= "LightGray"
                BorderThickness="1">
            <Grid Margin="5 5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*" MinWidth="300"/>
                    <ColumnDefinition Width="25"/>
                </Grid.ColumnDefinitions>
                <Grid Grid.Column="0"
                      Margin="5 0" VerticalAlignment="Top" Height="150">
                    <Image x:Name="DetailsImage"
                           Width="150"
                           Height="150"
                           Source="{Binding FilePreview}"
                           Stretch="Uniform" />
                    <Button x:Name="ChooseImage"
                            VerticalAlignment="Bottom"
                            HorizontalAlignment="Right"
                            Command="{Binding ChooseImageCommand}"
                            Style="{StaticResource RoundIconButton}">
                        <ContentControl Template="{StaticResource PencilIcon}" HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Button>
                </Grid>
                <Grid Grid.Column="1" Margin="5 0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="10"/>
                        <RowDefinition Height="*" MinHeight="120"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0"
                        TextTrimming="CharacterEllipsis"
                        Text="{Binding FileName, StringFormat='File Name: {0}'}"
                        Style="{StaticResource TextBase}"
                        Margin="0 2"/>
                    <TextBlock Grid.Row="1"
                               Text="{Binding FileType, StringFormat='File Type: {0}'}"
                               Style="{StaticResource TextBase}"
                               Margin="0 2"/>
                    <TextBlock Grid.Row="2"
                               Text="{Binding RevitVersion, StringFormat='Revit: {0}'}"
                               Style="{StaticResource TextBase}"
                               Foreground="{StaticResource Blue500}"
                               Margin="0 2"/>
                    <TextBlock Grid.Row="3"
                               Text="{Binding FileSize, StringFormat='File Size: {0}'}"
                               Style="{StaticResource TextBase}"
                               Margin="0 2"/>
                    <TextBlock Grid.Row="4"
                               Text="{Binding CreatedBy, StringFormat='Created By: {0}'}"
                               Style="{StaticResource TextBase}"
                               TextTrimming="CharacterEllipsis"
                               Margin="0 2"/>
                    <TextBlock Grid.Row="5"
                               Text="{Binding DateCreated, StringFormat='Date Created: {0}'}"
                               Style="{StaticResource TextBase}"
                               TextTrimming="CharacterEllipsis"
                               Margin="0 2"/>
                    <TextBlock Grid.Row="6"
                               Text="{Binding LastDateUpdated, StringFormat='Last Date Updated: {0}'}"
                               Style="{StaticResource TextBase}"
                               TextTrimming="CharacterEllipsis"
                               Margin="0 2"/>
                    <StackPanel Grid.Row="8" VerticalAlignment="Stretch">
                        <TextBlock Text="Description"
                                   Margin="0 5 0 2"
                                   Style="{StaticResource TextH6}"/>
                        <TextBox Text="{Binding Description,
                            UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                 Style="{StaticResource UiTextBox}"
                                 MinHeight="80"
                                 TextWrapping="Wrap"
                                 Tag="Enter details description"
                                 HorizontalAlignment="Stretch"
                                 VerticalAlignment="Stretch"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"/>
                    </StackPanel>
                </Grid>
                <Button Grid.Column="2"
                        Command="{Binding RejectFileCommand}"
                        Style="{StaticResource CloseExtraSmallButtonStyle}"
                        VerticalAlignment="Top"
                        HorizontalAlignment="Right"
                        Content="X"
                        Visibility="{Binding ShowRejectButton, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
