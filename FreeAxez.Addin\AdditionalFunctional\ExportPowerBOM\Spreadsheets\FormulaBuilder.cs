﻿using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Spreadsheets
{
    public class FormulaBuilder
    {
        private readonly string _cellPlaceholder = @"<<\d+:\d+>>";
        private Regex _cellPlaceholderRegex;


        public FormulaBuilder()
        {
            _cellPlaceholderRegex = new Regex(_cellPlaceholder);
        }


        public string ReplacePlaceholder(int startColumn, int startRow, string formula)
        {
            while (Regex.IsMatch(formula, _cellPlaceholder))
            {
                var cellPlaceholder = Regex.Match(formula, _cellPlaceholder).Value;
                var cellValue = GetColumnLetter(cellPlaceholder, startColumn) + GetRowNumber(cellPlaceholder, startRow);
                formula = _cellPlaceholderRegex.Replace(formula, cellValue, 1);
            }

            return formula;
        }

        private string GetColumnLetter(string cellPlaceholder, int startColumn)
        {
            int.TryParse(Regex.Matches(cellPlaceholder, @"\d+")[0].Value, out int column);
            return ColumnLetter(column + startColumn);
        }

        private string GetRowNumber(string cellPlaceholder, int startRow)
        {
            int.TryParse(Regex.Matches(cellPlaceholder, @"\d+")[1].Value, out int row);
            return (row + startRow).ToString();
        }

        private string ColumnLetter(int columnNumber)
        {
            string columnLetter = "";
            while (columnNumber > 0)
            {
                columnNumber--;
                columnLetter = (char)('A' + columnNumber % 26) + columnLetter;
                columnNumber = columnNumber / 26;
            }
            return columnLetter;
        }
    }
}
