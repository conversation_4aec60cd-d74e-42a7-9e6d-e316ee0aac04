using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data;
using Point = FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Data.Point;

namespace FreeAxez.Addin.AdditionalFunctional.AcadToRevitGriddBuilder.Core.Elements;

public class PlateCorner : BaseFaElement
{
    private static readonly PlateCornerConfiguration _config = new();

    private PlateCorner(int id, List<LineSegmentData> segments) : base(id, segments)
    {
    }

    public PlateCorner() : base()
    {
    }

    public override ElementTypeConfiguration Configuration => _config;

    protected override void CalculateCenter(List<LineSegmentData> segments)
    {
        // Find 4 base lines with corner length
        var cornerLines = segments.Where(d => Configuration.IsValidLength(d.Segment.Length)).ToList();

        if (cornerLines.Count != 4)
            throw new InvalidOperationException("PlateCorner requires exactly 4 valid corner-length segments");

        // Calculate center as geometric center of all corner points
        var allCornerPoints = cornerLines.SelectMany(s => new[] { s.Segment.P0, s.Segment.P1 }).ToList();
        var centerX = allCornerPoints.Average(p => p.X);
        var centerY = allCornerPoints.Average(p => p.Y);
        Center = new Point(centerX, centerY, 0);
    }

    protected override void CalculateRotationAngle(List<LineSegmentData> segments)
    {
        // Find 4 base lines with corner length
        var cornerLines = segments.Where(d => Configuration.IsValidLength(d.Segment.Length)).ToList();
        if (cornerLines.Count != 4)
        {
            RotationAngle = 0;
            return;
        }

        // Calculate rotation same as BaseUnit
        var angles = cornerLines.Select(s => s.Data.angle).ToArray();

        // Find the most common cardinal direction
        var cardinalCounts = new Dictionary<double, int>();
        foreach (var angle in angles)
        {
            var normalized = NormalizeAngle(angle);
            var cardinal = SnapToCardinal(normalized);
            if (cardinalCounts.ContainsKey(cardinal))
                cardinalCounts[cardinal]++;
            else
                cardinalCounts[cardinal] = 1;
        }

        if (cardinalCounts.Count == 0)
        {
            RotationAngle = 0;
            return;
        }

        var dominantCardinal = cardinalCounts.OrderByDescending(kvp => kvp.Value).First().Key;

        // Calculate rotation to align with base orientation
        double targetRotation = 0;
        if (Math.Abs(dominantCardinal - 90) < 5 || Math.Abs(dominantCardinal - 270) < 5)
        {
            targetRotation = 90; // Rotate 90 degrees if predominantly vertical
        }

        RotationAngle = targetRotation;
    }


    protected override bool IsValidComponentForThisType(List<LineSegmentData> component, HashSet<LineSegmentData> usedSegments)
    {
        if (component.All(d => usedSegments.Contains(d)))
            return false;

        // Check if component has exactly 4 corner lines
        var cornerLines = component.Where(d => !usedSegments.Contains(d) &&
                                               Configuration.IsValidLength(d.Segment.Length)).ToList();
        return cornerLines.Count == 4;
    }


}