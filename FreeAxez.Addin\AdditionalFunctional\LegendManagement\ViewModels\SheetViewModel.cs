﻿using FreeAxez.Addin.AdditionalFunctional.LegendManagement.Models;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System.Collections.Generic;

namespace FreeAxez.Addin.AdditionalFunctional.LegendManagement.ViewModels
{
    public class SheetViewModel : ViewModelBase
    {
        public SheetViewModel(SheetSize sheetSize, SheetSorting sheetSorting, Sheet sheet, List<Legend> presentLegends, List<Legend> missingLegends, List<Legend> redundantLegends)
        {
            SheetSize = sheetSize;
            SheetSorting = sheetSorting;
            Sheet = sheet;
            PresentLegends = presentLegends;
            MissingLegends = missingLegends;
            RedundantLegends = redundantLegends;
        }

        public SheetSize SheetSize { get; }
        public SheetSorting SheetSorting { get; }
        public Sheet Sheet { get; }
        public List<Legend> PresentLegends { get; }
        public List<Legend> MissingLegends { get; }
        public List<Legend> RedundantLegends { get; }
    }
}
