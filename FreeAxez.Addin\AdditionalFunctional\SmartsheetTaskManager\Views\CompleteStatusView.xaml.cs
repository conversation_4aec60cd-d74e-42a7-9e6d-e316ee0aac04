﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Imaging;

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Views;

public partial class CompleteStatusView : Window
{
    public CompleteStatusView()
    {
        Icon = new BitmapImage(new Uri(
        "pack://application:,,,/FreeAxez.Addin;component/AdditionalFunctional/SmartsheetTaskManager/Resources/Icons/smartsheet16.png"));

        InitializeComponent();

        this.SizeChanged += CompleteStatusView_SizeChanged;
    }

    private void CompleteStatusView_SizeChanged(object sender, SizeChangedEventArgs sizeChangedEventArgs)
    {
        if (sizeChangedEventArgs.HeightChanged && sizeChangedEventArgs.PreviousSize.Height != 0)
        {
            double windowHeightSizeDiferrence = sizeChangedEventArgs.NewSize.Height
                - sizeChangedEventArgs.PreviousSize.Height;

            PlaceholderTextBox.MaxHeight += windowHeightSizeDiferrence;
        }
    }

    private void Window_Closed(object sender, EventArgs e)
    {
        if (DataContext is CompleteStatusViewModel completeStatusViewModel)
        {
            completeStatusViewModel.CancellationTokenSource.Cancel();
        }
    }

    private void PlaceholderTextBox_PreviewKeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            var textBox = (TextBox)sender;
            int caretIndex = textBox.CaretIndex;
            textBox.Text = textBox.Text.Insert(caretIndex, Environment.NewLine);
            textBox.CaretIndex = caretIndex + Environment.NewLine.Length;
            e.Handled = true;
        }
    }

    private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
    {
        var completeStatusView = sender as CompleteStatusView;
        completeStatusView.Owner = null;
    }
}
