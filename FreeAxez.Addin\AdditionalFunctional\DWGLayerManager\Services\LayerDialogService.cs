using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services
{
    /// <summary>
    /// Service for showing layer-related dialogs and returning results
    /// </summary>
    public class LayerDialogService
    {
        private readonly DwgLayerManagerApiService _apiService;

        public LayerDialogService(DwgLayerManagerApiService apiService)
        {
            _apiService = apiService ?? throw new ArgumentNullException(nameof(apiService));
        }

        /// <summary>
        /// Shows color selection dialog
        /// </summary>
        public System.Windows.Media.Color? SelectColor(System.Windows.Media.Color currentColor)
        {
            var colorDialog = new ColorDialog
            {
                Color = Color.FromArgb(currentColor.A, currentColor.R, currentColor.G, currentColor.B)
            };

            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                return System.Windows.Media.Color.FromArgb(
                    colorDialog.Color.A,
                    colorDialog.Color.R,
                    colorDialog.Color.G,
                    colorDialog.Color.B);
            }

            return null;
        }

        /// <summary>
        /// Shows linetype selection dialog
        /// </summary>
        public LinetypeSelectionResult SelectLinetype(Guid currentLinetypeId)
        {
            var dialog = new SelectLinetypeWindow();
            var viewModel = new SimpleSelectLinetypeViewModel(_apiService, currentLinetypeId);
            dialog.DataContext = viewModel;

            if (dialog.ShowDialog() == true)
            {
                return new LinetypeSelectionResult
                {
                    LinetypeId = viewModel.SelectedLinetypeId,
                    LinetypeName = viewModel.SelectedLinetypeText,
                    LinetypeDescription = viewModel.SelectedLinetypeDescription
                };
            }

            return null;
        }

        /// <summary>
        /// Shows lineweight selection dialog
        /// </summary>
        public short? SelectLineweight(short currentLineweight)
        {
            var dialog = new LineweightWindow();
            var viewModel = new LineweightViewModel(currentLineweight);
            dialog.DataContext = viewModel;

            if (dialog.ShowDialog() == true)
            {
                return viewModel.SelectedLineweightValue;
            }

            return null;
        }

        /// <summary>
        /// Shows transparency selection dialog
        /// </summary>
        public int? SelectTransparency(int currentTransparency)
        {
            var dialog = new LayerTransparencyWindow(currentTransparency);
            if (dialog.ShowDialog() == true)
            {
                var viewModel = dialog.DataContext as LayerTransparencyViewModel;
                return viewModel?.SelectedTransparency;
            }

            return null;
        }

        /// <summary>
        /// Shows linetype loading dialog and returns loaded linetypes
        /// </summary>
        public LinetypeModel[] LoadLinetypesFromFile()
        {
            try
            {
                var dialog = new LoadOrReloadLinetypesWindow(_apiService);
                if (dialog.ShowDialog() == true)
                {
                    var viewModel = dialog.DataContext as LoadOrReloadLinetypesViewModel;
                    return viewModel?.AvailableLinetypes?.ToArray() ?? new LinetypeModel[0];
                }
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog("Error", $"Failed to load linetypes: {ex.Message}", MessageType.Error);
            }

            return new LinetypeModel[0];
        }

        #region Linetype File Operations (from LinetypeFileService)

        private static readonly Regex LinetypePattern = new(
            @"^\*([^,]+),(.*)$",
            RegexOptions.Compiled | RegexOptions.Multiline);

        /// <summary>
        /// Parses linetype file and returns linetype models
        /// </summary>
        public List<LinetypeModel> ParseLinetypeFile(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"Linetype file not found: {filePath}");

            var linetypes = new List<LinetypeModel>();

            try
            {
                var lines = File.ReadAllLines(filePath);

                for (var i = 0; i < lines.Length; i++)
                {
                    var line = lines[i].Trim();

                    // Skip empty lines and comments
                    if (string.IsNullOrEmpty(line) || line.StartsWith(";"))
                        continue;

                    // Check if this is a linetype definition line
                    if (line.StartsWith("*"))
                    {
                        var match = LinetypePattern.Match(line);
                        if (match.Success && match.Groups.Count >= 3)
                        {
                            var name = match.Groups[1].Value.Trim();
                            var description = match.Groups[2].Value.Trim();

                            // Skip if name is empty
                            if (string.IsNullOrEmpty(name))
                                continue;

                            // Look for pattern definition in the next line
                            var patternRaw = "A"; // Default continuous pattern
                            if (i + 1 < lines.Length)
                            {
                                var nextLine = lines[i + 1].Trim();
                                if (!string.IsNullOrEmpty(nextLine) && !nextLine.StartsWith("*") &&
                                    !nextLine.StartsWith(";")) patternRaw = nextLine;
                            }

                            linetypes.Add(new LinetypeModel
                            {
                                Id = Guid.NewGuid(),
                                Name = name,
                                Description = description,
                                PatternRaw = patternRaw,
                                UpdatedUtc = DateTime.UtcNow
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to parse linetype file: {ex.Message}", ex);
            }

            return linetypes;
        }

        /// <summary>
        /// Gets default linetype file path
        /// </summary>
        public string GetDefaultLinetypeFilePath()
        {
            // First, try to find acad.lin in the assembly directory
            var assemblyDirectory = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
            var assemblyAcadLinPath = Path.Combine(assemblyDirectory, "acad.lin");

            if (File.Exists(assemblyAcadLinPath))
                return assemblyAcadLinPath;

            // If not found in assembly directory, try AutoCAD AppData based on selected version
            var selectedVersion = Properties.Settings.Default.SelectedAutoCADVersion;
            if (selectedVersion > 0)
            {
                var appDataPath = GetAutoCADAppDataPath(selectedVersion);
                if (!string.IsNullOrEmpty(appDataPath))
                    return appDataPath;
            }

            return null;
        }

        private string GetAutoCADAppDataPath(int version)
        {
            try
            {
                var appDataRoot = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var autocadAppDataPath = Path.Combine(appDataRoot, "Autodesk", $"AutoCAD {version}");

                if (!Directory.Exists(autocadAppDataPath))
                    return null;

                // Look for Support folder recursively in subdirectories
                return FindSupportFolderRecursively(autocadAppDataPath);
            }
            catch
            {
                return null;
            }
        }

        private string FindSupportFolderRecursively(string rootPath)
        {
            try
            {
                // Look for Support folders in subdirectories (e.g., R25.0\enu\Support)
                var directories = Directory.GetDirectories(rootPath, "*", SearchOption.AllDirectories);

                foreach (var directory in directories)
                {
                    if (Path.GetFileName(directory).Equals("Support", StringComparison.OrdinalIgnoreCase))
                    {
                        var acadLinPath = Path.Combine(directory, "acad.lin");
                        if (File.Exists(acadLinPath))
                            return acadLinPath;
                    }
                }
            }
            catch
            {
                // Ignore access errors
            }

            return null;
        }

        #endregion
    }

    /// <summary>
    /// Result of linetype selection
    /// </summary>
    public class LinetypeSelectionResult
    {
        public Guid LinetypeId { get; set; }
        public string LinetypeName { get; set; }
        public string LinetypeDescription { get; set; }
    }
}
