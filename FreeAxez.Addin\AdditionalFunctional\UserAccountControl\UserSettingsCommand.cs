﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Dialog;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.View;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.ViewModel;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.UserAccountControl;

[Transaction(TransactionMode.Manual)]
public class UserSettingsCommand : BaseExternalCommand
{
    private  IDialogService _dialogService;
    public override Result Execute()
    {
        _dialogService = new DialogService();

        var viewModel = new SettingsVm()
        {
            Title = "Settings",
            Content = new SettingsView()
        };

        _dialogService.ShowDialog(viewModel, OnDialogClose);


        return Result.Succeeded;
    }

    async void OnDialogClose(bool? result)
    {
        if (result == true)
        {
        }
    }
}