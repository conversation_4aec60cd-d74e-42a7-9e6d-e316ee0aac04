﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model
{
    public class LoadDataFamilyUpdatesPage
    {
        public ObservableCollection<LibraryCategoryDto> Categories { get; set; }
        public ObservableCollection<LibraryItemDto> Families { get; set; }
        public ObservableCollection<string> RevitVersions { get; set; }
    }
}
