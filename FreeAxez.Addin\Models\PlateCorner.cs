﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class PlateCorner : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
            },
            FamilyNamesEndWith = new List<string>
            {
                "Plate-Corner"
            },
            FamilyNamesNotContains = new List<string>
            {
                "Outlet"
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public PlateCorner(Element element) : base(element)
        {
        }

        public static List<PlateCorner> Collect()
        {
            return FamilyCollector.Instances.Select(g => new PlateCorner(g)).ToList();
        }

        public static List<FamilyInstance> CollectInstances()
        {
            return FamilyCollector.Instances;
        }
    }
}
