<Window x:Class="FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views.FreeAxezLayersWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:converters="clr-namespace:FreeAxez.Addin.Infrastructure.Converters"
        Title="FreeAxez Layers"
        Height="600"
        Width="900"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/Icons.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <converters:ColorToBrushConverter x:Key="ColorToBrushConverter"/>

            <!-- Transparent button style for table cells -->
            <Style x:Key="TransparentTableButton" TargetType="Button">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="5,2"/>
                <Setter Property="HorizontalContentAlignment" Value="Left"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#10000000"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#20000000"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </Window.Resources>
    
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Search -->
        <TextBox Grid.Row="0"
                 Style="{StaticResource Search}"
                 Text="{Binding LayerManagerVM.SearchText, UpdateSourceTrigger=PropertyChanged}"
                 Margin="0,0,0,10"
                 Tag="Search Layers">
        </TextBox>

        <!-- Layers DataGrid -->
        <DataGrid Grid.Row="1"
                  Style="{DynamicResource DataGridWithoutBorders}"
                  ColumnHeaderStyle="{DynamicResource DataGridColumnHeader}"
                  ItemsSource="{Binding LayerManagerVM.FilteredLayers}"
                  SelectedItem="{Binding LayerManagerVM.SelectedLayer}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  CanUserReorderColumns="False"
                  CanUserResizeRows="False"
                  HeadersVisibility="Column"
                  SelectionMode="Single"
                  Margin="0,0,0,10">

            <DataGrid.Columns>
                <DataGridTextColumn Header="Name"
                                    Binding="{Binding Name}"
                                    Width="*"/>

                <DataGridTemplateColumn Header="Color" Width="140">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Command="{Binding DataContext.LayerManagerVM.SelectColorCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                    CommandParameter="{Binding}"
                                    Style="{StaticResource TransparentTableButton}"
                                    Height="25">
                                <StackPanel Orientation="Horizontal">
                                    <Rectangle Width="16" Height="16"
                                               Fill="{Binding Color, Converter={StaticResource ColorToBrushConverter}}"
                                               Stroke="Gray" StrokeThickness="1"
                                               Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding ColorRgbDisplay}"
                                               VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <DataGridTemplateColumn Header="Linetype" Width="2*">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="{Binding LinetypeDescriptionDisplay}"
                                    Command="{Binding DataContext.LayerManagerVM.SelectLinetypeCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                    CommandParameter="{Binding}"
                                    Style="{StaticResource TransparentTableButton}"
                                    Height="25"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <DataGridTemplateColumn Header="Lineweight" Width="140">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Command="{Binding DataContext.LayerManagerVM.SelectLineweightCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                    CommandParameter="{Binding}"
                                    Style="{StaticResource TransparentTableButton}"
                                    Height="25">
                                <StackPanel Orientation="Horizontal">
                                    <Rectangle Width="30" Height="{Binding LineweightThickness}"
                                               Fill="Black"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding LineweightDisplay}"
                                               VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <DataGridTemplateColumn Header="Transparency" Width="100">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="{Binding TransparencyDisplay}"
                                    Command="{Binding DataContext.LayerManagerVM.SelectTransparencyCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                    CommandParameter="{Binding}"
                                    Style="{StaticResource TransparentTableButton}"
                                    HorizontalContentAlignment="Center"
                                    Height="25"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- All buttons in one row -->
        <Grid Grid.Row="2" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Left side - Layer management buttons -->
            <StackPanel Grid.Column="0"
                        Orientation="Horizontal"
                        HorizontalAlignment="Left">
                <Button Command="{Binding LayerManagerVM.AddLayerCommand}"
                        Style="{StaticResource ButtonOutlinedBlue}"
                        Margin="0,0,10,0">
                    <StackPanel Orientation="Horizontal">
                        <ContentControl Template="{StaticResource AddIcon}" Margin="0,0,8,0"/>
                        <TextBlock Text="Add New Layer" VerticalAlignment="Center"
                                   Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>
                <Button Command="{Binding LayerManagerVM.DeleteLayerCommand}"
                        Style="{StaticResource ButtonOutlinedRed}">
                    <StackPanel Orientation="Horizontal">
                        <ContentControl Template="{StaticResource TrashIcon}" Margin="0,0,8,0"/>
                        <TextBlock Text="Delete Selected" VerticalAlignment="Center"
                                   Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>
            </StackPanel>

            <!-- Right side - Close button -->
            <Button Grid.Column="1"
                    Content="Close"
                    Command="{Binding CloseCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    Style="{StaticResource ButtonSimpleRed}"/>
        </Grid>
    </Grid>
</Window>
