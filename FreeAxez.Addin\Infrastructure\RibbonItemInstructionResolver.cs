﻿using Autodesk.Revit.UI;
using Autodesk.Windows;
using FreeAxez.Core.Services;
using FreeAxezTest.Animation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using RibbonItem = Autodesk.Revit.UI.RibbonItem;

namespace FreeAxez.Addin.Infrastructure
{
    public class RibbonItemInstructionResolver
    {
        private readonly string ContentPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "Anguleris Technologies", "FreeAxez", "VideoInstructions");

        private readonly IEnumerable<RibbonItem> _ribbonItems;
        private readonly IEnumerable<Instruction> _instructions;

        public RibbonItemInstructionResolver(IEnumerable<RibbonItem> ribbonItems,
                                         IEnumerable<Instruction> instructions)
        {
            _ribbonItems = ribbonItems;
            _instructions = instructions;
        }

        public void Resolve()
        {
            foreach (var ribbonItem in _ribbonItems)
            {
                if (ribbonItem is PushButton pushButton)
                {
                    string[] parts = pushButton.ClassName.Split('.');
                    string commandName = parts[parts.Length - 1].Replace("Command", "");

                    Instruction mappedInstruction = _instructions.FirstOrDefault(i => string.Equals(
                        commandName, i.CommandName, StringComparison.OrdinalIgnoreCase));

                    if (mappedInstruction != null)
                    {
                        var ribbonToolTip = new RibbonToolTip()
                        {
                            Title = pushButton.Name,
                            Content = pushButton.ToolTip,
                            ExpandedContent = mappedInstruction.CommandToolTip,
                            ExpandedVideo = SetExpandedVideoUri(mappedInstruction.CommandName),
                            IsHelpEnabled = true,
                            IsProgressive = true
                        };

                        pushButton.SetContextualHelp(new ContextualHelp(
                            ContextualHelpType.Url, $"{FreeAxezWebApiService.InstructionLink}{commandName}"));

                        SetRibbonItemToolTip(ribbonItem, ribbonToolTip);
                    }
                }
            }
        }

        private Uri SetExpandedVideoUri(string commandName)
        {
            string videoName = commandName.Replace("Command", "")
                .ToLower() + ".mp4";

            return new Uri(Path.Combine(ContentPath, videoName));
        }

        private void SetRibbonItemToolTip(RibbonItem item, RibbonToolTip toolTip)
        {
            var itemConverter = new InternalMethodUIRevitItemConverter();

            Autodesk.Windows.RibbonItem ribbonItem = itemConverter.GetRibbonItem(item);

            if (ribbonItem is null)
            {
                return;
            }

            ribbonItem.ToolTip = toolTip;
        }
    }
}
