﻿using System;
using System.Threading.Tasks;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;

public class AdminCategoryEditVm : AdminCategoryBaseVm
{
    public AdminCategoryEditVm(LibraryCategoryDto selectedCategory)
        : base(selectedCategory)
    {

    }

    protected override bool CanApply()
    {
        return !string.IsNullOrWhiteSpace(Name);
    }

    protected override async Task ApplyAsync()
    {
        try
        {
            var response = await ApiService.Instance.UpdateCategoryAsync(SelectedCategory);
            HandleResponse(response, result => CloseDialog(result));
        }
        catch (Exception ex)
        {
            Error = $"Error: {ex.Message}";
            LogHelper.Error($"An error occurred: {ex.Message}");
        }
    }
}