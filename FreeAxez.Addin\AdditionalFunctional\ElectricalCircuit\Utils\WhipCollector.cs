﻿using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.Models;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils
{
    public class WhipCollector
    {
        private readonly LevelHelper _levelHelper;

        public WhipCollector(LevelHelper levelHelper)
        {
            _levelHelper = levelHelper;
        }

        public List<FlexPipe> GetWhipElementsForBoxes()
        {
            return Whip.CollectFloorBoxWhips().Concat(Whip.CollectSpinLocks())
                .Select(w => w.Element as FlexPipe)
                .Where(_levelHelper.BelongsToTheSelectedLevel)
                .ToList();
        } 

        public List<FlexPipe> GetWhipElementsForTracks()
        {
            return Whip.CollectTrackWhips()
               .Select(w => w.Element as FlexPipe)
               .Where(_levelHelper.BelongsToTheSelectedLevel)
               .ToList();
        }
    }
}
