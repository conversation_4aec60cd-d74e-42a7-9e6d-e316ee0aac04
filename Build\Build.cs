using System;
using System.IO;
using System.Linq;
using System.Diagnostics;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Nuke.Common;
using Nuke.Common.IO;
using Nuke.Common.ProjectModel;
using Nuke.Common.Tools.DotNet;
using static Nuke.Common.Tools.DotNet.DotNetTasks;

class Build : NukeBuild
{
    static string _version = "1.32.4.0";

    static AbsolutePath _outputDir = RootDirectory / "Output";
    static AbsolutePath _tempDir = _outputDir / "Temp";
    static AbsolutePath _installerDir = _outputDir / "Installer";

    static AbsolutePath _contentPath = RootDirectory / "UserAddInContent";
    static AbsolutePath _addinPath = RootDirectory / "FreeAxez.Addin" / "FreeAxez.addin";
    static AbsolutePath _certificateFilePath = RootDirectory / "Signature" / "Anguleris_Technologies_LLC.pfx";
    static AbsolutePath _signToolPath = @"C:\Program Files (x86)\Microsoft SDKs\ClickOnce\SignTool\signtool.exe";


    [Solution(GenerateProjects = true)]
    readonly Solution Solution;


    public static int Main() => Execute<Build>(x => x.Compile);


    Target Compile => _ => _
        .Executes(() =>
        {
            CleanDirectory(_tempDir);

            var configurations = GetConfigurationsStartedWith("Release 20");
            foreach (var configuration in configurations)
            {
                var outputPath = _tempDir / ExtractRevitVersion(configuration) / "Anguleris Technologies" / "FreeAxez";
                BuildProject(Solution.FreeAxez_Addin, configuration, outputPath);
                SignFile(outputPath / Solution.FreeAxez_Addin.Name + ".dll");

                var outputBrowserPath = _tempDir / ExtractRevitVersion(configuration) / "Anguleris Technologies" / "FreeAxez" / "BrowserApp";
                BuildProject(Solution.FreeAxez_BrowserApp, configuration, outputBrowserPath);
                SignFile(outputBrowserPath / Solution.FreeAxez_BrowserApp.Name + ".exe");

                var outputAddinPath = _tempDir / ExtractRevitVersion(configuration) / Path.GetFileName(_addinPath);
                CopyAddin(outputAddinPath);

                var outputContentPath = outputPath / "Content";
                CopyContent(outputContentPath);
            }
        });

    Target CreateInstaller => _ => _
        .TriggeredBy(Compile)
        .Executes(() =>
        {
            CleanDirectory(_installerDir);

            Console.WriteLine("BUILD INSTALLER");
            DotNetBuild(settings => settings
                .SetProjectFile(Solution.Installer)
                .SetOutputDirectory(_installerDir));

            var installer = _installerDir / $"{Solution.Installer.Name}.exe";

            Console.WriteLine($"RUN INSTALLER {installer}");
            Process installerProcess = Process.Start(installer);
            installerProcess.WaitForExit();

            Directory.Delete(_installerDir, true);

            var msi = Directory.GetFiles(_outputDir)
                               .Where(f => f.EndsWith(".msi"))
                               .OrderByDescending(f => File.GetCreationTime(f))
                               .First();

            SignFile(msi);
        });


    List<string> GetConfigurationsStartedWith(string startedWith)
    {
        var configurations = Solution.Configurations
            .Select(keyValuePair => keyValuePair.Key)
            .Select(configuration => configuration.Remove(configuration.LastIndexOf('|')))
            .Where(configuration => configuration.StartsWith(startedWith, StringComparison.OrdinalIgnoreCase))
            .Distinct()
            .ToList();

        if (configurations.Count == 0)
            throw new Exception($"No solution configurations have been found. Pattern: {startedWith}*");

        return configurations;
    }

    void BuildProject(Project project, string configuration, string outputDir)
    {
        Console.WriteLine($"BUILD {project.Name.ToUpper()} CONFIGURATION {configuration.ToUpper()} STARTED");

        CleanDirectory(project.Directory / "obj");

        DotNetBuild(settings => settings
            .SetProjectFile(project)
            .SetConfiguration(configuration)
            .SetVersion(_version)
            .SetAssemblyVersion(_version)
            .SetOutputDirectory(outputDir)
            .SetVerbosity(DotNetVerbosity.quiet));

        Console.WriteLine($"BUILD {project.Name.ToUpper()} CONFIGURATION {configuration.ToUpper()} FINISHED");
    }

    string ExtractRevitVersion(string configurationName)
    {
        var match = Regex.Match(configurationName, @"\d+");
        if (match.Success) return match.Value;
        throw new Exception($"Could not extract Revit version from configuration name: {configurationName}");
    }

    void CleanDirectory(AbsolutePath absolutePath)
    {
        Console.WriteLine($"CLEAR DIRECTORY: {absolutePath}");
        absolutePath.CreateOrCleanDirectory();
    }

    void CopyAddin(AbsolutePath targetPath)
    {
        Console.WriteLine($"COPY ADDIN TO {targetPath}");
        File.Copy(_addinPath, targetPath);
    }

    void CopyContent(AbsolutePath targetPath)
    {
        Console.WriteLine($"COPY CONTENT TO {targetPath}");
        CopyDirectory(_contentPath, targetPath, true);
    }

    public void CopyDirectory(string sourceDir, string destinationDir, bool copySubDirs)
    {
        DirectoryInfo dir = new DirectoryInfo(sourceDir);
        if (!dir.Exists)
        {
            throw new DirectoryNotFoundException($"Source directory does not exist or could not be found: {sourceDir}");
        }

        if (!Directory.Exists(destinationDir))
        {
            Directory.CreateDirectory(destinationDir);
        }

        FileInfo[] files = dir.GetFiles();
        foreach (FileInfo file in files)
        {
            string tempPath = Path.Combine(destinationDir, file.Name);
            file.CopyTo(tempPath, false);
        }

        if (copySubDirs)
        {
            DirectoryInfo[] subDirs = dir.GetDirectories();
            foreach (DirectoryInfo subDir in subDirs)
            {
                string tempPath = Path.Combine(destinationDir, subDir.Name);
                CopyDirectory(subDir.FullName, tempPath, copySubDirs);
            }
        }
    }

    void SignFile(string path)
    {
        Console.WriteLine($"SIGN FILE {path}");

        var process = new Process();

        string arguments = $"sign /f \"{_certificateFilePath}\" /p Earl " +
            $"/t http://timestamp.digicert.com /fd sha256 \"{path}\"";

        process.StartInfo.FileName = _signToolPath;
        process.StartInfo.Arguments = arguments;
        process.StartInfo.UseShellExecute = false;
        process.StartInfo.RedirectStandardOutput = true;
        process.StartInfo.RedirectStandardError = true;

        process.OutputDataReceived += (sender, args) => Console.WriteLine(args.Data);
        process.ErrorDataReceived += (sender, args) => Console.WriteLine(args.Data);

        process.Start();
        process.BeginOutputReadLine();
        process.BeginErrorReadLine();

        process.WaitForExit();

        if (process.ExitCode != 0)
        {
            throw new Exception($"Failed to sign the file {path}");
        }
    }
}
