﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Abstractions;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Commands.TaskManager;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Constants;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.DataTransferObjects;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Mapping;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Threading;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;

public class TaskManagerViewModel : BaseViewModel
{
    private readonly TaskManagerView _taskManagerView;
    private readonly Dispatcher _dispatcher;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private readonly TaskManagerHttpClientBase _taskManagerHttpClientService;
    private readonly RevitGlobalParametersHandler _globalParametersHandler;
    private long _sheetId;
    private string? _projectName;
    private string? _scope;
    private string? _scopeUrl;
    private string? _step;
    private RowDto? _recentRowDto;
    private IEnumerable<ColumnDto>? _rowColumnDtos;
    private IEnumerable<CommentViewModel>? _rowComments;
    private IEnumerable<AttachmentDto>? _rowAttachmentDtos;
    private long? _previousParentRowDtoId;
    private bool _isParentTheSame = true;
    private string _errorResponse = string.Empty;
    private bool _isExecuting = true;

    public TaskManagerViewModel(TaskManagerView taskManagerView)
    {
        _taskManagerView = taskManagerView;
        _dispatcher = Dispatcher.CurrentDispatcher;
        _cancellationTokenSource = new CancellationTokenSource();
        _taskManagerHttpClientService = new TaskManagerHttpClientService();
        _globalParametersHandler = new RevitGlobalParametersHandler(RevitManager.Document);

        _dispatcher.InvokeAsync(InitializeAsync);

        ChangeStatusCommand = _isParentTheSame
                ? new SetCompleteStatusCommand(this, _taskManagerView, _dispatcher, _taskManagerHttpClientService)
                : new RequestForApproveCommand(this, _taskManagerView, _dispatcher, _taskManagerHttpClientService);
        DownloadFileCommand = new DownloadFileAsyncCommand(this);
        CancelCommand = new CancelCommand();
    }

    public long SheetId
    {
        get => _sheetId;
        set
        {
            _sheetId = value;
            OnPropertyChanged(nameof(SheetId));
        }
    }
    public RowDto? RecentRow
    {
        get => _recentRowDto;
        set
        {
            _recentRowDto = value;
            OnPropertyChanged(nameof(RecentRow));
        }
    }
    public string? ProjectName
    {
        get => _projectName;
        set
        {
            _projectName = value;
            OnPropertyChanged(nameof(ProjectName));
        }
    }
    public string? Scope
    {
        get => _scope;
        set
        {
            _scope = value;
            OnPropertyChanged(nameof(Scope));
        }
    }
    public string? ScopeUrl
    {
        get => _scopeUrl;
        set
        {
            _scopeUrl = value;
            OnPropertyChanged(nameof(ScopeUrl));
        }
    }
    public string? Step
    {
        get => _step;
        set
        {
            _step = value;
            OnPropertyChanged(nameof(Step));
        }
    }
    public IEnumerable<ColumnDto>? RowColumnDtos
    {
        get => _rowColumnDtos;
        set
        {
            _rowColumnDtos = value;
            OnPropertyChanged(nameof(RowColumnDtos));
        }
    }
    public IEnumerable<CommentViewModel>? RowComments
    {
        get => _rowComments;
        set
        {
            _rowComments = value;
            OnPropertyChanged(nameof(RowComments));
        }
    }
    public IEnumerable<AttachmentDto>? RowAttachmentDtos
    {
        get => _rowAttachmentDtos;
        set
        {
            _rowAttachmentDtos = value;
            OnPropertyChanged(nameof(RowAttachmentDtos));
        }
    }
    public string ChangeStatusButtonName => _isParentTheSame
        ? ViewModelConstants.TaskManager.SetCompleteStatusButtonName
        : ViewModelConstants.TaskManager.RequestForApproveButtonName;
    public bool IsExecuting
    {
        get => _isExecuting;
        set
        {
            _isExecuting = value;
            OnPropertyChanged(nameof(IsExecuting));
        }
    }
    public string ErrorResponse
    {
        get => _errorResponse;
        set
        {
            _errorResponse = value;
            OnPropertyChanged(nameof(ErrorResponse));
        }
    }

    public bool HasVideoInstruction => !string.IsNullOrEmpty(ScopeUrl);

    public ICommand ChangeStatusCommand { get; internal set; }
    public ICommand DownloadFileCommand { get; }
    public ICommand CancelCommand { get; }

    internal CancellationTokenSource CancellationTokenSource => _cancellationTokenSource;

    internal async Task InitializeAsync()
    {
        try
        {
            IsExecuting = true;

            string? sheetIdString = _globalParametersHandler.GetGlobalParameterValue
                <StringParameterValue, string>(ViewModelConstants.TaskManager.GlobalParameterName);

            if (long.TryParse(sheetIdString, out long sheetId))
                _sheetId = sheetId;
            else
                throw new ArgumentException($"Can't retrieve smartsheet table id from " +
                    $"\"{ViewModelConstants.TaskManager.GlobalParameterName}\" parameter value.");

            _recentRowDto = await _taskManagerHttpClientService.GetRecentRowDtoAsync(_sheetId, _cancellationTokenSource.Token);

            RowDto? newParentRowDto = _recentRowDto.ParentRowDto;
            if (newParentRowDto is null)
                throw new NullReferenceException("Parent row is not found.");

            ProjectName = _recentRowDto.ProjectName;

            string? scope = _recentRowDto.ParentRowDto?.ColumnDtos
                .FirstOrDefault(c => string.Equals(c.Title, "Name", StringComparison.OrdinalIgnoreCase))
                ?.CellValue as string;
            object? scopeNumber = _recentRowDto.ParentRowDto?.ColumnDtos
                .FirstOrDefault(c => c.Title.Equals("Row Number", StringComparison.OrdinalIgnoreCase))
                ?.CellValue;
           
            string? step = _recentRowDto.ColumnDtos
                .FirstOrDefault(c => string.Equals(c.Title, "Name", StringComparison.OrdinalIgnoreCase))
                ?.CellValue as string;
            if (_recentRowDto.ParentRowDto?.RowNumber != 1)
            {
                Scope = scope;
                Step = step;
            }
            else
            {
                Scope = step;
                Step = string.Empty;
            }
            RowColumnDtos = _recentRowDto.ColumnDtos;
            RowComments = _recentRowDto.CommentDtos.MapToCommentViewModels();
            RowAttachmentDtos = _recentRowDto.AttachmentsDtos;

            if (_previousParentRowDtoId is null && newParentRowDto.RowNumber != 1)
            {
                _isParentTheSame = newParentRowDto.RowNumber != 1;
                OnPropertyChanged(nameof(ChangeStatusButtonName));
                _previousParentRowDtoId = newParentRowDto.Id;
            }
            else if (_previousParentRowDtoId == newParentRowDto.Id
                && _previousParentRowDtoId is not null)
            {
                _isParentTheSame = true;
                _previousParentRowDtoId = newParentRowDto.Id;
            }
            else
            {
                _isParentTheSame = false;
                OnPropertyChanged(nameof(ChangeStatusButtonName));
                ChangeStatusCommand = new RequestForApproveCommand(this,
                                                                   _taskManagerView,
                                                                   _dispatcher,
                                                                   _taskManagerHttpClientService);
                OnPropertyChanged(nameof(ChangeStatusCommand));
                RowColumnDtos = _recentRowDto.ColumnDtos;
            }

            ScopeInstruction? scopeInstruction = null;
            if (scopeNumber is double doubleScopeNumber)
            {
                scopeInstruction = await _taskManagerHttpClientService.GetScopeInstructionByNumber(doubleScopeNumber, _cancellationTokenSource.Token);
            }
            else
            {
                scopeInstruction ??= await _taskManagerHttpClientService.GetScopeInstructionByName(scope, _cancellationTokenSource.Token);
            }

            ScopeUrl = scopeInstruction?.Url ?? string.Empty;

            OnPropertyChanged(nameof(HasVideoInstruction));

            IsExecuting = false;
        }
        catch (HttpRequestException)
        {
            ErrorResponse = "Web service is unresponsive now. Please try again later.";
        }
        catch (Exception exception)
        {
            ErrorResponse = exception.Message;
        }
        finally
        {
            IsExecuting = false;
        }
    }

}
