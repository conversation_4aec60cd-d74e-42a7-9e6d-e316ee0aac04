{"$schema": "http://json-schema.org/draft-04/schema#", "$ref": "#/definitions/build", "title": "Build Schema", "definitions": {"build": {"type": "object", "properties": {"Continue": {"type": "boolean", "description": "Indicates to continue a previously failed build attempt"}, "Help": {"type": "boolean", "description": "Shows the help text for this build assembly"}, "Host": {"type": "string", "description": "Host for execution. <PERSON><PERSON><PERSON> is 'automatic'", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "AzurePipelines", "Bamboo", "Bitbucket", "Bitrise", "GitHubActions", "GitLab", "<PERSON>", "Rider", "SpaceAutomation", "TeamCity", "Terminal", "TravisCI", "VisualStudio", "VSCode"]}, "NoLogo": {"type": "boolean", "description": "Disables displaying the NUKE logo"}, "Partition": {"type": "string", "description": "Partition to use on CI"}, "Plan": {"type": "boolean", "description": "Shows the execution plan (HTML)"}, "Profile": {"type": "array", "description": "Defines the profiles to load", "items": {"type": "string"}}, "Root": {"type": "string", "description": "Root directory during build execution"}, "Skip": {"type": "array", "description": "List of targets to be skipped. Empty list skips all dependencies", "items": {"type": "string", "enum": ["Compile", "CreateInstaller"]}}, "Solution": {"type": "string", "description": "Path to a solution file that is automatically loaded"}, "Target": {"type": "array", "description": "List of targets to be invoked. Default is '{default_target}'", "items": {"type": "string", "enum": ["Compile", "CreateInstaller"]}}, "Verbosity": {"type": "string", "description": "Logging verbosity during build execution. Default is 'Normal'", "enum": ["Minimal", "Normal", "Quiet", "Verbose"]}}}}}