﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class BaseUnitCutout : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
                "Cutout"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public BaseUnitCutout(Element element) : base(element)
        {
        }

        public static List<BaseUnitCutout> Collect()
        {
            return FamilyCollector.Instances.Select(f => new BaseUnitCutout(f)).ToList();
        }

        public static List<FamilyInstance> CollectInstances()
        {
            return FamilyCollector.Instances;
        }

        public static List<FamilySymbol> CollectSymbols(out string failureMessage)
        {
            var output = FamilyCollector.Symbols;

            failureMessage = "";
            if (output.Count == 0)
            {
                failureMessage = "There is no cutout family in the project with \"Cutout\" in the family name.";
            }

            return output;
        }
    }
}
