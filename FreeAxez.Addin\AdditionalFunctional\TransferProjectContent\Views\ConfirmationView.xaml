﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Views.ConfirmationView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Cancel Confirmation"         
        SizeToContent="WidthAndHeight"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>
    <Grid Margin="10">
        <StackPanel>
            <TextBlock Text="Are you sure you want to cancell operation?"
                       TextWrapping="Wrap"
                       Margin="0,0,0,10"
                       HorizontalAlignment="Center" />
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0 5">
                <Button Content="Yes"
                        Width="75"
                        Style="{StaticResource ButtonOutlinedRed}"
                        Margin="5,0"
                        Command="{Binding ConfirmCommand}" />
                <Button Content="No"
                        Width="75"
                        Style="{StaticResource ButtonSimpleGreen}"
                        Margin="5,0"
                        Command="{Binding CancelCommand}" />
            </StackPanel>
        </StackPanel>
    </Grid>
</Window>
