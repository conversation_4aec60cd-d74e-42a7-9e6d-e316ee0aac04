﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Views;
using FreeAxez.Addin.Infrastructure;
using System.Windows.Interop;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    internal class ExportGriddBOMCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var griddBomView = new BomSelectionView();
            var handler = new WindowInteropHelper(griddBomView);
            handler.Owner = RevitManager.UIApplication.MainWindowHandle;
            griddBomView.ShowDialog();

            return Result.Succeeded;
        }
    }
}
