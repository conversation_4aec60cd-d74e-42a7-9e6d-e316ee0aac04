﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class BaseUnit : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
            },
            FamilyNamesEndWith = new List<string>
            {
                "Base_Unit-Standard",
                "Base_Unit"
            },
            FamilyNamesNotContains = new List<string>
            {
                "Outlet",
                "High_Capacity",
                "Detail" // (Detail Only- Dont Use)
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public BaseUnit(Element element) : base(element)
        {
        }

        public static List<BaseUnit> Collect()
        {
            return FamilyCollector.Instances.Select(g => new BaseUnit(g)).ToList();
        }

        public static List<FamilyInstance> CollectInstances()
        {
            return FamilyCollector.Instances;
        }

        public static List<FamilySymbol> CollectSymbols(out string failureMessage)
        {
            var output = FamilyCollector.Symbols;

            failureMessage = "";
            if (output.Count == 0)
            {
                failureMessage = 
                    "No base unit family was found in the project with a family name " +
                    "ending in \"Base_Unit\" or \"Base_Unit-Standard\".";
            }

            return output;
        }
    }
}