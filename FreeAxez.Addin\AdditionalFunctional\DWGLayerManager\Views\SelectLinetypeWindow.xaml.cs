using System;
using System.Windows;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views
{
    /// <summary>
    /// Interaction logic for SelectLinetypeWindow.xaml
    /// </summary>
    public partial class SelectLinetypeWindow : Window
    {
        public SelectLinetypeWindow()
        {
            InitializeComponent();
            // DataContext will be set externally
        }
    }
}
