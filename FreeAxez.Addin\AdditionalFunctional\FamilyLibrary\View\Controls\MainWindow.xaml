<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls.MainWindow"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             Background="Transparent"
             d:DesignHeight="800" 
             d:DesignWidth="920">
    <Grid>
        <Border  Background="White"
                 CornerRadius="0 12 12 0"/>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions> 
            <StackPanel Grid.Row="0">
                <ContentPresenter Content="{Binding HeaderContent}"/>
            </StackPanel>
            <Border Grid.Row="0" 
                    BorderThickness="0 0 0  1" 
                    BorderBrush="LightGray"/>
            <StackPanel Grid.Row="1" 
                        Margin="5">
                <ContentPresenter Content="{Binding MainContent}"/>
            </StackPanel>
            <StackPanel Grid.Row="2" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Right">
                <ContentPresenter Content="{Binding FooterContent}"/>
            </StackPanel>
        </Grid>  
    </Grid>
</UserControl>
