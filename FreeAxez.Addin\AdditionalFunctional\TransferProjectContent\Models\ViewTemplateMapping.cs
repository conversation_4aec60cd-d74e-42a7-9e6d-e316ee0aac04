﻿using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Models
{
    public class ViewTemplateMapping : BaseViewModel
    {
        private bool _isKeyMatching;
        private bool _isTargetDuplicated;
        private ViewTemplateModel _target;

        public ViewTemplateMapping(ViewTemplateModel source, ViewTemplateModel target,
            List<ViewTemplateModel> allTargets)
        {
            Source = source;
            AllTargets = allTargets;

            Target = target;

            UpdateKeyMatching();
        }

        public ViewTemplateModel Source { get; }

        public ViewTemplateModel Target
        {
            get => _target;
            set
            {
                if (_target != value)
                {
                    _target = value;
                    OnPropertyChanged();
                    UpdateKeyMatching();
                }
            }
        }

        public List<ViewTemplateModel> AllTargets { get; set; }

        public bool IsKeyMatching
        {
            get => _isKeyMatching;
            private set => Set(ref _isKeyMatching, value);
        }

        public bool IsTargetDuplicated
        {
            get => _isTargetDuplicated;
            set => Set(ref _isTargetDuplicated, value);
        }

        private void UpdateKeyMatching()
        {
            if (Target != null)
            {
                var similarity = FuzzySimilarityCalculator.CalculateSimilarity(Source.MappingKey, Target.MappingKey);
                IsKeyMatching = similarity < 0.9;
            }
            else
            {
                IsKeyMatching = false;
            }
        }

        public static List<ViewTemplateMapping> Map(List<ViewTemplateModel> sourceList,
            List<ViewTemplateModel> targetList)
        {
            var output = new List<ViewTemplateMapping>();

            var targetIndex = BuildTargetIndex(targetList);

            foreach (var source in sourceList)
            {
                ViewTemplateModel bestMatch = null;
                var bestSimilarity = 0.0;

                var potentialTargets = GetPotentialTargets(source, targetIndex);

                foreach (var target in potentialTargets)
                {

                    var similarity =
                        FuzzySimilarityCalculator.CalculateSimilarity(source.MappingKey, target.MappingKey);

                    if (similarity > bestSimilarity)
                    {
                        bestMatch = target;
                        bestSimilarity = similarity;
                    }
                }

                output.Add(new ViewTemplateMapping(source, bestMatch, targetList));
            }

            return output;
        }

        private static Dictionary<string, List<ViewTemplateModel>> BuildTargetIndex(List<ViewTemplateModel> targetList)
        {
            var index = new Dictionary<string, List<ViewTemplateModel>>();

            foreach (var target in targetList)
            {
                foreach (var token in target.MappingTokens)
                {
                    if (!index.ContainsKey(token))
                    {
                        index[token] = new List<ViewTemplateModel>();
                    }

                    index[token].Add(target);
                }
            }

            return index;
        }

        private static IEnumerable<ViewTemplateModel> GetPotentialTargets(ViewTemplateModel source,
            Dictionary<string, List<ViewTemplateModel>> targetIndex)
        {
            var potentialTargets = new HashSet<ViewTemplateModel>();

            foreach (var token in source.MappingTokens)
            {
                if (targetIndex.ContainsKey(token))
                {
                    foreach (var target in targetIndex[token])
                    {
                        potentialTargets.Add(target);
                    }
                }
            }

            return potentialTargets;
        }
    }
}