﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.PanelScheduleCreation.Utils;
using FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement.Models;
using FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement.ViewModels
{
    internal class PanelSchedulePlacementViewModel : BaseViewModel
    {
        public PanelSchedulePlacementViewModel()
        {
            SheetSizes = LevelSheets.GetAllTrackScheduleSheetSizes();

            SelectedSheetSize = SheetSizes
                .Where(s => s == Properties.Settings.Default.panelSchedulePlacementSheetSize)
                .FirstOrDefault();
            if (SelectedSheetSize == null) 
            {
                SelectedSheetSize = SheetSizes.FirstOrDefault();
            }

            var previousSelectedLevels = new List<int>();
            try
            {
                previousSelectedLevels =                     
                    Properties.Settings.Default.panelSchedulePlacementSelectedLevels
                    .Split(',').Select(l => int.Parse(l)).ToList();
            }
            catch { }

            var trackScheduleManager = new TrackScheduleManager();
            Levels = trackScheduleManager
                .GetTrackSchedules()
                .Select(s => s.Name.Split('_').First().Trim().ToUpper())
                .Distinct()
                .OrderBy(l => l)
                .Select(l => new LevelModel(){ 
                    Name = l, 
                    IsChecked = previousSelectedLevels.Contains(GetLevelNumber(l))})
                .ToList();

            DeleteUnusedSheets = Properties.Settings.Default.panelSchedulePlacementDeleteUnused;
            Revision = Properties.Settings.Default.panelSchedulePlacementRevision;

            PlaceCommand = new RelayCommand(OnPlaceCommandExecute);
        }


        public List<string> SheetSizes { get; set; }
        public string SelectedSheetSize { get; set; }
        public List<LevelModel> Levels { get; set; }
        public bool DeleteUnusedSheets { get; set; }
        public string Revision { get; set; }

        public ICommand PlaceCommand { get; set; }
        private void OnPlaceCommandExecute(object p)
        {
            (p as Window).Close();
            SaveSettings();

            var selectedSheetHeight = Regex.Match(SelectedSheetSize, @"\d+").Value;
            if (!LevelSheets.ProjectHasTemplateSheet(selectedSheetHeight))
            {
                var templateNumber =
                    string.Format(LevelSheets.SheetNumberPattern, "01", "", selectedSheetHeight);

                MessageWindow.ShowDialog($"There is no template sheet with number \"{templateNumber}\".", MessageType.Notify);
                return;
            }

            var selectedLevels = Levels
                .Where(l => l.IsChecked)
                .Select(l => GetLevelNumber(l.Name))
                .ToList();

            var trackScheduleManager = new TrackScheduleManager();
            var schedulesToPlace = trackScheduleManager
                .GetTrackSchedules()
                .Where(vs => selectedLevels.Contains(GetLevelNumber(vs.Name)))
                .Select(vs => new TrackSchedulePart(vs))
                .GroupBy(sp => sp.TrackName)
                .Select(g => new TrackSchedule(g.ToList()))
                .ToList();

            TrackSchedule.CalculateDimensions(schedulesToPlace);

            var processedSheets = new List<ViewSheet>();
            using (var t = new Transaction(RevitManager.Document, "Place Schedules"))
            {
                t.Start();

                foreach (var level in selectedLevels)
                {
                    var schedulesForLevel = schedulesToPlace
                        .Where(s => GetLevelNumber(s.Level) == level)
                        .OrderBy(s => s.TrackName)
                        .ToList();
                    if (schedulesForLevel.Count == 0)
                    {
                        continue;
                    }

                    var levelSheets = new LevelSheets(level, SelectedSheetSize);
                    var placementManager = new PlacementManager(schedulesForLevel, levelSheets);
                    processedSheets.AddRange(placementManager.Place());
                }

                LevelSheets.DeleteUnusedSheetsForSize(selectedSheetHeight);

                t.Commit();
            }

            if (processedSheets.Count == 1)
            {
                MessageWindow.ShowDialog($"{processedSheets.Count} sheet processed.", MessageType.Info);
            }
            else
            {
                MessageWindow.ShowDialog($"{processedSheets.Count} sheets processed.", MessageType.Info);
            }
        }

        private void SaveSettings()
        {
            Properties.Settings.Default.panelSchedulePlacementSheetSize = SelectedSheetSize;
            Properties.Settings.Default.panelSchedulePlacementDeleteUnused = DeleteUnusedSheets;
            Properties.Settings.Default.panelSchedulePlacementSelectedLevels = string.Join(",", 
                Levels.Where(l => l.IsChecked).Select(l => GetLevelNumber(l.Name)));
            Properties.Settings.Default.panelSchedulePlacementRevision = Revision;
            Properties.Settings.Default.Save();
        }

        /// <summary>
        /// Get name from sheet number and schedule name.
        /// Schedule name example: LEVEL 1_T-01_(4)_Connected Components
        /// return                       ^
        /// Sheet number example: GP-T-01A
        /// return                     ^^
        /// </summary>
        private int GetLevelNumber(string stringWithNumber)
        {
            return int.Parse(Regex.Match(stringWithNumber, @"\d+").Value);
        }
    }
}
