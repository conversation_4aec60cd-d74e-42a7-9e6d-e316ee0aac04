﻿namespace FreeAxez.Addin.AdditionalFunctional.LineToFlexPipe
{
    partial class FormFlexPipe2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOk = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.lstPipeTypes = new System.Windows.Forms.ListBox();
            this.label2 = new System.Windows.Forms.Label();
            this.txtDiam = new System.Windows.Forms.TextBox();
            this.chkRound = new System.Windows.Forms.CheckBox();
            this.chkDeleteLines = new System.Windows.Forms.CheckBox();
            this.txtLevelOffset = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.chkSetPipeTypeByLength = new System.Windows.Forms.CheckBox();
            this.label1 = new System.Windows.Forms.Label();
            this.whipDropTextBox = new System.Windows.Forms.TextBox();
            this.whipDropLabel = new System.Windows.Forms.Label();
            this.whipRiseLabel = new System.Windows.Forms.Label();
            this.whipOverheadLabel = new System.Windows.Forms.Label();
            this.spliceLabel = new System.Windows.Forms.Label();
            this.whipRiseTextBox = new System.Windows.Forms.TextBox();
            this.whipOverheadTextBox = new System.Windows.Forms.TextBox();
            this.spliceTextBox = new System.Windows.Forms.TextBox();
            this.firstPigtailCheckBox = new System.Windows.Forms.CheckBox();
            this.secondPigtailCheckBox = new System.Windows.Forms.CheckBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // btnOk
            // 
            this.btnOk.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOk.Location = new System.Drawing.Point(559, 559);
            this.btnOk.Margin = new System.Windows.Forms.Padding(4);
            this.btnOk.Name = "btnOk";
            this.btnOk.Size = new System.Drawing.Size(100, 28);
            this.btnOk.TabIndex = 0;
            this.btnOk.Text = "OK";
            this.btnOk.UseVisualStyleBackColor = true;
            this.btnOk.Click += new System.EventHandler(this.btnOk_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(667, 559);
            this.btnCancel.Margin = new System.Windows.Forms.Padding(4);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(100, 28);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "Cancel";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // lstPipeTypes
            // 
            this.lstPipeTypes.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lstPipeTypes.FormattingEnabled = true;
            this.lstPipeTypes.ItemHeight = 16;
            this.lstPipeTypes.Location = new System.Drawing.Point(16, 231);
            this.lstPipeTypes.Margin = new System.Windows.Forms.Padding(4);
            this.lstPipeTypes.Name = "lstPipeTypes";
            this.lstPipeTypes.Size = new System.Drawing.Size(749, 292);
            this.lstPipeTypes.TabIndex = 2;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(12, 11);
            this.label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(148, 16);
            this.label2.TabIndex = 4;
            this.label2.Text = "Pipe Diameter in Inches";
            // 
            // txtDiam
            // 
            this.txtDiam.Location = new System.Drawing.Point(179, 7);
            this.txtDiam.Margin = new System.Windows.Forms.Padding(4);
            this.txtDiam.Name = "txtDiam";
            this.txtDiam.Size = new System.Drawing.Size(132, 22);
            this.txtDiam.TabIndex = 5;
            this.txtDiam.Text = "3/4\"";
            this.txtDiam.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtDiam.Leave += new System.EventHandler(this.txtDiam_Leave);
            // 
            // chkRound
            // 
            this.chkRound.AutoSize = true;
            this.chkRound.Checked = true;
            this.chkRound.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkRound.Location = new System.Drawing.Point(20, 89);
            this.chkRound.Margin = new System.Windows.Forms.Padding(4);
            this.chkRound.Name = "chkRound";
            this.chkRound.Size = new System.Drawing.Size(178, 20);
            this.chkRound.TabIndex = 6;
            this.chkRound.Text = "Create &Rounded Corners";
            this.chkRound.UseVisualStyleBackColor = true;
            // 
            // chkDeleteLines
            // 
            this.chkDeleteLines.AutoSize = true;
            this.chkDeleteLines.Checked = true;
            this.chkDeleteLines.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkDeleteLines.Location = new System.Drawing.Point(20, 117);
            this.chkDeleteLines.Margin = new System.Windows.Forms.Padding(4);
            this.chkDeleteLines.Name = "chkDeleteLines";
            this.chkDeleteLines.Size = new System.Drawing.Size(161, 20);
            this.chkDeleteLines.TabIndex = 7;
            this.chkDeleteLines.Text = "&Delete Selected Lines";
            this.chkDeleteLines.UseVisualStyleBackColor = true;
            // 
            // txtLevelOffset
            // 
            this.txtLevelOffset.Location = new System.Drawing.Point(179, 39);
            this.txtLevelOffset.Margin = new System.Windows.Forms.Padding(4);
            this.txtLevelOffset.Name = "txtLevelOffset";
            this.txtLevelOffset.Size = new System.Drawing.Size(132, 22);
            this.txtLevelOffset.TabIndex = 9;
            this.txtLevelOffset.Text = "1 1/2\"";
            this.txtLevelOffset.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtLevelOffset.Leave += new System.EventHandler(this.txtLevelOffset_Leave);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(12, 43);
            this.label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 16);
            this.label3.TabIndex = 8;
            this.label3.Text = "Level Offset";
            // 
            // chkSetPipeTypeByLength
            // 
            this.chkSetPipeTypeByLength.AutoSize = true;
            this.chkSetPipeTypeByLength.Checked = true;
            this.chkSetPipeTypeByLength.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkSetPipeTypeByLength.Location = new System.Drawing.Point(16, 203);
            this.chkSetPipeTypeByLength.Margin = new System.Windows.Forms.Padding(4);
            this.chkSetPipeTypeByLength.Name = "chkSetPipeTypeByLength";
            this.chkSetPipeTypeByLength.Size = new System.Drawing.Size(177, 20);
            this.chkSetPipeTypeByLength.TabIndex = 10;
            this.chkSetPipeTypeByLength.Text = "&Set Pipe Type By Length";
            this.chkSetPipeTypeByLength.UseVisualStyleBackColor = true;
            this.chkSetPipeTypeByLength.CheckedChanged += new System.EventHandler(this.chkSetPipeTypeByLength_CheckedChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(12, 183);
            this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(70, 16);
            this.label1.TabIndex = 3;
            this.label1.Text = "Pipe Type";
            // 
            // whipDropTextBox
            // 
            this.whipDropTextBox.Location = new System.Drawing.Point(480, 7);
            this.whipDropTextBox.Margin = new System.Windows.Forms.Padding(4);
            this.whipDropTextBox.Name = "whipDropTextBox";
            this.whipDropTextBox.Size = new System.Drawing.Size(132, 22);
            this.whipDropTextBox.TabIndex = 11;
            this.whipDropTextBox.Text = "0\"";
            this.whipDropTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.whipDropTextBox.Leave += new System.EventHandler(this.WhipDropTextBoxLeave);
            // 
            // whipDropLabel
            // 
            this.whipDropLabel.AutoSize = true;
            this.whipDropLabel.Location = new System.Drawing.Point(340, 11);
            this.whipDropLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.whipDropLabel.Name = "whipDropLabel";
            this.whipDropLabel.Size = new System.Drawing.Size(71, 16);
            this.whipDropLabel.TabIndex = 12;
            this.whipDropLabel.Text = "Whip Drop";
            // 
            // whipRiseLabel
            // 
            this.whipRiseLabel.AutoSize = true;
            this.whipRiseLabel.Location = new System.Drawing.Point(340, 43);
            this.whipRiseLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.whipRiseLabel.Name = "whipRiseLabel";
            this.whipRiseLabel.Size = new System.Drawing.Size(69, 16);
            this.whipRiseLabel.TabIndex = 13;
            this.whipRiseLabel.Text = "Whip Rise";
            // 
            // whipOverheadLabel
            // 
            this.whipOverheadLabel.AutoSize = true;
            this.whipOverheadLabel.Location = new System.Drawing.Point(340, 78);
            this.whipOverheadLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.whipOverheadLabel.Name = "whipOverheadLabel";
            this.whipOverheadLabel.Size = new System.Drawing.Size(101, 16);
            this.whipOverheadLabel.TabIndex = 14;
            this.whipOverheadLabel.Text = "Whip Overhead";
            // 
            // spliceLabel
            // 
            this.spliceLabel.AutoSize = true;
            this.spliceLabel.Location = new System.Drawing.Point(340, 113);
            this.spliceLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.spliceLabel.Name = "spliceLabel";
            this.spliceLabel.Size = new System.Drawing.Size(45, 16);
            this.spliceLabel.TabIndex = 15;
            this.spliceLabel.Text = "Splice";
            // 
            // whipRiseTextBox
            // 
            this.whipRiseTextBox.Location = new System.Drawing.Point(480, 39);
            this.whipRiseTextBox.Margin = new System.Windows.Forms.Padding(4);
            this.whipRiseTextBox.Name = "whipRiseTextBox";
            this.whipRiseTextBox.Size = new System.Drawing.Size(132, 22);
            this.whipRiseTextBox.TabIndex = 16;
            this.whipRiseTextBox.Text = "0\"";
            this.whipRiseTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.whipRiseTextBox.Leave += new System.EventHandler(this.WhipRiseTextBoxLeave);
            // 
            // whipOverheadTextBox
            // 
            this.whipOverheadTextBox.Location = new System.Drawing.Point(480, 74);
            this.whipOverheadTextBox.Margin = new System.Windows.Forms.Padding(4);
            this.whipOverheadTextBox.Name = "whipOverheadTextBox";
            this.whipOverheadTextBox.Size = new System.Drawing.Size(132, 22);
            this.whipOverheadTextBox.TabIndex = 17;
            this.whipOverheadTextBox.Text = "0\"";
            this.whipOverheadTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.whipOverheadTextBox.Leave += new System.EventHandler(this.WhipOverheadTextBoxLeave);
            // 
            // spliceTextBox
            // 
            this.spliceTextBox.Location = new System.Drawing.Point(480, 111);
            this.spliceTextBox.Margin = new System.Windows.Forms.Padding(4);
            this.spliceTextBox.Name = "spliceTextBox";
            this.spliceTextBox.Size = new System.Drawing.Size(132, 22);
            this.spliceTextBox.TabIndex = 18;
            this.spliceTextBox.Text = "0";
            this.spliceTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.spliceTextBox.Leave += new System.EventHandler(this.spliceTextBox_Leave);
            // 
            // firstPigtailCheckBox
            // 
            this.firstPigtailCheckBox.AutoSize = true;
            this.firstPigtailCheckBox.Location = new System.Drawing.Point(480, 175);
            this.firstPigtailCheckBox.Margin = new System.Windows.Forms.Padding(4);
            this.firstPigtailCheckBox.Name = "firstPigtailCheckBox";
            this.firstPigtailCheckBox.Size = new System.Drawing.Size(29, 20);
            this.firstPigtailCheckBox.TabIndex = 10;
            this.firstPigtailCheckBox.Text = "&";
            this.firstPigtailCheckBox.UseVisualStyleBackColor = true;
            // 
            // secondPigtailCheckBox
            // 
            this.secondPigtailCheckBox.AutoSize = true;
            this.secondPigtailCheckBox.Location = new System.Drawing.Point(480, 144);
            this.secondPigtailCheckBox.Margin = new System.Windows.Forms.Padding(4);
            this.secondPigtailCheckBox.Name = "secondPigtailCheckBox";
            this.secondPigtailCheckBox.Size = new System.Drawing.Size(29, 20);
            this.secondPigtailCheckBox.TabIndex = 19;
            this.secondPigtailCheckBox.Text = "&";
            this.secondPigtailCheckBox.UseVisualStyleBackColor = true;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(340, 149);
            this.label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(54, 16);
            this.label4.TabIndex = 20;
            this.label4.Text = "Pigtail 1";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(340, 180);
            this.label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(54, 16);
            this.label5.TabIndex = 21;
            this.label5.Text = "Pigtail 2";
            // 
            // FormFlexPipe2
            // 
            this.AcceptButton = this.btnOk;
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(783, 602);
            this.ControlBox = false;
            this.Controls.Add(this.label5);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.secondPigtailCheckBox);
            this.Controls.Add(this.spliceTextBox);
            this.Controls.Add(this.whipOverheadTextBox);
            this.Controls.Add(this.whipRiseTextBox);
            this.Controls.Add(this.spliceLabel);
            this.Controls.Add(this.whipOverheadLabel);
            this.Controls.Add(this.whipRiseLabel);
            this.Controls.Add(this.whipDropLabel);
            this.Controls.Add(this.whipDropTextBox);
            this.Controls.Add(this.firstPigtailCheckBox);
            this.Controls.Add(this.chkSetPipeTypeByLength);
            this.Controls.Add(this.txtLevelOffset);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.chkDeleteLines);
            this.Controls.Add(this.chkRound);
            this.Controls.Add(this.txtDiam);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.lstPipeTypes);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOk);
            this.Margin = new System.Windows.Forms.Padding(4);
            this.Name = "FormFlexPipe2";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Flex Pipe";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnOk;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.ListBox lstPipeTypes;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox txtDiam;
        private System.Windows.Forms.CheckBox chkRound;
        private System.Windows.Forms.CheckBox chkDeleteLines;
        private System.Windows.Forms.TextBox txtLevelOffset;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.CheckBox chkSetPipeTypeByLength;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox whipDropTextBox;
        private System.Windows.Forms.Label whipDropLabel;
        private System.Windows.Forms.Label whipRiseLabel;
        private System.Windows.Forms.Label whipOverheadLabel;
        private System.Windows.Forms.Label spliceLabel;
        private System.Windows.Forms.TextBox whipRiseTextBox;
        private System.Windows.Forms.TextBox whipOverheadTextBox;
        private System.Windows.Forms.TextBox spliceTextBox;
        private System.Windows.Forms.CheckBox firstPigtailCheckBox;
        private System.Windows.Forms.CheckBox secondPigtailCheckBox;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label5;
    }
}