﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.Pallets.Models;
using FreeAxez.Addin.AdditionalFunctional.Pallets.Utils;
using FreeAxez.Addin.AdditionalFunctional.Pallets.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System.Text;

namespace FreeAxez.Addin.AdditionalFunctional.Pallets
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    public class PalletsCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            if (RevitManager.UIDocument.ActiveView.ViewType != ViewType.FloorPlan)
            {
                InfoDialog.ShowDialog("Warning", "Pallet placement is only possible on the floor plan.");
                return Result.Cancelled;
            }

            // Check if the project contains the required families
            var palletFamilyManager = new PalletFamilyManager();
            if (!palletFamilyManager.ProjectContainsRequiredFamily())
            {
                InfoDialog.ShowDialog("Warning", $"No pallet family found in the project ending with \"Staging-Pallet\" or \"Staging_Pallet\".");
                return Result.Cancelled;
            }
            else if (!palletFamilyManager.ProjectContainsRequiredSymbols(out List<string> symbolNames))
            {
                InfoDialog.ShowDialog("Warning",
                    $"The pallet family does not contain the required types:\n" +
                    $"{string.Join("\n", symbolNames)}");
                return Result.Cancelled;
            }

            var unitCollector = new UnitCollector();
            var palletConfigurator = new PalletConfigurator(unitCollector);
            
            // Check that the view contains any units for pallet placement
            palletConfigurator.ConfigurePallets();
            if (palletConfigurator.GetPalletTypes().Count == 0 
                || palletConfigurator.GetPalletTypes().Count == 1 
                && palletConfigurator.GetPalletTypes().First() == PalletType.Undefined)
            {
                InfoDialog.ShowDialog("Warning", "There are no valid FreeAxez units on the level.");
                return Result.Cancelled;
            }
            else if (!palletConfigurator.GetPalletTypes().Contains(PalletType.BaseUnit))
            {
                InfoDialog.ShowDialog("Warning", "The level lacks the FreeAxez base units to create a grid for placing pallets.");
                return Result.Cancelled;
            }

            if (!PlacementOptionWindow.ShowDialog(ref unitCollector, out PlacementOption placementOption))
            {
                return Result.Cancelled;
            }

            // Configure the pallets again after changing selected region to the collector
            palletConfigurator.ConfigurePallets();

            // Check if the pallet plane is required
            var palletGrid = new PalletGrid(palletConfigurator, placementOption);
            if (!palletGrid.IsStagingPlaneRequired())
            {
                InfoDialog.ShowDialog("Warning",
                    "A staging plan is not required for the current level\n" +
                    "as the raised floor area is less than 400 feet.");
                return Result.Cancelled;
            }
            else if (palletConfigurator.GetUnitsAssociatedWithBaseUnits().Count == 0)
            {
                InfoDialog.ShowDialog("Warning",
                    "There are no base units in the selected placement region.\n" +
                    "Please select another region.");
                return Result.Cancelled;
            }

            var pallets = palletGrid.GetPallets();
            PlacePallets(palletFamilyManager, placementOption, pallets, unitCollector);
            ShowReport(pallets);

            return Result.Succeeded;
        }

        private void PlacePallets(
            PalletFamilyManager palletFamilyManager, 
            PlacementOption placementOption, 
            List<PalletLocation> pallets,
            UnitCollector unitCollector)
        {
            using (var t = new Transaction(RevitManager.Document, "Place Pallets"))
            {
                t.Start();

                var activeViewLevel = RevitManager.UIDocument.ActiveView.GenLevel;

                foreach (var pallet in pallets)
                {
                    var familySymbol = palletFamilyManager.GetFamilySymbolForPalletType(pallet.PalletType);

                    if (!familySymbol.IsActive)
                    {
                        familySymbol.Activate();
                    }

                    var palletInstance = RevitManager.Document.Create.NewFamilyInstance(
                        pallet.Location, familySymbol, activeViewLevel, Autodesk.Revit.DB.Structure.StructuralType.NonStructural);

                    palletInstance.get_Parameter(BuiltInParameter.ALL_MODEL_INSTANCE_COMMENTS)
                        .Set($"{pallet.PalletType.ToString()} {unitCollector.GriddType.ToString()}");

                    if (placementOption.VerticalDirection)
                    {
                        palletInstance.Location.Rotate(
                            Line.CreateBound(pallet.Location, new XYZ(pallet.Location.X, pallet.Location.Y, pallet.Location.Z + 1)),
                            Math.PI / 2);
                    }
                }

                t.Commit();
            }
        }

        private void ShowReport(List<PalletLocation> pallets)
        {
            var report = new StringBuilder();
            report.AppendLine("Created:");
            foreach (var group in pallets.GroupBy(p => p.PalletType))
            {
                report.AppendLine($"{group.Key.ToString()} - {group.Count()}");
            }
            InfoDialog.ShowDialog("Report", report.ToString());
        }
    }
}
