﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.Undersheet.Utils
{
    internal class Gridd
    {
        private readonly Line _startPosition;
        private readonly List<FamilyInstance> _units;
        private readonly double _undersheetWidth = 3.5;


        public Gridd(Line startPosition, List<FamilyInstance> units)
        {
            _startPosition = startPosition;
            _units = units;
            HorizontalOrientation = IsHorizontalOrientation(_startPosition);
        }


        public bool HorizontalOrientation { get; }

        public List<Line> Lines { get; private set; }


        public void CreateGridLines()
        {
            var grid = new List<Line>();

            var boundingBox = CreateBoundingBox(_units);

            if (HorizontalOrientation)
            {
                var yCoordinates = GetCoordinatesForAxis(_startPosition.GetEndPoint(0).Y, boundingBox.Min.Y, boundingBox.Max.Y);
                foreach (var y in yCoordinates)
                {
                    grid.Add(Line.CreateBound(
                        new XYZ(boundingBox.Min.X, y, boundingBox.Min.Z),
                        new XYZ(boundingBox.Max.X, y, boundingBox.Min.Z)));
                }
            }
            else
            {
                var xCoordinates = GetCoordinatesForAxis(_startPosition.GetEndPoint(0).X, boundingBox.Min.X, boundingBox.Max.X);
                foreach (var x in xCoordinates)
                {
                    grid.Add(Line.CreateBound(
                        new XYZ(x, boundingBox.Min.Y, boundingBox.Min.Z),
                        new XYZ(x, boundingBox.Max.Y, boundingBox.Min.Z)));
                }
            }

            Lines = grid;
        }

        private bool IsHorizontalOrientation(Line startPosition)
        {
            var start = startPosition.Evaluate(0, true);
            var end = startPosition.Evaluate(1, true);

            return Math.Abs(end.X - start.X) > Math.Abs(end.Y - start.Y);
        }
        
        private BoundingBoxXYZ CreateBoundingBox(List<FamilyInstance> units)
        {
            var boxes = units.Select(u => u.get_BoundingBox(RevitManager.Document.ActiveView));

            var mins = boxes.Select(b => b.Min);
            var minX = mins.Min(u => u.X);
            var minY = mins.Min(u => u.Y);
            
            var maxs = boxes.Select(b => b.Max);
            var maxX = maxs.Max(u => u.X);
            var maxY = maxs.Max(u => u.Y);
            
            var minZ = mins.First().Z;
            var maxZ = minZ + 1;

            return new BoundingBoxXYZ()
            {
                Min = new XYZ(minX, minY, minZ),
                Max = new XYZ(maxX, maxY, maxZ)
            };
        }
     
        private List<double> GetCoordinatesForAxis(double start, double min, double max)
        {
            var coordinates = new List<double>();

            while (start > min)
            {
                start -= _undersheetWidth;
            }
            while (start < min)
            {
                start += _undersheetWidth;
            }

            while (start <= max)
            {
                coordinates.Add(start);
                start += _undersheetWidth;
            }

            return coordinates;
        }
    }
}
