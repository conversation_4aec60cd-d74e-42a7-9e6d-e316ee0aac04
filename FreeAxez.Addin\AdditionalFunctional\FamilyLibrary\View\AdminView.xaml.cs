using System;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Interop;
using System.Windows.Media;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View;

public partial class AdminView : Window
{
    [DllImport("user32.dll")]
    private static extern IntPtr GetForegroundWindow();

    [DllImport("user32.dll")]
    private static extern IntPtr GetParent(IntPtr hWnd);

    public AdminView()
    {
        InitializeComponent();

        IntPtr revitHandle = GetForegroundWindow();
        IntPtr parentHandle = GetParent(revitHandle);
        IntPtr targetHandle = parentHandle != IntPtr.Zero ? parentHandle : revitHandle;

        Screen currentScreen = Screen.FromHandle(targetHandle);

        Matrix transformToDevice;
        HwndSourceParameters parameters = new HwndSourceParameters();
        parameters.ParentWindow = targetHandle;
        using (var source = new HwndSource(parameters))
        {
            transformToDevice = source.CompositionTarget.TransformToDevice;
        }
        double dpiFactorX = transformToDevice.M11;
        double dpiFactorY = transformToDevice.M22;

        double screenWidth = currentScreen.WorkingArea.Width / dpiFactorX;
        double screenHeight = currentScreen.WorkingArea.Height / dpiFactorY;

        this.Width = screenWidth * 0.8;
        this.Height = screenHeight * 0.8;
    }
}