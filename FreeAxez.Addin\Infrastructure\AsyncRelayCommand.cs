﻿using System;
using System.Threading.Tasks;
using System.Windows.Input;

namespace FreeAxez.Addin.Infrastructure;

public class AsyncRelayCommand : ICommand
{
    private readonly Func<object, bool> _canExecute;
    private readonly Func<object, Task> _executeAsync;

    public AsyncRelayCommand(Func<Task> executeAsync, Func<bool> canExecute = null)
    {
        _executeAsync = _ => executeAsync();
        _canExecute = _ => canExecute == null || canExecute();
    }

    public AsyncRelayCommand(Func<object, Task> executeAsync, Func<object, bool> canExecute = null)
    {
        _executeAsync = executeAsync ?? throw new ArgumentNullException(nameof(executeAsync));
        _canExecute = canExecute;
    }

    public bool CanExecute(object parameter)
    {
        return _canExecute == null || _canExecute(parameter);
    }

    public async void Execute(object parameter)
    {
        await _executeAsync(parameter);
    }

    public event EventHandler CanExecuteChanged
    {
        add => CommandManager.RequerySuggested += value;
        remove => CommandManager.RequerySuggested -= value;
    }
}