using System.Windows;
using System.Windows.Controls;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals
{
    public partial class DetailPreviewDialog : UserControl
    {
        public DetailPreviewDialogVm ViewModel => DataContext as DetailPreviewDialogVm;

        public DetailPreviewDialog()
        {
            InitializeComponent();
            LogHelper.Information("DetailPreviewDialog UserControl created");
        }
    }
}
