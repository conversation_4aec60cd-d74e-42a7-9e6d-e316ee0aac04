﻿﻿using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Dialog;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using Microsoft.Win32;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages
{
    public class DetailsPageVm : BasePageVm
    {
        private readonly DialogManager _dialogManager;
        private ObservableCollection<LibraryItemDetailsDto> _details;
        private bool _isLoading;
        private string _loadingStatus;
        private ObservableCollection<string> _fileTypes;
        private ObservableCollection<string> _revitVersions;
        private string _selectedFileType;
        private string _selectedRevitVersion;
        private bool _hasError;
        private string _errorMessage;
        private bool _isEmpty;

        public DetailsPageVm()
        {
            _dialogManager = new DialogManager();
            ResetFiltersCommand = new RelayCommand(ResetFilters);
            LoadDataCommand = new AsyncRelayCommand(async () => await LoadData());
            AddDetailsCommand = new RelayCommand(param => AddDetails());
            EditDetailsCommand = new RelayCommand(param => EditDetails((LibraryItemDetailsDto)param));
            DeleteDetailsCommand = new AsyncRelayCommand(async param => await ConfirmAndDeleteDetails(param));
            DownloadToPcCommand = new RelayCommand(ExecuteDownloadToPc);
            DownloadToRevitCommand = new RelayCommand(ExecuteDownloadToRevit);

            FileTypes = new ObservableCollection<string> { "RVT", "RFA", "RTE" };
            RevitVersions = new ObservableCollection<string>();

            Task.Run(LoadData);
        }

        public ICommand ResetFiltersCommand { get; }
        public ICommand LoadDataCommand { get; }
        public ICommand AddDetailsCommand { get; }
        public ICommand EditDetailsCommand { get; }
        public ICommand DeleteDetailsCommand { get; }
        public ICommand DownloadToPcCommand { get; }
        public ICommand DownloadToRevitCommand { get; }

        public ObservableCollection<LibraryItemDetailsDto> Details
        {
            get => _details;
            set
            {
                _details = value;
                OnPropertyChanged();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(ShowContent));
                OnPropertyChanged(nameof(ShowEmptyState));
                OnPropertyChanged(nameof(ShowErrorState));
            }
        }

        public string LoadingStatus
        {
            get => _loadingStatus;
            set
            {
                _loadingStatus = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<string> FileTypes
        {
            get => _fileTypes;
            set
            {
                _fileTypes = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<string> RevitVersions
        {
            get => _revitVersions;
            set
            {
                _revitVersions = value;
                OnPropertyChanged();
            }
        }

        public string SelectedFileType
        {
            get => _selectedFileType;
            set
            {
                _selectedFileType = value;
                OnPropertyChanged();
                Filter();
            }
        }

        public string SelectedRevitVersion
        {
            get => _selectedRevitVersion;
            set
            {
                _selectedRevitVersion = value;
                OnPropertyChanged();
                Filter();
            }
        }

        public bool HasError
        {
            get => _hasError;
            set
            {
                _hasError = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(ShowContent));
                OnPropertyChanged(nameof(ShowEmptyState));
                OnPropertyChanged(nameof(ShowErrorState));
            }
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                _errorMessage = value;
                OnPropertyChanged();
            }
        }

        public bool IsEmpty
        {
            get => _isEmpty;
            set
            {
                _isEmpty = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(ShowContent));
                OnPropertyChanged(nameof(ShowEmptyState));
                OnPropertyChanged(nameof(ShowErrorState));
            }
        }

        // Computed properties for UI visibility
        public bool ShowContent => !IsLoading && !HasError && !IsEmpty;
        public bool ShowEmptyState => !IsLoading && !HasError && IsEmpty;
        public bool ShowErrorState => !IsLoading && HasError;

        public async Task LoadData()
        {
            IsLoading = true;
            HasError = false;
            IsEmpty = false;
            ErrorMessage = null;

            try
            {
                var details = await ApiService.Instance.GetAllDetailsAsync();
                string currentRevitVersion = FamilyLibraryCore.CurrentRevitVersion;

                Details = new ObservableCollection<LibraryItemDetailsDto>();
                RevitVersions = new ObservableCollection<string>();

                if (details == null || !details.Any())
                {
                    IsEmpty = true;
                    return;
                }

                var filteredDetails = details
                    .Where(d => FamilyLibraryCore.IsRevitVersionCompatible(d.RevitVersion, currentRevitVersion))
                    .ToList();

                if (!filteredDetails.Any())
                {
                    IsEmpty = true;
                    return;
                }

                Details = new ObservableCollection<LibraryItemDetailsDto>(filteredDetails);
                RevitVersions = new ObservableCollection<string>(
                    filteredDetails
                        .Where(d => !string.IsNullOrEmpty(d.RevitVersion))
                        .Select(d => d.RevitVersion)
                        .Distinct()
                        .OrderBy(v => v));
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error loading details: {ex.Message}");
                HasError = true;
                ErrorMessage = ex.Message;
            }
            finally
            {
                IsLoading = false;
            }
        }

        protected override void Filter()
        {
            var view = CollectionViewSource.GetDefaultView(Details);
            view.Filter = item =>
            {
                var details = item as LibraryItemDetailsDto;
                if (details == null) return false;

                if (!string.IsNullOrEmpty(SelectedFileType))
                {
                    var fileExtension = Path.GetExtension(details.FilePath)?.ToUpperInvariant().Replace(".", "");
                    if (fileExtension != SelectedFileType)
                        return false;
                }

                if (!string.IsNullOrEmpty(SelectedRevitVersion))
                {
                    if (details.RevitVersion != SelectedRevitVersion)
                        return false;
                }

                if (!string.IsNullOrEmpty(SearchText))
                {
                    return details.Name.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                           (details.Description != null && details.Description.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0);
                }

                return true;
            };

            view.Refresh();
        }

        private void ResetFilters(object parameter)
        {
            SelectedFileType = null;
            SelectedRevitVersion = null;
            SearchText = string.Empty;
            Filter();
        }

        private async void OnDialogClose(bool? result)
        {
            if (result == true) await LoadData();
        }

        private void AddDetails()
        {
            var handler = FamilyLibraryCore.DetailsAddHandler;
            var externalEvent = FamilyLibraryCore.DetailsAddEvent;

            if (handler != null && externalEvent != null)
            {
                handler.SetData(this);
                externalEvent.Raise();
            }
            else
            {
                LogHelper.Error("DetailsAddHandler or ExternalEvent is null");
                FamilyLibraryCore.ShowMessage("Add Details", "Details add handler not initialized.", MessageType.Error);
            }
        }

        private void EditDetails(LibraryItemDetailsDto details)
        {
            var handler = FamilyLibraryCore.DetailsEditHandler;
            var externalEvent = FamilyLibraryCore.DetailsEditEvent;

            if (handler != null && externalEvent != null)
            {
                handler.SetData(details, this);
                externalEvent.Raise();
            }
            else
            {
                LogHelper.Error("DetailsEditHandler or ExternalEvent is null");
                FamilyLibraryCore.ShowMessage("Edit Details", "Details edit handler not initialized.", MessageType.Error);
            }
        }

        private Task ConfirmAndDeleteDetails(object parameter)
        {
            if (parameter is LibraryItemDetailsDto details)
            {
                _dialogManager.ShowDeleteDetailsDialog(details, OnDialogClose);
            }
            return Task.CompletedTask;
        }

        private void ExecuteDownloadToPc(object parameter)
        {
            if (parameter is LibraryItemDetailsDto details)
            {
                try
                {
                    string fileExtension = Path.GetExtension(details.FilePath).ToUpperInvariant().Replace(".", "");

                    var filter = fileExtension.Equals("RFA", StringComparison.OrdinalIgnoreCase)
                        ? "Revit Family Files (*.rfa)|*.rfa"
                        : fileExtension.Equals("RTE", StringComparison.OrdinalIgnoreCase)
                            ? "Revit Template Files (*.rte)|*.rte"
                            : "Revit Project Files (*.rvt)|*.rvt";

                    var saveFileDialog = new SaveFileDialog
                    {
                        FileName = details.Name,
                        Filter = filter
                    };

                    if (saveFileDialog.ShowDialog() == true)
                    {
                        using (var client = new WebClient())
                        {
                            client.DownloadFile(details.FilePath, saveFileDialog.FileName);
                        }

                        FamilyLibraryCore.ShowMessage("Download to PC",
                            $"File '{details.Name}' has been saved successfully.", MessageType.Success);
                    }
                }
                catch (Exception ex)
                {
                    FamilyLibraryCore.ShowMessage($"Error saving file: {ex.Message}",
                        "Save Error", MessageType.Error);
                }
            }
        }

        private async void ExecuteDownloadToRevit(object parameter)
        {
            try
            {
                LogHelper.Information("ExecuteDownloadToRevit called");
                if (parameter is LibraryItemDetailsDto details)
                {
                    LogHelper.Information($"Processing details: {details.Name}");
                    await Services.Details.DetailsContentProcessor.ProcessAsync(details);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Failed to process details: {ex.Message}");
                MessageBox.Show($"Error: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }


    }
}
