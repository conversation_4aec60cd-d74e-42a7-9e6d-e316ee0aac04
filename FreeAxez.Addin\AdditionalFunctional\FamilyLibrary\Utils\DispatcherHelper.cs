using System;
using System.Threading;
using System.Windows;
using System.Windows.Threading;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils
{
    public class DispatcherHelper
    {
        /// <summary>
        /// Finds or creates a dispatcher thread with STA apartment state and executes the given action on that thread.
        /// </summary>
        /// <param name="act"></param>
        public static void ExecuteActionOnDispatcherSTA(Action act)
        {
            var dsp = Application.Current == null
                ? Dispatcher.CurrentDispatcher
                : Application.Current.Dispatcher;

            if (dsp == null)
            {
                try
                {
                    dsp = new Application().Dispatcher;
                }
                catch
                {
                    // ignored
                }
            }

            if (dsp == null) throw new Exception("Failed to find or create dispatcher for content loading");

            if (dsp.Thread.GetApartmentState() != ApartmentState.STA)
            {
                var th = new Thread(() =>
                {
                    SynchronizationContext.SetSynchronizationContext(
                        new DispatcherSynchronizationContext(Dispatcher.CurrentDispatcher));

                    act();

                    Dispatcher.Run();
                });
                th.SetApartmentState(ApartmentState.STA);
                th.IsBackground = false;
                th.Start();
            }
            else
            {
                if (dsp.CheckAccess()) act();
                else dsp.Invoke(act);
            }
        }
    }
}
