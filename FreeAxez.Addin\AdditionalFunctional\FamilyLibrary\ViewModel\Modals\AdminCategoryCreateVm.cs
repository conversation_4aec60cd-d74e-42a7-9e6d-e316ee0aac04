﻿using System;
using System.Threading.Tasks;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;

public class AdminCategoryCreateVm : AdminCategoryBaseVm
{
    public AdminCategoryCreateVm(LibraryCategoryDto categoryToEdit = null)
        : base(categoryToEdit)
    {
    }

    protected override bool CanApply()
    {
        return !string.IsNullOrWhiteSpace(Name);
    }

    protected override async Task ApplyAsync()
    {
        try
        {
            var newCategory = new LibraryCategoryDto { CategoryName = Name, Description = Description, IsFreeAxezCategory = IsFreeAxezCategory };
            var response = await ApiService.Instance.AddCategoryAsync(newCategory);

            HandleResponse(response, result => CloseDialog(result));
        }
        catch (Exception ex)
        {
            Error = $"Error: {ex.Message}";
            LogHelper.Error($"An error occurred: {ex.Message}");
        }
    }
}