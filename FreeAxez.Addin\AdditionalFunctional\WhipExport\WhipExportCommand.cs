﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.Attributes;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.AdditionalFunctional.WhipExport.Views;
using System.Windows.Interop;

namespace FreeAxez.Addin.AdditionalFunctional.WhipExport
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]    
    internal class WhipExportCommand : BaseExternalCommand
    {                 
        private static WhipExportWindow _exportWindow;      

        public override Result Execute()
        {
            if (_exportWindow != null && _exportWindow.IsVisible) _exportWindow.Close();

            _exportWindow = new WhipExportWindow();
            var handler = new WindowInteropHelper(_exportWindow);
            handler.Owner = RevitManager.UIApplication.MainWindowHandle;
            _exportWindow.Show();

            return Result.Succeeded;
        }
    }
}
