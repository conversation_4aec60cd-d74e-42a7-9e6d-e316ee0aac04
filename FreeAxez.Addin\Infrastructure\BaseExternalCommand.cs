﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using System.Reflection;
using System.IO;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.Enums;

namespace FreeAxez.Addin.Infrastructure
{
    public abstract class BaseExternalCommand : IExternalCommand
    {
        public abstract Result Execute();

        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            AppDomain.CurrentDomain.AssemblyResolve += OnAssemblyResolve;
            LogHelper.Information($"The {GetType().Name} Started");
            RevitManager.CommandData = commandData;

#if revit2018
#else
            MessageWindow.MainWindowHandler = RevitManager.UIApplication.MainWindowHandle;
#endif

            var result = Result.Succeeded;
            try
            {
                result = Execute();
            }
            catch (Exception exception)
            {
                result = Result.Failed;
                LogHelper.Error(exception.Message + "\n" + exception.StackTrace);

                var errorMessage = "Unexpected runtime error, please contact developers.\n\n";

                try
                {
                    var documentName = RevitManager.CommandData?.Application?.ActiveUIDocument?.Document?.Title;
                    if (!string.IsNullOrEmpty(documentName))
                    {
                        errorMessage = $"Document: {documentName}\n\n" + errorMessage;
                    }
                }
                catch
                {
                    // Ignore any errors when trying to get document name
                }

                errorMessage += exception.Message + "\n" + exception.StackTrace;

                MessageWindow.ShowDialog(errorMessage, MessageType.Error);
            }
            finally
            {
                LogHelper.Information($"The {GetType().Name} {result}");
                AppDomain.CurrentDomain.AssemblyResolve -= new ResolveEventHandler(OnAssemblyResolve);
            }

            return result;
        }

        Assembly OnAssemblyResolve(object sender, ResolveEventArgs args)
        {
            Assembly assembly = null;

            LogHelper.Warning($"Try to resolve assembly {args.Name}");

            var assemblyName = args.Name.Split(',').FirstOrDefault();
            var directory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            var assemblyFile = Path.Combine(directory, assemblyName + ".dll");
            if (File.Exists(assemblyFile))
            {
                assembly = Assembly.LoadFrom(assemblyFile);
                LogHelper.Warning($"Successfully resolving assembly {args.Name} from {assemblyFile}");
            }
            else
            {
                LogHelper.Warning($"Failed to resolve assembly {args.Name} does not exist in {assemblyFile}");
            }

            return assembly;
        }
    }
}
