﻿using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Models;
using FreeAxez.Addin.Infrastructure.UI;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Utils
{
    public class GriddBomToExcelExport
    {
        private string _filePath;
        private readonly GriddBomRevision _griddBomRevision;
        private ExcelDocument _excelDocument;


        public GriddBomToExcelExport(string filePath, GriddBomRevision griddBomRevision)
        {
            _filePath = filePath;
            _griddBomRevision = griddBomRevision;
        }


        public void Export()
        {
            try
            {
                var sheetName = ReplaceInvalidCharacters(_griddBomRevision.SheetName);

                _excelDocument = ExcelDocument.Open(_filePath);
                CreateSheetIfNotExist(sheetName);
                DeleteDataFromRevSheet(sheetName);
                UpdateHeaderInformation(sheetName);

                for (int levelIndex = 0; levelIndex < _griddBomRevision.Levels.Count; levelIndex++)
                {
                    var cell = GriddBomExcelSchema.GetLevelCell(levelIndex);
                    _excelDocument.UpdateCell(sheetName, cell.row, cell.column,
                        _griddBomRevision.Levels[levelIndex].Number.ToString());

                    cell = GriddBomExcelSchema.GetAreaSFCell(levelIndex);
                    _excelDocument.UpdateCell(sheetName, cell.row, cell.column,
                        _griddBomRevision.Levels[levelIndex].LevelArea.ToString());


                    cell = GriddBomExcelSchema.GetGriddSFCell(levelIndex);
                    _excelDocument.UpdateCell(sheetName, cell.row, cell.column,
                        _griddBomRevision.Levels[levelIndex].GriddArea.ToString());


                    cell = GriddBomExcelSchema.GetReinforcedCell(levelIndex);
                    _excelDocument.UpdateCell(sheetName, cell.row, cell.column,
                        _griddBomRevision.Levels[levelIndex].ReinforcedArea.ToString());

                    cell = GriddBomExcelSchema.GetScheduleStartCell(levelIndex);
                    var data = _griddBomRevision.Levels[levelIndex].Products.Select(p => p.GetData()).ToList();
                    _excelDocument.UpdateCells(sheetName, cell.row, cell.column, data);

                }

                _excelDocument.ForceFormulaRecalculation(sheetName);
                _excelDocument.ActivateSheet(sheetName);
                _excelDocument.Save();
                
                MessageWindow.ShowDialog("Export Gridd BOM", "Export completed.", MessageType.Success);
                
                if (Properties.Settings.Default.exportGriddBOMOpenFile)
                {
                    try
                    {
                        var startInfo = new ProcessStartInfo()
                        {
                            FileName = _filePath,
                            UseShellExecute = true
                        };

                        Process.Start(startInfo);
                    }
                    catch (Exception ex)
                    {
                        MessageWindow.ShowDialog("Error Opening File", "Failed to open the exported file: " + ex.Message, MessageType.Error);
                    }

                }
            }
            catch (InvalidOperationException ex)
            {
                MessageWindow.ShowDialog("Export Error", ex.Message, MessageType.Error);
            }
            catch (UnauthorizedAccessException ex)
            {
                MessageWindow.ShowDialog("Export Error", "The file is locked by another process or syncing: " + ex.Message, MessageType.Info);
            }
            catch (IOException ex)
            {
                MessageWindow.ShowDialog("Export Gridd BOM", ex.Message, MessageType.Info);
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog("Export Error", "An unexpected error occurred: " + ex.Message, MessageType.Error);
            }
        }

        private void UpdateHeaderInformation(string sheetName)
        {
            _excelDocument.UpdateCell(sheetName, 10, 1, _griddBomRevision.RevisionDate);
            _excelDocument.UpdateCell(sheetName, 11, 1, _griddBomRevision.RevisionAuthor);
            _excelDocument.UpdateCell(sheetName, 12, 1, _griddBomRevision.RevisionNumber);
        }

        private void CreateSheetIfNotExist(string sheetName)
        {
            try
            {
                if (_excelDocument.GetSheetNames().Contains(sheetName)) return;

                var revisionNumber = -1;
                var lastRevSheetName = "";
                foreach (var currentSheetName in _excelDocument.GetSheetNames())
                {
                    var currentRevNumber = GriddBomExcelSchema.GetRevNumber(currentSheetName);
                    if (currentRevNumber > revisionNumber)
                    {
                        revisionNumber = currentRevNumber;
                        lastRevSheetName = currentSheetName;
                    }
                }

                if (revisionNumber == -1)
                {
                    throw new InvalidOperationException("Excel does not include a revision template sheet.");
                }

                _excelDocument.CopySheet(lastRevSheetName, sheetName);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"The sheet could not be copied due to the error: { ex.Message }.Please try copying the sheet manually.");
            }
        }

        private void DeleteDataFromRevSheet(string sheetName)
        {
            for (int i = 0; i < GriddBomExcelSchema.NumberOfPreparedLevelsOnRevSheet; i++)
            {
                var cell = GriddBomExcelSchema.GetLevelCell(i);
                _excelDocument.UpdateCell(sheetName, cell.row, cell.column, string.Empty);

                cell = GriddBomExcelSchema.GetAreaSFCell(i);
                _excelDocument.UpdateCell(sheetName, cell.row, cell.column, string.Empty);

                cell = GriddBomExcelSchema.GetGriddSFCell(i);
                _excelDocument.UpdateCell(sheetName, cell.row, cell.column, string.Empty);

                cell = GriddBomExcelSchema.GetReinforcedCell(i);
                _excelDocument.UpdateCell(sheetName, cell.row, cell.column, string.Empty);
            }

            var scheduleRowRange = GriddBomExcelSchema.GetScheduleRowRange();
            // Different sheets have a different number of rows, so should use the actual number of lines
            var numberOfRows = _excelDocument.GetNumberOfRows(sheetName);
            for (int i = scheduleRowRange.rowStart; i < numberOfRows; i++)
            {
                _excelDocument.CleanRow(sheetName, i);
            }
        }

        private string ReplaceInvalidCharacters(string stringToReplace)
        {
            StringBuilder stringBuilder = new StringBuilder();

            char[] invalidCharacters = { '\\', '/', '?', '*', '[', ']' };
            char[] charArray = stringToReplace.ToCharArray();

            foreach (char @char in charArray)
            {
                if (invalidCharacters.Contains(@char))
                {
                    if (@char == '/' || @char == '\\'
                        || @char == '?' || @char == '*')
                    {
                        stringBuilder.Append('-');
                    }
                    else if (@char == '[')
                    {
                        stringBuilder.Append('(');
                    }
                    else if (@char == ']')
                    {
                        stringBuilder.Append(')');
                    }
                }
                else
                {
                    stringBuilder.Append(@char);
                }
            }

            return stringBuilder.ToString();
        }
    }
}
