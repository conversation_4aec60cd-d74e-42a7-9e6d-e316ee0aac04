# DWG Layer Manager

Инструмент для управления слоями AutoCAD в Revit.

## 📋 Функциональность

### Вкладка 1: Менеджер слоёв FreeAxez
- Управление слоями в базе данных на сервере
- Добавление, удаление и редактирование слоёв
- Настройка свойств слоёв: цвет, тип линии, толщина, прозрачность
- Настройка VP-свойств для видовых экранов
- Поиск по таблице слоёв

### Вкладка 2: Mapping слоёв DWG
- Выбор DWG файла для анализа
- Извлечение информации о слоях из DWG
- Отображение статистики объектов на каждом слое
- Назначение слоёв FreeAxez для замены слоёв DWG
- Выполнение замены слоёв в файле

## 🏗️ Архитектура

### Структура проекта
```
DWGLayerManager/
├── DWGLayerManagerCommand.cs          # Главная команда Revit
├── Views/                             # WPF интерфейс
│   ├── DwgLayerManagerWindow.xaml     # Главное окно с двумя вкладками
│   ├── FreeAxezLayersWindow.xaml      # Окно управления слоями FreeAxez
│   ├── SelectLinetypeWindow.xaml      # Диалог выбора типов линий
│   └── LayerTransparencyWindow.xaml   # Диалог выбора прозрачности
├── ViewModels/                        # MVVM ViewModels
│   ├── LayerManagerViewModel.cs       # ViewModel для управления слоями
│   ├── DwgMappingViewModel.cs         # ViewModel для маппинга DWG
│   └── DwgLayerManagerWindowViewModel.cs # Главный ViewModel
├── Models/                            # Модели данных
│   ├── Core/                          # Основные модели
│   │   ├── LayerModel.cs              # Модель слоя FreeAxez
│   │   ├── LinetypeModel.cs           # Модель типа линии
│   │   ├── DwgLayerInfo.cs            # Информация о слое DWG
│   │   ├── AutoCADInstallation.cs     # Информация об установке AutoCAD
│   │   └── LineweightItem.cs          # Элемент толщины линии
│   ├── Interfaces/                    # Интерфейсы
│   │   └── ILayerInfo.cs              # Интерфейсы для слоев
│   └── Api/                           # DTO для API
│       ├── LayerApiDto.cs             # DTO для слоев
│       ├── LinetypeApiDto.cs          # DTO для типов линий
│       └── MergeLayersRequest.cs      # Запрос на слияние слоев
├── Services/                          # Бизнес-логика
│   ├── DwgLayerManagerApiService.cs   # REST API клиент
│   ├── DwgLayerService.cs             # Работа с DWG файлами
│   ├── AutoCADInvoker.cs              # Вызов AutoCAD команд
│   ├── AutoCADVersionService.cs       # Управление версиями AutoCAD
│   ├── FreeAxezLayerOperations.cs     # Операции со слоями FreeAxez
│   ├── LayerPropertyService.cs        # Управление свойствами слоев
│   ├── LayerDialogService.cs          # Диалоги для слоев
│   ├── LayerMappingOperations.cs      # Операции маппинга слоев
│   ├── DwgFileOperationService.cs     # Операции с DWG файлами
│   └── LineweightService.cs           # Сервис толщины линий
└── Infrastructure/
    └── TempFileManager.cs             # Управление временными файлами
```

### AutoCAD Plugin расширения
Добавлены новые команды в `FreeAxez.AutoCAD.Plugin`:
- `EXTRACT_LAYERS` - извлечение информации о слоях
- `REPLACE_LAYERS` - замена слоёв в DWG файле

## 🔧 Настройка

### Требования
- AutoCAD Core Console (любая поддерживаемая версия)
- Удалённый сервер с REST API для управления слоями
- .NET Framework совместимый с Revit

### Конфигурация сервера
В `ServerLayerService.cs` настройте URL сервера:
```csharp
_baseUrl = "https://api.freeaxez.com"; // Замените на ваш URL
```

### API Endpoints
Сервер должен предоставлять следующие endpoints:

#### Слои
- `GET /api/layers` - получить все слои
- `GET /api/layers/{id}` - получить слой по ID
- `POST /api/layers` - создать новый слой
- `PUT /api/layers/{id}` - обновить слой
- `DELETE /api/layers/{id}` - удалить слой

#### Типы линий
- `GET /api/linetypes` - получить все типы линий
- `POST /api/linetypes` - создать новый тип линии
- `DELETE /api/linetypes/{name}` - удалить тип линии

## 🚀 Использование

1. Запустите команду DWG Layer Manager в Revit
2. **Вкладка 1**: Управляйте слоями FreeAxez
   - Добавляйте новые слои кнопкой "Add New Layer"
   - Редактируйте свойства прямо в таблице
   - Выбирайте цвета и типы линий
   - Удаляйте ненужные слои
3. **Вкладка 2**: Работайте с DWG файлами
   - Выберите DWG файл кнопкой "Browse..."
   - Просмотрите извлечённые слои и их статистику
   - Назначьте слои FreeAxez для замены
   - Выполните замену кнопкой "Execute Layer Replacement"

## 🔍 Особенности

### Автоматическое определение AutoCAD
Система автоматически находит установленную версию AutoCAD через реестр Windows.

### Парсинг .lin файлов
Поддерживается загрузка типов линий из стандартных файлов AutoCAD (acad.lin).

### Валидация данных
- Проверка существования DWG файлов
- Валидация доступности AutoCAD Core Console
- Проверка корректности типов линий

### Обработка ошибок
- Graceful fallback при недоступности сервера
- Подробные сообщения об ошибках
- Логирование операций AutoCAD (сохраняется в файлы рядом с DWG)

### Рекурсивный поиск AutoCAD
Система использует рекурсивный поиск в реестре Windows для определения установленной версии AutoCAD, аналогично поиску Revit. Поддерживаются как 64-битные, так и 32-битные установки.

## 📝 Разработка

### Добавление новых свойств слоя
1. Обновите `LayerModel.cs` и соответствующие DTO
2. Добавьте поля в DataGrid в XAML
3. Обновите методы конвертации в ViewModels

### Расширение AutoCAD команд
1. Добавьте новые команды в `LineExtractionPlugin.cs`
2. Обновите `DwgLayerService.cs` для вызова новых команд
3. Добавьте соответствующие модели данных

---
*Создано с использованием существующей архитектуры FreeAxez и лучших практик MVVM.*
