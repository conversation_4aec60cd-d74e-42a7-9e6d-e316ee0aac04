﻿using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using FreeAxez.Core.Services;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace FreeAxez.Addin.AdditionalFunctional.ExportCutsheet.Models
{
    public class ProductDocumentationDownloader
    {
        private readonly List<string> _productDocumentationUrls;
        private readonly string _folderPath;
        private readonly CancellationToken _cancellationToken;

        public ProductDocumentationDownloader(List<string> productDocumentationUrls,
                                              string folderPath,
                                              CancellationToken cancellationToken)
        {
            _productDocumentationUrls = productDocumentationUrls;
            _folderPath = folderPath;
            _cancellationToken = cancellationToken;
        }

        public async Task DownloadAsync()
        {
            try
            {
                foreach (var url in _productDocumentationUrls)
                {
                    var filePath = Path.Combine(_folderPath, Path.GetFileName(url));
                    await DownloadFilesAsync(url, filePath);
                }
            }
            catch (OperationCanceledException)
            {
                LogHelper.Warning("The download operation has been cancelled");
                InfoDialog.ShowDialog("Warning", 
                    "Export was cancelled.\n" +
                    "All necessary files weren't downloaded.");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Failed to download files {ex.Message}");
                InfoDialog.ShowDialog("Error",
                    $"Failed to download files {ex.Message}.");
            }
        }

        private async Task DownloadFilesAsync(string url, string filePath)
        {
            byte[] response = await FreeAxezHttpClient.Instance.GetByteArrayAsync(url);
            _cancellationToken.ThrowIfCancellationRequested();

            using (FileStream sourceStream = new FileStream(filePath,
                FileMode.Create, FileAccess.Write, FileShare.None,
                bufferSize: 4096, useAsync: true))
            {
                await sourceStream.WriteAsync(response, 0, response.Length);
            };
        }
    }
}
