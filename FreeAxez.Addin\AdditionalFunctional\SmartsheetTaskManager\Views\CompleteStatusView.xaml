﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Views.CompleteStatusView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
        xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Converters"
        xmlns:controls="clr-namespace:FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Controls"
        mc:Ignorable="d" 
        Title="Complete Status"
        Height="400" Width="400"
        MinHeight="400" MinWidth="400"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResizeWithGrip"
        Closing="Window_Closing"
        Closed="Window_Closed">

    <Window.Resources>
        <ResourceDictionary >
            <converters:FilePathToFileNameConverter x:Key="FilePathToFileNameConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/AdditionalFunctional/SmartsheetTaskManager/Styles/Styles.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/AdditionalFunctional/SmartsheetTaskManager/Styles/LoadingSpinnerStyle.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/AdditionalFunctional/SmartsheetTaskManager/Styles/PlaceHolderTextBoxStyle.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="30"/>
            <RowDefinition MinHeight="130" />
            <RowDefinition Height="20"/>
            <RowDefinition MinHeight="130" Height="*"/>
            <RowDefinition Height="50"/>
        </Grid.RowDefinitions>

        <TextBlock Text="Attached Files:" Margin="10 5 10 5"
                   VerticalAlignment="Bottom"
                   Grid.Row="0"/>

        <ListView Grid.Row="1" ItemsSource="{Binding AttachedFilePaths}"
                  SelectedItem="{Binding SelectedFile}"
                  Style="{StaticResource ListView}"
                  ItemContainerStyle="{StaticResource ListViewItemNotFocusable}"
                  Margin="10 0 10 5">
            <ListView.View>
                <GridView>
                    <GridViewColumn Width="360">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding Converter={StaticResource FilePathToFileNameConverter}}"
                                               Margin="0 0 10 5"/>
                                    <Button Content="&#x2715;"
                                            Width="20"
                                            Foreground="Red"
                                            Command="{Binding DataContext.DeleteFileCommand,
                                                    RelativeSource={RelativeSource AncestorType={x:Type ListView}}}"
                                            CommandParameter="{Binding RelativeSource={RelativeSource Mode=Self},
                                                    Path=DataContext}">
                                        <Button.Template>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="5">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#FFE9F1FB"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Button.Template>
                                        <Button.Style>
                                            <Style TargetType="Button">
                                                <Setter Property="Background" Value="Transparent" />
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>

        <TextBlock Text="Comment:" Margin="10 0 0 0"
                   Grid.Row="2"/>

        <controls:PlaceholderTextBox Placeholder="Write a comment..."
                                     x:Name="PlaceholderTextBox"
                                     Grid.Row="3"
                                     PreviewKeyDown="PlaceholderTextBox_PreviewKeyDown"
                                     Margin="10 0 10 0"
                                     Padding="0 3 0 0"
                                     TextWrapping="Wrap"
                                     Text="{Binding Comment, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                     HorizontalContentAlignment="Left"
                                     VerticalContentAlignment="Top"
                                     MaxHeight="130"/>

        <StackPanel Orientation="Horizontal" Grid.Row="4"
                    HorizontalAlignment="Right" Margin="10 10 10 10"
                    VerticalAlignment="Bottom">
            <Button Content="Add files"
                    Command="{Binding AddFilesCommand}"
                    Style="{StaticResource CommonButtonStyle}"
                    Width="80" Height="25"
                    Margin="0 0 5 0"/>

            <Button Content="Ok"
                    Command="{Binding OkCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor,
                                        AncestorType={x:Type Window}}}"
                    Style="{StaticResource CommonButtonStyle}"
                    Width="60" Height="25"
                    Margin="5 0 5 0"/>

            <Button Content="Cancel"
                    Command="{Binding CancelCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor,
                                        AncestorType={x:Type Window}}}"
                    Style="{StaticResource CommonButtonStyle}"
                    Width="60" Height="25"
                    Margin="5 0 0 0"/>
        </StackPanel>
    </Grid>
</Window>
