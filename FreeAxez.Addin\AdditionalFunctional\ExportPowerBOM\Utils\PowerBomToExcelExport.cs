﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Models;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Spreadsheets;
using FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.ViewModels;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using CellType = NPOI.SS.UserModel.CellType;
using FillPattern = NPOI.SS.UserModel.FillPattern;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Utils
{
    public class PowerBomToExcelExport
    {
        private const string PathToTemplate = @"Content\ExportPowerBom.xlsx";
        private const string PriceSheetName = "Prices";
        private const string RevisionSheetNameInTemplate = "Template";
        private const string DetailsSheetNameInTemplate = "Template Details";

        private readonly string _path;
        private readonly PowerBomRevisionViewModel _revision;
        private readonly PowerBomDetailsViewModel _details;
        private readonly PowerBomFloorBoxCountViewModel _floorBoxCount;
        private readonly FormulaBuilder _formulaBuilder;


        public PowerBomToExcelExport(string path, 
            PowerBomRevisionViewModel revision, 
            PowerBomDetailsViewModel details, 
            PowerBomFloorBoxCountViewModel floorBoxCountViewModel)
        {
            _path = path;
            _revision = revision;
            _details = details;
            _floorBoxCount = floorBoxCountViewModel;
            _formulaBuilder = new FormulaBuilder();
        }


        private enum SheetType
        {
            Revision,
            Details,
            Empty
        }


        public void Export()
        {
            try
            {
                using (FileStream file = new FileStream(_path, FileMode.Open, FileAccess.ReadWrite))
                {
                    var workbook = new XSSFWorkbook(file);
                    var cellStylesConverter = new CellStyleConverter(workbook);

                    CreatePriceSheetIfNotExist(workbook);

                    var correctedRevisionSheetName = ReplaceInvalidCharacters(_revision.SheetName);

                    var revisionSheet = GetSheet(workbook, correctedRevisionSheetName, SheetType.Revision);
                    CleanData(revisionSheet, PowerBomExcelSchema.StartReportCell.Row);
                    WriteData(revisionSheet, cellStylesConverter, _revision);

                    var correctedDetailsSheetName = ReplaceInvalidCharacters(_details.SheetName);

                    var detailsSheet = GetSheet(workbook, correctedDetailsSheetName, SheetType.Details);
                    CleanData(detailsSheet, PowerBomExcelSchema.StartReportCell.Row);
                    WriteData(detailsSheet, cellStylesConverter, _details);

                    var floorBoxCountSheetName = ReplaceInvalidCharacters(_floorBoxCount.SheetName);

                    var floorBoxCountSheet = GetSheet(workbook, floorBoxCountSheetName, SheetType.Empty);
                    CleanData(floorBoxCountSheet, 0);
                    WriteReport(floorBoxCountSheet, cellStylesConverter, _floorBoxCount.Report, (0, 0));

                    SetActiveSheet(workbook, revisionSheet);
                    Save(workbook);
                }

                MessageWindow.ShowDialog("Export Power BOM", "Export completed.", MessageType.Success);

                if (Properties.Settings.Default.exportPowerBomOpenFile)
                {
                    try
                    {
                        var startInfo = new ProcessStartInfo()
                        {
                            FileName = _path,
                            UseShellExecute = true
                        };

                        Process.Start(startInfo);
                    }
                    catch (Exception ex)
                    {
                        MessageWindow.ShowDialog("Error Opening File", "Failed to open the exported file: " + ex.Message, MessageType.Error);
                    }
                }
            }
            catch (InvalidOperationException ex)
            {
                MessageWindow.ShowDialog("Export Error", ex.Message, MessageType.Error);
            }
            catch (UnauthorizedAccessException ex)
            {
                MessageWindow.ShowDialog("Export Error", "The file is locked by another process or syncing: " + ex.Message, MessageType.Info);
            }
            catch (IOException ex)
            {
                MessageWindow.ShowDialog("Export Power BOM", ex.Message, MessageType.Info);
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog("Export Error", "An unexpected error occurred: " + ex.Message, MessageType.Error);
            }
        }

        public void Save(XSSFWorkbook workbook)
        {
            string tempFilePath = Path.GetTempFileName(); 
            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                ms.Position = 0;  

                using (FileStream fs = new FileStream(tempFilePath, FileMode.Create, FileAccess.Write))
                {
                    ms.CopyTo(fs);
                }
            }

            bool fileDeleted = FileOperationHelper.TryDeleteFileWithRetries(_path, 5, 200);
            if (fileDeleted)
            {
                File.Move(tempFilePath, _path);
            }
            else
            {
                string newPath = FileOperationHelper.CreateTempFilePath(_path);
                File.Move(tempFilePath, newPath);
                throw new IOException($"The original file could not be overwritten due to access restrictions or active synchronization. The export was successfully saved to a new file: '{newPath}'");
            }
            
        }

        private ISheet GetSheet(IWorkbook workbook, string sheetName, SheetType sheetType)
        {
            var sheetNames = new List<string>();
            for (int i = 0; i < workbook.NumberOfSheets; i++)
            {
                sheetNames.Add(workbook.GetSheetName(i));
            }

            // Search sheet with same name or same revision name
            var existingSheetName = sheetNames.FirstOrDefault(name => name.Equals(sheetName));
            if (existingSheetName == null)
            {
                var revisionName = sheetName.Split(' ').First();
                if (sheetType == SheetType.Details)
                {
                    existingSheetName = sheetNames.FirstOrDefault(name => name.StartsWith(revisionName) && 
                                                                          name.EndsWith(SheetType.Details.ToString()));
                }
                else
                {
                    existingSheetName = sheetNames.FirstOrDefault(name => name.StartsWith(revisionName) && 
                                                                          !name.EndsWith(SheetType.Details.ToString()));
                }
            }

            if (!string.IsNullOrEmpty(existingSheetName))
            {
                var sheet = workbook.GetSheet(existingSheetName);
                if (sheetType == SheetType.Empty) return sheet;

                // Update revision date
                if (existingSheetName != sheetName)
                {
                    workbook.SetSheetName(workbook.GetSheetIndex(sheet), sheetName);
                }
                return sheet;
            }
            else
            {
                if (sheetType == SheetType.Empty)
                {
                    return workbook.CreateSheet(sheetName);
                }

                // Copy sheet from template
                var path = Path.Combine(
                    Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), PathToTemplate);
                using (FileStream sourceFile = new FileStream(path, FileMode.Open, FileAccess.ReadWrite))
                {
                    var templateWorkbook = new XSSFWorkbook(sourceFile);
                    ISheet templateSheet = null;
                    if (sheetType == SheetType.Details)
                    {
                        templateSheet = templateWorkbook.GetSheet(DetailsSheetNameInTemplate);
                    }
                    else
                    {
                        templateSheet = templateWorkbook.GetSheet(RevisionSheetNameInTemplate);
                    }
                    templateSheet.CopyTo(workbook, sheetName, true, false);
                }
                return workbook.GetSheet(sheetName);
            }
        }

        private void CreatePriceSheetIfNotExist(IWorkbook targetWorkbook)
        {
            try
            {
                if (targetWorkbook.GetSheet(PriceSheetName) != null)
                {
                    return;
                }

                var path = Path.Combine(
                    Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), PathToTemplate);
                using (FileStream sourceFile = new FileStream(path, FileMode.Open, FileAccess.ReadWrite))
                {
                    var templateWorkbook = new XSSFWorkbook(sourceFile);
                    var templateSheet = templateWorkbook.GetSheet(PriceSheetName);
                    templateSheet.CopyTo(targetWorkbook, PriceSheetName, true, true);
                }

                AddConditionalFormattingForPriceSheet(targetWorkbook);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"The sheet could not be copied due to the error: {ex.Message}.Please try copying the sheet manually.");
            }

        }

        private static void AddConditionalFormattingForPriceSheet(IWorkbook workbook)
        {
            var targetSheet = workbook.GetSheet(PriceSheetName);
            var targetConditionalFormatting
                = (XSSFSheetConditionalFormatting)targetSheet.SheetConditionalFormatting;

            // Set the foreground color to gray
            XSSFConditionalFormattingRule formatting = (XSSFConditionalFormattingRule)targetConditionalFormatting.
                CreateConditionalFormattingRule("AND(ISBLANK($B1), NOT(ISBLANK($A1)))");
            XSSFPatternFormatting fillGrey = (XSSFPatternFormatting)formatting.CreatePatternFormatting();
            fillGrey.FillBackgroundColor = IndexedColors.Grey25Percent.Index;
            fillGrey.FillPattern = FillPattern.SolidForeground;
            
            // Set Range as $A:$C
            CellRangeAddress[] range = { new CellRangeAddress(0, 1048575, 0, 2) };

            targetConditionalFormatting.AddConditionalFormatting(range, formatting);
        }

        private void CleanData(ISheet sheet, int startRow)
        {
            var rowIterator = sheet.GetRowEnumerator();
            while (rowIterator.MoveNext())
            {
                var row = (IRow)rowIterator.Current;
                if (row.RowNum < startRow)
                {
                    continue;
                }

                foreach (ICell cell in row.Cells)
                {
                    cell.SetBlank();
                    cell.CellStyle = null;
                }
            }
        }

        private void WriteData(ISheet sheet, CellStyleConverter styleConverter, ISheetViewModel sheetViewModel)
        {
            var formulaEvaluator = sheet.Workbook.GetCreationHelper().CreateFormulaEvaluator();

            // Set project name
            var cell = GetCell(sheet, PowerBomExcelSchema.ProjectNameCell.Column, PowerBomExcelSchema.ProjectNameCell.Row);
            cell.SetCellValue(sheetViewModel.ProjectName);

            // Set plan referenced
            cell = GetCell(sheet, PowerBomExcelSchema.PlanReferencedCell.Column, PowerBomExcelSchema.PlanReferencedCell.Row);
            cell.SetCellValue(sheetViewModel.PlanReferenced);

            // Set material list date
            cell = GetCell(sheet, PowerBomExcelSchema.MaterialListDateCell.Column, PowerBomExcelSchema.MaterialListDateCell.Row);
            cell.SetCellValue(sheetViewModel.RevisionDate);

            WriteReport(sheet, styleConverter, sheetViewModel.Report, PowerBomExcelSchema.StartReportCell);
        }

        private void WriteReport(ISheet sheet, 
            CellStyleConverter styleConverter, 
            List<OrderRow> rows, 
            (int Column, int Row) startReportCell)
        {
            var formulaEvaluator = sheet.Workbook.GetCreationHelper().CreateFormulaEvaluator();

            OrderCell orderCell;
            for (int i = 0; i < rows.Count; i++)
            {
                for (int j = 0; j < rows[i].Cells.Count; j++)
                {
                    if (rows[i].Cells.Count <= j)
                    {
                        continue;
                    }

                    var cell = GetCell(sheet,
                        j + startReportCell.Column,
                        i + +startReportCell.Row);

                    orderCell = rows[i].Cells[j];
                    if (orderCell.CellStyle != null)
                    {
                        cell.CellStyle = styleConverter.GetCellStyle(orderCell.CellStyle);
                    }

                    if (orderCell.CellType == CellType.Formula)
                    {
                        orderCell.Value = _formulaBuilder.ReplacePlaceholder(
                            startReportCell.Column, startReportCell.Row, orderCell.Value);
                        cell.SetCellFormula(orderCell.Value);
                        try
                        {
                            formulaEvaluator.EvaluateFormulaCell(cell);
                        }
                        catch { }
                    }
                    else if (orderCell.CellType == CellType.Numeric)
                    {
                        cell.SetCellType(orderCell.CellType);
                        if (double.TryParse(orderCell.Value, out double value))
                        {
                            cell.SetCellValue(value);
                        }
                        else
                        {
                            cell.SetCellValue(orderCell.Value);
                        }
                    }
                    else
                    {
                        cell.SetCellValue(orderCell.Value);
                    }
                }
            }
        }

        private ICell GetCell(ISheet sheet, int column, int row)
        {
            var sheetRow = sheet.GetRow(row);
            if (sheetRow == null) sheetRow = sheet.CreateRow(row);
            var cell = sheetRow.GetCell(column);
            if (cell == null) cell = sheetRow.CreateCell(column);
            return cell;
        }

        private void SetActiveSheet(IWorkbook workbook, ISheet sheet)
        {
            workbook.SetSelectedTab(workbook.GetSheetIndex(sheet));
            workbook.SetActiveSheet(workbook.GetSheetIndex(sheet));
        }

        private string ReplaceInvalidCharacters(string stringToReplace)
        {
            StringBuilder stringBuilder = new StringBuilder();

            char[] invalidCharacters = {'\\', '/', '?', '*', '[', ']'};
            char[] charArray = stringToReplace.ToCharArray();

            foreach (char @char in charArray)
            {
                if (invalidCharacters.Contains(@char))
                {
                    if (@char == '/' || @char == '\\'
                        || @char == '?' || @char == '*')
                    {
                        stringBuilder.Append('-');
                    }
                    else if (@char == '[')
                    {
                        stringBuilder.Append('(');
                    }
                    else if (@char == ']')
                    {
                        stringBuilder.Append(')');
                    }
                }
                else
                {
                    stringBuilder.Append(@char);
                }
            }

            return stringBuilder.ToString();
        }
    }
}
