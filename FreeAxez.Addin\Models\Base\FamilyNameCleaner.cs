﻿using System.Text.RegularExpressions;

namespace FreeAxez.Addin.Models.Base
{
    public static class FamilyNameCleaner
    {
        private static Dictionary<string, string> _cleanFamilyNames = new Dictionary<string, string>();

        public static string GetCleanFamilyName(string familyName)
        {
            if (!_cleanFamilyNames.ContainsKey(familyName))
            {
                var cleanName = Regex.Replace(familyName, @"-", "_"); // Before working with baseUnitPatterns

                if (Regex.IsMatch(cleanName, @"_v\d+$")) // zAccess_Flooring-FreeAxez-Gridd_Nested_Border_End_Cover_v2
                {
                }
                else if (Regex.IsMatch(cleanName, @"_\d+$")) // Access-Flooring-FreeAxez-Gridd-Generic-Ramp-External_Angle-1_20
                {
                }
                else if (Regex.IsMatch(cleanName, @"\d+$")) // Access_Flooring-FreeAxez-Gridd-Border-Full1
                {
                    cleanName = Regex.Replace(cleanName, @"\d+$", "");
                }

                _cleanFamilyNames.Add(familyName, cleanName);
            }

            return _cleanFamilyNames[familyName];
        }
    }
}
