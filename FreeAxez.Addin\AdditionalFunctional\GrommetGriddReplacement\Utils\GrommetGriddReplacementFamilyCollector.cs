﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Models;

namespace FreeAxez.Addin.AdditionalFunctional.GrommetGriddReplacement.Utils
{
    public class GrommetGriddReplacementFamilyCollector
    {
        private Dictionary<ElementId, ElementId> _outletSymbolIdByUnitSymbolId;
        private readonly List<Level> _levels;

        public GrommetGriddReplacementFamilyCollector(List<Level> levels)
        {
            _levels = levels;

            Grommets = Grommet.CollectInstances()
                .Where(i => levels.Any(l => l.Id.Equals(i.LevelId)))
                .ToList();

            BaseUnits = BaseUnit.CollectInstances()
                .Where(i => levels.Any(l => l.Id.Equals(i.LevelId)))
                .ToList();

            ChannelPlates = PlateChannel.CollectInstances()
                .Where(i => levels.Any(l => l.Id.Equals(i.LevelId)))
                .ToList();

            CornerPlates = PlateCorner.CollectInstances()
                .Where(i => levels.Any(l => l.Id.Equals(i.LevelId)))
                .ToList();
        }

        public List<FamilyInstance> Grommets { get; private set; }
        public List<FamilyInstance> BaseUnits { get; private set; }
        public List<FamilyInstance> ChannelPlates { get; private set; }
        public List<FamilyInstance> CornerPlates { get; private set; }

        public bool ValidateFamiliesExist(out string validationMessage)
        {
            if (!Grommets.Any())
            {
                validationMessage = "There are no grommets on selected levels.";
                return false;
            }
            else if (!ChannelPlates.Any() && !CornerPlates.Any() && !BaseUnits.Any())
            {
                validationMessage = "There are no units and plates to replace on selected levels.";
                return false;
            }
            else // Missed outlet families
            {
                var baseUnitOutletFamily = BaseUnitOutlet.GetFamily(out string missedFamilyBaseUnitOutlet);
                var channelOutletFamily = PlateChannelOutlet.GetFamily(out string missedFamilyChannelOutlet);
                var cornerOutletFamiy = PlateCornerOutlet.GetFamily(out string missedFamilyCornerOutlet);
                var missedMessages = new List<string>() 
                {
                    missedFamilyBaseUnitOutlet,
                    missedFamilyChannelOutlet,
                    missedFamilyCornerOutlet
                };

                if (baseUnitOutletFamily == null && channelOutletFamily == null && cornerOutletFamiy == null)
                {
                    validationMessage = "All outlet families are missing:\n" +
                        string.Join("\n", missedMessages.Where(s => !string.IsNullOrWhiteSpace(s)).Select(s => "- " + s));

                    return false; // Do not allow to continue logic if all outlet families missed
                }
                else if (baseUnitOutletFamily == null || channelOutletFamily == null ||  cornerOutletFamiy == null)
                {
                    validationMessage = "Some outlet families are missing:\n" +
                        string.Join("\n", missedMessages.Where(s => !string.IsNullOrWhiteSpace(s)).Select(s => "- " + s)) +
                        "\n\nDo you want to continue without these families?";

                    return true; // Allow to continue logic
                }
            }

            validationMessage = string.Empty;
            return true;
        }

        public ElementId GetOutletSymbolId(ElementId unitSymbolId)
        {
            if (_outletSymbolIdByUnitSymbolId == null)
            {
                _outletSymbolIdByUnitSymbolId = MapFamilies();
            }

            if (_outletSymbolIdByUnitSymbolId.TryGetValue(unitSymbolId, out var symbolId))
            {
                return symbolId;
            }

            return null;
        }

        private Dictionary<ElementId, ElementId> MapFamilies()
        {
            // Base units
            var baseUnitSymbols = BaseUnits.Select(u => u.Symbol).ToList();
            var outletBaseUnitSymbols = BaseUnitOutlet.GetFamily(out _)?
                .GetFamilySymbolIds()
                .Select(id => (FamilySymbol)RevitManager.Document.GetElement(id))
                .ToList();

            var baseUnitsMap = MapUniqueSymbols(baseUnitSymbols, outletBaseUnitSymbols);

            // Channels
            var channelPlateSymbols = ChannelPlates.Select(u => u.Symbol).ToList();
            var outletChannelPlateSymbols = PlateChannelOutlet.GetFamily(out _)?
                .GetFamilySymbolIds()
                .Select(id => (FamilySymbol)RevitManager.Document.GetElement(id))
                .ToList();

            var channelPlatesMap = MapUniqueSymbols(channelPlateSymbols, outletChannelPlateSymbols);

            // Corners
            var cornerPlateSymbols = CornerPlates.Select(u => u.Symbol).ToList();
            var outletCornerPlateSymbols = PlateCornerOutlet.GetFamily(out _)?
                .GetFamilySymbolIds()
                .Select(id => (FamilySymbol)RevitManager.Document.GetElement(id))
                .ToList();

            var cornerPlatesMap = MapUniqueSymbols(cornerPlateSymbols, outletCornerPlateSymbols);

            // Merge dictionaries
            return baseUnitsMap
                .Concat(channelPlatesMap)
                .Concat(cornerPlatesMap)
                .ToDictionary(kv => kv.Key, kv => kv.Value);
        }

        private Dictionary<ElementId, ElementId> MapUniqueSymbols(
            List<FamilySymbol> unitSymbols, List<FamilySymbol> outletSymbols)
        {
            var output = new Dictionary<ElementId, ElementId>();

            if (unitSymbols == null
                || outletSymbols == null
                || unitSymbols.Count == 0
                || outletSymbols.Count == 0)
            {
                return output;
            }

            foreach (var unitSymbol in unitSymbols)
            {
                if (output.ContainsKey(unitSymbol.Id))
                {
                    continue;
                }

                var outletSymbol = outletSymbols.FirstOrDefault(s => s.Name == unitSymbol.Name);
                output.Add(unitSymbol.Id, outletSymbol.Id);
            }

            return output;
        }
    }
}
