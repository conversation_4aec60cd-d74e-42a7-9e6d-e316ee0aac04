﻿using Microsoft.Extensions.Configuration;
using System;
using System.IO;
using System.Reflection;

namespace FreeAxez.Core.Helpers
{
    public static class ConfigurationHelper
    {
        private static IConfigurationRoot _configuration;

        public static IConfigurationRoot GetConfiguration()
        {
            if (_configuration == null)
            {
                string basePath = Directory.GetCurrentDirectory();
                if (string.IsNullOrWhiteSpace(basePath))
                    basePath = Path.GetDirectoryName(Assembly.GetEntryAssembly().Location);

                var builder = new ConfigurationBuilder()
                    .SetBasePath(basePath)
                    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}.json", optional: true, reloadOnChange: true);

                _configuration = builder.Build();
            }

            return _configuration;
        }

        public static string GetValue(string key)
        {
            return GetConfiguration().GetSection(key).Value;
        }
    }
}