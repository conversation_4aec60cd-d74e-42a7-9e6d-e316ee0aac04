﻿using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using Point = System.Drawing.Point;

namespace FreeAxez.Addin.Infrastructure.UI.Views;

/// <summary>
///     Interaction logic for NotificationView.xaml
/// </summary>
public partial class NotificationView : Window
{
    private readonly DispatcherTimer _closeTimer;

    public NotificationView()
    {
        InitializeComponent();

        _closeTimer = new DispatcherTimer();
        _closeTimer.Interval = TimeSpan.FromSeconds(2);
        _closeTimer.Tick += CloseTimer_Tick;
        _closeTimer.Start();

        Loaded += NotificationWindow_Loaded;
    }

    [DllImport("user32.dll")]
    private static extern IntPtr GetForegroundWindow();

    [DllImport("user32.dll", SetLastError = true)]
    private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

    private Rectangle GetActiveScreenBounds()
    {
        var hWnd = GetForegroundWindow();
        if (hWnd != IntPtr.Zero)
        {
            GetWindowRect(hWnd, out var rect);
            var point = new Point(rect.Left + (rect.Right - rect.Left) / 2, rect.Top + (rect.Bottom - rect.Top) / 2);
            return Screen.FromPoint(point).WorkingArea;
        }

        return Screen.PrimaryScreen.WorkingArea;
    }

    private void NotificationWindow_Loaded(object sender, RoutedEventArgs e)
    {
        var screenBounds = GetActiveScreenBounds();
        Left = screenBounds.Right - Width - 20;
        Top = screenBounds.Bottom - Height - 100;
        StartShowAnimation();
    }

    private void StartShowAnimation()
    {
        var moveAnimation = new DoubleAnimation
        {
            From = Top + 50,
            To = Top,
            Duration = TimeSpan.FromSeconds(0.3)
        };

        var opacityAnimation = new DoubleAnimation
        {
            From = 0.0,
            To = 1.0,
            Duration = TimeSpan.FromSeconds(0.3)
        };

        BeginAnimation(TopProperty, moveAnimation);
        BeginAnimation(OpacityProperty, opacityAnimation);
    }

    private void CloseTimer_Tick(object sender, EventArgs e)
    {
        _closeTimer.Stop();
        Close();
    }

    [StructLayout(LayoutKind.Sequential)]
    private struct RECT
    {
        public int Left;
        public int Top;
        public int Right;
        public int Bottom;
    }
}