using System.ComponentModel;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;

public sealed class LinetypeModel : BaseViewModel
{
    private bool _isVisible = true;

    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string PatternRaw { get; set; } = string.Empty;
    public DateTime UpdatedUtc { get; set; }

    public bool IsVisible
    {
        get => _isVisible;
        set => Set(ref _isVisible, value);
    }
}