﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.Undersheet.Views.PlacementOptionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        WindowStartupLocation="CenterScreen"
        Topmost="True"
        Title="Undersheet"
        ResizeMode="NoResize"
        SizeToContent="WidthAndHeight">
    <DockPanel Margin="10">
        
        <GroupBox DockPanel.Dock="Top" Header="Placement Region">
            <StackPanel Margin="3">
                <RadioButton Content="Whole Plan" IsChecked="True" GroupName="placementRegion"/>
                <RadioButton x:Name="selectPlacementRegion" Margin="0,3,0,0" Content="Select Region" GroupName="placementRegion" Checked="selectPlacementRegion_Checked" Unchecked="selectPlacementRegion_Checked"/>
            </StackPanel>
        </GroupBox>

        <DockPanel DockPanel.Dock="Top" Margin="0,10,0,0">
            <Label Content="Start Line"/>
            <Button x:Name="startLine" Margin="5,0,0,0" Width="100" HorizontalAlignment="Right" Height="25" Content="Select" Click="startLine_Click"/>
        </DockPanel>

        <DockPanel x:Name="selectPlacementRegionButton" Visibility="Collapsed" DockPanel.Dock="Top" Margin="0,5,0,0">
            <Label Content="Placement Region" />
            <Button x:Name="placementArea" Margin="5,0,0,0" Width="100" HorizontalAlignment="Right" Height="25" Content="Select" Click="placementArea_Click"/>
        </DockPanel>

        <Button Margin="0,20,0,0" Content="Place Undersheets" Height="25" Width="212" Click="placeUndersheets_Click"/>
        
    </DockPanel>
</Window>
