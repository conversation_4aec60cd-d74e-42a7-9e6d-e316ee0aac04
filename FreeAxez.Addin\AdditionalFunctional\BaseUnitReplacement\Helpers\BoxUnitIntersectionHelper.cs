﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.Models;
using FreeAxez.Addin.Services;
using NetTopologySuite.Geometries;
using NetTopologySuite.Index.Strtree;

namespace FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.Helpers
{
    public class BoxUnitIntersectionHelper
    {
        private const double BoxUnitIntersectionTolerance = 0.1;
        private readonly BaseUnitReplacementFamilyCollector _familyCollector;

        public BoxUnitIntersectionHelper(BaseUnitReplacementFamilyCollector familyCollector)
        {
            _familyCollector = familyCollector;
        }

        public List<BoxUnitsIntersection> GetIntersections()
        {
            var output = new List<BoxUnitsIntersection>();

            // Lonely boxes
            // Box with base units
            // Box with cutout units
            var units = _familyCollector.BaseUnitInstances;
            units.AddRange(_familyCollector.CutoutInstances);
            var intersections = GetBoxToUnitsIntersections(units, _familyCollector.FloorBoxInstances);

            // Lonely cutout units
            var usedUnits = intersections.SelectMany(i => i.Units).ToList();
            var lonelyCutouts = _familyCollector.CutoutInstances
                .Where(c => !usedUnits.Any(u => u.Id.Equals(c.Id)))
                .Select(c => new BoxUnitsIntersection() { Units = new List<FamilyInstance>() { c } })
                .ToList();

            output.AddRange(intersections);
            output.AddRange(lonelyCutouts);
            return output;
        }

        public List<BoxUnitsIntersection> GetBoxToUnitsIntersections(List<FamilyInstance> units, List<FamilyInstance> floorBoxes)
        {
            var output = new List<BoxUnitsIntersection>();

            var rTree = new STRtree<FamilyInstance>();
            foreach (var unit in units)
            {
                var bb = unit.get_BoundingBox(null);
                var envelope = new Envelope(
                    new Coordinate(bb.Min.X, bb.Min.Y), 
                    new Coordinate(bb.Max.X, bb.Max.Y));
                rTree.Insert(envelope, unit);
            }
            rTree.Build();

            foreach (var floorBox in floorBoxes)
            {
                var boxOutline = GeometryHelper.GetSolidsOutline(floorBox);

                var searchEnvelope = new Envelope(
                    boxOutline.MinimumPoint.X, 
                    boxOutline.MaximumPoint.X,
                    boxOutline.MinimumPoint.Y, 
                    boxOutline.MaximumPoint.Y);

                var possibleUnits = rTree.Query(searchEnvelope);

                var intersectedUnits = possibleUnits.Where(u => floorBox.LevelId.Equals(u.LevelId)
                                                             && boxOutline.Contains((u.Location as LocationPoint).Point, BoxUnitIntersectionTolerance))
                                            .ToList();

                var intersection = new BoxUnitsIntersection()
                {
                    Box = floorBox,
                    Units = intersectedUnits
                };

                output.Add(intersection);
            }

            return output;
        }
    }
}
