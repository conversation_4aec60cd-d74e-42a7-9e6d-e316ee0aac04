<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals.AdminDetailsEdit"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Converters"
             mc:Ignorable="d"
             Width="600"
             d:DesignHeight="600" d:DesignWidth="600">
    <UserControl.Resources>
        <ResourceDictionary>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
            <converters:BooleanToLoadingTextConverter x:Key="BooleanToLoadingTextConverter"/>
            <converters:BooleanAndConverter x:Key="BooleanAndConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <StackPanel Margin="0 0 0 20">
            <TextBlock Style="{StaticResource TextLarge}"
                       Foreground="Black"
                       Text="Current Version"
                       FontWeight="Bold"
                       Margin="0 10 "></TextBlock>
            <ContentControl Content="{Binding CurrentDetailsInfo}"/>
            <TextBlock Style="{StaticResource TextLarge}"
                       Foreground="Black"
                       Visibility="{Binding HasUploadedFiles,
                        Converter={StaticResource BooleanToVisibilityConverter}}"
                       Text="New Version"
                       FontWeight="Bold"
                       Margin="0 10 "/>
            <ItemsControl
                Background="White"
                ItemsSource="{Binding UploadFileInfo}"/>
            <ItemsControl ItemsSource="{Binding Warnings}"/>
            <ItemsControl ItemsSource="{Binding Errors}"/>
            <CheckBox Content="Leave Image From Current Version"
                      IsChecked="{Binding LeaveImageFromCurrentVersion, Mode=TwoWay}"
                      Visibility="{Binding HasUploadedFiles,
                Converter={StaticResource BooleanToVisibilityConverter}}"
                      Margin="5 20 5 5"/>
            <ProgressBar Height="8"
                         Visibility="{Binding IsUploading,
                        Converter={StaticResource BooleanToVisibilityConverter}}"
                         IsIndeterminate="True"
                         Foreground="{StaticResource Blue500}"
                         Margin="0 10"/>
        </StackPanel>
        <Grid Grid.Row="1" Margin="0 0 0 5">
            <Button Width="160"
                    Style="{StaticResource ButtonSimpleGreen}"
                    Margin="0 0 20 0"
                    HorizontalAlignment="Left"
                    IsEnabled="{Binding HasUploadedFiles, Converter={StaticResource InverseBooleanConverter}}"
                    Command="{Binding ChooseFileCommand}">
                Upload new version
            </Button>
            <StackPanel Orientation="Horizontal"
                        HorizontalAlignment="Right">
                <Button Style="{StaticResource ButtonOutlinedBlue}"
                        Margin="0 0 20 0"
                        Width="120"
                        Command="{Binding CancelCommand}">
                    Cancel
                </Button>
                <Button Width="160"
                        Command="{Binding ApplyCommand}"
                        Style="{StaticResource ButtonSimpleBlue}"
                >
                    <Button.IsEnabled>
                        <MultiBinding Converter="{StaticResource BooleanAndConverter}">
                            <Binding Path="CanApply"/>
                            <Binding Path="IsUploading"
                                     Converter="{StaticResource InverseBooleanConverter}"/>
                        </MultiBinding>
                    </Button.IsEnabled>
                    <StackPanel Orientation="Horizontal">
                        <ContentControl Template="{StaticResource CloudUploadIcon}" HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding IsUploading,
                            Converter={StaticResource BooleanToLoadingTextConverter},
                            ConverterParameter='Save changes'}"
                                   Margin="5 0"
                                   Foreground="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Button}}, Path=Foreground}"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
