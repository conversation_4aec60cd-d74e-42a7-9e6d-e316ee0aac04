﻿using Autodesk.Revit.DB;

namespace FreeAxez.Addin.AdditionalFunctional.CutUnitByVoid.Utils
{
    internal class RollBackFailuresPreprocessor : IFailuresPreprocessor
    {
        public FailureProcessingResult PreprocessFailures(FailuresAccessor failuresAccessor)
        {
            if (failuresAccessor.GetFailureMessages().Count > 0)
            {
                return FailureProcessingResult.ProceedWithRollBack;
            }

            return FailureProcessingResult.Continue;
        }
    }
}
