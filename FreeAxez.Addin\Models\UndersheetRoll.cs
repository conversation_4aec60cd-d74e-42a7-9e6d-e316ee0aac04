﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Models.Base;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.Models
{
    public class UndersheetRoll : Product
    {
        public UndersheetRoll(Element element) : base(element)
        {
        }

        public static List<UndersheetRoll> Collect()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_DetailComponents)
                .WhereElementIsNotElementType()
                .Where(e => e is FamilyInstance)
                .Cast<FamilyInstance>()
                .Where(f => f.Symbol.FamilyName.EndsWith("Undersheet"))
                .Select(g => new UndersheetRoll(g))
                .ToList();
        }
    }
}