﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.PanelScheduleCreation.Utils
{
    public class BaseSchedules
    {
        private static Dictionary<ScheduleType, string> _baseScheduleNameByType = new Dictionary<ScheduleType, string>()
        {
            { ScheduleType.PanelCircuit, "BASE Panel & Circuit" },
            { ScheduleType.WhipLengths, "BASE FM & IL Lengths" },
            { ScheduleType.PartCount, "BASE Part Count" },
            { ScheduleType.ConnectedComponents, "BASE Connected Components" },
            { ScheduleType.ConnectedComponentsByNumber, "BASE Connected Components by Component No." }
        };
        private List<ViewSchedule> _existingSchedules;


        public BaseSchedules()
        {
            _existingSchedules = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(ViewSchedule))
                .WhereElementIsNotElementType()
                .Cast<ViewSchedule>()
                .ToList();
        }


        public static string ConnectedComponentsName => _baseScheduleNameByType[ScheduleType.ConnectedComponents];
        public static string ConnectedComponentsByNumber => _baseScheduleNameByType[ScheduleType.ConnectedComponentsByNumber];


        public static List<string> GetAllBaseScheduleTypeDescriptions()
        {
            return _baseScheduleNameByType.Keys.Select(k => GetScheduleTypeDescription(k)).ToList();
        }

        public static string GetScheduleTypeDescription(ScheduleType scheduleType)
        {
            var baseScheduleName = _baseScheduleNameByType[scheduleType];
            return baseScheduleName.Replace("BASE ", "");
        }

        public bool IsBaseScheduleNotExist(out List<string> missedBaseSchedules)
        {
            missedBaseSchedules = new List<string>();
            foreach (var baseScheduleName in _baseScheduleNameByType.Values)
            {
                if (!_existingSchedules.Any(s => s.Name == baseScheduleName))
                {
                    missedBaseSchedules.Add(baseScheduleName);
                }
            }
            return missedBaseSchedules.Count > 0;
        }

        public ViewSchedule GetBaseSchedule(ScheduleType scheduleType)
        {
            var baseScheduleName = _baseScheduleNameByType[scheduleType];
            return _existingSchedules.First(s => s.Name == baseScheduleName);
        }
    }
}
