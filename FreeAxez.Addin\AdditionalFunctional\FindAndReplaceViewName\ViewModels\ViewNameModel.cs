﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FindAndReplaceViewName.ViewModels
{
    public class ViewNameModel : BaseViewModel
    {
        private View _view;
        private string _name;
        private string _originalName;
        private bool _isSelected;

        public ViewNameModel(View view)
        {
            _view = view;
            _name = GetName(view);
            _originalName = _name;
        }

        public View View
        {
            get => _view;
        }

        public string Name
        {
            get => _name;
            set => Set(ref _name, value);
        }

        public string OriginalName
        {
            get => _originalName;
        }

        public ViewType ViewType
        {
            get => _view.ViewType;
        }

        public string ViewTypeName
        {
            get
            {
                switch (ViewType)
                {
                    case ViewType.FloorPlan:
                        return "Floor Plan";
                    case ViewType.DrawingSheet:
                        return $"Sheet {(_view as ViewSheet).SheetNumber} - {(_view as ViewSheet).Name}";
                    default:
                        return "Unsupported View Type";
                }
            }
        }

        public bool IsSelected
        {
            get => _isSelected;
            set => Set(ref _isSelected, value);
        }

        public bool IsNameChanged
        {
            get => _name != _originalName;
        }

        private string GetName(View view)
        {
            switch (view.ViewType)
            {
                case ViewType.FloorPlan:
                    return view.Name ?? "";
                case ViewType.DrawingSheet:
                    return view.LookupParameter("Sheet Title")?.AsString() ?? "";
                default:
                    return "Unsupported View Type";
            }
        }
    }
}
