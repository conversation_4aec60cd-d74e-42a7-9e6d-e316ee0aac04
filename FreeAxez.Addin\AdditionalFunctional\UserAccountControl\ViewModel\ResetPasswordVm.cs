﻿using System.Threading.Tasks;
using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Services;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.UserAccountControl.ViewModel;

public class ResetPasswordVm : WindowViewModel
{
    private bool _isLoading;
    private string _userEmail;

    public ResetPasswordVm()
    {
        ResetPasswordCommand = new AsyncRelayCommand(ExecuteResetPassword);
    }

    public ICommand ResetPasswordCommand { get; private set; }

    public bool CanApply => !string.IsNullOrWhiteSpace(UserEmail);

    public string UserEmail
    {
        get => _userEmail;
        set
        {
            _userEmail = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(CanApply));
        }
    }

    public bool IsLoading
    {
        get => _isLoading;
        set
        {
            if (_isLoading != value)
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }
    }

    private async Task ExecuteResetPassword(object parameter)
    {
        IsLoading = true;
        var message =
            "Please check your email for instructions on how to reset your password. " +
            "If you do not receive an email within a few minutes, be sure to check your spam or junk folder";
        var response = await UserAuthApiService.Instance.ForgotPassword(UserEmail);
        if (response.IsSuccessStatusCode)
        {
            MessageWindow.ShowDialog("Info", message, MessageType.Info);
        }
        else
        {
            var errorContent = await response.Content.ReadAsStringAsync();
            Error = errorContent;
        }

        IsLoading = false;
    }
}