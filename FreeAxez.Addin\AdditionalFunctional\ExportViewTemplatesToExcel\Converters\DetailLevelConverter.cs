﻿using Autodesk.Revit.DB;
using OfficeOpenXml;

namespace FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Converters
{
    public class DetailLevelConverter
    {
        private static readonly Dictionary<ViewDetailLevel, string> DetailLevelMap = new()
        {
            { ViewDetailLevel.Coarse, "Coarse" },
            { ViewDetailLevel.Medium, "Medium" },
            { ViewDetailLevel.Fine, "Fine" }
        };

        public static string ConvertDetailLevel(ViewDetailLevel detailLevel)
        {
            if (DetailLevelMap.TryGetValue(detailLevel, out var detailString)) return detailString;
            return "Unknown";
        }

        public static void AddDetailLevelValidation(ExcelWorksheet ws, string cellAddress)
        {
            var dv = ws.DataValidations.AddListValidation(cellAddress);
            foreach (var level in DetailLevelMap.Values) dv.Formula.Values.Add(level);
            dv.ShowErrorMessage = true;
            dv.ErrorTitle = "Invalid Detail Level";
            dv.Error = "Please select a valid detail level.";
        }

        public static ViewDetailLevel ParseDetailLevel(string detailString)
        {
            var reverseMap = new Dictionary<string, ViewDetailLevel>();
            foreach (var kvp in DetailLevelMap)
                reverseMap[kvp.Value] = kvp.Key;

            if (reverseMap.TryGetValue(detailString, out var dl))
                return dl;

            return ViewDetailLevel.Undefined; 
        }
    }
}
