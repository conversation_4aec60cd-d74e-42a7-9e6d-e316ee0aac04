using System.Windows;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Details
{
    public class DetailPreviewVm : BaseViewModel
    {
        private readonly Window _window;
        private BitmapSource _previewImage;
        private string _title;

        public DetailPreviewVm(Window window, BitmapSource previewImage, string title)
        {
            _window = window;
            _previewImage = previewImage;
            _title = title ?? "Detail View Preview";

            BackCommand = new RelayCommand(_ => CloseWindow());
            CloseCommand = new RelayCommand(_ => CloseWindow());
        }

        public BitmapSource PreviewImage
        {
            get => _previewImage;
            set
            {
                if (_previewImage == value) return;
                _previewImage = value;
                OnPropertyChanged();
            }
        }

        public string Title
        {
            get => _title;
            set
            {
                if (_title == value) return;
                _title = value;
                OnPropertyChanged();
            }
        }

        public ICommand BackCommand { get; }
        public ICommand CloseCommand { get; }

        private void CloseWindow()
        {
            _window?.Close();
        }
    }
}
