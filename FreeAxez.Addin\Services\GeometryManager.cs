﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.Services
{
    public static class GeometryManager
    {
        public static List<XYZ> GetPointsFromTransitionLines(List<Curve> transitionLineCurves)
        {
            var transitionLinePoints = new List<XYZ>();

            foreach (Curve curve in transitionLineCurves)
            {
                var start = curve.GetEndPoint(0);
                var end = curve.GetEndPoint(1);

                var roundedStart = new XYZ(
                    Math.Round(start.X, 3),
                    Math.Round(start.Y, 3),
                    Math.Round(start.Z, 3)
                );

                var roundedEnd = new XYZ(
                    Math.Round(end.X, 3),
                    Math.Round(end.Y, 3),
                    Math.Round(end.Z, 3)
                );

                transitionLinePoints.Add(roundedStart);
                transitionLinePoints.Add(roundedEnd);
            }

            return transitionLinePoints;
        }

        public static List<FilledRegion> CreateModifiedRegions(List<CurveLoop> regionLoops,
           FilledRegionType filledRegionType)
        {
            List<FilledRegion> modifiedRegions = new List<FilledRegion>();
            if (regionLoops == null || filledRegionType == null || RevitManager.Document == null)
            {
                TaskDialog.Show("Error", "Invalid input parameters for creating modified regions.");
                return modifiedRegions;
            }

            Transaction trans = new Transaction(RevitManager.Document, "Create Modified Regions");
            trans.Start();
            try
            {
                foreach (CurveLoop loop in regionLoops)
                {
                    if (loop.IsCounterclockwise(RevitManager.Document.ActiveView.ViewDirection))
                    {
                        loop.Flip();
                    }

                    FilledRegion modifiedRegion = FilledRegion.Create(RevitManager.Document, filledRegionType.Id,
                        RevitManager.Document.ActiveView.Id, new List<CurveLoop> { loop });
                    modifiedRegions.Add(modifiedRegion);
                }

                trans.Commit();
            }
            catch (Exception ex)
            {
                TaskDialog.Show("Error", "Failed to create modified regions: " + ex.Message);
                trans.RollBack();
            }

            return modifiedRegions;
        }

        public static List<CurveLoop> CreateLoopsForRegions(List<List<Curve>> regionsCurves)
        {
            List<CurveLoop> modifiedRegionsLoops = new List<CurveLoop>();
            foreach (var regionCurves in regionsCurves)
            {
                var modifiedCurveLoop = CurveLoop.Create(regionCurves);
                modifiedRegionsLoops.Add(modifiedCurveLoop);
            }

            return modifiedRegionsLoops;
        }

        public static bool AreDetailLinesOnRegionEdges(List<Curve> transitionLineCurves,
            List<FilledRegion> filledRegions)
        {
            List<Curve> regionEdges = new List<Curve>();
            foreach (var region in filledRegions)
            {
                var boundaries = region.GetBoundaries();
                foreach (var boundary in boundaries)
                {
                    regionEdges.AddRange(boundary);
                }
            }

            foreach (var lineCurve in transitionLineCurves)
            {
                XYZ start = lineCurve.GetEndPoint(0);
                XYZ end = lineCurve.GetEndPoint(1);

                if (!IsPointOnAnyCurve(start, regionEdges) || !IsPointOnAnyCurve(end, regionEdges))
                {
                    return false;
                }
            }

            return true;
        }

        private static bool IsPointOnAnyCurve(XYZ point, List<Curve> curves)
        {
            foreach (var curve in curves)
            {
                if (curve.Distance(point) < 0.001)
                {
                    return true;
                }
            }

            return false;
        }

        private static bool IsTransitionLineOnCurve(Curve transitionLineCurve, Curve currentCurve)
        {
            XYZ start = transitionLineCurve.GetEndPoint(0);
            XYZ end = transitionLineCurve.GetEndPoint(1);

            return IsPointCloseToCurve(start, currentCurve) && IsPointCloseToCurve(end, currentCurve);
        }

        private static bool IsPointCloseToCurve(XYZ point, Curve curve, double tolerance = 0.001)
        {
            return curve.Distance(point) < tolerance;
        }

        public static List<List<Curve>> ModifyRegionCurvesWithTransitions(List<Curve> transitionLineCurves,
            List<FilledRegion> filledRegions)
        {
            Dictionary<FilledRegion, List<Curve>> regionToTransitions = new Dictionary<FilledRegion, List<Curve>>();
            List<List<Curve>> allModifiedRegionCurves = new List<List<Curve>>();

            foreach (var region in filledRegions)
            {
                regionToTransitions[region] = new List<Curve>();
            }

            foreach (var line in transitionLineCurves)
            {
                foreach (var region in filledRegions)
                {
                    if (IsTransitionLineCurveInsideRegion(line, region))
                    {
                        regionToTransitions[region].Add(line);
                        break;
                    }
                }
            }

            foreach (var region in filledRegions)
            {
                List<Curve> regionCurves = GetRegionCurves(region);
                List<Curve> transitionLines = regionToTransitions[region];
                List<Curve> modifiedCurves = transitionLines.Count > 0
                    ? ModifyCurves(regionCurves, transitionLines)
                    : new List<Curve>(regionCurves);
                allModifiedRegionCurves.Add(modifiedCurves);
            }

            return allModifiedRegionCurves;
        }

        private static bool IsTransitionLineCurveInsideRegion(Curve transitionLineCurve, FilledRegion region)
        {
            var regionEdges = GetRegionCurves(region);

            XYZ start = transitionLineCurve.GetEndPoint(0);
            XYZ end = transitionLineCurve.GetEndPoint(1);

            if (!IsPointOnAnyCurve(start, regionEdges) || !IsPointOnAnyCurve(end, regionEdges))
            {
                return false;
            }

            return true;
        }

        private static List<Curve> GetRegionCurves(FilledRegion region)
        {
            List<Curve> regionEdges = new List<Curve>();
            var boundaries = region.GetBoundaries();
            foreach (var boundary in boundaries)
            {
                regionEdges.AddRange(boundary);
            }

            return regionEdges;
        }

        public static List<Curve> ModifyCurves(List<Curve> regionCurves, List<Curve> transitionLines)
        {
            List<Curve> modifiedCurves = new List<Curve>(regionCurves);
            List<Curve> tempCurves = new List<Curve>();

            foreach (Curve originalCurve in modifiedCurves)
            {
                bool curveModified = false;
                foreach (var transitionLine in transitionLines)
                {
                    if (IsTransitionLineOnCurve(transitionLine, originalCurve) && originalCurve is Line)
                    {
                        XYZ transStart = transitionLine.GetEndPoint(0);
                        XYZ transEnd = transitionLine.GetEndPoint(1);

                        Line curveLine = originalCurve as Line;
                        Line transLine = transitionLine as Line;

                        bool sameDirection = (curveLine.Direction.Normalize()
                            .IsAlmostEqualTo(transLine.Direction.Normalize()));

                        XYZ curveStart = originalCurve.GetEndPoint(0);
                        XYZ curveEnd = originalCurve.GetEndPoint(1);
                        XYZ includedTransCurveStart = sameDirection ? transStart : transEnd;
                        XYZ includedTransCurveEnd = sameDirection ? transEnd : transStart;

                        bool startMatches = curveStart.IsAlmostEqualTo(includedTransCurveStart);
                        bool endMatches = curveEnd.IsAlmostEqualTo(includedTransCurveEnd);

                        if (startMatches && endMatches)
                        {
                            continue;
                        }

                        curveModified = true;

                        if (!startMatches && !endMatches)
                        {
                            tempCurves.Add(Line.CreateBound(curveStart, includedTransCurveStart));
                            tempCurves.Add(Line.CreateBound(includedTransCurveStart, includedTransCurveEnd));
                            tempCurves.Add(Line.CreateBound(includedTransCurveEnd, curveEnd));
                        }

                        if (!startMatches && endMatches)
                        {
                            tempCurves.Add(Line.CreateBound(curveStart, includedTransCurveStart));
                            tempCurves.Add(Line.CreateBound(includedTransCurveStart, includedTransCurveEnd));
                        }

                        if (!endMatches && startMatches)
                        {
                            tempCurves.Add(Line.CreateBound(includedTransCurveStart, includedTransCurveEnd));
                            tempCurves.Add(Line.CreateBound(includedTransCurveEnd, curveEnd));
                        }

                        break;
                    }
                }

                if (!curveModified)
                {
                    tempCurves.Add(originalCurve);
                }
            }

            return tempCurves; 
        }
    }
}
