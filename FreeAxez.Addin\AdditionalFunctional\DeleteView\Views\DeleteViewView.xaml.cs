﻿using FreeAxez.Addin.AdditionalFunctional.DeleteView.Models;
using System.Windows;
using System.Windows.Controls;

namespace FreeAxez.Addin.AdditionalFunctional.DeleteView.Views
{
    public partial class DeleteViewView : Window
    {
        public DeleteViewView()
        {
            InitializeComponent();
        }


        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            foreach (RevitView revitView in dataGrid.SelectedItems)
            {
                revitView.IsCheck = (bool)(sender as CheckBox).IsChecked;
            }
        }
    }
}
