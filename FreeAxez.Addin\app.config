<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="FreeAxez.Addin.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    </sectionGroup>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="FreeAxez.Addin.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
    </sectionGroup>
  </configSections>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <applicationSettings>
    <FreeAxez.Addin.Properties.Settings>
      <setting name="browserAppName" serializeAs="String">
        <value>FreeAxez.BrowserApp.exe</value>
      </setting>
    </FreeAxez.Addin.Properties.Settings>
  </applicationSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/></startup>
  <userSettings>
    <FreeAxez.Addin.Properties.Settings>
      <setting name="selectedRailingTypeName" serializeAs="String">
        <value />
      </setting>
      <setting name="deleteSelectedLineOption" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="selectedLineStyleName" serializeAs="String">
        <value />
      </setting>
      <setting name="baseOffsetForRailing" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="createRailingForEachLine" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="userParameterSets" serializeAs="String">
        <value />
      </setting>
      <setting name="TagAllRailingsLengthToCenterOfTag" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="TagAllRailingsStartTag" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="TagAllRailingsEndTag" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="TagAllRailingsSelectedRailingTagTypeName" serializeAs="String">
        <value />
      </setting>
      <setting name="ExportCADPrefix" serializeAs="String">
        <value />
      </setting>
      <setting name="ExportCADPath" serializeAs="String">
        <value />
      </setting>
      <setting name="FlexPipeToLineSelectedLineStyleName" serializeAs="String">
        <value />
      </setting>
      <setting name="TagAllRailingsTagElementsWithEmptyParameter" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="FloorBoxNumberingPrefix" serializeAs="String">
        <value />
      </setting>
      <setting name="FloorBoxNumberingStartNumber" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="FloorBoxNumberingHorizontalDirection" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="TagAllRailingsTagVisibleInView" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="ElementNumberingParameterName" serializeAs="String">
        <value />
      </setting>
      <setting name="ElementNumberingStartNumber" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="ElementNumberingPrefix" serializeAs="String">
        <value />
      </setting>
      <setting name="ElementNumberingHorizontalDirection" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="FrameSelectedGridd40" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="exportGriddBOMPath" serializeAs="String">
        <value />
      </setting>
      <setting name="exportGriddBOMOpenFile" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="panelSchedulePlacementSelectedLevels" serializeAs="String">
        <value />
      </setting>
      <setting name="panelSchedulePlacementDeleteUnused" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="FrameSelectedCornerLong" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="exportPowerBomPath" serializeAs="String">
        <value />
      </setting>
      <setting name="exportPowerBomOpenFile" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="panelSchedulePlacementSheetSize" serializeAs="String">
        <value />
      </setting>
      <setting name="panelSchedulePlacementRevision" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="panelScheduleCreationConnectedComponents" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="TagAllCurbsLengthToCenterOfTag" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="TagCurbsOffset" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="TagCurbsVisibleInView" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="TagAllFramesLengthToCenterOfTag" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="TagFramesVisibleInView" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="TransferViewTemplatesDeleteTemplates" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="TransferViewTemplatesSourceFilePath" serializeAs="String">
        <value />
      </setting>
      <setting name="EхportCADColor" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="RampGriddType" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="RampRampSlope" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="RampIsLeftSideSlopeChecked" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="RampIsRightSideSlopeChecked" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="TagRampLengthToCenterOfTag" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="TagRampIsVisibleInView" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="TagRampIsSelectRampComponents" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="TagTypeRump" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="ExportCutsheetFolderPath" serializeAs="String">
        <value />
      </setting>
      <setting name="ExportCutsheetIsOpenDirectory" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="ExportForVRPath" serializeAs="String">
        <value />
      </setting>
      <setting name="ExportForVRCopyFurniture" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="ExportPowerBomAccessories" serializeAs="String">
        <value />
      </setting>
      <setting name="ExportGriddBomAccessories" serializeAs="String">
        <value />
      </setting>
      <setting name="ExportCADIncludeFloatingInformation" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="GriddBuilderBaseUnitHeight" serializeAs="String">
        <value>40</value>
      </setting>
      <setting name="SelectedAutoCADVersion" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="ExportForVRLightweightFamiliesPath" serializeAs="String">
        <value />
      </setting>
    </FreeAxez.Addin.Properties.Settings>
  </userSettings>
</configuration>
