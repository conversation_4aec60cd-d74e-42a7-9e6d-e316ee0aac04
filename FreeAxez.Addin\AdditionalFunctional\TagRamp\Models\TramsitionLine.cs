﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagRamp.Models
{
    public class TramsitionLine
    {
        public XYZ CenterPoint { get; set; }
        public XYZ Direction { get; set; }
        public XYZ RampDirection { get; set; }
        public double TagOffset { get; set; }
        public string RampSlope { get; set; }

        public static TramsitionLine Create(XYZ centerPoint,
                                            XYZ direction,
                                            XYZ rampDirection,
                                            double tagOffset,
                                            string rampSlope)
        {
            return new TramsitionLine
            {
                CenterPoint = centerPoint,
                Direction = direction,
                RampDirection = rampDirection,
                TagOffset = tagOffset,
                RampSlope = rampSlope
            };
        }

        public static List<XYZ> OrderPointsByCurveDirection(Curve curve, List<XYZ> points)
        {
            var lineDirection = (curve as Line).Direction;

            List<XYZ> sortedPoints = new List<XYZ>();

            if (lineDirection.X >= 0 && lineDirection.Y >= 0)
            {
                sortedPoints = points.OrderBy(p => Math.Round(p.X, 2))
                    .ThenBy(p => Math.Round(p.Y, 2)).ToList();
            }
            else if (lineDirection.X >= 0 && lineDirection.Y <= 0)
            {
                sortedPoints = points.OrderBy(p => Math.Round(p.X, 2))
                    .ThenByDescending(p => Math.Round(p.Y, 2)).ToList();
            }
            else if (lineDirection.X <= 0 && lineDirection.Y <= 0)
            {
                sortedPoints = points.OrderByDescending(p => Math.Round(p.X, 2))
                    .ThenByDescending(p => Math.Round(p.Y, 2)).ToList();
            }
            else
            {
                sortedPoints = points.OrderByDescending(p => Math.Round(p.X, 2))
                    .ThenBy(p => Math.Round(p.Y, 2)).ToList();
            }

            return sortedPoints;
        }
    }
}