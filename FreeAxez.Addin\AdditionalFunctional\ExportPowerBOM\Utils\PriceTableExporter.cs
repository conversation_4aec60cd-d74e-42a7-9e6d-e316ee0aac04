﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBom.Utils
{
    /// <summary>
    /// A class for exporting products from Revit to CSV to form a Price table.
    /// </summary>
    public class PriceTableExporter
    {
        public void Export(string path)
        {
            var whipPrices = GetPricesForWhips();
            var trackPrices = GetPricesForTracks();
            var boxPrices = GetPricesForBoxes();

            var table = new List<string>();
            table.AddRange(whipPrices);
            table.AddRange(trackPrices);
            table.AddRange(boxPrices);

            using (StreamWriter outputFile = new StreamWriter(path, false))
            {
                foreach (var prod in table)
                {
                    outputFile.WriteLine(prod);
                }
            }
        }

        private List<string> GetPricesForWhips()
        {
            var output = new List<string>();

            var separateWhipsAbbreviation = new List<string>()
            {
                "-Interlink",
                "-Supply",
                "-FeedModule",
            };

            var whipTypes = new FilteredElementCollector(RevitManager.Document)
               .OfClass(typeof(FlexPipeType))
               .Where(i => separateWhipsAbbreviation.Any(v => i.Name.Contains(v)))
               .OrderBy(i => i.Name)
               .ToList();

            foreach (var whipType in  whipTypes)
            {
                var productName = whipType.LookupParameter("Product Name").AsString();

                output.Add($"{productName}");

                if (whipType.Name.Contains("-FeedModule"))
                {
                    for (int i = 5; i < 325; i += 5)
                    {
                        output.Add($"{productName},{i}");
                    }
                }
                else if (whipType.Name.Contains("-Interlink"))
                {
                    for (int i = 5; i < 100; i += 5)
                    {
                        output.Add($"{productName},{i}");
                    }
                }
                else if (whipType.Name.Contains("-Supply"))
                {
                    for (int i = 5; i < 100; i += 5)
                    {
                        output.Add($"{productName},{i}");
                    }
                }
            }

            return output;
        }

        private List<string> GetPricesForTracks()
        {
            var output = new List<string>();

            var trackFamiliesAbbreviation = new List<string>()
            {
                 "-Track-4'",
                 "-Track-8'"
            };

            var trackFamilies = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Family))
                .Cast<Family>()
                .Where(i => trackFamiliesAbbreviation.Any(v => i.Name.Contains(v)))
                .OrderBy(i => i.Name)
                .ToList();


            foreach (var trackFamily in trackFamilies)
            {
                var symbol = RevitManager.Document.GetElement(trackFamily.GetFamilySymbolIds().First());
                var productName = symbol.LookupParameter("Product Name").AsString();
                output.Add($"{productName}");
                output.Add($"{productName},4");
                output.Add($"{productName},8");
            }

            return output;
        }

        private List<string> GetPricesForBoxes()
        {
            var output = new List<string>();

            var floorBoxAbbreviation = new List<string>()
            {
                "-FB-",
                "-DeskMount-",
                "-Desk_Mount-",
            };

            var boxFamilies = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Family))
                .Cast<Family>()
                .Where(i => floorBoxAbbreviation.Any(v => i.Name.Contains(v)))
                .OrderBy(i => i.Name)
                .ToList();

            foreach (var boxFamily in boxFamilies)
            {
                var symbols = boxFamily.GetFamilySymbolIds()
                    .Select(id => RevitManager.Document.GetElement(id))
                    .Where(s => s.Name != "Generic")
                    .OrderBy(s => s.Name)
                    .ToList();

                foreach (var symbol in symbols)
                {
                    var productName = symbol.LookupParameter("Product Name").AsString();

                    output.Add($"{productName} {symbol.Name}");
                    for (int i = 8; i < 28; i += 4)
                    {
                        output.Add($"{productName} {symbol.Name},{i}");
                    }
                }
            }

            return output;
        }
    }
}
