﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Interfaces;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils
{
    public static class ProjectDataManager
    {
        public static void TransferProjectInformation(Document source, Document target,
            IProgressReporter progressReporter, CancellationToken cancellationToken)
        {
            progressReporter.ReportStatus("Transferring Project Information...");
            cancellationToken.ThrowIfCancellationRequested();

            var sourceProjectInfo = source.ProjectInformation;
            var targetProjectInfo = target.ProjectInformation;

            using (var trans = new Transaction(target, "Transfer Project Information"))
            {
                CommonFailuresPreprocessor.SetFailuresPreprocessor(trans);
                trans.Start();

                int totalParameters = sourceProjectInfo.Parameters.Size;
                int processedParameters = 0;

                foreach (Parameter sourceParam in sourceProjectInfo.Parameters)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    if (sourceParam.IsReadOnly) continue;
                    if (sourceParam.Definition.Name=="Template Version") continue;

                    var targetParam = targetProjectInfo.get_Parameter(sourceParam.Definition);
                    if (targetParam != null && !targetParam.IsReadOnly)
                    {
                        switch (sourceParam.StorageType)
                        {
                            case StorageType.String:
                                targetParam.Set(sourceParam.AsString());
                                break;
                            case StorageType.Integer:
                                targetParam.Set(sourceParam.AsInteger());
                                break;
                            case StorageType.Double:
                                targetParam.Set(sourceParam.AsDouble());
                                break;
                            case StorageType.ElementId:
                                targetParam.Set(sourceParam.AsElementId());
                                break;
                        }
                    }

                    processedParameters++;
                    double progress = (double)processedParameters / totalParameters * 100;
                    progressReporter.ReportProgress(progress);

                    // Keep UI responsive
                    System.Windows.Forms.Application.DoEvents();
                }

                trans.Commit();
            }

            progressReporter.ReportStatus("Project Information transferred successfully.");
        }

        public static void CopyProjectParameters(Document source, Document target, IProgressReporter progressReporter,
            CancellationToken cancellationToken)
        {
            progressReporter.ReportStatus("Copying Project Parameters...");
            cancellationToken.ThrowIfCancellationRequested();

            try
            {
                var sourceBindingMap = source.ParameterBindings;
                var targetBindingMap = target.ParameterBindings;

                using (var t = new Transaction(target, "Copy Project Parameters"))
                {
                    CommonFailuresPreprocessor.SetFailuresPreprocessor(t);

                    t.Start();

                    var iterator = sourceBindingMap.ForwardIterator();
                    int totalParameters = sourceBindingMap.Size;
                    int processedParameters = 0;

                    while (iterator.MoveNext())
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        var parameterDefinition = iterator.Key as InternalDefinition;
                        var parameterBinding = iterator.Current as ElementBinding;

                        if (parameterDefinition != null && parameterBinding != null)
                        {
                            if (targetBindingMap.Contains(parameterDefinition))
                            {
                                processedParameters++;
                                continue;
                            }

#if revit2020 || revit2021 || revit2022 || revit2023 || revit2024
                            var parameterGroup = parameterDefinition.ParameterGroup;
#else
                            var parameterGroup = parameterDefinition.GetGroupTypeId();
#endif
                            if (parameterBinding is InstanceBinding instanceBinding)
                            {
                                targetBindingMap.Insert(parameterDefinition, instanceBinding, parameterGroup);
                            }
                            else if (parameterBinding is TypeBinding typeBinding)
                            {
                                targetBindingMap.Insert(parameterDefinition, typeBinding, parameterGroup);
                            }
                        }

                        processedParameters++;
                        double progress = (double)processedParameters / totalParameters * 100;
                        progressReporter.ReportProgress(progress);

                        // Keep UI responsive
                        System.Windows.Forms.Application.DoEvents();
                    }

                    t.Commit();
                }

                progressReporter.ReportStatus("Project Parameters copied successfully.");
                LogHelper.Information("Project parameters have been copied");
            }
            catch (Exception ex)
            {
                string errorMessage = $"Error copying project parameters: {ex.Message}";
                LogHelper.Error(errorMessage);
                progressReporter.ReportStatus(errorMessage);
            }
        }
    }
}
