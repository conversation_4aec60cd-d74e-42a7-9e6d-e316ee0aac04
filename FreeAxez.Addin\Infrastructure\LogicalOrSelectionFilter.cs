﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.Infrastructure
{
    public class LogicalOrSelectionFilter : ISelectionFilter
    {
        private readonly List<ISelectionFilter> _filters;

        public LogicalOrSelectionFilter(List<ISelectionFilter> filters)
        {
            _filters = filters;
        }

        public bool AllowElement(Element elem)
        {
            return _filters.Any(f => f.AllowElement(elem));
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            return false;
        }
    }
}
