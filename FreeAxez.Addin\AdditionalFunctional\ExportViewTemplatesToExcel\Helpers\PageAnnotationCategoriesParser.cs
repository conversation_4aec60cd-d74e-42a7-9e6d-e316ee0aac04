﻿using System;
using System.Collections.Generic;
using System.Linq;
using OfficeOpenXml;
using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.ExportViewTemplatesToExcel.Helpers
{
    public class PageAnnotationCategoriesParser
    {
        private const string WorksheetName = "Annotation Categories";

        public void ParseSheetAndUpdateAnnotationCategories(ExcelPackage package, Document doc, Dictionary<string, View> viewTemplates, Dictionary<string, Category> annotationCategories, LocalLogger localLogger)
        {
            var worksheet = package.Workbook.Worksheets[WorksheetName];
            if (worksheet == null)
                throw new InvalidOperationException($"Worksheet '{WorksheetName}' not found.");

            // Statistics tracking
            int processedRows = 0;
            int updatedCategories = 0;
            var errors = new List<string>();

            int startRow = 2;
            int endRow = worksheet.Dimension.End.Row;

            var nonModifiableAnnotations = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "Annotation Crop Boundary",
                "Callout Boundary",
                "Callout Heads",
                "Cameras",
                "Crop Boundaries",
                "Rebar Set Toggle",
                "Render Regions",
                "Section Line",
                "Section Marks",
                "Spot Elevation Symbols",
                "View Reference",
                "Viewports",
                "Elevation Marks",
                "Level Heads",
                "Grid Heads",
                "Multi-Rebar Annotations",
                "Rebar Cover References",
            };

            for (int row = startRow; row <= endRow; row++)
            {
                try
                {
                    string vtName = worksheet.Cells[row, 1].Text.Trim();
                    if (string.IsNullOrEmpty(vtName)) continue;
                    if (!viewTemplates.TryGetValue(vtName, out View viewTemplate)) continue;

                    string catName = worksheet.Cells[row, 2].Text.Trim();
                    if (string.IsNullOrEmpty(catName)) continue;
                    if (!annotationCategories.TryGetValue(catName, out Category category)) continue;
                    ElementId catId = category.Id;

                    if (nonModifiableAnnotations.Contains(catName))
                    {
                        continue;
                    }

                    string visText = worksheet.Cells[row, 3].Text.Trim();
                    if (!string.IsNullOrEmpty(visText))
                    {
                        try
                        {
                            viewTemplate.SetCategoryHidden(catId, !visText.Equals("Yes", StringComparison.OrdinalIgnoreCase));
                        }
                        catch (Exception ex)
                        {
                            errors.Add($"Row {row}: Error setting visibility for template '{vtName}', annotation category '{catName}': {ex.Message}");
                        }
                    }

                    OverrideGraphicSettings ogs = null;
                    try
                    {
                        ogs = viewTemplate.GetCategoryOverrides(catId);
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"Row {row}: Error getting overrides for template '{vtName}', annotation category '{catName}': {ex.Message}");
                        continue;
                    }

                    processedRows++;
                    updatedCategories++;

                    try
                    {
                        var lineColor = GetRevitColorFromCell(worksheet.Cells[row, 4]);
                        if (lineColor != null)
                            ogs.SetProjectionLineColor(lineColor);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting line color for template '{vtName}', annotation category '{catName}': {ex.Message}"); }

                    try
                    {
                        string linePatternName = worksheet.Cells[row, 5].Text.Trim();
                        if (!string.IsNullOrEmpty(linePatternName))
                            ogs.SetProjectionLinePatternId(GetLinePatternIdByName(doc, linePatternName));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting line pattern for template '{vtName}', annotation category '{catName}': {ex.Message}"); }

                    try
                    {
                        if (int.TryParse(worksheet.Cells[row, 6].Text.Trim(), out int lineWeight))
                            ogs.SetProjectionLineWeight(lineWeight);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting line weight for template '{vtName}', annotation category '{catName}': {ex.Message}"); }

                    try
                    {
                        string halftoneText = worksheet.Cells[row, 7].Text.Trim();
                        if (!string.IsNullOrEmpty(halftoneText))
                            ogs.SetHalftone(halftoneText.Equals("Yes", StringComparison.OrdinalIgnoreCase));
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error setting halftone for template '{vtName}', annotation category '{catName}': {ex.Message}"); }

                    try
                    {
                        viewTemplate.SetCategoryOverrides(catId, ogs);
                    }
                    catch (Exception ex) { errors.Add($"Row {row}: Error updating annotation overrides for template '{vtName}', category '{catName}': {ex.Message}"); }
                }
                catch (Exception ex)
                {
                    errors.Add($"Row {row}: Error processing row in Annotation Categories: {ex.Message}");
                }
            }

            // Log summary
            if (errors.Any())
            {
                localLogger?.Error($"Annotation Categories processing completed with {errors.Count} errors:");
                foreach (var error in errors.Take(20))
                {
                    localLogger?.Error($"  {error}");
                }
                if (errors.Count > 20)
                    localLogger?.Error($"  ... and {errors.Count - 20} more errors");
            }

            localLogger?.Information($"Annotation Categories processed: {processedRows} rows, {updatedCategories} categories updated");
        }

        private Autodesk.Revit.DB.Color GetRevitColorFromCell(ExcelRange cell)
        {
            var rgb = cell.Style.Fill.BackgroundColor.Rgb;
            if (string.IsNullOrEmpty(rgb) || rgb.Length != 8) return null;
            int r = int.Parse(rgb.Substring(2, 2), System.Globalization.NumberStyles.HexNumber);
            int g = int.Parse(rgb.Substring(4, 2), System.Globalization.NumberStyles.HexNumber);
            int b = int.Parse(rgb.Substring(6, 2), System.Globalization.NumberStyles.HexNumber);
            return new Autodesk.Revit.DB.Color((byte)r, (byte)g, (byte)b);
        }

        private ElementId GetLinePatternIdByName(Document doc, string patternName)
        {
            if (string.IsNullOrEmpty(patternName)) return ElementId.InvalidElementId;
            var lpe = new FilteredElementCollector(doc)
                .OfClass(typeof(LinePatternElement))
                .Cast<LinePatternElement>()
                .FirstOrDefault(x => x.Name.Equals(patternName, StringComparison.OrdinalIgnoreCase));
            return lpe?.Id ?? ElementId.InvalidElementId;
        }
    }
}
