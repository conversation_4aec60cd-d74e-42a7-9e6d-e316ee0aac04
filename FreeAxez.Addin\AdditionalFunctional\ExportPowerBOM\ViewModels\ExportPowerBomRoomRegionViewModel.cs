﻿using System.Diagnostics;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Enums;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Models;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Utils;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.ViewModels;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using FreeAxez.Addin.Infrastructure.UI;
using FreeAxez.Addin.Infrastructure;
using System.IO;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Input;
using Settings = FreeAxez.Addin.Properties.Settings;

namespace FreeAxez.Addin.AdditionalFunctional.ExportPowerBOM.ViewModels
{
    internal class ExportPowerBomRoomRegionViewModel : WindowViewModel
    {
        private string _exportFolderPath;
        private string _revisionNumber = string.Empty;
        private string _areaName = string.Empty;
        private readonly SelectionOption _selectionOption;
        private readonly Element _selectedElement;

        public ExportPowerBomRoomRegionViewModel()
        {
        }

        public ExportPowerBomRoomRegionViewModel(SelectionOption selectionOption, Element selectedElement)
        {
            _selectionOption = selectionOption;
            _selectedElement = selectedElement;

            InitializeViewModel();
            SetSelection();
        }

        public string ExportFolderPath
        {
            get
            {
                string savedPath = Properties.Settings.Default.ExportPowerBOMFolderPath;
                return !string.IsNullOrWhiteSpace(savedPath) && Directory.Exists(savedPath) ? savedPath : string.Empty;
            }
            set
            {
                if (Properties.Settings.Default.ExportPowerBOMFolderPath != value)
                {
                    Properties.Settings.Default.ExportPowerBOMFolderPath = value;
                    Properties.Settings.Default.Save();
                    OnPropertyChanged();
                }
            }
        }

        public string RevisionNumber
        {
            get => _revisionNumber;
            set
            {
                if (_revisionNumber != value)
                {
                    _revisionNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public string AreaName
        {
            get => _areaName;
            set
            {
                if (_areaName != value)
                {
                    _areaName = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool OpenFile
        {
            get
            {
                return Properties.Settings.Default.exportPowerBomOpenFile;
            }
            set
            {
                Properties.Settings.Default.exportPowerBomOpenFile = value;
                Properties.Settings.Default.Save();
            }
        }

        public bool IsRegionSelected => _selectionOption == SelectionOption.SelectedRegion;

        public List<NodeViewModel> Accessories { get; private set; }

        public ICommand BrowseFolderCommand { get; set; }
        public ICommand CheckAllCommand { get; set; }
        public ICommand UncheckAllCommand { get; set; }
        public ICommand ExportCommand { get; set; }

        private void InitializeViewModel()
        {
            Accessories = new List<NodeViewModel>();

            var checkedAccessories = Settings.Default.ExportPowerBomAccessories.Split(';');
            Accessories = GriddProductCollector.GetOptionalProductNames().Select(a =>
                new NodeViewModel { Name = a, IsChecked = checkedAccessories.Contains(a) }).ToList();

            BrowseFolderCommand = new RelayCommand(OnBrowseFolderCommandExecute);
            CheckAllCommand = new RelayCommand(OnCheckAllCommandExecute);
            UncheckAllCommand = new RelayCommand(OnUncheckAllCommandExecute);
            ExportCommand = new RelayCommand(OnExportCommandExecute, CanExport);

            SetSelection();
        }

        private void SetSelection()
        {
            if (_selectionOption == SelectionOption.SelectedRegion)
            {
                AreaName = string.Empty; // User should input this
            }
            else if (_selectionOption == SelectionOption.SelectedRoom && _selectedElement is Room room)
            {
                AreaName = room.Name;
            }
            else
            {
                AreaName = string.Empty;
            }
            OnPropertyChanged(nameof(IsRegionSelected));
            OnPropertyChanged(nameof(AreaName));
        }

        private bool CanExport(object p)
        {
            if (string.IsNullOrWhiteSpace(RevisionNumber))
            {
                return false;
            }

            if (string.IsNullOrWhiteSpace(ExportFolderPath))
            {
                return false;
            }

            if (IsRegionSelected && string.IsNullOrWhiteSpace(AreaName))
            {
                return false;
            }

            return true;
        }

        private void OnBrowseFolderCommandExecute(object p)
        {
            var folderBrowserDialog = new FolderBrowserDialog
            {
                Description = "Select a folder to export the Excel file to:",
                SelectedPath = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory)
            };

            if (folderBrowserDialog.ShowDialog() == DialogResult.OK)
            {
                ExportFolderPath = folderBrowserDialog.SelectedPath;
            }
        }

        private void OnCheckAllCommandExecute(object p)
        {
            Accessories.ForEach(a => a.IsChecked = true);
        }

        private void OnUncheckAllCommandExecute(object p)
        {
            Accessories.ForEach(a => a.IsChecked = false);
        }

        private void OnExportCommandExecute(object p)
        {
            var revision = new RevisionViewModel
            {
                RevisionNumber = RevisionNumber,
                RevisionDate = DateTime.Now.ToString("MM-dd-yyyy"),
                RevisionAuthor = ""
            };

            var selectedAccessories = Accessories.Where(a => a.IsChecked).Select(a => a.Name).ToList();
            var griddBomRevision = new GriddBomRevisionForRoomRegion(revision, selectedAccessories, _selectedElement, _selectionOption, AreaName);
            griddBomRevision.CalculateBom();

            var griddBomToExcelExport = new GriddBomToExcelExportForRoomRegion(ExportFolderPath, griddBomRevision);
            griddBomToExcelExport.Export();

            if (p is Window window)
            {
                window.Close();
            }

            if (OpenFile)
            {
                try
                {
                    var startInfo = new ProcessStartInfo()
                    {
                        FileName = Path.Combine(_exportFolderPath, griddBomRevision.DocumentName),
                        UseShellExecute = true
                    };

                    Process.Start(startInfo);
                }
                catch (Exception ex)
                {
                    MessageWindow.ShowDialog("Error Opening File", "Failed to open the exported file: " + ex.Message, MessageType.Error);
                }
            }
        }
    }
}
