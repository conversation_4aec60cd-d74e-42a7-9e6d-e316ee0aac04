﻿using System.Globalization;
using System.Windows.Data;
using System;

namespace FreeAxez.Addin.AdditionalFunctional.Ramp.Converters
{
    public class EnumToBoolConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || parameter == null) return false;

            if (value.Equals(parameter)) return true;

            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || parameter == null || !(bool)value) return null;

            return parameter;
        }
    }
}