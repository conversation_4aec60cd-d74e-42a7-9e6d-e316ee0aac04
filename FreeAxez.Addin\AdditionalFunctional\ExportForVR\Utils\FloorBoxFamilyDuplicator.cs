using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.IO;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.ExportForVR.Utils
{
    public class FloorBoxFamilyDuplicator
    {
        private readonly Document _document;
        private readonly string _familyDirPath;

        public FloorBoxFamilyDuplicator(Document document, string familyDirPath)
        {
            _document = document;
            _familyDirPath = familyDirPath;
        }

        public void DuplicateFamilies()
        {
            if (!Directory.Exists(_familyDirPath))
            {
                return;
            }

            var categoryFamilies = new FilteredElementCollector(_document)
                .OfClass(typeof(Family))
                .Cast<Family>()
                .Where(f => f.FamilyCategoryId?.GetIntegerValue() == (int)BuiltInCategory.OST_ElectricalFixtures)
                .ToList();

            var familyNameRegex = new Regex(@"-FB\d+$");

            foreach (var family in categoryFamilies)
            {
                try
                {
                    if (familyNameRegex.IsMatch(family.Name) == false) continue; // Skip families that do not match the FB

                    var filePathWithSuffix = Path.Combine(_familyDirPath, $"{family.Name}.rfa");

                    if (File.Exists(filePathWithSuffix)) continue; // Family file with suffix already exists

                    var match = familyNameRegex.Match(family.Name);
                    var familyNameWithoutSuffix = family.Name.Substring(0, match.Index);
                    var familyFileNameWithoutSuffix = $"{familyNameWithoutSuffix}.rfa";
                    var filePathWithoutSuffix = Path.Combine(_familyDirPath, familyFileNameWithoutSuffix);

                    if (File.Exists(filePathWithoutSuffix) == false) continue; // Original family file does not exist

                    File.Copy(filePathWithoutSuffix, filePathWithSuffix); // Copy original family file to new file with suffix
                    LogHelper.Information("Copy FB family: " + filePathWithoutSuffix + " copied to " + filePathWithSuffix);
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Failed to duplicate family '{family.Name}': {ex.Message}");
                }
            }
        }
    }
}
