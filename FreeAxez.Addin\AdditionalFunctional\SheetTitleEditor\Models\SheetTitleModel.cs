using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.SheetTitleEditor.Models
{
    public class SheetTitleModel : BaseViewModel
    {
        private string _sheetTitle;
        private string _originalSheetTitle;
        private bool _isSelected;
        private string _sheetTitleParameterName = "Sheet Title";

        public SheetTitleModel(ViewSheet viewSheet)
        {
            _sheetTitle = viewSheet.LookupParameter(_sheetTitleParameterName)?.AsString() ?? "";
            _originalSheetTitle = _sheetTitle;

            ViewSheet = viewSheet;
            ViewName = GetViewName(viewSheet);
            BrowserPath = GetBrowserPath(viewSheet);
        }

        public ViewSheet ViewSheet { get; }

        public string SheetNumber => ViewSheet.SheetNumber;

        public string SheetName => ViewSheet.Name;

        public string ViewName { get; }

        public string BrowserPath { get; }

        public string SheetTitle
        {
            get => _sheetTitle;
            set => Set(ref _sheetTitle, value);
        }

        public string OriginalSheetTitle => _originalSheetTitle;

        public bool IsSelected
        {
            get => _isSelected;
            set => Set(ref _isSelected, value);
        }

        public bool IsTitleChanged => _sheetTitle != _originalSheetTitle;

        public bool UpdateSheetTitleInModel()
        {
            if (OriginalSheetTitle.Equals(SheetTitle))
                return false; // No change, do nothing

            var titleParameter = ViewSheet.LookupParameter(_sheetTitleParameterName);

            if (titleParameter == null)
            {
                // Parameter not found, cannot update
                return false;
            }

            return titleParameter.Set(SheetTitle);
        }

        private string GetViewName(ViewSheet viewSheet)
        {
            var viewPlan = viewSheet
                .GetAllPlacedViews()
                .Select(id => RevitManager.Document.GetElement(id) as View)
                .Where(v => v is ViewPlan)
                .FirstOrDefault();

            return viewPlan?.Name ?? "";
        }

        private string GetBrowserPath(ViewSheet viewSheet)
        {
            var browserOrganization = BrowserOrganization.GetCurrentBrowserOrganizationForSheets(RevitManager.Document);
            var path = string.Join(" > ", browserOrganization.GetFolderItems(viewSheet.Id).Select(i => i.Name)); 
            return path;
        }
    } 
}
