﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;

namespace FreeAxez.Addin.AdditionalFunctional.CountBaseUnits.Utils
{
    public class InstanceSelectionFilter : ISelectionFilter
    {
        public bool AllowElement(Element elem)
        {
            if (elem is Instance)
            {
                return true;
            }
                
            return false;
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            return false;
        }
    }
}
