﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.DeleteViewBySize.Views;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.DeleteViewBySize
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    internal class DeleteViewBySizeCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            var window = new DeleteViewBySizeView();
            window.ShowDialog();

            return Result.Succeeded;
        }
    }
}
