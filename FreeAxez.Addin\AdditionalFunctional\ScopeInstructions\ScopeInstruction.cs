﻿using System;

namespace FreeAxez.Addin.AdditionalFunctional.ScopeInstructions
{
    public class ScopeInstruction
    {
        public ScopeInstruction() { }

        public ScopeInstruction(Guid id,
                                string scopeName,
                                string scopeNumber,
                                string fileName,
                                string url,
                                string description)
        {
            Id = id;
            ScopeName = scopeName;
            ScopeNumber = double.Parse(scopeNumber);
            FileName = fileName;
            Url = url;
            Description = description;
        }

        public Guid Id { get; set; }
        public string ScopeName { get; set; }
        public double ScopeNumber { get; set; }
        public string FileName { get; set; }
        public string Url { get; set; }
        public string Description { get; set; }
    }
}
