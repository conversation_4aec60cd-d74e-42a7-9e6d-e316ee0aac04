﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Utils;
using FreeAxez.Addin.Utils;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Models
{
    public class GriddBomProduct
    {
        private const string ModelParamName = "Model";
        private const string ProductParamName = "Product Name";
        private const string LengthParamName = "Length";
        private const string EmptyValuePlaceholder = "N/A";

        private List<FamilyInstance> _familyInstances;
        private readonly SmartLookupParameter _smartLookupParameter;

        private Dictionary<string, double> _linearElementLengthByModel = new Dictionary<string, double>()
        {
            { "FA-4206", 95.0 / 12 }, // Frames
            { "FA-7206", 95.0 / 12 }, // Frames

            { "FA-0430", 96.0 / 12 }, // Curbs
            { "FA-0730", 96.0 / 12 }, // Curbs

            { "FA-4104", 20.0 / 12 }, // End Cover
            { "FA-7104", 20.0 / 12 }, // End Cover

            { "FA-4104L", 39.6 / 12 }, // End Cover Long
            { "FA-7104L", 39.6 / 12 }, // End Cover Long

            { "FA-4096", 900.0 }, // Undersheet
        };


        public GriddBomProduct(List<FamilyInstance> familyInstances, SmartLookupParameter smartLookupParameter)
        {
            _familyInstances = familyInstances;
            _smartLookupParameter = smartLookupParameter;
        }


        public string Model { get; set; }
        public string Name { get; set; }
        public int Count { get; set; }


        public static string GetProductKey(FamilyInstance instance, SmartLookupParameter smartLookupParameter)
        {
            var model = smartLookupParameter.LookupParameter(instance.Symbol, ModelParamName)?.AsString();
            if (string.IsNullOrWhiteSpace(model)) model = EmptyValuePlaceholder;

            var productName = smartLookupParameter.LookupParameter(instance.Symbol, ProductParamName)?.AsString();
            if (string.IsNullOrWhiteSpace(productName)) productName = EmptyValuePlaceholder;

            return $"{model} : {productName}";
        }

        public void CalculateBom()
        {
            var instance = _familyInstances.First();

            var model = _smartLookupParameter.LookupParameter(instance.Symbol, ModelParamName)?.AsString();
            if (string.IsNullOrWhiteSpace(model)) model = EmptyValuePlaceholder;

            var productName = _smartLookupParameter.LookupParameter(instance.Symbol, ProductParamName)?.AsString();
            if (string.IsNullOrWhiteSpace(productName)) productName = EmptyValuePlaceholder;

            Model = model;
            Name = productName;
            Count = GetCount(Model);
        }

        public List<string> GetData()
        {
            return new List<string>() { Model, Name, Count.ToString() };
        }

        private int GetCount(string model)
        {
            if (!_linearElementLengthByModel.ContainsKey(model))
            {
                return _familyInstances.Count;
            }
            else
            {
                var stockLength = _linearElementLengthByModel[model];
                var lengthOfAllPieces = _familyInstances
                    .Select(f => GetLengthFromInstanceOrSymbol(f))
                    .SelectMany(l => SplitByStockLength(l, stockLength))
                    .ToList();

                return LinearElementCutting.CalculateCountOfStockElements(lengthOfAllPieces, stockLength);
            }
        }

        private List<double> SplitByStockLength(double length, double stockLength)
        {
            var output = new List<double>();

            if (length <= stockLength)
            {
                output.Add(length);
            }
            else
            {
                var stockItemsCount = (int)Math.Floor(length / stockLength);
                for (int i = 0; i < stockItemsCount; i++)
                {
                    output.Add(stockLength);
                }
                var lastItemLength = length - stockItemsCount * stockLength;
                output.Add(lastItemLength);
            }

            return output;
        }

        private double GetLengthFromInstanceOrSymbol(FamilyInstance instance)
        {
            var lengthParameter = _smartLookupParameter.LookupParameter(instance, LengthParamName);
            if (lengthParameter == null)
            {
                lengthParameter = _smartLookupParameter.LookupParameter(instance.Symbol, LengthParamName);
            }

            return lengthParameter != null ? lengthParameter.AsDouble() : 0;
        }
    }
}
