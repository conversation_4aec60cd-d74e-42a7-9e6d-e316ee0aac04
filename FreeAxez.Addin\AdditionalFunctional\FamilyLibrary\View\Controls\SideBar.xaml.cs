using System.Windows;
using System.Windows.Controls;
using System.Windows.Markup;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls;

[ContentProperty("InnerContent")]
public partial class SideBar : UserControl
{
    public static readonly DependencyProperty InnerContentProperty =
        DependencyProperty.Register("InnerContent", typeof(object), typeof(SideBar), new UIPropertyMetadata(null));
    public SideBar()
    {
        InitializeComponent();
        DataContext = this;
    }
        
    public string SidebarTitle { get; set; }
        
    public object InnerContent
    {
        get => (object)GetValue(InnerContentProperty);
        set => SetValue(InnerContentProperty, value);
    }
}