﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.TransferParameterValue.Utils
{
    class SelectedTypeSelectionFilter : ISelectionFilter
    {
        private Element _sourceElement;
        private ElementType _sourceElementType;


        public SelectedTypeSelectionFilter(Element sourceElement)
        {
            _sourceElement = sourceElement;
            _sourceElementType = RevitManager.Document.GetElement(_sourceElement.GetTypeId()) as ElementType;
        }


        public bool AllowElement(Element elem)
        {
            if (_sourceElement.Id.GetIntegerValue() == elem.Id.GetIntegerValue())
            {
                return false;
            }
            
            var targetElementType = RevitManager.Document.GetElement(elem.GetTypeId());

            if (_sourceElementType is FamilySymbol)
            {
                if (targetElementType is FamilySymbol)
                {
                    return (_sourceElementType as FamilySymbol).Family.Id.GetIntegerValue() == (targetElementType as FamilySymbol).Family.Id.GetIntegerValue();
                }

                return false;
            }
            else
            {
                if (targetElementType.Category?.Id?.GetIntegerValue() == null)
                {
                    return false;
                }

                return targetElementType.Category.Id.GetIntegerValue() == _sourceElementType.Category.Id.GetIntegerValue();
            }
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            return false;
        }
    }
}
