﻿using FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Models;

namespace FreeAxez.Addin.AdditionalFunctional.ElectricalCircuit.Utils
{
    public class TrackWhipFinder
    {
        public List<Connection> FindTrackWhipCombinations(
            List<CircuitDevice> tracks,
            List<CircuitWhip> whips,
            double tolerance = 1.0)
        {
            var graph = new ConnectionGraph();

            var input = new List<CircuitElement>();
            input.AddRange(tracks);
            input.AddRange(whips);

            foreach (var current in input)
            {
                foreach (var next in input)
                {
                    if (current != next 
                        && !(current is CircuitWhip && next is CircuitWhip)
                        && current.LevelId.Equals(next.LevelId) 
                        && current.Geometries.Any(cg => next.Geometries.Any(ng => cg.Intersects(ng))))
                    {
                        graph.AddConnection(current, next);
                    }
                }
            }

            var result = new List<Connection>();
            var visited = new HashSet<CircuitElement>();

            // Go through starting with the starting whips
            var startWhips = new List<CircuitWhip>();
            foreach (var whip in whips)
            {
                var connectedElements = graph.GetConnectedElements(whip);
                if (connectedElements.Count == 1 && connectedElements.First() is CircuitDevice)
                {
                    startWhips.Add(whip);
                }
            }

            foreach (var whip in startWhips)
            {
                if (!visited.Contains(whip))
                {
                    TraverseBranchFromWhip(whip, graph, result, visited);
                }
            }

            // Add lonely whips
            foreach (var whip in whips)
            {
                if (!result.Any(c => c.CircuitElements.Any(cu => cu == whip)))
                {
                    result.Add(new Connection(whip, null));
                }
            }

            // Add lonely tracks
            foreach (var track in tracks)
            {
                if (!visited.Contains(track))
                {
                    result.Add(new Connection(track, null));
                }
            }

            return result;
        }

        private void TraverseBranchFromWhip(
            CircuitElement current,
            ConnectionGraph graph,
            List<Connection> result,
            HashSet<CircuitElement> visited)
        {
            if (visited.Contains(current))
                return;

            visited.Add(current);

            var connectedElements = graph.GetConnectedElements(current);

            foreach (var next in connectedElements)
            {
                if (visited.Contains(next))
                    continue;

                if (current is CircuitWhip && next is CircuitDevice) // Direction from whip to track
                {
                    result.Add(new Connection(current, next));
                }

                TraverseBranchFromWhip(next, graph, result, visited);
            }
        }
    }
}
