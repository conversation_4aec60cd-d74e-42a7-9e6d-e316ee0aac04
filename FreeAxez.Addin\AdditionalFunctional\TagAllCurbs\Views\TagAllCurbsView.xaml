﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Views.TagAllCurbsView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.ViewModels"
        SizeToContent="WidthAndHeight"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Title="Tag Curbs">
    <Window.DataContext>
        <vm:TagAllCurbsViewModel/>
    </Window.DataContext>
    <Grid Margin="10,0,10,10">
        <StackPanel Grid.ColumnSpan="2">
            <DockPanel Margin="0,10,0,0">
                <Label DockPanel.Dock="Left" Content="Leader Length"/>
                <TextBox VerticalContentAlignment="Center" Text="{Binding LengthToCenter}"/>
            </DockPanel>
            <GroupBox Margin="0,5,0,5" Header="Select Curbs">
                <StackPanel Margin="0,5,0,2">
                    <RadioButton Content="Visible In View" IsChecked="{Binding TagAll}" GroupName="tagregCurbs"/>
                    <RadioButton Margin="0,5,0,0" Content="Select Instances" IsChecked="{Binding TagSelected}" GroupName="tagregCurbs"/>
                </StackPanel>
            </GroupBox>
            <StackPanel Margin="0,10,0,0" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Width="100" Height="30" Content="Create" Command="{Binding CreateCommand}" 
                        CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
                <Button Width="100" Margin="10,0,0,0" Content="Cancel" Command="{Binding CancelCommand}" 
                        CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"/>
            </StackPanel>
        </StackPanel>
    </Grid>
</Window>