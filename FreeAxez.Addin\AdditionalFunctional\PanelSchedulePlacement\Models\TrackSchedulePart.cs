﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement.Models
{
    internal class TrackSchedulePart
    {
        private const double BoundingBoxOffsetForSchedule = 0.00694444444444486;
        private const double RowHeight = 0.015174851;
        private const double TitleHeight = 0.034722222;
        private readonly ViewSchedule _viewSchedule;


        /// <summary>
        /// A wrapper class around a revit specification for easy access to dimensions.
        /// </summary>
        public TrackSchedulePart(ViewSchedule viewSchedule)
        {
            _viewSchedule = viewSchedule;
        }


        public string Name => _viewSchedule.Name;
        public string TrackName => _viewSchedule.Name.Split('_')[1].Trim();
        public string Level => _viewSchedule.Name.Split('_').First().Trim();
        public double Height { get; private set; }
        public double Width { get; private set; }


        public ScheduleSheetInstance Place(ElementId viewSheetId, XYZ origin)
        {
            return ScheduleSheetInstance.Create(
                RevitManager.Document, viewSheetId, _viewSchedule.Id, origin);
        }

        public void SetDimensionsFromInstance(ScheduleSheetInstance scheduleInstance)
        {
            var boundingBox = scheduleInstance.get_BoundingBox(
                RevitManager.Document.GetElement(scheduleInstance.OwnerViewId) as ViewSheet);

            if (_viewSchedule.Definition.ShowTitle)
            {
                Height = boundingBox.Max.Y - boundingBox.Min.Y - BoundingBoxOffsetForSchedule;
            }
            else
            {
                Height = boundingBox.Max.Y - boundingBox.Min.Y - BoundingBoxOffsetForSchedule * 2;
            }

            if (HasNoRows(scheduleInstance))
            {
                Height += RowHeight;
            }

            Width = boundingBox.Max.X - boundingBox.Min.X - BoundingBoxOffsetForSchedule * 2;
        }

        private bool HasNoRows(ScheduleSheetInstance scheduleInstance)
        {
            // The number of rows can be obtained via TableSectionData,
            // but obtaining TableSectionData for each table is very time-consuming.
            // An easier and faster way is to simply hardcode the row and header heights.

            var dataHeight = Height;
            if (_viewSchedule.Definition.ShowTitle)
            {
                dataHeight -= TitleHeight;
            }

            if (dataHeight < RowHeight * 1.5) // Less than a line and a half
            {
                return true;
            }

            return false;
        }
    }
}
