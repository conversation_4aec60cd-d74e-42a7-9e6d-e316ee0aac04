﻿using System;
using System.Collections.Generic;
using System.Windows.Media.Imaging;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Modals;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Details;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Dialog;

public class DialogManager
{
    private readonly IDialogService _dialogService;

    public DialogManager()
    {
        _dialogService = new DialogService();
    }

    public void ShowAddCategoryDialog(Action<bool?> onDialogClose)
    {
        var viewModel = new AdminCategoryCreateVm
        {
            Title = "Add New Category",
            Content = new AdminCreateCategory()
        };
        _dialogService.ShowDialog(viewModel, onDialogClose);
    }

    public void ShowEditCategoryDialog(LibraryCategoryDto category, Action<bool?> onDialogClose)
    {
        var viewModel = new AdminCategoryEditVm(category)
        {
            Title = "Edit Category",
            Content = new AdminEditCategory()
        };
        _dialogService.ShowDialog(viewModel, onDialogClose);
    }

    public void ShowDeleteCategoryDialog(LibraryCategoryDto category, Action<bool?> onDialogClose)
    {
        var viewModel = new AdminCategoryDeleteVm(category)
        {
            Title = "Confirm Delete",
            Content = new AdminDeleteCategory()
        };
        _dialogService.ShowDialog(viewModel, onDialogClose);
    }

    public void ShowAddFamilyDialog(Action<bool?> onDialogClose)
    {
        var viewModel = new AdminFamilyAddVm
        {
            Title = "Add New Families",
            Content = new AdminFamilyAdd()
        };
        _dialogService.ShowDialog(viewModel, onDialogClose);
    }

    public void ShowEditFamilyDialog(LibraryItemVm libraryItemVm, Action<bool?> onDialogClose)
    {
        var viewModel = new AdminFamilyEditVm(libraryItemVm.LibraryItem)
        {
            Title = "Edit Family",
            Content = new AdminFamilyEdit()
        };
        _dialogService.ShowDialog(viewModel, onDialogClose);
    }

    public void ShowDeleteFamilyDialog(LibraryItemDto libraryItemDto, Action<bool?> onDialogClose)
    {
        var viewModel = new AdminFamilyDeleteVm(libraryItemDto)
        {
            Title = "Confirm Delete",
            Content = new AdminDeleteFamily()
        };
        _dialogService.ShowDialog(viewModel, onDialogClose);
    }

    public void ShowAddDetailsDialog(Action<bool?> onDialogClose)
    {
        var viewModel = new AdminDetailsAddVm
        {
            Title = "Add New Details",
            Content = new AdminDetailsAdd()
        };
        _dialogService.ShowDialog(viewModel, onDialogClose);
    }

    public void ShowEditDetailsDialog(LibraryItemDetailsDto details, Action<bool?> onDialogClose)
    {
        var viewModel = new AdminDetailsEditVm(details)
        {
            Title = "Edit Details",
            Content = new AdminDetailsEdit()
        };
        _dialogService.ShowDialog(viewModel, onDialogClose);
    }

    public void ShowDeleteDetailsDialog(LibraryItemDetailsDto details, Action<bool?> onDialogClose)
    {
        var viewModel = new AdminDetailsDeleteVm(details)
        {
            Title = "Confirm Delete",
            Content = new AdminDeleteDetails()
        };
        _dialogService.ShowDialog(viewModel, onDialogClose);
    }

    public void ShowDetailSelectionDialog(List<DetailViewModel> detailViews, Action<bool?, List<Autodesk.Revit.DB.ElementId>> onDialogClose)
    {
        var viewModel = new DetailSelectionDialogVm(detailViews)
        {
            Title = "Select Detail Views to Import",
            Content = new DetailSelectionDialog()
        };

        _dialogService.ShowDialog(viewModel, result =>
        {
            var selectedViewIds = result == true ? viewModel.SelectedViewIds : new List<Autodesk.Revit.DB.ElementId>();
            onDialogClose?.Invoke(result, selectedViewIds);
        });
    }

    public void ShowDetailPreviewDialog(BitmapSource previewImage, string title)
    {
        var viewModel = new DetailPreviewDialogVm(previewImage, title)
        {
            Content = new DetailPreviewDialog()
        };

        _dialogService.ShowDialog(viewModel, null);
    }
}