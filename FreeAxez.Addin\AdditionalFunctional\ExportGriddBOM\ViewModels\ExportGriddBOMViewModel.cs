﻿using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Models;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Input;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.ViewModels
{
    internal class ExportGriddBomViewModel : WindowViewModel
    {
        private List<NodeViewModel> _accessories;
        private Element _selectedElement;
        private SelectionOption _selectionOption;

        public ExportGriddBomViewModel()
        {
            if (File.Exists(Properties.Settings.Default.exportGriddBOMPath))
            {
                FilePath = Properties.Settings.Default.exportGriddBOMPath; 
            }

            _accessories = new List<NodeViewModel>();

            var checkedAccessories = Properties.Settings.Default.ExportGriddBomAccessories.Split(';');
            _accessories = GriddProductCollector.GetOptionalProductNames().Select(a =>
                new NodeViewModel() { Name = a, IsChecked = checkedAccessories.Contains(a) }).ToList();

            BrowseCommand = new RelayCommand(OnBrowseCommandExecute);
            CheckAllCommand = new RelayCommand(OnCheckAllCommandExecute);
            UncheckAllCommand = new RelayCommand(OnUncheckAllCommandExecute);
            ExportCommand = new RelayCommand(OnExportCommandExecute);
        }

        public string FilePath { get; set; }
        public void SetSelection(Element selectedElement, SelectionOption option)
        {
            _selectedElement = selectedElement;
            _selectionOption = option;
        }
        public List<string> Sheets
        {
            get
            {
                if (!File.Exists(FilePath))
                {
                    return new List<string>();
                }

                try
                {
                    var excelDocument = ExcelDocument.Open(FilePath);
                    var revSheetNames = excelDocument.GetSheetNames()
                        .Where(s => GriddBomExcelSchema.GetRevNumber(s) != -1)
                        .OrderByDescending(s => GriddBomExcelSchema.GetRevNumber(s))
                        .ToList();

                    var lastRev = GriddBomExcelSchema.GetRevNumber(revSheetNames.First());
                    revSheetNames.Insert(0, string.Format(GriddBomExcelSchema.RevSheetNameTemplate, lastRev + 1, DateTime.Now.ToString("dd-MM-yy")) + " (New)");
                    SelectedSheet = revSheetNames.First();
                    OnPropertyChanged(nameof(SelectedSheet));
                    return revSheetNames;
                }
                catch
                {
                    return new List<string>();
                }
            }
        }

        public string SelectedSheet { get; set; }
        public List<NodeViewModel> Accessories { get => _accessories; }

        public bool OpenFile
        {
            get
            {
                return Properties.Settings.Default.exportGriddBOMOpenFile;
            }
            set
            {
                Properties.Settings.Default.exportGriddBOMOpenFile = value;
                Properties.Settings.Default.Save();
            }
        }

        public ICommand BrowseCommand { get; set; }

        private void OnBrowseCommandExecute(object p)
        {
            var initialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory);
            if (File.Exists(Properties.Settings.Default.exportGriddBOMPath))
            {
                initialDirectory = Path.GetDirectoryName(Properties.Settings.Default.exportGriddBOMPath);
            }

            var openFileDialog = new OpenFileDialog
            {
                InitialDirectory = initialDirectory,
                Filter = "Excel Files|*.xls;*.xlsx;*.xlsm",
                Title = "Select an Excel file"
            };

            if (openFileDialog.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            if (IsOpened(openFileDialog.FileName))
            {
                MessageWindow.ShowDialog("The file is open. Please close the file and try again.", MessageType.Notify);
                return;
            }

            var excelDocument = ExcelDocument.Open(openFileDialog.FileName);
            var revSheetNames = excelDocument.GetSheetNames()
                .Where(s => GriddBomExcelSchema.GetRevNumber(s) != -1)
                .ToList();
            if (revSheetNames.Count == 0)
            {
                MessageWindow.ShowDialog("The file does not contain any revision sheet.", MessageType.Notify);
                return;
            }

            if (File.Exists(openFileDialog.FileName))
            {
                Properties.Settings.Default.exportGriddBOMPath = openFileDialog.FileName;
                Properties.Settings.Default.Save();
            }

            FilePath = openFileDialog.FileName;
            OnPropertyChanged(nameof(FilePath));
            OnPropertyChanged(nameof(Sheets));
            OnPropertyChanged(nameof(SelectedSheet));
        }

        public ICommand CheckAllCommand { get; set; }
        private void OnCheckAllCommandExecute(object p)
        {
            Accessories.ForEach(a => a.IsChecked = true);
        }

        public ICommand UncheckAllCommand { get; set; }
        private void OnUncheckAllCommandExecute(object p)
        {
            Accessories.ForEach(a => a.IsChecked = false);
        }

        public ICommand ExportCommand { get; set; }
        private void OnExportCommandExecute(object p)
        {
            (p as Window).Close();

            Properties.Settings.Default.ExportGriddBomAccessories =
                string.Join(";", _accessories.Where(a => a.IsChecked).Select(a => a.Name).ToList());
            Properties.Settings.Default.Save();

            if (string.IsNullOrEmpty(SelectedSheet))
            {
                MessageWindow.ShowDialog("The file does not contain any revision sheet.", MessageType.Notify);
                return;
            }

            if (!File.Exists(FilePath))
            {
                MessageWindow.ShowDialog("The path to the document is incorrect.", MessageType.Notify);
                return;
            }

            if (IsOpened(FilePath))
            {
                MessageWindow.ShowDialog("The file is open. Please close the file and try again.", MessageType.Notify);
                return;
            }

            var revisionViewModel = new RevisionViewModel()
            {
                RevisionNumber = GriddBomExcelSchema.GetRevNumber(SelectedSheet).ToString(),
                RevisionDate = SelectedSheet.Split(' ')[1],
                RevisionAuthor = ""
            };

            var griddBomRevision = new GriddBomRevision(revisionViewModel, _accessories.Where(a => a.IsChecked).Select(a => a.Name).ToList());
            griddBomRevision.CalculateBom();

            var griddBomToExcelExport = new GriddBomToExcelExport(FilePath, griddBomRevision);
            griddBomToExcelExport.Export();

            (p as Window).Close();
        }


        private bool IsOpened(string filePath)
        {
            try
            {
                using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.None))
                {
                    return false;
                }
            }
            catch (IOException)
            {
                return true;
            }
        }
    }
}
