<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls.HeaderBar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             Height="40"
             d:DesignWidth="900"
             Background="Transparent">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <StackPanel>
             <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="40"/>
                </Grid.ColumnDefinitions>
                <TextBlock Margin="20 0 0 0"
                                        Style="{StaticResource TextH4}"
                                        Text="{Binding PageName,
                                        RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=UserControl}}"
                           />
                <TextBox Grid.Column="1"
                         Panel.ZIndex="1"
                         WindowChrome.IsHitTestVisibleInChrome="True"
                         Tag="Search"
                         Style="{StaticResource Search}"
                         Margin="20 0"
                         Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"/>
                <Button Grid.Column="2"
                             WindowChrome.IsHitTestVisibleInChrome="True"
                             VerticalAlignment="Top"
                             Style="{StaticResource ButtonCloseMain}"
                             Command="{Binding CloseMainWindowCommand }"
                             Content="X" CommandParameter="{Binding
                            RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Window}}}" >
                </Button>
            </Grid>
            <Separator  Background="#DADADA"
                        Height="1"></Separator>
        </StackPanel>
    </Grid>
</UserControl>
