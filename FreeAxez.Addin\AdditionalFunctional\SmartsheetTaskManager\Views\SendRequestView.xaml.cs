﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;
using System;
using System.Windows;
using System.Windows.Media.Imaging;

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Views;

public partial class SendRequestView : Window
{
    public SendRequestView()
    {
        Icon = new BitmapImage(new Uri(
        "pack://application:,,,/FreeAxez.Addin;component/AdditionalFunctional/SmartsheetTaskManager/Resources/Icons/smartsheet16.png"));

        InitializeComponent();
    }

    private void Window_Closed(object sender, EventArgs e)
    {
        if (DataContext is SendRequestViewModel sendRequestViewModel)
        {
            sendRequestViewModel.CancellationTokenSource.Cancel();
        }
    }
}
