﻿using System;
using Serilog;

namespace FreeAxez.Core.Services
{
    public static class LogInitializer
    {
        public static void Initialize(string name)
        {
            var path = String.Format(@"falogs\\{0}.txt", name);
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.RollingFile(path)
                .CreateLogger();
        }

        public static void InitializeWithFullPath(string path)
        {
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.RollingFile(path)
                .CreateLogger();
        }
    }
}