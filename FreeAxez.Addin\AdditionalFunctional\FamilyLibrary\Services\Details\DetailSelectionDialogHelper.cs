using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Media.Imaging;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Dialog;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Details;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services.Details
{
    /// <summary>
    /// Helper for showing detail selection dialog using modal dialog system
    /// </summary>
    public static class DetailSelectionDialogHelper
    {
        /// <summary>
        /// Result of the detail selection dialog
        /// </summary>
        public class DetailSelectionResult
        {
            public bool DialogResult { get; set; }
            public HashSet<ElementId> SelectedViewIds { get; set; } = new();
        }

        /// <summary>
        /// Shows detail selection dialog using modal dialog system
        /// </summary>
        public static DetailSelectionResult ShowModalDetailSelectionDialog(Document srcDoc, List<ViewDrafting> draftingViews)
        {
            var result = new DetailSelectionResult();

            try
            {
                LogHelper.Information("Creating modal detail selection dialog");

                // Create view models for each drafting view
                var detailViewModels = new List<DetailViewModel>();

                foreach (var draftingView in draftingViews)
                {
                    LogHelper.Information($"Creating view model for: {draftingView.Name}");

                    // Create preview image
                    var previewImage = RevitPreviewUtil.CreatePreviewImage(draftingView, srcDoc);

                    var viewModel = new DetailViewModel
                    {
                        ElementId = draftingView.Id,
                        Name = draftingView.Name,
                        ViewType = DetailViewType.DraftingView,
                        IsEnabled = true,
                        IsSelected = false,
                        PreviewImage = previewImage
                    };

                    detailViewModels.Add(viewModel);
                    LogHelper.Information($"Added view: {draftingView.Name}");
                }

                LogHelper.Information($"Total views in collection: {detailViewModels.Count}");

                // Show modal dialog
                var dialogManager = new DialogManager();
                bool? dialogResult = null;
                List<ElementId> selectedViewIds = null;

                LogHelper.Information("About to show modal dialog...");

                // ShowDialog is synchronous, so callback will be called before this method returns
                dialogManager.ShowDetailSelectionDialog(detailViewModels, (dlgResult, viewIds) =>
                {
                    dialogResult = dlgResult;
                    selectedViewIds = viewIds;
                    LogHelper.Information($"Modal dialog result: {dlgResult}");
                });

                // Dialog has completed (ShowDialog is synchronous)
                result.DialogResult = dialogResult == true;
                result.SelectedViewIds = new HashSet<ElementId>(selectedViewIds ?? new List<ElementId>());

                LogHelper.Information($"Dialog completed with result: {result.DialogResult}");
                LogHelper.Information($"Selected views count: {result.SelectedViewIds.Count}");

                return result;
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error in ShowDetailSelectionDialog: {ex.Message}");
                LogHelper.Error($"Stack trace: {ex.StackTrace}");
                result.DialogResult = false;
                result.SelectedViewIds = new HashSet<ElementId>();
                return result;
            }
        }
    }
}
