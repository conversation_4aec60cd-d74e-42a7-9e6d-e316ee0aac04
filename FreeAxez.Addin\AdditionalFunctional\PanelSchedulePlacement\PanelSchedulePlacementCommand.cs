﻿using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using Autodesk.Revit.Attributes;
using FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement.Views;
using FreeAxez.Addin.AdditionalFunctional.PanelScheduleCreation.Utils;
using FreeAxez.Addin.Infrastructure.UI;
using FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement.Models;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.PanelSchedulePlacement
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    internal class PanelSchedulePlacementCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            if (ProjectHasNoTrackSchedules())
            {
                MessageWindow.ShowDialog(
                    "There are no track schedules for placement in the project.\n" +
                    "Please create schedules for the tracks using " +
                    "the \"Create Schedules\" button and try again.", 
                    MessageType.Notify);

                return Result.Cancelled;
            }
            else if (LevelSheets.GetAllTrackScheduleSheetSizes().Count == 0)
            {
                MessageWindow.ShowDialog(
                    "There are no track schedule sheets in the project, " +
                    "or the parameter \"Sheet Size\" is incorrectly filled.\n\n" +

                    "Track schedule sheet is a sheet whose number corresponds to the template " +
                    "\"GP-T-01-24\", where 01 is the level number and 24 is the height.\n\n" +

                    "The value of the Sheet Size parameter should correspond " +
                    "to the dimensions of the sheet, for example: 24x36.\n\n" +

                    "Please create schedule sheets and try again.", 
                    MessageType.Notify);

                return Result.Cancelled;
            }

            var view = new PanelSchedulePlacementView();
            view.ShowDialog();

            return Result.Succeeded;
        }

        private bool ProjectHasNoTrackSchedules()
        {
            var trackScheduleManager = new TrackScheduleManager();
            var trackSchedules = trackScheduleManager.GetTrackSchedules();
            return trackSchedules.Count == 0;
        }
    }
}