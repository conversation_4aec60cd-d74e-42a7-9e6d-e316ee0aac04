﻿using System.Windows.Forms;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Interfaces;
using FreeAxez.Addin.Infrastructure;
using View = Autodesk.Revit.DB.View;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils
{
    public static class ModelManager
    {
        public static void CopyElements(Document source, Document target, IProgressReporter progressReporter, CancellationToken cancellationToken)
        {
            progressReporter.ReportStatus("Analyzing elements for copying from the source model...");
            Application.DoEvents();
            cancellationToken.ThrowIfCancellationRequested();

            var elementIds = new FilteredElementCollector(source)
                .WhereElementIsNotElementType()
                .WherePasses(new ElementMulticategoryFilter(new[]
                {
            BuiltInCategory.OST_SpecialityEquipment,
            BuiltInCategory.OST_FlexPipeCurves,
            BuiltInCategory.OST_ElectricalEquipment,
            BuiltInCategory.OST_ElectricalFixtures,
            BuiltInCategory.OST_Floors,
            BuiltInCategory.OST_StairsRailing,
            BuiltInCategory.OST_RailingSystem,
            BuiltInCategory.OST_Railings,
            BuiltInCategory.OST_Lines,
            BuiltInCategory.OST_Grids
                }))
                .Where(e => !(e is FamilyInstance) || (e as FamilyInstance).SuperComponent == null)
                .Where(e => e.Category.Id.GetIntegerValue() != (int)BuiltInCategory.OST_Lines || e is ModelCurve)
                .Select(e => e.Id)
                .ToList();

            if (!elementIds.Any())
            {
                progressReporter.ReportStatus("No elements found in the source model for copying.");
                LogHelper.Information("No elements to copy from the source model.");
                return;
            }

            progressReporter.ReportStatus($"Found {elementIds.Count} elements to copy. Starting the process...");
            Application.DoEvents();

            var totalElements = elementIds.Count;
            var copiedElements = 0;

            // Group elements for small transactions
            const int elementsPerBatch = 500;
            var batches = elementIds
                .Select((element, index) => new { Element = element, Index = index })
                .GroupBy(x => x.Index / elementsPerBatch)
                .Select(g => g.Select(x => x.Element).ToList())
                .ToList();

            LogHelper.Information($"Starting to copy {elementIds.Count} elements.");
            var copyPasteOptions = new CopyPasteOptions();
            copyPasteOptions.SetDuplicateTypeNamesHandler(new DuplicateTypeNamesHandler());

            foreach (var batch in batches)
            {
                cancellationToken.ThrowIfCancellationRequested();
                Application.DoEvents();

                try
                {
                    using (var t = new Transaction(target, "Copy Elements"))
                    {
                        CommonFailuresPreprocessor.SetFailuresPreprocessor(t);
                        t.Start();

                        ElementTransformUtils.CopyElements(source, batch, target, null, copyPasteOptions);

                        t.Commit();
                    }

                    copiedElements += batch.Count;
                    var progress = (double)copiedElements / totalElements * 100;
                    progressReporter.ReportProgress(progress);

                    progressReporter.ReportStatus(
                        $"Copied {copiedElements} of {totalElements} elements from Source to Target model.");
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Error copying elements: {ex.Message}");
                    progressReporter.ReportStatus($"An error occurred while copying elements: {ex.Message}. Skipping batch...");
                }

                Application.DoEvents();
            }

            progressReporter.ReportStatus("All elements have been successfully copied to the target model.");
            LogHelper.Information($"Successfully copied {totalElements} elements from source to target.");
            Application.DoEvents();
        }

        public static void CopyVolumeOfInterestElements(Document source, Document target)
        {
            try
            {
                LogHelper.Information("Starting to copy OST_VolumeOfInterest elements.");

                BuiltInCategory category = BuiltInCategory.OST_VolumeOfInterest;

                List<ElementId> elementIds = new FilteredElementCollector(source)
                    .WhereElementIsNotElementType()
                    .OfCategory(category)
                    .Where(e => !(e is FamilyInstance fi) || fi.SuperComponent == null)
                    .Select(e => e.Id)
                    .ToList();

                if (!elementIds.Any())
                {
                    LogHelper.Information("No OST_VolumeOfInterest elements found in the source model for copying.");
                    return;
                }

                LogHelper.Information($"Found {elementIds.Count} OST_VolumeOfInterest elements to copy.");

                CopyPasteOptions copyPasteOptions = new CopyPasteOptions();
                copyPasteOptions.SetDuplicateTypeNamesHandler(new DuplicateTypeNamesHandler());

                using (Transaction trans = new Transaction(target, "Copy OST_VolumeOfInterest Elements"))
                {
                    CommonFailuresPreprocessor.SetFailuresPreprocessor(trans);
                    trans.Start();

                    ICollection<ElementId> copiedElementIds = ElementTransformUtils.CopyElements(
                        source,
                        elementIds,
                        target,
                        null,
                        copyPasteOptions
                    );

                    trans.Commit();

                    LogHelper.Information($"Successfully copied {copiedElementIds.Count} OST_VolumeOfInterest elements.");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error copying OST_VolumeOfInterest elements: {ex.Message}");
            }
        }


        public static void CleanupTargetModel(Document target, IProgressReporter progressReporter,
            CancellationToken cancellationToken)
        {
            progressReporter.ReportStatus("Analyzing elements for cleanup in the target model...");
            Application.DoEvents();
            cancellationToken.ThrowIfCancellationRequested();

            BuiltInCategory[] categoriesToDelete =
            {
                BuiltInCategory.OST_Lines,
                BuiltInCategory.OST_ElectricalEquipment,
                BuiltInCategory.OST_ElectricalFixtures,
                BuiltInCategory.OST_GenericModel,
                BuiltInCategory.OST_Floors,
                BuiltInCategory.OST_Walls,
                BuiltInCategory.OST_SpecialityEquipment,
                BuiltInCategory.OST_LightingFixtures,
                BuiltInCategory.OST_DetailComponents,
                BuiltInCategory.OST_VolumeOfInterest,
                BuiltInCategory.OST_FlexPipeCurves,
                BuiltInCategory.OST_StairsRailing,
                BuiltInCategory.OST_RevisionClouds,
                BuiltInCategory.OST_RevisionCloudTags
            };

            var multiCategoryFilter = new ElementMulticategoryFilter(categoriesToDelete);

            // Collect views to exclude from deletion
            var excludedViewIds = new FilteredElementCollector(target)
                .OfClass(typeof(View))
                .Cast<View>()
                .Where(v => v.ViewType == ViewType.Legend || v.ViewType == ViewType.DraftingView || v.IsTemplate)
                .Select(v => v.Id)
                .ToList();

            // Collect elements to delete
            var elementsToDelete = new FilteredElementCollector(target)
                .WherePasses(multiCategoryFilter)
                .WhereElementIsNotElementType()
                .Where(e => !excludedViewIds.Contains(e.OwnerViewId))
                .Select(e => e.Id)
                .ToList();

            if (!elementsToDelete.Any())
            {
                progressReporter.ReportStatus("No removable elements were found in the target model.");
                LogHelper.Information("Cleanup process skipped. No elements to delete.");
                return;
            }

            var totalElements = elementsToDelete.Count;
            progressReporter.ReportStatus($"Found {totalElements} elements to delete. Starting cleanup...");
            Application.DoEvents();

            int processedElements = 0;
            const int elementsPerBatch = 500;

            // Process elements in batches for better performance
            var batches = elementsToDelete
                .Select((element, index) => new { Element = element, Index = index })
                .GroupBy(x => x.Index / elementsPerBatch)
                .Select(g => g.Select(x => x.Element).ToList())
                .ToList();

            foreach (var batch in batches)
            {
                cancellationToken.ThrowIfCancellationRequested();
                Application.DoEvents();

                try
                {
                    using (var trans = new Transaction(target, "Delete Elements"))
                    {
                        CommonFailuresPreprocessor.SetFailuresPreprocessor(trans);
                        trans.Start();

                        target.Delete(batch);

                        trans.Commit();
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Error during cleanup: {ex.Message}");
                    progressReporter.ReportStatus(
                        $"An error occurred during cleanup: {ex.Message}. Continuing with the next items...");
                }

                processedElements += batch.Count;
                double progress = (double)processedElements / totalElements * 100;
                progressReporter.ReportProgress(progress);

                progressReporter.ReportStatus($"Cleaned up {processedElements} of {totalElements} elements in target model.");
                Application.DoEvents();
            }

            progressReporter.ReportStatus(
                "Cleanup process completed successfully. All removable elements have been deleted.");
            LogHelper.Information($"Cleanup process finished. Deleted {totalElements} elements from the target model.");
            Application.DoEvents();
        }
    }
}