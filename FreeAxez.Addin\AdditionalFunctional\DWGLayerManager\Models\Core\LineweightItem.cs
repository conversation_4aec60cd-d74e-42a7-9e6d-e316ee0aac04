namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core
{
    public class LineweightItem
    {
        public short Value { get; set; }
        public string DisplayName { get; set; } = string.Empty;

        public LineweightItem(short value, string displayName)
        {
            Value = value;
            DisplayName = displayName;
        }

        /// <summary>Visual thickness for UI display (1-8 pixels)</summary>
        public double VisualThickness =>
            Math.Max(1, Math.Min(8, Value == -3 ? 1 : Math.Abs(Value) / 25.0));

        public override string ToString() => DisplayName;
    }
}
