﻿namespace FreeAxez.Addin.AdditionalFunctional.LegendManagement.Models
{
    public class Legend
    {
        public Legend(long id, string name)
        {
            Id = id;
            Name = name;
        }

        public long Id { get; }
        public string Name { get; }

        public override bool Equals(object obj)
        {
            if (obj is null)
            {
                return false;
            }

            if (ReferenceEquals(this, obj))
            {
                return true;
            }

            if (obj is Legend legend)
            {
                return legend.Name.Equals(Name, System.StringComparison.OrdinalIgnoreCase);
            }

            return false;
        }

        public override int GetHashCode()
        {
            return Name.GetHashCode();
        }
    }
}
