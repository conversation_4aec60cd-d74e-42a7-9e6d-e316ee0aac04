﻿using FreeAxez.Addin.AdditionalFunctional.ExportCutsheet.ViewModels;
using System.Windows;

namespace FreeAxez.Addin.AdditionalFunctional.ExportCutsheet.Views
{
    public partial class ExportCutsheetView : Window
    {
        public ExportCutsheetView()
        {
            InitializeComponent();
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            if (DataContext is ExportCutsheetViewModel exportCutsheetViewModel)
            {
                exportCutsheetViewModel.WindowClosingCommand.Execute(sender);
            }
        }
    }
}
