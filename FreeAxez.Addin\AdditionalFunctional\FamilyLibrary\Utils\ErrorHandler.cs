﻿using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;

public static class ErrorHandler
{
    public static void HandleError(string context, Exception ex, MessageType messageType = MessageType.Error,
        Action onError = null)
    {
        LogHelper.Error($"{context}: {ex.Message}");
        FamilyLibraryCore.ShowMessage("Download to Revit", $"{context}: {ex.Message}", messageType);
        onError?.Invoke();
    }
}