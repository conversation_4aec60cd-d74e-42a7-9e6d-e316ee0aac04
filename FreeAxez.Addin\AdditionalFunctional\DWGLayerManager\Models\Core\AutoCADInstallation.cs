using System;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;

public class AutoCADInstallation : IEquatable<AutoCADInstallation>
{
    public int Version { get; set; }
    public string InstallPath { get; set; }
    public string CoreConsolePath { get; set; }
    public string DisplayName => $"AutoCAD {Version}";

    public bool IsValid => !string.IsNullOrEmpty(CoreConsolePath) &&
                          System.IO.File.Exists(CoreConsolePath);

    public bool Equals(AutoCADInstallation other)
    {
        if (other == null) return false;
        return Version == other.Version &&
               string.Equals(CoreConsolePath, other.CoreConsolePath, StringComparison.OrdinalIgnoreCase);
    }

    public override bool Equals(object obj)
    {
        return Equals(obj as AutoCADInstallation);
    }

    public override int GetHashCode()
    {
        unchecked
        {
            var hash = 17;
            hash = hash * 23 + Version.GetHashCode();
            hash = hash * 23 + (CoreConsolePath?.ToLowerInvariant()?.GetHashCode() ?? 0);
            return hash;
        }
    }
}
