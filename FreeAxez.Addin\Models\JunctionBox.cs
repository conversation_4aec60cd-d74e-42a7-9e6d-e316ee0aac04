﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class JunctionBox : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_ElectricalFixtures,
            FamilyNamesContains = new List<string>()
            {
            },
            FamilyNamesEndWith = new List<string>
            {
                "Junction_Box",
                "JunctionBox"
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public JunctionBox(Element element) : base(element)
        {
        }

        public static List<JunctionBox> Collect()
        {
            return FamilyCollector.Instances.Select(g => new JunctionBox(g)).ToList();
        }

        public static ISelectionFilter CreateSelectionFilter()
        {
            return new JunctionBoxSelectionFilter();
        }

        private class JunctionBoxSelectionFilter : ISelectionFilter
        {
            private readonly List<ElementId> _symbolIds;

            public JunctionBoxSelectionFilter()
            {
                _symbolIds = FamilyCollector.Symbols.Select(s => s.Id).ToList();
            }

            public bool AllowElement(Element elem)
            {
                return elem is FamilyInstance instance
                    && _symbolIds.Any(s => instance.Symbol.Id.Equals(s));
            }

            public bool AllowReference(Reference reference, XYZ position)
            {
                return true;
            }
        }
    }
}
