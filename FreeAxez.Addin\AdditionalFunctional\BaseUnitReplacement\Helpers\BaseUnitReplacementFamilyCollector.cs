﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models;

namespace FreeAxez.Addin.AdditionalFunctional.BaseUnitReplacement.Helpers
{
    public class BaseUnitReplacementFamilyCollector
    {
        private readonly List<Level> _levels;
        private readonly string _cutoutSymbolsMessage = "";

        public BaseUnitReplacementFamilyCollector(List<Level> levels)
        {
            _levels = levels;

            CutoutSymbols = BaseUnitCutout.CollectSymbols(out _cutoutSymbolsMessage);
            CutoutInstances = BaseUnitCutout.CollectInstances()
                .Where(i => levels.Any(l => l.Id.Equals(i.LevelId)))
                .ToList();

            BaseUnitSymbols = BaseUnit.CollectSymbols(out _);
            BaseUnitInstances = BaseUnit.CollectInstances()
                .Where(i => levels.Any(l => l.Id.Equals(i.LevelId)))
                .ToList();

            FloorBoxInstances = FloorBox.CollectFloorBoxInstances()
                .Where(i => levels.Any(l => l.Id.Equals(i.LevelId)))
                .ToList();
        }

        public List<FamilySymbol> CutoutSymbols { get; private set; }
        public List<FamilyInstance> CutoutInstances { get; private set; }
        public List<FamilySymbol> BaseUnitSymbols { get; private set; }
        public List<FamilyInstance> BaseUnitInstances { get; private set; }
        public List<FamilyInstance> FloorBoxInstances { get; private set; }

        public bool IsFamiliesExist(out string message)
        {
            if (CutoutSymbols.Count == 0)
            {
                message = _cutoutSymbolsMessage;
                return false;
            }

            if (FloorBoxInstances.Count == 0)
            {
                message = "There are no floor boxes on the selected levels.";
                return false;
            }

            if (BaseUnitInstances.Count == 0)
            {
                message = "There are no base units at the selected levels.";
                return false;
            }

            message = "";
            return true;
        }

        // TODO: Verify method
        public FamilySymbol GetValidUnitSymbolForIntersection(FamilySymbol unitSymbol, FamilySymbol boxSymbol)
        {
            if (unitSymbol == null) return null;

            var isCutoutSymbol = CutoutSymbols.Any(s => s.Id.Equals(unitSymbol.Id));
            var griddSize = unitSymbol.Name.Contains("40") ? "40" : "70";
            if (boxSymbol == null)
            {
                return isCutoutSymbol
                       ? BaseUnitSymbols.FirstOrDefault(s => s.Name.Contains(griddSize)) // Replace cutout to base unit
                       : unitSymbol; // Base unit without box is valid
            }

            if (isCutoutSymbol) return unitSymbol; // Valid intersection
  
            var boxSize = boxSymbol.Name.Contains("Small") ? "Small" : "Large";
            return CutoutSymbols.FirstOrDefault(s => s.Name.Contains(boxSize) && s.Name.Contains(griddSize)); // Replace base to cutout unit
        }
    }
}
