﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="ColorsPalette.xaml" />
        <ResourceDictionary Source="CheckBoxes.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <ControlTemplate x:Key="ComboBoxFilterButton"
                     TargetType="{x:Type ToggleButton}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition Width="30" />
            </Grid.ColumnDefinitions>
            <Border x:Name="Border"
                    Grid.ColumnSpan="2"
                    CornerRadius="8"
                    BorderBrush="{StaticResource Gray200}"
                    BorderThickness="1"
                    Background="{StaticResource Gray25}"
                    SnapsToDevicePixels="true" />
            <Border Grid.Column="1">
                <Viewbox Width="20"
                         Height="20"
                         HorizontalAlignment="Center"
                         VerticalAlignment="Center">
                    <Canvas Width="1024"
                            Height="1024">
                        <Path x:Name="Filter"
                              Fill="{StaticResource Gray300}"
                              Data="M859.02 234.524l0.808-0.756 0.749-0.813c27.047-29.356 33.876-70.34 17.823-106.957-15.942-36.366-50.416-58.957-89.968-58.957H163.604c-38.83 0-73.043 22.012-89.29 57.444-16.361 35.683-10.632 76.301 14.949 106.004l0.97 1.126 280.311 266.85 2.032 312.074c0.107 16.502 13.517 29.805 29.995 29.805l0.2-0.001c16.568-0.107 29.912-13.626 29.804-30.194l-2.198-337.564-296.478-282.241c-9.526-11.758-11.426-26.933-5.044-40.851 6.446-14.059 19.437-22.452 34.75-22.452h624.828c15.6 0 28.69 8.616 35.017 23.047 6.31 14.391 3.924 29.831-6.354 41.497l-304.13 284.832 1.302 458.63c0.047 16.54 13.469 29.916 29.998 29.915h0.087c16.568-0.047 29.962-13.517 29.915-30.085L573.04 502.36l285.98-267.836z" />
                        <Path x:Name="Filter2"
                              Fill="{StaticResource Gray300}"
                              Data="M657.265 595.287c0 16.498 13.499 29.997 29.997 29.997h243.897c16.498 0 29.997-13.498 29.997-29.997 0-16.498-13.499-29.997-29.997-29.997H687.262c-16.498 0-29.997 13.499-29.997 29.997z m273.894 138.882H687.262c-16.498 0-29.997 13.499-29.997 29.997s13.499 29.997 29.997 29.997h243.897c16.498 0 29.997-13.499 29.997-29.997 0-16.498-13.499-29.997-29.997-29.997z m0 168.878H687.262c-16.498 0-29.997 13.499-29.997 29.997s13.499 29.997 29.997 29.997h243.897c16.498 0 29.997-13.499 29.997-29.997 0-16.498-13.499-29.997-29.997-29.997z" />
                    </Canvas>
                </Viewbox>
            </Border>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="BorderBrush"
                        Value="{StaticResource Gray300}"
                        TargetName="Border" />
                <Setter Property="Fill"
                        Value="{StaticResource Gray500}"
                        TargetName="Filter" />
                <Setter Property="Fill"
                        Value="{StaticResource Gray500}"
                        TargetName="Filter2" />
            </Trigger>
            <DataTrigger Binding="{Binding IsDropDownOpen, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}"
                         Value="True">
                <Setter Property="BorderBrush"
                        Value="{DynamicResource Green500}"
                        TargetName="Border" />
                <Setter Property="Fill"
                        Value="{StaticResource Gray600}"
                        TargetName="Filter" />
                <Setter Property="Fill"
                        Value="{StaticResource Gray600}"
                        TargetName="Filter2" />
            </DataTrigger>
            <DataTrigger Binding="{Binding IsAnyItemSelected}"
                         Value="True">
                <Setter Property="Background"
                        Value="{StaticResource Gray600}"
                        TargetName="Border" />
                <Setter Property="Fill"
                        Value="White"
                        TargetName="Filter" />
                <Setter Property="Fill"
                        Value="White"
                        TargetName="Filter2" />
            </DataTrigger>
            <Trigger Property="IsKeyboardFocused"
                     Value="True">
                <Setter TargetName="Border"
                        Property="BorderBrush"
                        Value="{StaticResource Gray600}" />
                <Setter TargetName="Border"
                        Property="BorderThickness"
                        Value="1" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="ComboBoxArrowButton"
                     TargetType="{x:Type ToggleButton}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition Width="30" />
            </Grid.ColumnDefinitions>
            <Border x:Name="FilterBorder"
                    Grid.ColumnSpan="2"
                    CornerRadius="8"
                    BorderBrush="{StaticResource Gray200}"
                    BorderThickness="1"
                    Background="{StaticResource Gray25}"
                    SnapsToDevicePixels="true" />
            <Border Grid.Column="1">
                <Path x:Name="Arrow"
                      Data="F1 M 0,0 L 2.667,2.66665 L 5.3334,0 L 5.3334,-1.78168 L 2.6667,0.88501 L0,-1.78168 L0,0 Z"
                      Fill="{StaticResource Gray400}"
                      Stretch="Fill"
                      Height="7"
                      Width="10"
                      HorizontalAlignment="Center"
                      VerticalAlignment="Center" />
            </Border>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="BorderBrush"
                        Value="{StaticResource Gray300}"
                        TargetName="FilterBorder" />
                <Setter Property="Fill"
                        Value="{StaticResource Gray500}"
                        TargetName="Arrow" />
            </Trigger>
            <DataTrigger Binding="{Binding IsDropDownOpen, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}"
                         Value="True">
                <Setter Property="BorderBrush"
                        Value="{StaticResource Gray400}"
                        TargetName="FilterBorder" />
                <Setter Property="BorderThickness"
                        Value="2"
                        TargetName="FilterBorder" />
                <Setter Property="Fill"
                        Value="{StaticResource Gray600}"
                        TargetName="Arrow" />
            </DataTrigger>
            <Trigger Property="IsKeyboardFocused"
                     Value="True">
                <Setter Property="BorderBrush"
                        Value="{StaticResource Gray400}"
                        TargetName="FilterBorder" />
                <Setter Property="BorderThickness"
                        Value="1"
                        TargetName="FilterBorder" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="ComboBoxTextBox"
                     TargetType="{x:Type TextBox}">
        <Border x:Name="PART_ContentHost"
                Focusable="False"
                Background="{TemplateBinding Background}" />
    </ControlTemplate>

    <Style x:Key="Filter"
           TargetType="{x:Type ComboBox}">
        <Setter Property="Height"
                Value="32" />
        <Setter Property="Width"
                Value="Auto" />
        <Setter Property="Cursor"
                Value="Hand" />
        <Setter Property="BorderThickness"
                Value="1" />
        <Setter Property="FontSize"
                Value="13" />
        <Setter Property="Foreground"
                Value="Black" />
        <Setter Property="HorizontalAlignment"
                Value="Stretch" />
        <Setter Property="ItemTemplate">
            <Setter.Value>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal"
                                HorizontalAlignment="Stretch">
                        <CheckBox Style="{StaticResource CheckBoxStyle}"
                                  Content="{Binding ContentValue}"
                                  HorizontalAlignment="Stretch"
                                  IsChecked="{Binding IsSelected, Mode=TwoWay}" />
                    </StackPanel>
                </DataTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ComboBox}">
                    <Grid>
                        <ToggleButton x:Name="ToggleButton"
                                      Template="{StaticResource ComboBoxFilterButton}"
                                      Focusable="False"
                                      ClickMode="Press"
                                      IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" />
                        <Label x:Name="Watermark"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Left"
                               Foreground="{StaticResource Gray600}"
                               Visibility="Visible"
                               Padding="8 0 0 0"
                               Margin="10 0 30 0"
                               Content="{TemplateBinding Tag}"
                               Background="Transparent"
                               FontSize="13"
                               FontWeight="SemiBold" />
                        <Popup x:Name="Popup"
                               Placement="Bottom"
                               IsOpen="{TemplateBinding IsDropDownOpen}"
                               AllowsTransparency="True"
                               Focusable="False"
                               PopupAnimation="Slide">
                            <Grid x:Name="DropDown"
                                  SnapsToDevicePixels="True"
                                  MinWidth="{TemplateBinding ActualWidth}"
                                  MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                <Border x:Name="DropDownBorder"
                                        BorderThickness="1"
                                        BorderBrush="LightGray"
                                        Background="White">
                                    <ScrollViewer Margin="4 6 4 6"
                                                  SnapsToDevicePixels="True"
                                                  OverridesDefaultStyle="True"
                                                  Foreground="Black"
                                                  FontSize="13">
                                        <StackPanel IsItemsHost="True"
                                                    KeyboardNavigation.DirectionalNavigation="Contained" />
                                    </ScrollViewer>
                                </Border>
                            </Grid>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="HasItems"
                                 Value="False">
                            <Setter TargetName="DropDownBorder"
                                    Property="MinHeight"
                                    Value="95" />
                        </Trigger>
                        <Trigger Property="IsGrouping"
                                 Value="True">
                            <Setter Property="ScrollViewer.CanContentScroll"
                                    Value="False" />
                        </Trigger>
                        <Trigger SourceName="Popup"
                                 Property="AllowsTransparency"
                                 Value="True">
                            <Setter TargetName="DropDownBorder"
                                    Property="CornerRadius"
                                    Value="4" />
                            <Setter TargetName="DropDownBorder"
                                    Property="Margin"
                                    Value="0 2 0 0" />
                        </Trigger>
                        <DataTrigger Binding="{Binding IsDropDownOpen}"
                                     Value="True">
                            <Setter TargetName="Watermark"
                                    Property="Foreground"
                                    Value="{StaticResource Green500}" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding IsAnyItemSelected}"
                                     Value="True">
                            <Setter TargetName="Watermark"
                                    Property="Foreground"
                                    Value="White" />
                            <Setter TargetName="Watermark"
                                    Property="FontWeight"
                                    Value="Bold" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="Combobox"
           TargetType="{x:Type ComboBox}">
        <Setter Property="Height"
                Value="32" />
        <Setter Property="Width"
                Value="Auto" />
        <Setter Property="Cursor"
                Value="Hand" />
        <Setter Property="BorderThickness"
                Value="1" />
        <Setter Property="FontSize"
                Value="13" />
        <Setter Property="Foreground"
                Value="{StaticResource Gray700}" />
        <Setter Property="HorizontalAlignment"
                Value="Stretch" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ComboBox}">
                    <Grid>
                        <ToggleButton x:Name="ToggleButton"
                                      Template="{StaticResource ComboBoxArrowButton}"
                                      Focusable="False"
                                      ClickMode="Press"
                                      IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" />
                        <ContentPresenter x:Name="ContentSite"
                                          IsHitTestVisible="False"
                                          Content="{TemplateBinding SelectionBoxItem}"
                                          ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                          ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                          Margin="11 0 25 0"
                                          VerticalAlignment="Center"
                                          HorizontalAlignment="Left"
                                          IsEnabled="True" />
                        <Label x:Name="Watermark"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Left"
                               Foreground="{StaticResource Gray800}"
                               Visibility="Collapsed"
                               Padding="8 0 0 0"
                               Content="{TemplateBinding Tag}"
                               Background="Transparent"
                               FontSize="13"
                               FontWeight="Light" />
                        <TextBox x:Name="PART_EditableTextBox"
                                 Template="{StaticResource ComboBoxTextBox}"
                                 HorizontalAlignment="Left"
                                 VerticalAlignment="Center"
                                 Focusable="True"
                                 Visibility="Hidden" />
                        <Popup x:Name="Popup"
                               Placement="Bottom"
                               IsOpen="{TemplateBinding IsDropDownOpen}"
                               AllowsTransparency="True"
                               Focusable="False"
                               PopupAnimation="Slide">
                            <Grid x:Name="DropDown"
                                  SnapsToDevicePixels="True"
                                  MinWidth="{TemplateBinding ActualWidth}"
                                  MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                <Border x:Name="DropDownBorder"
                                        BorderThickness="1"
                                        BorderBrush="LightGray"
                                        Background="White">
                                    <ScrollViewer Margin="4 6 4 6"
                                                  SnapsToDevicePixels="True"
                                                  OverridesDefaultStyle="True"
                                                  Foreground="Black"
                                                  FontSize="13">
                                        <StackPanel IsItemsHost="True"
                                                    KeyboardNavigation.DirectionalNavigation="Contained" />
                                    </ScrollViewer>
                                </Border>
                            </Grid>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled"
                                 Value="False">
                            <Setter Property="TextElement.Foreground"
                                    Value="{StaticResource Gray400}"
                                    TargetName="ContentSite" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Text"
                                           Value="" />
                            </MultiTrigger.Conditions>
                            <Setter Property="Visibility"
                                    Value="Visible"
                                    TargetName="Watermark" />
                        </MultiTrigger>
                        <Trigger Property="HasItems"
                                 Value="False">
                            <Setter TargetName="DropDownBorder"
                                    Property="MinHeight"
                                    Value="95" />
                        </Trigger>
                        <Trigger Property="IsGrouping"
                                 Value="True">
                            <Setter Property="ScrollViewer.CanContentScroll"
                                    Value="False" />
                        </Trigger>
                        <Trigger SourceName="Popup"
                                 Property="AllowsTransparency"
                                 Value="True">
                            <Setter TargetName="DropDownBorder"
                                    Property="CornerRadius"
                                    Value="4" />
                            <Setter TargetName="DropDownBorder"
                                    Property="Margin"
                                    Value="0 2 0 0" />
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                 Value="False">
                            <Setter TargetName="ContentSite"
                                    Property="Opacity"
                                    Value="0.5" />
                            <Setter Property="Foreground"
                                    Value="{StaticResource Gray400}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ComboBoxStyleFilter"
           TargetType="{x:Type ComboBox}">
        <Setter Property="Height"
                Value="32" />
        <Setter Property="Width"
                Value="Auto" />
        <Setter Property="Cursor"
                Value="Hand" />
        <Setter Property="BorderThickness"
                Value="1" />
        <Setter Property="FontSize"
                Value="13" />
        <Setter Property="Foreground"
                Value="Black" />
        <Setter Property="HorizontalAlignment"
                Value="Stretch" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ComboBox}">
                    <Grid>
                        <ToggleButton x:Name="ToggleButton"
                                      Template="{StaticResource ComboBoxFilterButton}"
                                      Focusable="False"
                                      ClickMode="Press"
                                      IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" />
                        <ContentPresenter x:Name="ContentSite"
                                          IsHitTestVisible="False"
                                          Content="{TemplateBinding SelectionBoxItem}"
                                          ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                          ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                          Margin="11 0 25 0"
                                          VerticalAlignment="Center"
                                          HorizontalAlignment="Left"
                                          IsEnabled="True" />
                        <Label x:Name="Watermark"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Left"
                               Foreground="{StaticResource Gray600}"
                               Visibility="Collapsed"
                               Padding="8 0 0 0"
                               Content="{TemplateBinding Tag}"
                               FontWeight="Normal"
                               FontSize="13" />
                        <TextBox x:Name="PART_EditableTextBox"
                                 Template="{StaticResource ComboBoxTextBox}"
                                 HorizontalAlignment="Left"
                                 VerticalAlignment="Center"
                                 Focusable="True"
                                 Visibility="Hidden" />
                        <Popup x:Name="Popup"
                               Placement="Bottom"
                               IsOpen="{TemplateBinding IsDropDownOpen}"
                               AllowsTransparency="True"
                               Focusable="False"
                               PopupAnimation="Slide">
                            <Grid x:Name="DropDown"
                                  SnapsToDevicePixels="True"
                                  MinWidth="{TemplateBinding ActualWidth}"
                                  MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                <Border x:Name="DropDownBorder"
                                        BorderThickness="1"
                                        BorderBrush="LightGray"
                                        Background="White">
                                    <ScrollViewer Margin="4 6 4 6"
                                                  SnapsToDevicePixels="True"
                                                  OverridesDefaultStyle="True"
                                                  Foreground="Black"
                                                  FontSize="13">
                                        <StackPanel IsItemsHost="True"
                                                    KeyboardNavigation.DirectionalNavigation="Contained" />
                                    </ScrollViewer>
                                </Border>
                            </Grid>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Text"
                                           Value="" />
                            </MultiTrigger.Conditions>
                            <Setter Property="Visibility"
                                    Value="Visible"
                                    TargetName="Watermark" />
                        </MultiTrigger>
                        <Trigger Property="HasItems"
                                 Value="False">
                            <Setter TargetName="DropDownBorder"
                                    Property="MinHeight"
                                    Value="95" />
                        </Trigger>
                        <Trigger Property="IsGrouping"
                                 Value="True">
                            <Setter Property="ScrollViewer.CanContentScroll"
                                    Value="False" />
                        </Trigger>
                        <Trigger SourceName="Popup"
                                 Property="AllowsTransparency"
                                 Value="True">
                            <Setter TargetName="DropDownBorder"
                                    Property="CornerRadius"
                                    Value="4" />
                            <Setter TargetName="DropDownBorder"
                                    Property="Margin"
                                    Value="0 2 0 0" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="ComboBoxSmToggleButton"
                     TargetType="{x:Type ToggleButton}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition Width="30" />
            </Grid.ColumnDefinitions>
            <Border x:Name="Border"
                    Grid.ColumnSpan="2"
                    CornerRadius="5"
                    BorderBrush="White"
                    BorderThickness="1"
                    SnapsToDevicePixels="true"
                    Background="#125b8a" />
            <Border Grid.Column="1">
                <Path x:Name="Arrow"
                      Data="F1 M 0,0 L 2.667,2.66665 L 5.3334,0 L 5.3334,-1.78168 L 2.6667,0.88501 L0,-1.78168 L0,0 Z"
                      Fill="White"
                      Stretch="Fill"
                      Height="7"
                      Width="10"
                      HorizontalAlignment="Center"
                      VerticalAlignment="Center" />
            </Border>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver"
                     Value="True">
                <Setter Property="BorderBrush"
                        Value="#606060"
                        TargetName="Border" />
                <Setter Property="Fill"
                        Value="#606060"
                        TargetName="Arrow" />
            </Trigger>
            <Trigger Property="IsKeyboardFocused"
                     Value="True">
                <Setter Property="BorderBrush"
                        Value="#606060"
                        TargetName="Border" />
                <Setter Property="BorderThickness"
                        Value="0.5 0.5 0.5 3"
                        TargetName="Border" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="ComboBoxSmStyleFa"
           TargetType="{x:Type ComboBox}">
        <Setter Property="Height"
                Value="32" />
        <Setter Property="Width"
                Value="180" />
        <Setter Property="Cursor"
                Value="Hand" />
        <Setter Property="BorderThickness"
                Value="1" />
        <Setter Property="FontSize"
                Value="13" />
        <Setter Property="FontWeight"
                Value="Bold" />
        <Setter Property="Foreground"
                Value="White" />
        <Setter Property="HorizontalAlignment"
                Value="Stretch" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ComboBox}">
                    <Grid>
                        <ToggleButton x:Name="ToggleButton"
                                      Template="{StaticResource ComboBoxSmToggleButton}"
                                      Focusable="False"
                                      ClickMode="Press"
                                      IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" />
                        <ContentPresenter x:Name="ContentSite"
                                          IsHitTestVisible="False"
                                          Content="{TemplateBinding SelectionBoxItem}"
                                          ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                          ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                          Margin="11 0 25 0"
                                          VerticalAlignment="Center"
                                          HorizontalAlignment="Left"
                                          IsEnabled="True" />
                        <Label x:Name="Watermark"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Left"
                               Foreground="White"
                               Visibility="Collapsed"
                               Padding="8 0 0 0"
                               Content="{TemplateBinding Tag}"
                               FontSize="13"
                               FontWeight="Light" />
                        <TextBox x:Name="PART_EditableTextBox"
                                 Template="{StaticResource ComboBoxTextBox}"
                                 HorizontalAlignment="Left"
                                 VerticalAlignment="Center"
                                 Focusable="True"
                                 Visibility="Hidden" />
                        <Popup x:Name="Popup"
                               Placement="Bottom"
                               IsOpen="{TemplateBinding IsDropDownOpen}"
                               AllowsTransparency="True"
                               Focusable="False"
                               PopupAnimation="Slide">
                            <Grid x:Name="DropDown"
                                  SnapsToDevicePixels="True"
                                  MinWidth="{TemplateBinding ActualWidth}"
                                  MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                <Border x:Name="DropDownBorder"
                                        BorderThickness="1"
                                        BorderBrush="LightGray"
                                        Background="White">
                                    <ScrollViewer Margin="4 6 4 6"
                                                  SnapsToDevicePixels="True"
                                                  OverridesDefaultStyle="True"
                                                  Foreground="#303039"
                                                  FontWeight="Light"
                                                  FontSize="13">
                                        <StackPanel IsItemsHost="True"
                                                    KeyboardNavigation.DirectionalNavigation="Contained" />
                                    </ScrollViewer>
                                </Border>
                            </Grid>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Text"
                                           Value="" />
                            </MultiTrigger.Conditions>
                            <Setter Property="Visibility"
                                    Value="Visible"
                                    TargetName="Watermark" />
                        </MultiTrigger>
                        <Trigger Property="HasItems"
                                 Value="False">
                            <Setter TargetName="DropDownBorder"
                                    Property="MinHeight"
                                    Value="95" />
                        </Trigger>
                        <Trigger Property="IsGrouping"
                                 Value="True">
                            <Setter Property="ScrollViewer.CanContentScroll"
                                    Value="False" />
                        </Trigger>
                        <Trigger SourceName="Popup"
                                 Property="AllowsTransparency"
                                 Value="True">
                            <Setter TargetName="DropDownBorder"
                                    Property="CornerRadius"
                                    Value="4" />
                            <Setter TargetName="DropDownBorder"
                                    Property="Margin"
                                    Value="0 2 0 0" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
    