﻿using Microsoft.Web.WebView2.Core;
using Microsoft.Win32;
using Serilog;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;

namespace FreeAxez.BrowserApp
{
    public partial class MainWindow : Window
    {
        public const string BaseUrl = "https://freeaxez.bimsmith.com/";
        //public const string BaseUrl = "http://localhost:4200/preview/";

        private readonly WindowDragHandler _dragHandler;

        public MainWindow(string revitUniqueId, string fileName)
        {
            var appData = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var loggerPath = Path.Combine(appData, "Anguleris Technologies", "FreeAxez", "Logs", "log-web-.txt");
            var webViewCachePath = Path.Combine(appData, "Anguleris Technologies", "FreeAxez", "WebView2Cache");

            var targetUrl = $"{BaseUrl}preview/{revitUniqueId}/{fileName}";

            _dragHandler = new WindowDragHandler(this);

            InitializeComponent();
            InitializeLogger(loggerPath);
            InitializeWebView2Async(webViewCachePath, targetUrl);
        }

        private void InitializeLogger(string path)
        {
            Log.Logger = new LoggerConfiguration()
               .MinimumLevel.Debug()
               .WriteTo.File(path, rollingInterval: RollingInterval.Day)
               .CreateLogger();
        }

        private async void InitializeWebView2Async(string webViewCachePath, string targetUrl)
        {
            try
            {
                var env = await CoreWebView2Environment.CreateAsync(null, webViewCachePath);
                await webView.EnsureCoreWebView2Async(env);

                webView.CoreWebView2.NavigationStarting += OnNavigationStarting;
                webView.NavigationCompleted += NavigationCompleted;
                webView.CoreWebView2.WebMessageReceived += WebMessageReceived;
                webView.CoreWebView2.DownloadStarting += OnDownloadStarting;

                webView.CoreWebView2.Navigate(targetUrl);

                Log.Information("Successful initialization of WebView2");
            }
            catch (Exception ex)
            {
                Log.Information($"Error initializing WebView2: {ex.Message}");
                MessageBox.Show($"Error initializing WebView2: {ex.Message}");
            }
        }

        private void OnNavigationStarting(object? sender, CoreWebView2NavigationStartingEventArgs e)
        {
            // Https check
            if (e.Uri != null && !e.Uri.StartsWith("https://"))
            {
                e.Cancel = true;
                var errorMessage = $"{e.Uri} is not safe, try an https link";
                Log.Warning(errorMessage);
                webView.CoreWebView2.ExecuteScriptAsync($"alert('{errorMessage}')");
                return;
            }

            // Domain check
            if (e.Uri == null
                || (!e.Uri.StartsWith(BaseUrl, StringComparison.OrdinalIgnoreCase)
                && !e.Uri.StartsWith("https://freeaxezstorage.blob.core.windows.net", StringComparison.OrdinalIgnoreCase)
                && !e.Uri.StartsWith("https://api-freeaxez.bimsmith.com", StringComparison.OrdinalIgnoreCase)))
            {
                e.Cancel = true;
                string errorMessage = $"The attempt to go to {e.Uri} was canceled because the URL does not match any of the allowed base URLs: " +
                                      $"{BaseUrl}, https://freeaxezstorage.blob.core.windows.net, or https://api-freeaxez.bimsmith.com.";
                Log.Warning(errorMessage);
                webView.CoreWebView2.ExecuteScriptAsync($"console.warn('{errorMessage}');");
                return;
            }

            Log.Information($"Navigation to {e.Uri}");
        }

        private void NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                string script = @"
                    (function() {
                        // We keep the original console.trace method
                        const originalTrace = console.trace;

                        // Overriding the console.trace method
                        console.trace = function() {
                            const args = Array.from(arguments).join(' ');
    
                            // Checking if this is one of our messages
                            if (args === ""Close"") {
                                // Sending the close command to C#
                                if (window.chrome && window.chrome.webview) {
                                    window.chrome.webview.postMessage(""close"");
                                }
                            } 
                            else if (args === ""down"") {
                                // Sending the down command to C#
                                if (window.chrome && window.chrome.webview) {
                                    window.chrome.webview.postMessage(""down"");
                                }
                            }
    
                            // Call the original console.trace method
                            originalTrace.apply(console, arguments);
                        };

                        // Add an additional handler for mousedown on the entire document
                        document.addEventListener('mousedown', function(event) {
                            // If the left mouse button is pressed and a click is made on an element that can be dragged
                            if (event.button === 0 && event.target.classList.contains('draggable-area')) {
                                console.trace(""down"");
                            }
                        });
                    })();
                ";

                webView.CoreWebView2.ExecuteScriptAsync(script);
            }
        }

        private void OnDownloadStarting(object? sender, CoreWebView2DownloadStartingEventArgs e)
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.FileName = e.ResultFilePath.Substring(e.ResultFilePath.LastIndexOf('\\') + 1);
            saveFileDialog.Filter = "Rvt files (*.rvt)|*.rvt";
            saveFileDialog.DefaultExt = ".rvt";

            if (saveFileDialog.ShowDialog() == true)
            {
                e.ResultFilePath = saveFileDialog.FileName;
                Log.Information($"Save file {e.ResultFilePath}");
            }
            else
            {
                Log.Information("File save canceled");
                e.Cancel = true;
            }
        }

        private async void WebMessageReceived(object? sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            string message = e.TryGetWebMessageAsString();

            if (string.Equals(message, "close", StringComparison.InvariantCultureIgnoreCase))
            {
                if (webView != null && webView.CoreWebView2 != null)
                {
                    try
                    {
                        await webView.CoreWebView2.Profile.ClearBrowsingDataAsync();
                    }
                    catch (Exception ex)
                    {
                        Log.Warning($"Error clearing browsing data: {ex.Message}");
                    }
                }

                this.Dispatcher.InvokeShutdown();
            }
            else if (string.Equals(message, "down", StringComparison.InvariantCultureIgnoreCase))
            {
                _dragHandler.DragWindow();
            }
        }
    }

    internal class WindowDragHandler
    {
        private readonly Window _window;

        [DllImport("user32.dll")]
        public static extern bool ReleaseCapture();

        [DllImport("user32.dll")]
        public static extern int SendMessage(IntPtr hWnd, int Msg, int wParam, int lParam);

        private const int WM_NCLBUTTONDOWN = 0xA1;
        private const int HT_CAPTION = 0x2;

        public WindowDragHandler(Window window)
        {
            _window = window;
        }

        public void DragWindow()
        {
            _window.Dispatcher.BeginInvoke(new Action(() => {
                var hwnd = new WindowInteropHelper(_window).Handle;
                ReleaseCapture();
                SendMessage(hwnd, WM_NCLBUTTONDOWN, HT_CAPTION, 0);
            }));
        }
    }
}
