using System;
using System.Windows;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Windows;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services
{
    /// <summary>
    /// Service for managing progress windows across the Family Library
    /// </summary>
    public class ProgressWindowService
    {
        private static readonly object Lock = new();
        private static ProgressWindowService? _instance;

        private ProgressWindow? _progressWindow;
        private ProgressWindowVm? _progressViewModel;

        private ProgressWindowService() { }

        public static ProgressWindowService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (Lock)
                    {
                        if (_instance == null)
                            _instance = new ProgressWindowService();
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Show progress window with initial message
        /// </summary>
        public void ShowProgress(string title, string message)
        {
            try
            {
                // Close existing window if any
                CloseProgress();

                // Create new progress window
                _progressWindow = new ProgressWindow();
                _progressViewModel = new ProgressWindowVm();
                _progressWindow.DataContext = _progressViewModel;

                // Set initial values
                _progressViewModel.Title = title;
                _progressViewModel.StatusMessage = message;
                _progressViewModel.IsIndeterminate = true; // Spinner mode

                // Set Revit as owner and show non-modal
                RevitManager.SetRevitAsWindowOwner(_progressWindow);
                _progressWindow.Show(); // NON-MODAL

                LogHelper.Information($"Progress window shown: {title} - {message}");
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error showing progress window: {ex.Message}");
            }
        }

        /// <summary>
        /// Update progress message
        /// </summary>
        public void UpdateProgress(string message)
        {
            try
            {
                if (_progressViewModel != null)
                {
                    _progressViewModel.StatusMessage = message;
                    LogHelper.Information($"Progress updated: {message}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error updating progress: {ex.Message}");
            }
        }

        /// <summary>
        /// Update progress with percentage (0-100)
        /// </summary>
        public void UpdateProgress(string message, int percentage)
        {
            try
            {
                if (_progressViewModel != null)
                {
                    _progressViewModel.StatusMessage = message;
                    _progressViewModel.IsIndeterminate = false;
                    _progressViewModel.ProgressValue = percentage;
                    LogHelper.Information($"Progress updated: {message} ({percentage}%)");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error updating progress with percentage: {ex.Message}");
            }
        }

        /// <summary>
        /// Close progress window
        /// </summary>
        public void CloseProgress()
        {
            try
            {
                if (_progressWindow != null)
                {
                    _progressWindow.Close();
                    _progressWindow = null;
                    _progressViewModel = null;
                    LogHelper.Information("Progress window closed");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error closing progress window: {ex.Message}");
            }
        }

        /// <summary>
        /// Check if progress window is currently shown
        /// </summary>
        public bool IsProgressShown => _progressWindow != null;

        /// <summary>
        /// Execute action safely with error handling
        /// </summary>
        public void ExecuteSafely(Action action)
        {
            try
            {
                action();
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error in progress window operation: {ex.Message}");
                CloseProgress();
            }
        }

        /// <summary>
        /// Show progress for async operation with automatic cleanup
        /// </summary>
        public void ShowProgressForOperation(string title, string initialMessage, Func<Action<string>, System.Threading.Tasks.Task> operation)
        {
            _ = System.Threading.Tasks.Task.Run(async () =>
            {
                try
                {
                    // Show progress window
                    Application.Current.Dispatcher.Invoke(() => ShowProgress(title, initialMessage));

                    // Execute operation with progress callback
                    await operation((message) =>
                    {
                        Application.Current.Dispatcher.Invoke(() => UpdateProgress(message));
                    });
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Error in progress operation: {ex.Message}");
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        UpdateProgress($"Error: {ex.Message}");
                        System.Threading.Tasks.Task.Delay(2000).ContinueWith(_ =>
                        {
                            Application.Current.Dispatcher.Invoke(CloseProgress);
                        });
                    });
                }
            });
        }
    }
}
