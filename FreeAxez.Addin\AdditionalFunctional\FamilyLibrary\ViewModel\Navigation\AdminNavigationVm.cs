﻿using System.Windows.Input;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Pages;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Navigation;

public class AdminNavigationVm : BaseViewModel
{
    private readonly CategoriesPageVm _adminCategoriesBasePageVm = new();
    private readonly FamiliesPageVm _adminFamiliesBasePageVm = new();
    private readonly TemplatesPageVm _adminTemplatesBasePageVm = new();
    private readonly HistoryPageVm _historyPageVm = new();
    private readonly DetailsPageVm _detailsPageVm = new();
    private object _currentView;

    public AdminNavigationVm()
    {
        FamiliesPageCommand = new RelayCommand(_ => SelectPage(_adminFamiliesBasePageVm));
        TemplatesPageCommand = new RelayCommand(_ => SelectPage(_adminTemplatesBasePageVm));
        CategoriesPageCommand = new RelayCommand(_ => SelectPage(_adminCategoriesBasePageVm));
        HistoryPageCommand = new RelayCommand(_ => SelectPage(_historyPageVm));
        DetailsPageCommand = new RelayCommand(_ => SelectPage(_detailsPageVm));

        SelectPage(_adminFamiliesBasePageVm);
    }

    public ICommand FamiliesPageCommand { get; }
    public ICommand TemplatesPageCommand { get; }
    public ICommand CategoriesPageCommand { get; }
    public ICommand HistoryPageCommand { get; }
    public ICommand DetailsPageCommand { get; }


    public object CurrentView
    {
        get => _currentView;
        set
        {
            _currentView = value;
            OnPropertyChanged();
        }
    }

    private void SelectPage(object viewModel)
    {
        CurrentView = viewModel;
    }
}