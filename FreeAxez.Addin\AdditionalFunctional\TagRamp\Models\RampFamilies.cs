﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.TagRamp.Models
{
    public class RampFamilies
    {
        private const string RampAnnotationFamilyName = "FA - Ramp Annotation"; 
        private const string AnnotationSymbolName = "GRIDD RAMP TYPICAL";
        public Family _annotationFamily;

        private const string MultiСategoryTagFamilyName = "FA - Multicategory Tag";
        private const string MyltiCategorySymbolName = "Standard - Opaque";
        public Family _multiCategoryTagFamily;

        private const string RampSlopeAnnotationFamilyName = "FA - Ramp Slope Annotation";
        public Family _rampSlopeAnnotationFamily;

        public RampFamilies()
        {
            GetRampAnnotationFamily();
            GetMultiCategoryTagFamily();
            GetRampSlopeTagFamily();
        }

        public void GetRampAnnotationFamily()
        {
            _annotationFamily = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Family))
                .Cast<Family>()
                .Where(f => f.Name == RampAnnotationFamilyName)
                .FirstOrDefault();

            if (_annotationFamily != null)
            {
                var symbols = _annotationFamily.GetFamilySymbolIds()
                   .Select(id => RevitManager.Document.GetElement(id)).Cast<FamilySymbol>();
                
                AnnotationSymbol = symbols.FirstOrDefault(s => s.Name == AnnotationSymbolName);
            }
        }

        public void GetMultiCategoryTagFamily()
        {
            _multiCategoryTagFamily = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Family))
                .Cast<Family>()
                .Where(f => f.Name == MultiСategoryTagFamilyName)
                .FirstOrDefault();

            if (_multiCategoryTagFamily != null)
            {
                var symbols = _multiCategoryTagFamily.GetFamilySymbolIds()
                   .Select(id => RevitManager.Document.GetElement(id))
                   .Cast<FamilySymbol>();

                MyltiCategoryTagSymbol = symbols.FirstOrDefault(s => s.Name == MyltiCategorySymbolName);
            }
        }

        public FamilySymbol GetRampSlopeFamilySymbol(string rampSlope)
        {
            FamilySymbol _rampSlopeSymbol = new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_GenericAnnotation)
                .WhereElementIsElementType()
                .Cast<AnnotationSymbolType>()
                .Where(a => a.FamilyName == RampSlopeAnnotationFamilyName)
                .Where(a => a.Name.Split(' ').Any(n => n == rampSlope))
                .FirstOrDefault();

            return _rampSlopeSymbol;
        }


        public void GetRampSlopeTagFamily()
        {
            _rampSlopeAnnotationFamily = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(Family))
                .Cast<Family>()
                .Where(f => f.Name == RampSlopeAnnotationFamilyName)
                .FirstOrDefault();

            if (_rampSlopeAnnotationFamily != null)
            {
                RampSlopeTagFamily = _rampSlopeAnnotationFamily;
            }
        }

        public Family RampSlopeTagFamily { get; private set; }
        public FamilySymbol AnnotationSymbol { get; private set; }
        public FamilySymbol MyltiCategoryTagSymbol { get; private set; }

        public bool IsFamilyNotExist(out string message)
        {
            message = "";

            if (_annotationFamily == null)
            {
                message = $"Family \"{RampAnnotationFamilyName}\" is not loaded into the project";
            }

            else if (_multiCategoryTagFamily == null)
            {
                message = $"Family \"{MultiСategoryTagFamilyName}\" is not loaded into the project";
            }
            else if (MyltiCategoryTagSymbol == null)
            {
                message = $"The multi category tag family \"{MultiСategoryTagFamilyName}\" doesn't contains types\n\"{MyltiCategorySymbolName}\".";
            }

            else if (_rampSlopeAnnotationFamily == null)
            {
                message = $"Family \"{RampSlopeAnnotationFamilyName}\" is not loaded into the project";
            }

            return _annotationFamily == null ||
                _multiCategoryTagFamily == null || 
                MyltiCategorySymbolName == null ||
                _rampSlopeAnnotationFamily == null;
        }
    }
}
