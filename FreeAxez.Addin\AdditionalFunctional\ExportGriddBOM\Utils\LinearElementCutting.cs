﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.ExportGriddBOM.Utils
{
    public static class LinearElementCutting
    {
        public static int CalculateCountOfStockElements(List<double> lengths, double stockLength)
        {
            if (InvalidLengths(lengths, stockLength))
            {
                throw new ArgumentException("The length of the element is greater than the stock length.");
            }

            lengths.Sort();
            lengths.Reverse();

            var scraps = new List<double>();
            var stockNeeded = 0;
            foreach (var length in lengths)
            {
                var scrap = scraps.Where(s => s >= length).OrderBy(s => s).FirstOrDefault();
                if (scrap == 0)
                {
                    stockNeeded++;
                    scraps.Add(stockLength - length);
                }
                else
                {
                    scraps.Remove(scrap);
                    scraps.Add(scrap - length);
                }
            }
            return stockNeeded;
        }

        private static bool InvalidLengths(List<double> lengths, double stockLength)
        {
            foreach (var length in lengths)
            {
                if (length > stockLength)
                {
                    return true;
                }
            }
            return false;
        }
    }
}
