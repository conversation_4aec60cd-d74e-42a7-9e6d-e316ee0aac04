﻿using System.Windows;

namespace FreeAxez.Addin.AdditionalFunctional.Frame.Views
{
    public partial class FrameView : Window
    {
        public FrameView()
        {
            InitializeComponent();
            gridd40.IsChecked = Properties.Settings.Default.FrameSelectedGridd40;
            gridd70.IsChecked = !gridd40.IsChecked;

            cornerLong.IsChecked = Properties.Settings.Default.FrameSelectedCornerLong;
            cornerShort.IsChecked = !cornerLong.IsChecked;
        }

        private void Select_Click(object sender, RoutedEventArgs e)
        {
            Properties.Settings.Default.FrameSelectedGridd40 = (bool)gridd40.IsChecked;
            Properties.Settings.Default.FrameSelectedCornerLong = (bool)cornerLong.IsChecked;
            Properties.Settings.Default.Save();
            DialogResult = true;
        }
    }
}
