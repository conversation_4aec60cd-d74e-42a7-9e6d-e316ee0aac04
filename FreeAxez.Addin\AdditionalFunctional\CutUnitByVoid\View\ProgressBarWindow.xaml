﻿<Window x:Class="FreeAxez.Addin.AdditionalFunctional.CutUnitByVoid.View.ProgressBarWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Cutting Progress"
        Topmost="True"
        ResizeMode="NoResize"
        SizeToContent="WidthAndHeight"
        WindowStartupLocation="CenterScreen">
    <StackPanel Margin="10">
        <TextBlock x:Name="progressText" Margin="0,0,0,10" FontSize="12" Text="Search for elements to crop..." />
        <ProgressBar x:Name="progressBar" Height="30" Width="300" IsIndeterminate="True"/>
    </StackPanel>
</Window>
