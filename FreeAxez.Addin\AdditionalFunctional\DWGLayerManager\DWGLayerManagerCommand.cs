using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager;

/// <summary>
///     Revit command for DWG Layer Management
/// </summary>
[Transaction(TransactionMode.Manual)]
public class DwgLayerManagerCommand : BaseExternalCommand
{
    public override Result Execute()
    {
        return ExecuteLayerManager();
    }

    private Result ExecuteLayerManager()
    {
        var window = new DwgLayerManagerWindow();
        RevitManager.SetRevitAsWindowOwner(window);

        var dialogResult = window.ShowDialog();
        return dialogResult == true ? Result.Succeeded : Result.Cancelled;
    }
}