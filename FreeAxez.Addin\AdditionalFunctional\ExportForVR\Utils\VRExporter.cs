﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.ExportForClient.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using System.IO;
using System.Text;

namespace FreeAxez.Addin.AdditionalFunctional.ExportForVR.Utils
{
    internal class VRExporter
    {
        private readonly string _folderPath;
        private readonly string _lightweightFamiliesPath;
        private readonly bool _copyFurniture;
        private readonly List<Level> _selectedLevels;

        public VRExporter(string folderPath, string lightweightFamiliesPath, bool copyFurniture, List<Level> selectedLevels)
        {
            _folderPath = folderPath;
            _lightweightFamiliesPath = lightweightFamiliesPath;
            _copyFurniture = copyFurniture;
            _selectedLevels = selectedLevels;
        }

        public void Export()
        {
            FamilyReplacementManager familyReplacementManager = null;
            var validationReport = new StringBuilder();

            // Duplicate FB families in folder to correspond FB naming like "-FB3"
            if (Directory.Exists(_lightweightFamiliesPath))
            {
                var floorBoxFamilyDuplicator = new FloorBoxFamilyDuplicator(RevitManager.Document, _lightweightFamiliesPath);
                floorBoxFamilyDuplicator.DuplicateFamilies();
            }

            // Validate lightweight families
            if (string.IsNullOrWhiteSpace(_lightweightFamiliesPath) == false)
            {
                familyReplacementManager = new FamilyReplacementManager(_lightweightFamiliesPath);
                if (familyReplacementManager.ValidateFamilies(out var familyValidationReport) == false)
                {
                    validationReport.Append(familyValidationReport);
                }
            }

            // Validate missing railing types
            var missingRailingTypes = FlexPipeRailingsValidator.GetMissingRailingTypes();
            if (missingRailingTypes.Any())
            {
                if (validationReport.Length > 0)
                {
                    validationReport.AppendLine();
                }
                validationReport.AppendLine("MISSING RAILING TYPES:");
                foreach (var missingType in missingRailingTypes.OrderBy(t => t))
                {
                    validationReport.AppendLine($"{missingType}");
                }
            }

            // Show combined validation report if needed
            if (validationReport.Length > 0)
            {
                validationReport.AppendLine();
                validationReport.AppendLine("Do you want to continue with the export?");
                if (MessageWindow.ShowDialog("Validation Warning", validationReport.ToString(), MessageType.Warning) != true)
                {
                    return;
                }
            }

            var successfulExport = 0;

            foreach (var level in _selectedLevels)
            {
                Document exportDoc = null;

                var filePath = Path.Combine(_folderPath, $"VR_{RevitManager.Document.Title}_{level.Name}.rvt");
                File.Copy(RevitManager.Document.PathName, filePath, true);

                try
                {
                    exportDoc = RevitFile.OpenDocumentInBackground(RevitManager.Application, filePath);

                    DeleteOtherLevels(exportDoc, level);
                    DeleteElementsFromBlackList(exportDoc);
                    DeleteNonFreeAxezFamilies(exportDoc);
                    FlexPipeToRailings.Convert(exportDoc);

                    familyReplacementManager?.PerformReplacement(exportDoc);

                    if (_copyFurniture) CopyElementsFromLinkedModels(exportDoc);
                    
                    var purgeManager = new PurgeManager(exportDoc);
                    purgeManager.DeleteAllViews();
                    purgeManager.PurgeTypes();

                    successfulExport++;
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Failed to process the document {filePath} due to {ex.Message}");
                    continue;
                }
                finally
                {
                    if (exportDoc != null)
                    {
                        RevitFile.SaveAndClose(exportDoc);
                    }
                    RevitFile.DeleteBackupFiles(filePath);
                }
            }

            var finalReport = new StringBuilder();
            finalReport.AppendLine($"Export completed. Processed {successfulExport} levels.");

            var replacementReport = familyReplacementManager?.GenerateReplacementReport();
            if (!string.IsNullOrEmpty(replacementReport))
            {
                finalReport.AppendLine();
                finalReport.Append(replacementReport);
            }

            var distinctMissingTypes = missingRailingTypes.Distinct().OrderBy(t => t).ToList();
            if (distinctMissingTypes.Any())
            {
                finalReport.AppendLine();
                finalReport.AppendLine("MISSING RAILING TYPES:");
                foreach (var missingType in distinctMissingTypes)
                {
                    finalReport.AppendLine($"{missingType}");
                }
            }

            MessageWindow.ShowDialog("Export For VR", finalReport.ToString(), MessageType.Success);
        }

        private void DeleteOtherLevels(Document exportDoc, Level level)
        {
            var otherLevelIds = new FilteredElementCollector(exportDoc)
                .OfClass(typeof(Level))
                .ToElementIds()
                .Where(l => l.Equals(level.Id) == false)
                .ToList();

            LogHelper.Information($"Delete {otherLevelIds.Count} levels");

            if (otherLevelIds.Count == 0) return;

            using (var t = new Transaction(exportDoc, "Delete Other Levels"))
            {
                t.Start();

                var failOpt = t.GetFailureHandlingOptions();
                failOpt.SetFailuresPreprocessor(new WarningSwallower());
                t.SetFailureHandlingOptions(failOpt);

                exportDoc.Delete(otherLevelIds);

                t.Commit();
            }
        }

        private void DeleteElementsFromBlackList(Document exportDoc)
        {
            var familyInstancesToDelete = new FilteredElementCollector(exportDoc)
                .OfClass(typeof(FamilyInstance))
                .Cast<FamilyInstance>()
                .Where(fi => fi.SuperComponent == null)
                .Where(fi => IsPlate(fi) || IsStagingPallet(fi) || IsBorder(fi) || IsPanelboard(fi))
                .ToList();

            var floorsToDelete = new FilteredElementCollector(exportDoc)
                .OfCategory(BuiltInCategory.OST_Floors)
                .WhereElementIsNotElementType()
                .ToList();

            var elementIdsToDelete = familyInstancesToDelete.Select(fi => fi.Id)
                .Concat(floorsToDelete.Select(f => f.Id))
                .ToList();

            LogHelper.Information($"Delete {elementIdsToDelete.Count} family instances from blacklist");

            if (elementIdsToDelete.Count == 0) return;

            using (var t = new Transaction(exportDoc, "Delete Elements"))
            {
                t.Start();

                var failOpt = t.GetFailureHandlingOptions();
                failOpt.SetFailuresPreprocessor(new WarningSwallower());
                t.SetFailureHandlingOptions(failOpt);

                exportDoc.Delete(elementIdsToDelete);

                t.Commit();
            }
        }

        private bool IsPlate(FamilyInstance familyInstance)
        {
            var familyName = familyInstance.Symbol.FamilyName;
            return familyName.Contains("Plate") && familyName.Contains("Corner")
                || familyName.Contains("Plate") && familyName.Contains("Channel");
        }

        private bool IsStagingPallet(FamilyInstance familyInstance)
        {
            return familyInstance.Symbol.FamilyName.Contains("Staging");
        }

        private bool IsBorder(FamilyInstance familyInstance)
        {
            return familyInstance.Symbol.FamilyName.Contains("Border");
        }

        private bool IsPanelboard(FamilyInstance familyInstance)
        {
            return familyInstance.Symbol.FamilyName.Contains("Panelboard");
        }

        private void DeleteNonFreeAxezFamilies(Document exportDoc)
        {
            var families = new FilteredElementCollector(exportDoc)
                .OfClass(typeof(Family))
                .Cast<Family>()
                .Where(f => f.FamilyCategory.Id.GetIntegerValue() != (int)BuiltInCategory.OST_ProfileFamilies)
                .ToList();

            var nonFreeAxezFamilyIds = new List<ElementId>();
            foreach (var family in families)
            {
                var symbols = family.GetFamilySymbolIds().Select(id => exportDoc.GetElement(id) as FamilySymbol).ToList();
                if (symbols.Any(s => IsFreeAxezFamily(s)))
                {
                    continue; // Skip families that have at least one FreeAxez family symbol
                }

                nonFreeAxezFamilyIds.Add(family.Id);
            }

            LogHelper.Information($"Delete {nonFreeAxezFamilyIds.Count} non FreeAxez families");

            if (nonFreeAxezFamilyIds.Count == 0) return;

            using (var t = new Transaction(exportDoc, "Delete Non FreeAxez Families"))
            {
                t.Start();

                var failOpt = t.GetFailureHandlingOptions();
                failOpt.SetFailuresPreprocessor(new WarningSwallower());
                t.SetFailureHandlingOptions(failOpt);

                exportDoc.Delete(nonFreeAxezFamilyIds);

                t.Commit();
            }
        }

        private bool IsFreeAxezFamily(FamilySymbol familySymbol)
        {
            var manufacturer = familySymbol.get_Parameter(BuiltInParameter.ALL_MODEL_MANUFACTURER)?.AsString();
            if (string.IsNullOrWhiteSpace(manufacturer) || manufacturer.Contains("FreeAxez") == false) return false;
            return true;
        }

        private void CopyElementsFromLinkedModels(Document exportDoc)
        {
            var linkInstances = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(RevitLinkInstance))
                .Cast<RevitLinkInstance>()
                .ToList();

            if (linkInstances.Count == 0) return;

            using (var t = new Transaction(exportDoc, "Copy Elements From Links"))
            {
                t.Start();

                var copyPasteOptions = new CopyPasteOptions();
                copyPasteOptions.SetDuplicateTypeNamesHandler(new UseDestinationTypesHandler());

                foreach (var linkInstance in linkInstances)
                {
                    var linkType = RevitManager.Document.GetElement(linkInstance.GetTypeId()) as RevitLinkType;

                    if (linkType.GetLinkedFileStatus() != LinkedFileStatus.Loaded) continue;

                    var linkDocument = linkInstance.GetLinkDocument();

                    var elementsToCopy = new FilteredElementCollector(linkDocument)
                        .OfCategory(BuiltInCategory.OST_Furniture)
                        .WhereElementIsNotElementType()
                        .ToElementIds()
                        .ToList();

                    if (elementsToCopy.Count > 0)
                    {
                        ElementTransformUtils.CopyElements(
                            linkDocument, elementsToCopy, exportDoc, linkInstance.GetTransform(), copyPasteOptions);
                    }
                }

                t.Commit();
            }
        }
    }
}
