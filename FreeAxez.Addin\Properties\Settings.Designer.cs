﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FreeAxez.Addin.Properties {
    
    
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "17.14.0.0")]
    internal sealed partial class Settings : global::System.Configuration.ApplicationSettingsBase {
        
        private static Settings defaultInstance = ((Settings)(global::System.Configuration.ApplicationSettingsBase.Synchronized(new Settings())));
        
        public static Settings Default {
            get {
                return defaultInstance;
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("FreeAxez.BrowserApp.exe")]
        public string browserAppName {
            get {
                return ((string)(this["browserAppName"]));
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string selectedRailingTypeName {
            get {
                return ((string)(this["selectedRailingTypeName"]));
            }
            set {
                this["selectedRailingTypeName"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool deleteSelectedLineOption {
            get {
                return ((bool)(this["deleteSelectedLineOption"]));
            }
            set {
                this["deleteSelectedLineOption"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string selectedLineStyleName {
            get {
                return ((string)(this["selectedLineStyleName"]));
            }
            set {
                this["selectedLineStyleName"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("0")]
        public double baseOffsetForRailing {
            get {
                return ((double)(this["baseOffsetForRailing"]));
            }
            set {
                this["baseOffsetForRailing"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool createRailingForEachLine {
            get {
                return ((bool)(this["createRailingForEachLine"]));
            }
            set {
                this["createRailingForEachLine"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string userParameterSets {
            get {
                return ((string)(this["userParameterSets"]));
            }
            set {
                this["userParameterSets"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("1")]
        public double TagAllRailingsLengthToCenterOfTag {
            get {
                return ((double)(this["TagAllRailingsLengthToCenterOfTag"]));
            }
            set {
                this["TagAllRailingsLengthToCenterOfTag"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool TagAllRailingsStartTag {
            get {
                return ((bool)(this["TagAllRailingsStartTag"]));
            }
            set {
                this["TagAllRailingsStartTag"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool TagAllRailingsEndTag {
            get {
                return ((bool)(this["TagAllRailingsEndTag"]));
            }
            set {
                this["TagAllRailingsEndTag"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string TagAllRailingsSelectedRailingTagTypeName {
            get {
                return ((string)(this["TagAllRailingsSelectedRailingTagTypeName"]));
            }
            set {
                this["TagAllRailingsSelectedRailingTagTypeName"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string ExportCADPrefix {
            get {
                return ((string)(this["ExportCADPrefix"]));
            }
            set {
                this["ExportCADPrefix"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string ExportCADPath {
            get {
                return ((string)(this["ExportCADPath"]));
            }
            set {
                this["ExportCADPath"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string FlexPipeToLineSelectedLineStyleName {
            get {
                return ((string)(this["FlexPipeToLineSelectedLineStyleName"]));
            }
            set {
                this["FlexPipeToLineSelectedLineStyleName"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool TagAllRailingsTagElementsWithEmptyParameter {
            get {
                return ((bool)(this["TagAllRailingsTagElementsWithEmptyParameter"]));
            }
            set {
                this["TagAllRailingsTagElementsWithEmptyParameter"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string FloorBoxNumberingPrefix {
            get {
                return ((string)(this["FloorBoxNumberingPrefix"]));
            }
            set {
                this["FloorBoxNumberingPrefix"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("1")]
        public int FloorBoxNumberingStartNumber {
            get {
                return ((int)(this["FloorBoxNumberingStartNumber"]));
            }
            set {
                this["FloorBoxNumberingStartNumber"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool FloorBoxNumberingHorizontalDirection {
            get {
                return ((bool)(this["FloorBoxNumberingHorizontalDirection"]));
            }
            set {
                this["FloorBoxNumberingHorizontalDirection"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool TagAllRailingsTagVisibleInView {
            get {
                return ((bool)(this["TagAllRailingsTagVisibleInView"]));
            }
            set {
                this["TagAllRailingsTagVisibleInView"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string ElementNumberingParameterName {
            get {
                return ((string)(this["ElementNumberingParameterName"]));
            }
            set {
                this["ElementNumberingParameterName"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("1")]
        public int ElementNumberingStartNumber {
            get {
                return ((int)(this["ElementNumberingStartNumber"]));
            }
            set {
                this["ElementNumberingStartNumber"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string ElementNumberingPrefix {
            get {
                return ((string)(this["ElementNumberingPrefix"]));
            }
            set {
                this["ElementNumberingPrefix"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool ElementNumberingHorizontalDirection {
            get {
                return ((bool)(this["ElementNumberingHorizontalDirection"]));
            }
            set {
                this["ElementNumberingHorizontalDirection"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool FrameSelectedGridd40 {
            get {
                return ((bool)(this["FrameSelectedGridd40"]));
            }
            set {
                this["FrameSelectedGridd40"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string exportGriddBOMPath {
            get {
                return ((string)(this["exportGriddBOMPath"]));
            }
            set {
                this["exportGriddBOMPath"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool exportGriddBOMOpenFile {
            get {
                return ((bool)(this["exportGriddBOMOpenFile"]));
            }
            set {
                this["exportGriddBOMOpenFile"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string panelSchedulePlacementSelectedLevels {
            get {
                return ((string)(this["panelSchedulePlacementSelectedLevels"]));
            }
            set {
                this["panelSchedulePlacementSelectedLevels"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool panelSchedulePlacementDeleteUnused {
            get {
                return ((bool)(this["panelSchedulePlacementDeleteUnused"]));
            }
            set {
                this["panelSchedulePlacementDeleteUnused"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool FrameSelectedCornerLong {
            get {
                return ((bool)(this["FrameSelectedCornerLong"]));
            }
            set {
                this["FrameSelectedCornerLong"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string exportPowerBomPath {
            get {
                return ((string)(this["exportPowerBomPath"]));
            }
            set {
                this["exportPowerBomPath"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool exportPowerBomOpenFile {
            get {
                return ((bool)(this["exportPowerBomOpenFile"]));
            }
            set {
                this["exportPowerBomOpenFile"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string panelSchedulePlacementSheetSize {
            get {
                return ((string)(this["panelSchedulePlacementSheetSize"]));
            }
            set {
                this["panelSchedulePlacementSheetSize"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("0")]
        public string panelSchedulePlacementRevision {
            get {
                return ((string)(this["panelSchedulePlacementRevision"]));
            }
            set {
                this["panelSchedulePlacementRevision"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool panelScheduleCreationConnectedComponents {
            get {
                return ((bool)(this["panelScheduleCreationConnectedComponents"]));
            }
            set {
                this["panelScheduleCreationConnectedComponents"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("1")]
        public double TagAllCurbsLengthToCenterOfTag {
            get {
                return ((double)(this["TagAllCurbsLengthToCenterOfTag"]));
            }
            set {
                this["TagAllCurbsLengthToCenterOfTag"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("1")]
        public double TagCurbsOffset {
            get {
                return ((double)(this["TagCurbsOffset"]));
            }
            set {
                this["TagCurbsOffset"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool TagCurbsVisibleInView {
            get {
                return ((bool)(this["TagCurbsVisibleInView"]));
            }
            set {
                this["TagCurbsVisibleInView"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("1")]
        public double TagAllFramesLengthToCenterOfTag {
            get {
                return ((double)(this["TagAllFramesLengthToCenterOfTag"]));
            }
            set {
                this["TagAllFramesLengthToCenterOfTag"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool TagFramesVisibleInView {
            get {
                return ((bool)(this["TagFramesVisibleInView"]));
            }
            set {
                this["TagFramesVisibleInView"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool TransferViewTemplatesDeleteTemplates {
            get {
                return ((bool)(this["TransferViewTemplatesDeleteTemplates"]));
            }
            set {
                this["TransferViewTemplatesDeleteTemplates"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string TransferViewTemplatesSourceFilePath {
            get {
                return ((string)(this["TransferViewTemplatesSourceFilePath"]));
            }
            set {
                this["TransferViewTemplatesSourceFilePath"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string TargetRevitFilePath {
            get {
                return ((string)(this["TargetRevitFilePath"]));
            }
            set {
                this["TargetRevitFilePath"] = value;
            }
        }

        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool EхportCADColor {
            get {
                return ((bool)(this["EхportCADColor"]));
            }
            set {
                this["EхportCADColor"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool RampGriddType {
            get {
                return ((bool)(this["RampGriddType"]));
            }
            set {
                this["RampGriddType"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("0")]
        public int RampRampSlope {
            get {
                return ((int)(this["RampRampSlope"]));
            }
            set {
                this["RampRampSlope"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool RampIsLeftSideSlopeChecked {
            get {
                return ((bool)(this["RampIsLeftSideSlopeChecked"]));
            }
            set {
                this["RampIsLeftSideSlopeChecked"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool RampIsRightSideSlopeChecked {
            get {
                return ((bool)(this["RampIsRightSideSlopeChecked"]));
            }
            set {
                this["RampIsRightSideSlopeChecked"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("1")]
        public double TagRampLengthToCenterOfTag {
            get {
                return ((double)(this["TagRampLengthToCenterOfTag"]));
            }
            set {
                this["TagRampLengthToCenterOfTag"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool TagRampIsVisibleInView {
            get {
                return ((bool)(this["TagRampIsVisibleInView"]));
            }
            set {
                this["TagRampIsVisibleInView"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool TagRampIsSelectRampComponents {
            get {
                return ((bool)(this["TagRampIsSelectRampComponents"]));
            }
            set {
                this["TagRampIsSelectRampComponents"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool TagTypeRump {
            get {
                return ((bool)(this["TagTypeRump"]));
            }
            set {
                this["TagTypeRump"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string ExportCutsheetFolderPath {
            get {
                return ((string)(this["ExportCutsheetFolderPath"]));
            }
            set {
                this["ExportCutsheetFolderPath"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool ExportCutsheetIsOpenDirectory {
            get {
                return ((bool)(this["ExportCutsheetIsOpenDirectory"]));
            }
            set {
                this["ExportCutsheetIsOpenDirectory"] = value;
            }
        }

        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string UserEmail {
            get {
                return ((string)(this["UserEmail"]));
            } set
            {
                this["UserEmail"] = value;
            }
        }

        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string AuthToken {
            get { 
                return ((string)(this["AuthToken"])); 
            }
            set { 
                this["AuthToken"] = value; 
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string ExportForVRPath {
            get {
                return ((string)(this["ExportForVRPath"]));
            }
            set {
                this["ExportForVRPath"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool ExportForVRCopyFurniture {
            get {
                return ((bool)(this["ExportForVRCopyFurniture"]));
            }
            set {
                this["ExportForVRCopyFurniture"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string ExportPowerBomAccessories {
            get {
                return ((string)(this["ExportPowerBomAccessories"]));
            }
            set {
                this["ExportPowerBomAccessories"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string ExportGriddBomAccessories {
            get {
                return ((string)(this["ExportGriddBomAccessories"]));
            }
            set {
                this["ExportGriddBomAccessories"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool ExportCADIncludeFloatingInformation {
            get {
                return ((bool)(this["ExportCADIncludeFloatingInformation"]));
            }
            set {
                this["ExportCADIncludeFloatingInformation"] = value;
            }
        }

        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string ExportGriddBOMFolderPath {
            get {
                return ((string)(this["ExportGriddBOMFolderPath"]));
            }
            set {
                this["ExportGriddBOMFolderPath"] = value;
            }
        }

        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string ExportPowerBOMFolderPath {
            get {
                return ((string)(this["ExportPowerBOMFolderPath"]));
            }
            set {
                this["ExportPowerBOMFolderPath"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("40")]
        public int GriddBuilderBaseUnitHeight {
            get {
                return ((int)(this["GriddBuilderBaseUnitHeight"]));
            }
            set {
                this["GriddBuilderBaseUnitHeight"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("0")]
        public int SelectedAutoCADVersion {
            get {
                return ((int)(this["SelectedAutoCADVersion"]));
            }
            set {
                this["SelectedAutoCADVersion"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string ExportForVRLightweightFamiliesPath {
            get {
                return ((string)(this["ExportForVRLightweightFamiliesPath"]));
            }
            set {
                this["ExportForVRLightweightFamiliesPath"] = value;
            }
        }
    }
}
