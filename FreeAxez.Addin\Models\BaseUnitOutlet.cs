﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Models.Base;

namespace FreeAxez.Addin.Models
{
    public class BaseUnitOutlet : Product
    {
        private static FamilyCollector _familyCollector;
        private static FamilyDefinition _familyDefinition = new FamilyDefinition()
        {
            BuiltInCategory = BuiltInCategory.OST_SpecialityEquipment,
            FamilyNamesContains = new List<string>()
            {
                "Outlet_Base_Unit"
            },
            FamilyNamesEndWith = new List<string>
            {
            },
            FamilyNamesNotContains = new List<string>
            {
            }
        };

        private static FamilyCollector FamilyCollector
        {
            get
            {
                if (_familyCollector == null) _familyCollector = new FamilyCollector(_familyDefinition);
                return _familyCollector;
            }
        }

        public BaseUnitOutlet(Element element) : base(element)
        {
        }

        public static List<BaseUnitOutlet> Collect()
        {
            return FamilyCollector.Instances.Select(f => new BaseUnitOutlet(f)).ToList();
        }

        public static Family GetFamily(out string missedFamilyMessage)
        {
            var family = FamilyCollector.Families.FirstOrDefault();

            missedFamilyMessage = string.Empty;
            if (family == null)
            {
                missedFamilyMessage =
                    "No family with Outlet_Base_Unit in its name.";
            }

            return family;
        }
    }
}
