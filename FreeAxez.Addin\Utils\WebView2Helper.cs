﻿using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using Microsoft.Win32;
using System.Diagnostics;

namespace FreeAxez.Addin.Utils
{
    public static class WebView2Helper
    {
        public static bool CheckWebView2Installed()
        {
            if (!IsWebView2Installed())
            {
                if (MessageWindow.ShowDialog("WebView2 Runtime is required. Do you want to download and install it now?\n\n" +
                                             "If you agree, a file will be downloaded via your browser. You will need to install it manually.", 
                                             MessageType.Warning) == true)
                {
                    try
                    {
                        var startInfo = new ProcessStartInfo()
                        {
                            // Deploying the Evergreen WebView2 Runtime https://learn.microsoft.com/en-us/microsoft-edge/webview2/concepts/distribution?tabs=dotnetcsharp#deploying-the-evergreen-webview2-runtime
                            FileName = "https://go.microsoft.com/fwlink/p/?LinkId=2124703",
                            UseShellExecute = true
                        };

                        Process.Start(startInfo);
                    }
                    catch (Exception ex)
                    {
                        MessageWindow.ShowDialog("Error Opening File", "Failed to open the exported file: " + ex.Message, MessageType.Error);
                    }
                }

                return false;
            }

            return true;
        }

        private static bool IsWebView2Installed()
        {
            string[] registryPaths = new[]
            {
                @"HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}",
                @"HKEY_CURRENT_USER\Software\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}"
            };

            foreach (var path in registryPaths)
            {
                var version = Registry.GetValue(path, "pv", null) as string;
                if (!string.IsNullOrWhiteSpace(version) && version != "0.0.0.0")
                {
                    return true;
                }
            }

            return false;
        }
    }
}
