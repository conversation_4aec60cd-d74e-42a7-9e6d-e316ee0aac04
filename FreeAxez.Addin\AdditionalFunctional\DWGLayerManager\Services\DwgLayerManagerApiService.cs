using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Core;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Models.Api;
using FreeAxez.Addin.AdditionalFunctional.UserAccountControl.Utils;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using Newtonsoft.Json;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;

public class DwgLayerManagerApiService
{
    //private const string BaseApiUrl = "https://api-freeaxez-uat.bimsmith.com/api";

    private readonly string _dwgLayerManagerApiUrl = $"{BaseApiUrl}/dwglayermanager";

    //private static readonly string BaseApiUrl = "https://localhost:44376/api";
    private const string BaseApiUrl = "https://api-freeaxez.bimsmith.com/api";
    private readonly HttpClient _httpClient;

    public DwgLayerManagerApiService()
    {
        _httpClient = new HttpClient();

        var (_, token) = UserAuthManager.GetCredentials();
        if (!string.IsNullOrEmpty(token))
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
    }

    private async Task HandleHttpError(HttpResponseMessage response, string operation)
    {
        var errorDetails = "";
        try
        {
            errorDetails = await response.Content.ReadAsStringAsync();
        }
        catch
        {
        }

        if (response.StatusCode == HttpStatusCode.Unauthorized)
            MessageWindow.ShowDialog("Authorization Error",
                "You are not authorized to perform this operation. Please check your login credentials.",
                MessageType.Error);
        else if (response.StatusCode == HttpStatusCode.Forbidden)
            MessageWindow.ShowDialog("Access Denied",
                "You don't have permission to perform this operation.",
                MessageType.Error);
        else if (response.StatusCode == HttpStatusCode.BadRequest)
            MessageWindow.ShowDialog("Bad Request",
                $"Failed to {operation}. Error: {errorDetails}",
                MessageType.Error);
        else
            MessageWindow.ShowDialog("Server Error",
                $"Failed to {operation}. Server returned: {response.StatusCode}\nDetails: {errorDetails}",
                MessageType.Error);
    }

    #region Layer Methods

    public async Task<List<LayerModel>> GetAllLayersAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_dwgLayerManagerApiUrl}/layers");
            if (response.IsSuccessStatusCode)
            {
                var jsonString = await response.Content.ReadAsStringAsync();
                var layerDtos = JsonConvert.DeserializeObject<List<LayerDto>>(jsonString) ?? new List<LayerDto>();
                return layerDtos.Select(ConvertFromDto).ToList();
            }

            await HandleHttpError(response, "load layers");
            return new List<LayerModel>();
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog("Network Error",
                $"Failed to connect to server while loading layers: {ex.Message}",
                MessageType.Error);
            return new List<LayerModel>();
        }
    }

    public async Task<LayerModel> GetLayerAsync(Guid id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_dwgLayerManagerApiUrl}/layers/{id}");
            if (response.IsSuccessStatusCode)
            {
                var jsonString = await response.Content.ReadAsStringAsync();
                var layerDto = JsonConvert.DeserializeObject<LayerDto>(jsonString);
                return layerDto != null ? ConvertFromDto(layerDto) : null;
            }

            return null;
        }
        catch
        {
            return null;
        }
    }

    public async Task<LayerModel> CreateNewLayerAsync()
    {
        try
        {
            var response = await _httpClient.PostAsync($"{_dwgLayerManagerApiUrl}/layers/new", null);

            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync();
                var layerDto = JsonConvert.DeserializeObject<LayerDto>(responseJson);
                return layerDto != null ? ConvertFromDto(layerDto) : null;
            }

            await HandleHttpError(response, "create new layer");
            return null;
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog("Network Error",
                $"Failed to connect to server while creating layer: {ex.Message}",
                MessageType.Error);
            return null;
        }
    }

    public async Task<LayerModel> UpdateLayerAsync(LayerModel layerModel)
    {
        try
        {
            var layerDto = ConvertToDto(layerModel);
            var jsonString = JsonConvert.SerializeObject(layerDto);
            var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync($"{_dwgLayerManagerApiUrl}/layers", content);

            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync();
                var updatedDto = JsonConvert.DeserializeObject<LayerDto>(responseJson);
                return updatedDto != null ? ConvertFromDto(updatedDto) : null;
            }

            await HandleHttpError(response, "update layer");
            return null;
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog("Network Error",
                $"Failed to connect to server while updating layer: {ex.Message}",
                MessageType.Error);
            return null;
        }
    }

    public async Task<bool> DeleteLayerAsync(Guid id)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"{_dwgLayerManagerApiUrl}/layers/{id}");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region Linetype Methods

    public async Task<List<LinetypeModel>> GetAllLinetypesAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_dwgLayerManagerApiUrl}/linetypes");
            if (response.IsSuccessStatusCode)
            {
                var jsonString = await response.Content.ReadAsStringAsync();
                var linetypeDtos = JsonConvert.DeserializeObject<List<LinetypeDto>>(jsonString) ?? new List<LinetypeDto>();
                return linetypeDtos.Select(ConvertLinetypeFromDto).ToList();
            }

            await HandleHttpError(response, "load linetypes");
            return new List<LinetypeModel>();
        }
        catch (Exception ex)
        {
            MessageWindow.ShowDialog("Network Error",
                $"Failed to connect to server while loading linetypes: {ex.Message}",
                MessageType.Error);
            return new List<LinetypeModel>();
        }
    }

    public async Task<LinetypeModel> CreateLinetypeAsync(LinetypeModel linetypeModel)
    {
        try
        {
            var linetypeDto = ConvertLinetypeToDto(linetypeModel);
            var jsonString = JsonConvert.SerializeObject(linetypeDto);
            var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync($"{_dwgLayerManagerApiUrl}/linetypes", content);

            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync();
                var createdDto = JsonConvert.DeserializeObject<LinetypeDto>(responseJson);
                return createdDto != null ? ConvertLinetypeFromDto(createdDto) : null;
            }

            return null;
        }
        catch
        {
            return null;
        }
    }

    public async Task<LinetypeModel> UpdateLinetypeAsync(LinetypeModel linetypeModel)
    {
        try
        {
            var linetypeDto = ConvertLinetypeToDto(linetypeModel);
            var jsonString = JsonConvert.SerializeObject(linetypeDto);
            var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync($"{_dwgLayerManagerApiUrl}/linetypes", content);

            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync();
                var updatedDto = JsonConvert.DeserializeObject<LinetypeDto>(responseJson);
                return updatedDto != null ? ConvertLinetypeFromDto(updatedDto) : null;
            }

            return null;
        }
        catch
        {
            return null;
        }
    }

    public async Task<bool> DeleteLinetypeAsync(Guid id)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"{_dwgLayerManagerApiUrl}/linetypes/{id}");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region Conversion Methods

    private LayerModel ConvertFromDto(LayerDto dto)
    {
        return new LayerModel
        {
            Id = dto.Id,
            Name = dto.Name,
            Color24bit = dto.Color24bit,
            LinetypeId = dto.LinetypeId,
            LinetypeName = dto.LinetypeName,
            LinetypeDescription = dto.LinetypeDescription,
            Lineweight01mm = dto.Lineweight01mm,
            TransparencyPct = dto.TransparencyPct,
            UpdatedUtc = dto.UpdatedUtc,
            IsVisible = true,
            IsLocked = false
        };
    }

    private LayerDto ConvertToDto(LayerModel model)
    {
        return new LayerDto
        {
            Id = model.Id,
            Name = model.Name,
            Color24bit = model.Color24bit,
            LinetypeId = model.LinetypeId,
            LinetypeName = model.LinetypeName,
            LinetypeDescription = model.LinetypeDescription,
            Lineweight01mm = model.Lineweight01mm,
            TransparencyPct = model.TransparencyPct,
            UpdatedUtc = DateTime.UtcNow
        };
    }

    private LinetypeModel ConvertLinetypeFromDto(LinetypeDto dto)
    {
        return new LinetypeModel
        {
            Id = dto.Id,
            Name = dto.Name,
            Description = dto.Description,
            PatternRaw = dto.PatternRaw,
            UpdatedUtc = dto.UpdatedUtc
        };
    }

    private LinetypeDto ConvertLinetypeToDto(LinetypeModel model)
    {
        return new LinetypeDto
        {
            Id = model.Id,
            Name = model.Name,
            Description = model.Description,
            PatternRaw = model.PatternRaw,
            UpdatedUtc = DateTime.UtcNow
        };
    }

    #endregion
}