﻿using System;
using System.Windows.Input;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals;

public class ModalDialogVm : BaseViewModel
{
    public string Title { get; set; }
    public object Content { get; set; }
    public Action<bool?> CloseDialog { get; set; }

    public ModalDialogVm()
    {
        CancelCommand = new RelayCommand(Cancel);
    }

    public ICommand CancelCommand { get; protected set; }

    private string _error;
    public string Error
    {
        get => _error;
        set
        {
            if (_error != value)
            {
                _error = value;
                OnPropertyChanged();
            }
        }
    }

    protected void CloseModal(bool? result = null)
    {
        CloseDialog?.Invoke(result);
    }

    protected virtual void Cancel(object parameter)
    {
        CloseDialog(false);
    }
}