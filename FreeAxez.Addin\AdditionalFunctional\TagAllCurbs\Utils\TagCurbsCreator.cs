﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Models;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllCurbs.Utils
{
    public class TagCurbsCreator
    {
        private readonly CurbTagOptions _options;
        private readonly CurbFamily _curbFamily;
        private readonly CurbAnnatationFamily _curbAnnatationFamily;

        public TagCurbsCreator(CurbTagOptions options)
        {
            _options = options;
            _curbFamily = new CurbFamily();
            _curbAnnatationFamily = new CurbAnnatationFamily();
        }

        public List<AnnotationSymbol> CreateAnnatation()
        {
            var output = new List<AnnotationSymbol>();

            var curbsToTag = SelectCurbsToTag();
            List<List<FamilyInstance>> separatedCurbs = new SeparateCurbResolver().Resolve(curbsToTag);

            using (var t = new Transaction(RevitManager.Document, "Curbs Annotation Symbols"))
            {
                t.Start();

                foreach (List<FamilyInstance> separatedCurb in separatedCurbs)
                {
                    foreach (var curb in separatedCurb)
                    {
                        output.Add(CreateAnnotation(curb));
                    }

                    RevitManager.Document.Regenerate();

                    foreach (var annotation in output)
                    {
                        SetLeaderPosition(annotation);
                    }
                }

                t.Commit();
            }

            return output;
        }

        private List<FamilyInstance> SelectCurbsToTag()
        {
            if (_options.TagCurbsVisibleInView)
            {
                return _curbFamily.InstancesOnView;
            }
            return _curbFamily.SelectInstances();
        }

        private AnnotationSymbol CreateAnnotation(FamilyInstance curb)
        {
            var curbPoint = (curb.Location as LocationPoint).Point;
            var length = _options.LengthToCenterOfTag;
            var scale = RevitManager.UIDocument.ActiveView.Scale;

            var annotationPoint = new XYZ(curbPoint.X - length * scale,
                                          curbPoint.Y - length / 2 * scale,
                                          curbPoint.Z);

            var tagSymbol = _curbAnnatationFamily.TagSymbol;
            if (CurbFamily.IsCorner(curb))
            {
                tagSymbol = _curbAnnatationFamily.CornerTagSymbol;
            }

            var annotation = RevitManager.Document.Create.NewFamilyInstance(
                annotationPoint, tagSymbol, RevitManager.UIDocument.ActiveView) as AnnotationSymbol;

            annotation.addLeader();

            return annotation;
        }

        private void SetLeaderPosition(AnnotationSymbol annotation)
        {
            var leader = annotation.GetLeaders().FirstOrDefault();
            if (leader == null)
            {
                return;
            }

            var leaderPoint = (annotation.Location as LocationPoint).Point;
            var length = _options.LengthToCenterOfTag;
            var scale = RevitManager.UIDocument.ActiveView.Scale;

            var end = new XYZ(leaderPoint.X + (length * scale),
                              leaderPoint.Y + (length / 2 * scale),
                              leaderPoint.Z);

            var elbow = new XYZ(end.X - (length / 3 * scale),
                                end.Y - (length / 2 * scale),
                                end.Z);

            leader.End = end;
            leader.Elbow = elbow;
        }
    }
}
