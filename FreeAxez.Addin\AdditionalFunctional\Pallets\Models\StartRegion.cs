﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.Pallets.Models
{
    public class StartRegion
    {
        private FilledRegion _filledRegion;

        public StartRegion(FilledRegion filledRegion)
        {
            _filledRegion = filledRegion;

            var bb = _filledRegion.get_BoundingBox(RevitManager.UIDocument.ActiveView);
            var max = bb.Max.Add(bb.Transform.Origin);
            var min = bb.Min.Add(bb.Transform.Origin);

            Length = bb.Max.X - bb.Min.X;
            Width = bb.Max.Y - bb.Min.Y;
            Center = new XYZ((max.X + min.X) / 2, (max.Y + min.Y) / 2, (max.Z + min.Z) / 2);
        }

        /// <summary>
        /// Length along the X axis.
        /// </summary>
        public double Length { get; }

        /// <summary>
        /// Width along the Y axis.
        /// </summary>
        public double Width { get; }

        public XYZ Center { get; }
    }
}
