﻿using System;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.ApplicationServices.Core;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;

namespace FreeAxez.AutoCAD.Plugin.Commands
{
    /// <summary>
    /// Deletes all unused block definitions first, then all layers that
    /// become truly unused.  The routine repeats until nothing more can
    /// be purged, exactly like the built-in  -PURGE All  command.
    /// </summary>
    public class DeleteEmptyLayersCommand
    {
        [CommandMethod("DELETEEMPTYLAYERS", CommandFlags.Session)]
        public void DeleteEmptyLayers()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            Database db = doc.Database;

            int totalBlocks = 0;
            int totalLayers = 0;
            int pass = 0;

            try
            {
                using (doc.LockDocument())
                {
                    bool anythingGone;
                    do
                    {
                        pass++;
                        anythingGone = false;

                        // --------------------------------------------------
                        // Phase 1 — block definitions
                        // --------------------------------------------------
                        var blockIds = CollectUnusedBlocks(db);
                        db.Purge(blockIds);
                        int goneB = EraseObjects(db, blockIds, eraseSoftPointers: false);
                        if (goneB > 0) { totalBlocks += goneB; anythingGone = true; }

                        // --------------------------------------------------
                        // Phase 2 — layers (clear overrides → purge → erase)
                        // --------------------------------------------------
                        var layerIds = CollectUnusedLayers(db);
                        ClearViewportOverrides(db, layerIds);           // new
                        db.Purge(layerIds);
                        int goneL = EraseObjects(db, layerIds, eraseSoftPointers: true);
                        if (goneL > 0) { totalLayers += goneL; anythingGone = true; }

                    } while (anythingGone && pass < 10);               // safety cap
                }

                ed.WriteMessage(
                    $"\nRemoved unused layers: {totalLayers}, " +
                    $"unused block definitions: {totalBlocks}");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\nError while purging: {ex.Message}");
            }
        }

        // --------------------------------------------------------------
        // Helpers
        // --------------------------------------------------------------

        private static ObjectIdCollection CollectUnusedBlocks(Database db)
        {
            var ids = new ObjectIdCollection();

            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);

                foreach (ObjectId btrId in bt)
                {
                    var btr = (BlockTableRecord)tr.GetObject(btrId, OpenMode.ForRead);

                    if (btr.IsAnonymous || btr.IsLayout || btr.IsDependent)
                        continue;

                    // FALSE = include *indirect* references
                    if (btr.GetBlockReferenceIds(false, true).Count != 0)
                        continue;

                    ids.Add(btrId);
                }
                tr.Commit();
            }
            return ids;
        }

        private static ObjectIdCollection CollectUnusedLayers(Database db)
        {
            var ids = new ObjectIdCollection();

            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                var lt = (LayerTable)tr.GetObject(db.LayerTableId, OpenMode.ForRead);

                foreach (ObjectId lid in lt)
                {
                    var lr = (LayerTableRecord)tr.GetObject(lid, OpenMode.ForRead);

                    if (lr.IsErased || lr.IsDependent) continue; // xref / VP-layers
                    if (lid == db.Clayer) continue; // current layer
                    if (IsProtected(lr.Name)) continue; // 0 / Defpoints

                    ids.Add(lid);
                }
                tr.Commit();
            }
            return ids;
        }

        /// <summary>Remove every viewport override from each candidate layer.</summary>
        private static void ClearViewportOverrides(Database db, ObjectIdCollection layerIds)
        {
            if (layerIds.Count == 0) return;

            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                foreach (ObjectId lid in layerIds)
                {
                    var lr = (LayerTableRecord)tr.GetObject(lid, OpenMode.ForWrite);
                    if (lr.HasOverrides)                     // :contentReference[oaicite:2]{index=2}
                        lr.RemoveAllOverrides();             // :contentReference[oaicite:3]{index=3}
                }
                tr.Commit();
            }
        }

        private static int EraseObjects(Database db,
                                        ObjectIdCollection ids,
                                        bool eraseSoftPointers)
        {
            int erased = 0;

            if (ids.Count == 0) return erased;

            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                foreach (ObjectId id in ids)
                {
                    try
                    {
                        var obj = (DBObject)tr.GetObject(id, OpenMode.ForWrite);
                        obj.Erase(eraseSoftPointers);
                        erased++;
                    }
                    catch { /* still referenced → skip */ }
                }
                tr.Commit();
            }
            return erased;
        }

        private static bool IsProtected(string name) =>
            !string.IsNullOrEmpty(name) &&
            (name.Equals("0", StringComparison.OrdinalIgnoreCase) ||
             name.Equals("Defpoints", StringComparison.OrdinalIgnoreCase));
    }
}