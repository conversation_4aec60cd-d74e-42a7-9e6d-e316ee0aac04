using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.ViewModels
{
    /// <summary>
    /// ViewModel for Low Voltage Path dialog
    /// </summary>
    public class LowVoltagePathViewModel : WindowViewModel
    {
        private LowVoltagePathSettings _settings;
        private bool _isExecuting = false;

        public LowVoltagePathViewModel()
        {
            _settings = new LowVoltagePathSettings();
            
            // Initialize commands
            ExecuteCommand = new RelayCommand(ExecuteAsync, CanExecute);
            CancelCommand = new RelayCommand(Cancel);
            
            // Load available railing types
            LoadAvailableRailingTypes();
            
            // Set default railing type
            if (AvailableRailingTypes.Count > 0)
            {
                Settings.SelectedRailingType = AvailableRailingTypes.First();
            }
        }

        #region Properties

        /// <summary>
        /// Settings for the Low Voltage Path operation
        /// </summary>
        public LowVoltagePathSettings Settings
        {
            get => _settings;
            set => Set(ref _settings, value);
        }

        /// <summary>
        /// Available railing types in the project
        /// </summary>
        public ObservableCollection<RailingType> AvailableRailingTypes { get; private set; } = new ObservableCollection<RailingType>();

        /// <summary>
        /// Available scope types for the operation
        /// </summary>
        public Array ScopeTypes => Enum.GetValues(typeof(ScopeType));

        /// <summary>
        /// Whether the command is currently executing
        /// </summary>
        public bool IsExecuting
        {
            get => _isExecuting;
            set
            {
                if (Set(ref _isExecuting, value))
                {
                    (ExecuteCommand as RelayCommand)?.RaiseCanExecuteChanged();
                }
            }
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to execute the Low Voltage Path operation
        /// </summary>
        public ICommand ExecuteCommand { get; }

        /// <summary>
        /// Command to cancel the dialog
        /// </summary>
        public ICommand CancelCommand { get; }

        #endregion

        #region Command Handlers

        private async void ExecuteAsync(object parameter)
        {
            if (IsExecuting) return;

            IsExecuting = true;
            Error = string.Empty;

            try
            {
                // Validate settings
                if (!ValidateSettings())
                {
                    return;
                }

                // Close dialog with success result
                if (parameter is Window window)
                {
                    window.DialogResult = true;
                    window.Close();
                }
            }
            catch (Exception ex)
            {
                Error = $"Error during execution: {ex.Message}";
                MessageWindow.ShowDialog(Error, MessageType.Error);
            }
            finally
            {
                IsExecuting = false;
            }
        }

        private bool CanExecute(object parameter)
        {
            return !IsExecuting && 
                   Settings?.SelectedRailingType != null;
        }

        private void Cancel(object parameter)
        {
            if (parameter is Window window)
            {
                window.DialogResult = false;
                window.Close();
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Loads available railing types from the project
        /// </summary>
        private void LoadAvailableRailingTypes()
        {
            try
            {
                var railingTypes = new FilteredElementCollector(RevitManager.Document)
                    .OfClass(typeof(RailingType))
                    .Cast<RailingType>()
                    .OrderBy(rt => rt.Name)
                    .ToList();

                AvailableRailingTypes.Clear();
                foreach (var railingType in railingTypes)
                {
                    AvailableRailingTypes.Add(railingType);
                }
            }
            catch (Exception ex)
            {
                Error = $"Failed to load railing types: {ex.Message}";
            }
        }

        /// <summary>
        /// Validates the current settings
        /// </summary>
        private bool ValidateSettings()
        {
            var errors = new List<string>();

            if (Settings.SelectedRailingType == null)
            {
                errors.Add("Please select a railing type.");
            }

            if (AvailableRailingTypes.Count == 0)
            {
                errors.Add("No railing types found in the project.");
            }

            if (errors.Count > 0)
            {
                Error = string.Join("\n", errors);
                MessageWindow.ShowDialog(Error, MessageType.Warning);
                return false;
            }

            return true;
        }

        #endregion
    }
}
