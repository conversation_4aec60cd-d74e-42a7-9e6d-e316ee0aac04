<Window x:Class="FreeAxez.Addin.AdditionalFunctional.SheetTitleEditor.Views.SheetTitleEditorView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FreeAxez.Addin.AdditionalFunctional.SheetTitleEditor.Views"
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen"
        Title="Sheet Title Editor" 
        Height="800" 
        MinHeight="300"
        Width="800"
        MinWidth="300">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Window.Style>
        <Style TargetType="{x:Type Window}"
               BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>

    <Grid Margin="0,10,0,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Search panel -->
        <DockPanel Grid.Row="0" Margin="0,0,0,10">
            <Button DockPanel.Dock="Right"
                    Content="X Clear"
                    Command="{Binding ClearFiltersCommand}"
                    Width="90"
                    Height="25"
                    Style="{StaticResource ButtonOutlinedRed}" />
            <TextBlock Text="Search:"
                       Style="{StaticResource TextH5}"
                       Margin="0,0,10,0" />
            <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                     Style="{StaticResource UiTextBox}"
                     Height="25" 
                     Margin="0,0,10,0"/>
        </DockPanel>

        <!-- List of sheet titles -->
        <ListView Grid.Row="1"
                  ItemsSource="{Binding FilteredItems}"
                  SelectionMode="Extended"
                  HorizontalAlignment="Stretch"
                  HorizontalContentAlignment="Stretch"
                  Style="{StaticResource ListViewBase}"
                  Margin="0,0,0,10">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <Border BorderBrush="#DDDDDD"
                            BorderThickness="0,0,0,1"
                            Padding="0,5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <CheckBox Grid.Row="0"
                                      Grid.Column="0"
                                      Grid.RowSpan="4"
                                      IsChecked="{Binding IsSelected, Mode=TwoWay}"
                                      VerticalAlignment="Center"
                                      Margin="0,0,10,0"
                                      Checked="CheckBox_CheckedChanged"
                                      Unchecked="CheckBox_CheckedChanged" />

                            <StackPanel Grid.Row="0"
                                        Grid.Column="1"
                                        Orientation="Horizontal">
                                <TextBlock Text="{Binding BrowserPath}"
                                           Foreground="#666666"
                                           FontSize="11" />
                                <TextBlock Text=" > "
                                           Foreground="#666666"
                                           FontSize="11" />
                                <TextBlock Text="{Binding SheetNumber}"
                                           FontWeight="Bold" />
                                <TextBlock Text=" - " />
                                <TextBlock Text="{Binding SheetName}"
                                           FontWeight="Bold" />
                            </StackPanel>

                            <StackPanel Grid.Row="2"
                                        Grid.Column="1"
                                        Orientation="Horizontal"
                                        Margin="0,3,0,0">
                                <TextBlock Text="View Name:"
                                           Margin="0,0,8,0"
                                           Width="80"
                                           VerticalAlignment="Center" />
                                <TextBlock Grid.Row="2"
                                           Grid.Column="1"
                                           FontSize="13"
                                           Text="{Binding ViewName}" />
                            </StackPanel>

                            <DockPanel Grid.Row="3"
                                       Grid.Column="1"
                                       Margin="0,3,0,0">
                                <TextBlock DockPanel.Dock="Left"
                                           Text="Sheet Title:"
                                           Width="80"
                                           VerticalAlignment="Center" />
                                <TextBox Text="{Binding SheetTitle, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource UiTextBox}"
                                         Height="25" 
                                         Margin="0,0,10,0" />
                            </DockPanel>
                        </Grid>
                    </Border>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>

        <!-- Header with counts -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    Margin="0,0,0,10">
            <TextBlock Text="Filtered: "
                       Style="{StaticResource TextH5}" />
            <TextBlock Text="{Binding FilteredCount}"
                       Style="{StaticResource TextH5}"
                       Margin="5,0,15,0" />
            <TextBlock Text="Selected: "
                       Style="{StaticResource TextH5}" />
            <TextBlock Text="{Binding SelectedCount}"
                       Style="{StaticResource TextH5}"
                       Margin="5,0,0,0" />
        </StackPanel>

        <!-- Button panel -->
        <UniformGrid Grid.Row="3" Rows="1" Columns="5" >
            <Button Content="Check Filtered" 
                    Command="{Binding SelectAllCommand}" 
                    Style="{StaticResource ButtonOutlinedBlue}"
                    Margin="0,0,5,0" 
                    Padding="10,5"/>

            <Button Content="Uncheck Filtered" 
                    Command="{Binding ClearSelectionCommand}" 
                    Style="{StaticResource ButtonOutlinedBlue}"
                    Margin="5,0" 
                    Padding="10,5"/>

            <Button Content="Copy View Name" 
                    Command="{Binding CopyViewNameToTitleCommand}" 
                    Style="{StaticResource ButtonOutlinedBlue}"
                    Margin="5,0" 
                    Padding="10,5"/>

            <Button Content="Apply" 
                    Command="{Binding ApplyCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"
                    Style="{StaticResource ButtonSimpleGreen}"
                    Margin="5,0" 
                    Padding="10,5"/>

            <Button Content="Cancel" 
                    Command="{Binding CancelCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Window}}}"
                    Style="{StaticResource ButtonSimpleRed}"
                    Margin="5,0,0,0" 
                    Padding="10,5"/>
        </UniformGrid>
    </Grid>
</Window>