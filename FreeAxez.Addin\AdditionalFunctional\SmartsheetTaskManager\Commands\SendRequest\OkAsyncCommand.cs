﻿using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Abstractions;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Constants;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Contracts.Requests;
using FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

#nullable enable

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.Commands.SendRequest
{
    public class OkAsyncCommand : AsyncCommandBase
    {
        private readonly SendRequestViewModel _sendRequestViewModel;
        private readonly TaskManagerViewModel _taskManagerViewModel;
        private readonly Dispatcher _dispatcher;
        private readonly TaskManagerHttpClientBase _taskManagerHttpClientService;

        public OkAsyncCommand(SendRequestViewModel sendRequestViewModel,
                              TaskManagerViewModel taskManagerViewModel,
                              Dispatcher dispatcher,
                              TaskManagerHttpClientBase taskManagerHttpClientService)
        {
            _sendRequestViewModel = sendRequestViewModel;
            _taskManagerViewModel = taskManagerViewModel;
            _dispatcher = dispatcher;
            _taskManagerHttpClientService = taskManagerHttpClientService;

            _taskManagerViewModel.PropertyChanged += OnViewModelPropertyChanged;
        }

        // Handle if 2 or more email assigned
        public async override Task ExecuteAsync(object parameter)
        {
            Window? window = parameter as Window;
            window.Owner = null;
            window.Visibility = Visibility.Hidden;

            try
            {
                if (!string.IsNullOrEmpty(_taskManagerViewModel.RecentRow.AssignedToEmail))
                {
                    _taskManagerViewModel.IsExecuting = true;

                    // ToDo: Create through ONE http request
                    IEnumerable<long> subRowDtoIds = await _taskManagerHttpClientService
                        .GetSubRowDtoIdsFromParent(_taskManagerViewModel.SheetId,
                                                   _taskManagerViewModel.RecentRow.Id,
                                                   _sendRequestViewModel.CancellationTokenSource.Token);

                    var updateEmailRequest = new UpdateEmailRequest
                    {
                        ColumnIds = new List<long>(_taskManagerViewModel.RecentRow.ColumnDtos
                            .Select(colInfo => colInfo.Id)),
                        Message = ViewModelConstants.TaskManager.UpdateRequestMessage,
                        RowIds = subRowDtoIds.ToList(),
                        SendTo = _taskManagerViewModel.RecentRow.AssignedToEmail,
                        Subject = $"{_taskManagerViewModel.ProjectName} - {_taskManagerViewModel.Scope}" +
                                  $"- {ViewModelConstants.TaskManager.UpdateRequestSubject}"
                    };

                    HttpResponseMessage httpResponseMessage = await _taskManagerHttpClientService
                        .SendUpdateRequestEmail(_taskManagerViewModel.SheetId,
                                                updateEmailRequest,
                                                _sendRequestViewModel.CancellationTokenSource.Token);
                }
            }
            catch (TaskCanceledException)
            {
                _taskManagerViewModel.ErrorResponse = string.Empty;
            }
            catch (Exception exception)
            {
                _taskManagerViewModel.ErrorResponse = exception.Message;
            }
            finally
            {
                _taskManagerViewModel.IsExecuting = false;
                window?.Close();
            }
        }

        public override bool CanExecute(object parameter)
        {
            return base.CanExecute(parameter)
                && !string.IsNullOrEmpty(_taskManagerViewModel.RecentRow?.AssignedToEmail);
        }

        private void OnViewModelPropertyChanged(object sender, PropertyChangedEventArgs eventArguments)
        {
            if (eventArguments.PropertyName == nameof(TaskManagerViewModel.IsExecuting))
            {
                _dispatcher.Invoke(OnCanExecuteChanged);
            }
        }
    }
}
