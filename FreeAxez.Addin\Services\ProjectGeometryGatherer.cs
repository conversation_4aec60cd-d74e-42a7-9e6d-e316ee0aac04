﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastucture;
using FreeAxez.Core.Dto;
using FreeAxez.Core.GeometryModel;
using FreeAxez.Core.Utilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace FreeAxez.Addin.Services
{
    public class ProjectGeometryGatherer
    {
        private readonly JsonSerializerSettings _serializerSettings;

        public ProjectGeometryGatherer()
        {
            DefaultContractResolver contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            _serializerSettings = new JsonSerializerSettings()
            {
                ContractResolver = contractResolver,
                Formatting = Formatting.None
            };
        }

        public List<List<LineModel>> GatherLoop(List<FilledRegion> regions, bool checkLoop = true)
        {
            var loops = new List<List<LineModel>>();

            foreach (var elem in regions)
            {
                var lines = GetFlatLoopsData(elem);

                if (checkLoop)
                {
                    foreach (var line in lines)
                    {
                        Func<PointModel, LineModel, bool> lineComparator = (originalPoint, l) =>
                            (originalPoint.EqualTo(l.StartPoint) || originalPoint.EqualTo(l.EndPoint))
                            && !(line.StartPoint.EqualTo(l.StartPoint) && line.EndPoint.EqualTo(l.EndPoint));

                        Func<LineModel, bool> startPointComparator = l => lineComparator(line.StartPoint, l);
                        var startLine = lines.FirstOrDefault(startPointComparator);

                        Func<LineModel, bool> endPointComparator = l => lineComparator(line.EndPoint, l);
                        var endLine = lines.FirstOrDefault(endPointComparator);

                        if (startLine != null && endLine != null)
                            continue;

                        // TODO Exception with custom type and handle at command root.
                        RevitDialogHelper.ShowNotification(
                            $"The line ({line.StartPoint.X,(int) MathUtilities.DefaultPrecision};{line.StartPoint.Y,(int) MathUtilities.DefaultPrecision})"
                            + $" -> ({line.EndPoint.X,(int) MathUtilities.DefaultPrecision};{line.EndPoint.Y,(int) MathUtilities.DefaultPrecision})"
                            + " is not attached to other lines.");

                        return null;
                    }
                }

                loops.Add(lines);
            }

            return loops;
        }

        public ProjectDto GatherRegionsGeometry(List<FilledRegion> regions, Document document)
        {
            if (regions == null || !regions.Any())
                return null;

            LogHelper.Information($"Gathering geometry. Regions: {regions.Select(x => x.Id.GetIntegerValue()).ToList()}. Document path: {document.PathName}.");
            
            var project = new ProjectDto()
            {
                RevitProjectId = document.ProjectInformation.Id.GetIntegerValue(),
                RevitUniqueId = document.ProjectInformation.UniqueId,
                RevitName = Path.GetFileName(document.PathName),
                Options = new List<OptionDto>()
            };

            var regionModels = new List<RegionDto>();
            var regionIndex = 0;
            var levelId = GetLevelId(document);

            var loops = GatherLoop(regions);

            if (loops == null)
                return null;

            for (int i = 0; i < regions.Count; ++i)
            {
                var region = regions[i];
                var loop = loops[i];

                var regionDto = new RegionDto()
                {
                    OrderIndex = regionIndex,
                    RevitRegionId = region.Id.GetIntegerValue(),
                    LevelId = levelId,
                    LayoutJson = JsonConvert.SerializeObject(loop, _serializerSettings)
                };

                regionModels.Add(regionDto);
                regionIndex++;
            }

            project.Options.Add(new OptionDto() { Regions = regionModels, OrderIndex = 0 });
            LogHelper.Information($"Gathering complete for: {document.PathName}");
            return project;
        }

        public ProjectDto MergeRegionsGeometry(List<FilledRegion> regions, List<FilledRegion> highTrafficRegions, List<XYZ> transitionLinePositions, Document document)
        {
            if (regions == null || !regions.Any())
                return null;

            LogHelper.Information($"Gathering geometry. Regions: {regions.Select(x => x.Id.GetIntegerValue()).ToList()}. Document path: {document.PathName}.");

            var project = new ProjectDto()
            {
                RevitProjectId = document.ProjectInformation.Id.GetIntegerValue(),
                RevitUniqueId = document.ProjectInformation.UniqueId,
                RevitName = Path.GetFileName(document.PathName),
                Options = new List<OptionDto>()
            };

            var regionModels = new List<RegionDto>();
            var regionIndex = 0;
            var levelId = GetLevelId(document);

            var loops = GatherLoop(regions);
            /*?.Select(loop => loop
                .Select(l => new RegionLineModel {StartPoint = l.StartPoint, EndPoint = l.EndPoint, IsTransitionLine = false})
                .ToList())
            .ToList();*/
            
            if (loops == null)
                return null;

            /*foreach (var loop in loops)
                foreach (var line in loop)
                    line.IsTransitionLine = transitionLinePositions.Any(tl => Utilities.FindDistanceToSegment(new PointModel(tl.X, tl.Y), line, out _) < 0.01); // TODO Threshold*/

            var specialArea = new SpecialAreaModel
            {
                TransitionLinesPositions = transitionLinePositions != null ? transitionLinePositions.Select(xyz => new PointModel(xyz.X, xyz.Y)).ToList() : new List<PointModel>(),
                HighTrafficAreas = highTrafficRegions != null ? highTrafficRegions.Select(r => GetLoopsData(r)).ToList() : new List<List<List<LineModel>>>()
            };

            for (int i = 0; i < regions.Count; ++i)
            {
                var region = regions[i];
                var loop = loops[i];

                var regionDto = new RegionDto()
                {
                    OrderIndex = regionIndex,
                    RevitRegionId = region.Id.GetIntegerValue(),
                    LevelId = levelId,
                    LayoutJson = JsonConvert.SerializeObject(loop, _serializerSettings),
                    SpecialAreasJson = JsonConvert.SerializeObject(specialArea, _serializerSettings)
                };

                regionModels.Add(regionDto);
                regionIndex++;
            }

            project.Options.Add(new OptionDto() { Regions = regionModels, OrderIndex = 0 });
            LogHelper.Information($"Gathering complete for: {document.PathName}");
            return project;
        }

        private int? GetLevelId(Document document)
        {
            View active = document.ActiveView;

            Parameter level = active.LookupParameter("Associated Level");

            FilteredElementCollector lvlCollector = new FilteredElementCollector(document);
            ICollection<Element> lvlCollection = lvlCollector.OfClass(typeof(Level)).ToElements();

            foreach (Element l in lvlCollection)
            {
                Level lvl = l as Level;
                if (lvl.Name == level.AsString())
                {
                    return lvl.Id.GetIntegerValue();
                }
            }

            return null;
        }

        private List<LineModel> GetFlatLoopsData(FilledRegion elem)
        {
            List<LineModel> result = new List<LineModel>();
            IList<CurveLoop> loops = elem.GetBoundaries();

            foreach (CurveLoop loop in loops)
            {
                foreach (Curve curve in loop)
                {
                    if (curve is Arc)
                    {
                        result.Add(GetArc(curve as Arc));
                    }
                    else if (curve is Line)
                    {
                        result.Add(GetLine(curve as Line));
                    }
                }
            }

            return result;
        }

        private List<List<LineModel>> GetLoopsData(FilledRegion elem)
        {
            var output = new List<List<LineModel>>();

            foreach (CurveLoop loop in elem.GetBoundaries())
            {
                var loopLines = new List<LineModel>();
                foreach (Curve curve in loop)
                {
                    if (curve is Arc)
                    {
                        loopLines.Add(GetArc(curve as Arc));
                    }
                    else if (curve is Line)
                    {
                        loopLines.Add(GetLine(curve as Line));
                    }
                }
                output.Add(loopLines);
            }

            return output;
        }

        private LineModel GetLine(Line line)
        {
            XYZ start = line.GetEndPoint(0);
            XYZ end = line.GetEndPoint(1);
            return new LineModel()
            {
                StartPoint = new PointModel() { X = RoundCoordinate(start.X), Y = RoundCoordinate(start.Y) },
                EndPoint = new PointModel() { X = RoundCoordinate(end.X), Y = RoundCoordinate(end.Y) }
            };
        }

        private ArcModel GetArc(Arc arc)
        {
            var startPoint = arc.GetEndPoint(0);
            var endPoint = arc.GetEndPoint(1);
            return new ArcModel()
            {
                CenterPoint = new PointModel()
                {
                    X = RoundCoordinate(arc.Center.X),
                    Y = RoundCoordinate(arc.Center.Y)
                },
                StartPoint = new PointModel()
                {
                    X = RoundCoordinate(startPoint.X),
                    Y = RoundCoordinate(startPoint.Y)
                },
                EndPoint = new PointModel()
                {
                    X = RoundCoordinate(endPoint.X),
                    Y = RoundCoordinate(endPoint.Y)
                },
                Radius = arc.Radius,
                IsCyclic = arc.IsCyclic,
                IsCounterclockwise = arc.Normal.Z > 0,
                ArcLength = arc.Length
            };
        }

        /// <summary>
        /// When the model has minimal line deviations 
        /// from the normal, the backend does not form the initial model.
        /// It seems that the lines of the regions are breaking
        /// </summary>
        private double RoundCoordinate(double value)
        {
            return Math.Round(value, 3);
        }

#if DEBUG
        public ProjectDto GatherElementGeometry(List<Element> elems, Document document)
        {
            var project = new ProjectDto()
            {
                RevitProjectId = document.ProjectInformation.Id.GetIntegerValue(),
                RevitUniqueId = document.ProjectInformation.UniqueId,
                RevitName = Path.GetFileName(document.PathName),
                Options = new List<OptionDto>()
            };

            var regionModels = new List<RegionDto>();
            int ordInd = 0;
            foreach (var element in elems)
            {
                var points = GetBoundaryCorners(element, document);

                var region = new RegionDto()
                {
                    OrderIndex = ordInd,
                    RevitRegionId = element.Id.GetIntegerValue(),
                    LayoutJson = JsonConvert.SerializeObject(points, _serializerSettings)
                };

                regionModels.Add(region);
                ordInd++;
            }

            project.Options.Add(new OptionDto() { Regions = regionModels, OrderIndex = 0 });
            return project;
        }

        List<LineModel> GetBoundaryCorners(Element element, Document document)
        {
            List<LineModel> result = new List<LineModel>();
            //var elment = document.GetElement(region.ElementId);

            var geometryOptions = document.Application.Create.NewGeometryOptions();
            var familyInstance = element as FamilyInstance;
            var geometryElement = familyInstance.get_Geometry(geometryOptions);
            foreach (var geometryInstance in geometryElement)
            {
                var instance = geometryInstance as GeometryInstance;
                if (instance != null)
                {
                    foreach (GeometryObject geometryObject in instance.SymbolGeometry)
                    {
                        if (geometryObject is Solid)
                        {
                            var solid = geometryObject as Solid;
                            foreach (var edge in solid.Edges)
                            {
                                var edgeAsEdge = edge as Edge;
                                var edgeCurve = edgeAsEdge?.AsCurve();
                                if (edgeCurve != null && solid.Edges.Size <= 131)
                                {
                                    XYZ start = edgeCurve.GetEndPoint(0);
                                    XYZ end = edgeCurve.GetEndPoint(1);

                                    //if (start.Z > 0 || end.Z > 0)
                                    //{
                                    //    continue;
                                    //}

                                    //if (!(Math.Abs(start.X - end.X) == 0 || Math.Abs(start.Y - end.Y) == 0))
                                    //{
                                    //    continue;
                                    //}

                                    //do not add points
                                    if (MathUtilities.Approximately(start.X, end.X) && MathUtilities.Approximately(start.Y, end.Y))
                                    {
                                        continue;
                                    }

                                    //do not add duplicates
                                    if (result.Any(x => MathUtilities.Approximately(x.EndPoint.X, end.X) && MathUtilities.Approximately(x.EndPoint.Y, end.Y)))
                                    {
                                        continue;
                                    }

                                    result.Add(new LineModel()
                                    {
                                        StartPoint = new PointModel() { X = start.X, Y = start.Y },
                                        EndPoint = new PointModel() { X = end.X, Y = end.Y }
                                    });
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }
#endif
    }
}