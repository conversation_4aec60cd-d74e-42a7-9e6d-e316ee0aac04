using System.Windows.Controls;
using System.Windows;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls;

public partial class MainWindow : UserControl
{
    public static readonly DependencyProperty HeaderContentProperty =
        DependencyProperty.Register("HeaderContent", typeof(object), typeof(MainWindow), new UIPropertyMetadata(null));
    public static readonly DependencyProperty MainContentProperty =
        DependencyProperty.Register("MainContent", typeof(object), typeof(MainWindow), new UIPropertyMetadata(null));
    public static readonly DependencyProperty FooterContentProperty =
        DependencyProperty.Register("FooterContent", typeof(object), typeof(MainWindow), new UIPropertyMetadata(null));
    public MainWindow()
    {
        InitializeComponent();
        DataContext = this;
    }
     
    public object HeaderContent
    {
        get => (object)GetValue(HeaderContentProperty);
        set => SetValue(HeaderContentProperty, value);
    }
    public object MainContent
    {
        get => (object)GetValue(MainContentProperty);
        set => SetValue(MainContentProperty, value);
    }
    public object FooterContent
    {
        get => (object)GetValue(FooterContentProperty);
        set => SetValue(FooterContentProperty, value);
    }
}