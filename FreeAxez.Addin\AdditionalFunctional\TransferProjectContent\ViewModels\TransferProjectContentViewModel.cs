﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Enums;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Models;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Utils;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.ViewModels;

public class TransferProjectContentViewModel : WindowViewModel
{
    private bool _copyElements;
    private ProgressView _progressView;
    private ScheduleHandlingOption _selectedScheduleHandlingOption;
    private ViewHandlingOption _selectedViewHandlingOption;
    private string _sourceDocumentName;
    private string _targetDocumentName;
    private ObservableCollection<ViewTemplateMapping> _viewTemplateMapping;

    public TransferProjectContentViewModel()
    {
        OpenedDocuments = GetOpenedDocuments();
        _sourceDocumentName = RevitManager.Document.Title;

        if (OpenedDocuments.Count > 1)
        {
            _targetDocumentName = OpenedDocuments.FirstOrDefault(doc => doc != _sourceDocumentName);
        }
        else
        {
            _targetDocumentName = _sourceDocumentName;
        }

        _selectedViewHandlingOption = OptionsManager.ViewHandlingOption;
        _selectedScheduleHandlingOption = OptionsManager.ScheduleHandlingOption;

        CopyElementsCommand = new RelayCommand(OnCopyElementsCommandExecute);
        UpdateTargetDuplication();
    }

    public List<string> OpenedDocuments { get; set; }

    public string SourceDocument
    {
        get => _sourceDocumentName;
        set
        {
            _sourceDocumentName = value;
            _viewTemplateMapping = null;
            OnPropertyChanged(nameof(ViewTemplateMappings));

            OnPropertyChanged(nameof(AvailableTargetDocuments));

            if (!AvailableTargetDocuments.Contains(_targetDocumentName))
            {
                TargetDocument = AvailableTargetDocuments.FirstOrDefault();
            }

            UpdateTargetDuplication();
        }
    }

    public string TargetDocument
    {
        get => _targetDocumentName;
        set
        {
            _targetDocumentName = value;
            _viewTemplateMapping = null;
            OnPropertyChanged(nameof(ViewTemplateMappings));

            UpdateTargetDuplication();
        }
    }

    public ViewHandlingOption SelectedViewHandlingOption
    {
        get => _selectedViewHandlingOption;
        set
        {
            if (_selectedViewHandlingOption != value)
            {
                _selectedViewHandlingOption = value;
                OptionsManager.ViewHandlingOption = value;
                OnPropertyChanged();
            }
        }
    }

    public ScheduleHandlingOption SelectedScheduleHandlingOption
    {
        get => _selectedScheduleHandlingOption;
        set
        {
            if (_selectedScheduleHandlingOption != value)
            {
                _selectedScheduleHandlingOption = value;
                OptionsManager.ScheduleHandlingOption = value;
                OnPropertyChanged();
            }
        }
    }

    public bool CopyRevisions
    {
        get => OptionsManager.CopyRevisions;
        set
        {
            if (OptionsManager.CopyRevisions != value)
            {
                OptionsManager.CopyRevisions = value;
                OnPropertyChanged();
            }
        }
    }

    public bool CopyDimensions
    {
        get => OptionsManager.CopyDimensions;
        set
        {
            if (OptionsManager.CopyDimensions != value)
            {
                OptionsManager.CopyDimensions = value;
                OnPropertyChanged();
            }
        }
    }

    public bool CopyTagsAndTextNotes
    {
        get => OptionsManager.CopyTagsAndTextNotes;
        set
        {
            if (OptionsManager.CopyTagsAndTextNotes != value)
            {
                OptionsManager.CopyTagsAndTextNotes = value;
                OnPropertyChanged();
            }
        }
    }

    public bool CopyDetailLinesAndRegions
    {
        get => OptionsManager.CopyDetailLinesAndRegions;
        set
        {
            if (OptionsManager.CopyDetailLinesAndRegions != value)
            {
                OptionsManager.CopyDetailLinesAndRegions = value;
                OnPropertyChanged();
            }
        }
    }

    public ObservableCollection<ViewTemplateMapping> ViewTemplateMappings
    {
        get
        {
            if (_viewTemplateMapping == null)
            {
                var source = ViewTemplateModel.CollectViewTemplatesFromDocument(GetDocumentByName(SourceDocument));
                var target =
                    ViewTemplateModel.CollectViewTemplatesFromDocument(GetDocumentByName(TargetDocument), true);
                _viewTemplateMapping =
                    new ObservableCollection<ViewTemplateMapping>(ViewTemplateMapping.Map(source, target));
                foreach (var vm in _viewTemplateMapping)
                {
                    vm.PropertyChanged += Mapping_PropertyChanged;
                }
            }

            return _viewTemplateMapping;
        }
    }

    public ICommand CopyElementsCommand { get; }

    public List<string> AvailableTargetDocuments
    {
        get
        {
            var targets = OpenedDocuments.Where(doc => doc != SourceDocument).ToList();
            if (!targets.Any())
            {
                // If no other documents are open, include the source document
                targets.Add(SourceDocument);
            }

            return targets;
        }
    }

    private List<string> GetOpenedDocuments()
    {
        var output = new List<string>();
        foreach (Document doc in RevitManager.Application.Documents)
        {
            output.Add(doc.Title);
        }

        output.Sort();
        return output;
    }

    private Document GetDocumentByName(string name)
    {
        foreach (Document doc in RevitManager.Application.Documents)
        {
            if (doc.Title == name)
            {
                return doc;
            }
        }

        return null;
    }


    private void OnCopyElementsCommandExecute(object p)
    {
        var confirmationWindow = MessageWindow.ShowDialog("Confirm Copy Operation",
            $"Please ensure that all copy settings are correct and that you wish to proceed with copying elements from {_sourceDocumentName} to {_targetDocumentName}.",
            MessageType.Warning);

        if (confirmationWindow == true)
        {
            (p as Window).Close();
            StartCopyElements();
        }
    }

    private void StartCopyElements()
    {
        var sourceDocument = GetDocumentByName(_sourceDocumentName);
        var targetDocument = GetDocumentByName(_targetDocumentName);

        var cancellationTokenSource = new CancellationTokenSource();
        var cancellationToken = cancellationTokenSource.Token;

        var progressViewModel = new ProgressViewModel(cancellationTokenSource);

        ShowCancellationWindowOnNewThread(progressViewModel);

        var rebaseManager = new RebaseManager(sourceDocument, targetDocument, progressViewModel, ViewTemplateMappings);
        rebaseManager.Rebase(cancellationToken);
        _progressView.Dispatcher.Invoke(() => { _progressView.Close(); });
    }

    private void ShowCancellationWindowOnNewThread(ProgressViewModel progressViewModel)
    {
        var cancellationWindowThread = new Thread(() =>
        {
            _progressView = new ProgressView();
            RevitManager.SetRevitAsWindowOwner(_progressView);
            _progressView.DataContext = progressViewModel;
            _progressView.Show();

            Dispatcher.Run();

            _progressView.Closed += (sender, args) =>
            {
                // Use IsBackground instead of InvokeShutdown because InvokeShutdown breaks Revit
                //Dispatcher.CurrentDispatcher.InvokeShutdown();
            };
        });

        cancellationWindowThread.SetApartmentState(ApartmentState.STA);
        cancellationWindowThread.IsBackground = true;
        cancellationWindowThread.Start();
    }

    private void Mapping_PropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        UpdateTargetDuplication();
    }


    private void UpdateTargetDuplication()
    {
        var allSelectedTargets = ViewTemplateMappings
            .Where(m => m.Target != null)
            .Select(m => m.Target)
            .ToList();

        var duplicateTargets = allSelectedTargets
            .GroupBy(t => t)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key)
            .ToHashSet();

        foreach (var mapping in ViewTemplateMappings)
        {
            if (mapping.Target != null && duplicateTargets.Contains(mapping.Target))
            {
                mapping.IsTargetDuplicated = true;
            }
            else
            {
                mapping.IsTargetDuplicated = false;
            }
        }
    }
}