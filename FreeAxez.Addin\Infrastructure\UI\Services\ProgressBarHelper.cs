﻿using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Interfaces;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.Views;
using System.Windows.Threading;

namespace FreeAxez.Addin.Infrastructure.UI.Services
{
    public class ProgressBarHelper : IProgressReporter
    {
        private readonly object _lock = new();
        private Thread _progressBarThread;
        private ProgressView _progressView;
        private ProgressViewModel _progressViewModel;
        private CancellationTokenSource _cancellationTokenSource;

        public ProgressBarHelper()
        {
            _cancellationTokenSource = new CancellationTokenSource();
        }

        public CancellationTokenSource CancellationTokenSource => _cancellationTokenSource;

        public void Show()
        {
            lock (_lock)
            {
                if (_progressBarThread != null && _progressBarThread.IsAlive)
                    return;

                _progressViewModel = new ProgressViewModel(_cancellationTokenSource);

                _progressBarThread = new Thread(() =>
                {
                    try
                    {
                        _progressView = new ProgressView();
                        _progressView.DataContext = _progressViewModel;
                        _progressView.Closed += OnProgressBarClosed;
                        _progressView.Show();

                        Dispatcher.Run();
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error($"Progress bar error: {ex.Message}");
                    }
                });

                _progressBarThread.SetApartmentState(ApartmentState.STA);
                _progressBarThread.IsBackground = true;
                _progressBarThread.Start();
            }
        }

        public void Close()
        {
            lock (_lock)
            {
                if (_progressView != null)
                {
                    if (!_progressView.Dispatcher.HasShutdownStarted && !_progressView.Dispatcher.HasShutdownFinished)
                    {
                        if (_progressView.Dispatcher.CheckAccess())
                        {
                            _progressView.Close();
                        }
                        else
                        {
                            _progressView.Dispatcher.Invoke(() => _progressView.Close());
                        }
                    }

                    _progressView = null;
                    _progressViewModel = null;
                }

                _progressBarThread = null;
            }
        }

        public void ReportProgress(double value)
        {
            lock (_lock)
            {
                try
                {
                    if (_progressViewModel != null)
                    {
                        _progressViewModel.ProgressValue = value;
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Failed to update progress bar {ex.Message}");
                }
            }
        }

        public void ReportStatus(string status)
        {
            lock (_lock)
            {
                try
                {
                    if (_progressViewModel != null)
                    {
                        _progressViewModel.ReportStatus(status);
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Failed to set status in progress bar {ex.Message}");
                }
            }
        }

        public void ReportStepStatus(string status)
        {
            lock (_lock)
            {
                try
                {
                    if (_progressViewModel != null)
                    {
                        _progressViewModel.ReportStepStatus(status);
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"Failed to set step status in progress bar {ex.Message}");
                }
            }
        }

        private void OnProgressBarClosed(object sender, EventArgs e)
        {
            _progressView.Dispatcher.InvokeShutdown();
            _progressView.Closed -= OnProgressBarClosed;
        }
    }
}
