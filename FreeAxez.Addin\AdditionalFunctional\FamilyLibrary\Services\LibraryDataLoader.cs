﻿using System.Collections.ObjectModel;
using System.IO;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Enums;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Utils;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;

public class LibraryDataLoader
{
    public async Task<LoadDataFamilyUpdatesPage> LoadFamilyUpdatesPageData()
    {
        var categories = await SafeApiCall(() => ApiService.Instance.GetAllCategoriesAsync());
        var allLibraryFamilies = await SafeApiCall(() => ApiService.Instance.GetAllFamiliesAsync());

        // Filter families by Revit version compatibility
        string currentRevitVersion = FamilyLibraryCore.CurrentRevitVersion;
        var compatibleFamilies = allLibraryFamilies
            .Where(f => FamilyLibraryCore.IsRevitVersionCompatible(f.RevitVersion, currentRevitVersion))
            .ToList();

        var familyInfos = GetFamilyInfosForFamilyUpdatesPage();
        var validatedLibraryFamilies = ValidateLibraryFamiliesForFamilyUpdatesPage(compatibleFamilies, familyInfos);
        AssignCategoriesToFamilies(validatedLibraryFamilies, categories);
        var revitVersions = ExtractRevitVersions(validatedLibraryFamilies);

        return new LoadDataFamilyUpdatesPage
        {
            Categories = new ObservableCollection<LibraryCategoryDto>(categories),
            Families = new ObservableCollection<LibraryItemDto>(validatedLibraryFamilies),
            RevitVersions = new ObservableCollection<string>(revitVersions)
        };
    }

    public async Task<LoadDataFamiliesPage> LoadFamiliesPageData()
    {
        var categories = await SafeApiCall(() => ApiService.Instance.GetAllCategoriesAsync());
        var allLibraryFamilies = await SafeApiCall(() => ApiService.Instance.GetAllFamiliesAsync());

        // Filter families by Revit version compatibility
        string currentRevitVersion = FamilyLibraryCore.CurrentRevitVersion;
        var compatibleFamilies = allLibraryFamilies
            .Where(f => FamilyLibraryCore.IsRevitVersionCompatible(f.RevitVersion, currentRevitVersion))
            .ToList();

        var groupedLibraryFamilies = GroupLibraryFamilies(compatibleFamilies);
        var latestFamilies = ExtractLatestFamilies(groupedLibraryFamilies);
        AssignCategoriesToFamilies(latestFamilies, categories);
        var revitVersions = ExtractRevitVersions(latestFamilies);

        return new LoadDataFamiliesPage
        {
            Categories = new ObservableCollection<LibraryCategoryDto>(categories),
            Families = new ObservableCollection<LibraryItemVm>(latestFamilies.Select(f => new LibraryItemVm(f))),
            RevitVersions = new ObservableCollection<string>(revitVersions),
            GroupedLibraryFamilies = groupedLibraryFamilies
        };
    }

    public async Task<LoadDataUnmatchedFamiliesPage> LoadUnmatchedFamiliesPageData(Document doc)
    {
        var specialityEquipmentCategory =
            doc.Settings.Categories.get_Item(BuiltInCategory.OST_SpecialityEquipment);
        var categoryId = specialityEquipmentCategory.Id;
        var clientFamilySymbols = new FilteredElementCollector(doc)
            .OfClass(typeof(FamilySymbol))
            .Cast<FamilySymbol>()
            .Where(fs => fs.Family.FamilyCategoryId == categoryId &&
                         fs.LookupParameter("Manufacturer")?.AsString() == "FreeAxez")
            .Select(fs => new UnmatchedFamilySymbol
            {
                FamilySymbolName = fs.Name,
                FamilyName = fs.Family.Name,
                Version = fs.LookupParameter("Version")?.AsString()
            })
            .ToList();

        var serverFamilies = await SafeApiCall(() => ApiService.Instance.GetAllFamiliesAsync());
        var serverFamilyNames = serverFamilies.Select(f => Path.GetFileNameWithoutExtension(f.Name)).ToHashSet();

        var unmatchedFamilies = clientFamilySymbols
            .Where(cfs => !serverFamilyNames.Contains(cfs.FamilyName))
            .ToList();

        return new LoadDataUnmatchedFamiliesPage
        {
            UnmatchedFamilies = new ObservableCollection<UnmatchedFamilySymbol>(unmatchedFamilies)
        };
    }

    public async Task<LoadDataHistoryPage> LoadHistoryPageData()
    {
        var familyHistoryAsync = await SafeApiCall(() => ApiService.Instance.GetAllHistoryAsync());
        var detailsHistoryAsync = await SafeApiCall(() => ApiService.Instance.GetAllDetailsHistoryAsync());

        return new LoadDataHistoryPage
        {
            FamilyHistoryEntries = familyHistoryAsync != null
                ? new ObservableCollection<LibraryItemHistoryEntryDto>(familyHistoryAsync)
                : new ObservableCollection<LibraryItemHistoryEntryDto>(),
            DetailsHistoryEntries = detailsHistoryAsync != null
                ? new ObservableCollection<LibraryItemDetailsHistoryEntryDto>(detailsHistoryAsync)
                : new ObservableCollection<LibraryItemDetailsHistoryEntryDto>()
        };
    }

    public async Task<LoadDataCategoriesPage> LoadCategoriesPageData()
    {
        var categories = await SafeApiCall(() => ApiService.Instance.GetAllCategoriesAsync());
        var families = await SafeApiCall(() => ApiService.Instance.GetLatestFamiliesAsync());

        if (categories != null && families != null)
            foreach (var category in categories)
            {
                category.LibraryItems.Clear();
                var matchedFamilies = families.Where(f => f.CategoryId == category.Id).ToList();
                foreach (var family in matchedFamilies) category.LibraryItems.Add(family);
            }

        return new LoadDataCategoriesPage
        {
            Categories = new ObservableCollection<LibraryCategoryDto>(categories ?? new List<LibraryCategoryDto>())
        };
    }

    private List<FamilyInfo> GetFamilyInfosForFamilyUpdatesPage()
    {
        var familyInfos = new FilteredElementCollector(RevitManager.Document)
            .OfClass(typeof(Family))
            .Cast<Family>()
            .SelectMany(f =>
                f.GetFamilySymbolIds().Take(1).Select(fid => RevitManager.Document.GetElement(fid) as FamilySymbol))
            .Where(fs => fs != null && fs.LookupParameter("Manufacturer")?.AsString() == "FreeAxez")
            .Select(fs => new FamilyInfo
            {
                Name = fs.Family.Name,
                Version = fs.LookupParameter("Version")?.AsString()
            })
            .ToList();

        return familyInfos;
    }

    private Dictionary<Guid, List<LibraryItemDto>> GroupLibraryFamilies(IEnumerable<LibraryItemDto> libraryFamilies)
    {
        var groupedLibraryFamilies = libraryFamilies
            .GroupBy(family => family.OriginalItemId)
            .ToDictionary(
                group => group.Key,
                group => group.OrderByDescending(f => f.DateCreated).ToList()
            );

        return groupedLibraryFamilies;
    }

    private List<LibraryItemDto> ExtractLatestFamilies(Dictionary<Guid, List<LibraryItemDto>> groupedLibraryFamilies)
    {
        var latestFamilies = groupedLibraryFamilies
            .Select(group => group.Value.First())
            .ToList();

        return latestFamilies;
    }

    private void AssignCategoriesToFamilies(IEnumerable<LibraryItemDto> families,
        IEnumerable<LibraryCategoryDto> categories)
    {
        foreach (var family in families)
        {
            var matchingCategory = categories.FirstOrDefault(c => c.Id == family.CategoryId);
            family.Category = matchingCategory;
        }
    }

    private List<LibraryItemDto> ValidateLibraryFamiliesForFamilyUpdatesPage(
        IEnumerable<LibraryItemDto> libraryFamilies, List<FamilyInfo> familyInfos)
    {
        var groupedLibraryFamilies = libraryFamilies
            .GroupBy(family => family.OriginalItemId)
            .ToDictionary(group => group.Key, group => group.OrderByDescending(f => f.DateCreated).ToList());

        var validatedLibraryFamilies = new List<LibraryItemDto>();

        foreach (var group in groupedLibraryFamilies)
        {
            var latestFamily = group.Value.First();
            var status = FamilyStatus.NotPresentInProject;

            foreach (var serverFamily in group.Value)
            {
                var familyNameWithoutExtension = Path.GetFileNameWithoutExtension(serverFamily.Name);
                var matchingFamily = familyInfos.FirstOrDefault(clientFamily =>
                    clientFamily.Name == familyNameWithoutExtension);

                if (matchingFamily != null)
                {
                    latestFamily.MatchingName = serverFamily.Name;
                    status = DetermineFamilyStatus(matchingFamily, latestFamily);
                    break;
                }
            }

            latestFamily.Status = status;
            validatedLibraryFamilies.Add(latestFamily);
        }

        return validatedLibraryFamilies;
    }

    private FamilyStatus DetermineFamilyStatus(FamilyInfo matchingFamily, LibraryItemDto serverFamily)
    {
        if (matchingFamily != null
            && !string.IsNullOrWhiteSpace(matchingFamily.Version)
            && !string.IsNullOrWhiteSpace(serverFamily.Version))
        {
            if (FamilyPropertiesComparer.IsVersionGreater(matchingFamily.Version, serverFamily.Version))
                return FamilyStatus.OutdatedVersionInProject;
            if (FamilyPropertiesComparer.IsVersionSame(matchingFamily.Version, serverFamily.Version))
                return FamilyStatus.CurrentVersionInProject;
        }

        return FamilyStatus.NotPresentInProject;
    }

    private List<string> ExtractRevitVersions(IEnumerable<LibraryItemDto> libraryFamilies)
    {
        var revitVersions = libraryFamilies
            .Select(family => family.RevitVersion)
            .Distinct()
            .OrderBy(version => version)
            .ToList();

        return revitVersions;
    }

    public async Task<LoadDataDetailsPage> LoadDetailsPageData()
    {
        var allDetails = await SafeApiCall(() => ApiService.Instance.GetAllDetailsAsync());

        return new LoadDataDetailsPage
        {
            Details = new ObservableCollection<LibraryItemDetailsDto>(allDetails ?? new List<LibraryItemDetailsDto>())
        };
    }

    private async Task<T> SafeApiCall<T>(Func<Task<T>> apiCall, T defaultValue = default)
    {
        try
        {
            return await apiCall();
        }
        catch (Exception ex)
        {
            LogHelper.Error($"API call failed: {ex.Message}");
            return defaultValue;
        }
    }

    public class FamilyInfo
    {
        public string Name { get; set; }
        public string Version { get; set; }
    }
}