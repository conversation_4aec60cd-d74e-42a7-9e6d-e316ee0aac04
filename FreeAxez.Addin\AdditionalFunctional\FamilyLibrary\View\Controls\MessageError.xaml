<UserControl x:Class="FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls.MessageError"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="30" d:DesignWidth="300">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <StackPanel Orientation="Horizontal" Margin="0 5">
            <Viewbox Width="16" Height="16">
                <Canvas Width="512" Height="512">
                    <Path Data="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336h24V272H216c-13.3 0-24-10.7-24-24s10.7-24 24-24h48c13.3 0 24 10.7 24 24v88h8c13.3 0 24 10.7 24 24s-10.7 24-24 24H216c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-208a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"
                          Fill="{StaticResource Red500}" Margin="0 0 5 0"/>
                </Canvas>
            </Viewbox>
            <TextBlock Text="{Binding Message}" 
                       Foreground="Red" 
                       Style="{StaticResource TextBase}"
                       FontWeight="SemiBold"
                       />
        </StackPanel>
    </Grid>
</UserControl>
