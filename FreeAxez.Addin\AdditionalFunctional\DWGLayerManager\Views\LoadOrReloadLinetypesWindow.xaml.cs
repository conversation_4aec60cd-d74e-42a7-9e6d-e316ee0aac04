using System.Windows;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.ViewModels;
using FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Services;

namespace FreeAxez.Addin.AdditionalFunctional.DWGLayerManager.Views
{
    public partial class LoadOrReloadLinetypesWindow : Window
    {
        public LoadOrReloadLinetypesWindow(DwgLayerManagerApiService apiService)
        {
            InitializeComponent();
            DataContext = new LoadOrReloadLinetypesViewModel(apiService);
        }
    }
}
