﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.ExportCAD.Utils
{
    public class CADExporter
    {
        private string _path;
        private string _prefix;
        private bool _color;
        private List<ViewSheet> _viewSheets;
        private readonly bool _includeFloatingInformation;
        private string _freeAxezDWGExportOptionsNameColor = "FreeAxezColor";
        private string _freeAxezDWGExportOptionsNameMonochrome = "FreeAxezMonochrome";
        private int _colorNumber = 8;


        public CADExporter(string path,
                           string prefix,
                           bool color,
                           List<ViewSheet> viewSheets,
                           bool includeFloatingInformation = true)
        {
            _path = path;
            _prefix = prefix;
            _color = color;
            _viewSheets = viewSheets;
            _includeFloatingInformation = includeFloatingInformation;
        }

        public void Export()
        {
            var validViewTypes = new ViewType[]
            {
                ViewType.AreaPlan,
                ViewType.CeilingPlan,
                ViewType.Elevation,
                ViewType.EngineeringPlan,
                ViewType.FloorPlan,
                ViewType.Section,
                ViewType.ThreeD
            };

            var views = _viewSheets
                .SelectMany(viewSheet => viewSheet.GetAllPlacedViews())
                .Select(RevitManager.Document.GetElement)
                .OfType<View>()
                .Where(view => validViewTypes.Contains(view.ViewType))
                .ToList();

            var options = GetFreeAxezDWGExportOptions();

            if (_includeFloatingInformation)
            {
                foreach (var sheet in _viewSheets)
                {
                    var formattedSheetName = $"{_prefix}{sheet.SheetNumber} - {sheet.Name}";

                    var validName = string.Concat(formattedSheetName.Split(Path.GetInvalidFileNameChars()));
                    RevitManager.Document.Export(_path, validName, [sheet.Id], options);
                }
            }
            else
            {
                foreach (var view in views)
                {
                    string sheetNumber = view
                        .get_Parameter(BuiltInParameter.VIEWPORT_SHEET_NUMBER)
                        ?.AsString() ?? string.Empty;

                    string sheetName = view
                        .get_Parameter(BuiltInParameter.VIEWPORT_SHEET_NAME)
                        ?.AsString() ?? string.Empty;

                    string formattedViewName = $"{_prefix}{sheetNumber}_{sheetName}_{view.Name}";
                    var validName = string.Concat(formattedViewName.Split(Path.GetInvalidFileNameChars()));
                    RevitManager.Document.Export(_path, validName, [view.Id], options);
                }
            }
        }

        private DWGExportOptions GetFreeAxezDWGExportOptions()
        {
            if (Properties.Settings.Default.EхportCADColor == true)
            {
                var options = DWGExportOptions.GetPredefinedOptions(RevitManager.Document, _freeAxezDWGExportOptionsNameColor);
                
                if (options == null)
                {
                    options = CreateDWGExportOptionsForColor();
                }

                return options;
            }
            else
            {
                var options = DWGExportOptions.GetPredefinedOptions(RevitManager.Document, _freeAxezDWGExportOptionsNameMonochrome);
                
                if (options == null)
                {
                    options = CreateDWGExportOptionsForMonoton();
                }

                return options;                
            }
        }

        private DWGExportOptions CreateDWGExportOptionsForColor()
        {
            var options = new DWGExportOptions();
            options.Colors = ExportColorMode.TrueColorPerView;
            options.LayerMapping = "AIA";
            options.MergedViews = true;
            options.FileVersion = ACADVersion.R2007;

            var tempName = Guid.NewGuid().ToString();
            var isExport = RevitManager.Document.Export(_path, tempName, new List<ElementId>() { _viewSheets.First().Id }, options);

            if (isExport == true)
            {
                var files = Directory.GetFiles(_path);
                foreach (var file in files)
                {
                    if (Path.GetFileNameWithoutExtension(file) == tempName)
                    {
                        File.Delete(file);
                    }
                }
            }

            using (var t = new Transaction(RevitManager.Document))
            {
                t.Start("Create FreeAxez Export Settings");
                
                var exportDWGSettings = ExportDWGSettings.Create(RevitManager.Document, _freeAxezDWGExportOptionsNameColor, options);
                                
                t.Commit();
            }

            return options;
        }

        private DWGExportOptions CreateDWGExportOptionsForMonoton()
        {
            var options = new DWGExportOptions();
            options.PropOverrides = PropOverrideMode.ByLayer;
            options.LayerMapping = "AIA";
            options.MergedViews = true;
            options.FileVersion = ACADVersion.R2007;

            // Probably bug in revit
            // Export in necessary because in other case export layer table will be empty and we can't override their
            // The file will then be replaced with a new file with the correct layer colors
            var tempName = Guid.NewGuid().ToString();
            var isExport = RevitManager.Document.Export(_path, tempName, new List<ElementId>() { _viewSheets.First().Id }, options);

            if (isExport == true)
            {
                var files = Directory.GetFiles(_path);
                foreach (var file in files)
                {
                    if (Path.GetFileNameWithoutExtension(file) == tempName)
                    {
                        File.Delete(file);
                    }
                }
            }

            var exportLayerTable = options.GetExportLayerTable();

            // Probably bug in revit
            // The ExportLayerTable does not contain all categories, so some categories will not be colored
            // Re-adding categories to fix it
            foreach (Category category in RevitManager.Document.Settings.Categories)
            {
                if (category.CategoryType == CategoryType.Invalid || category.CategoryType == CategoryType.Internal)
                {
                    continue;
                }

                var key = new ExportLayerKey(category.Name, string.Empty, SpecialType.Default);
                if (exportLayerTable.ContainsKey(key))
                {
                    var value = exportLayerTable[key];
                    value.ColorNumber = _colorNumber;
                    value.CutColorNumber = _colorNumber;
                    exportLayerTable[key] = value;
                }
                else
                {
                    var value = new ExportLayerInfo();
                    value.ColorNumber = _colorNumber;
                    value.CutColorNumber = _colorNumber;
                    exportLayerTable.Add(key, value);
                }

                if (category.SubCategories == null)
                {
                    continue;
                }

                foreach (Category subCategory in category.SubCategories)
                {
                    var subKey = new ExportLayerKey(category.Name, subCategory.Name, SpecialType.Default);
                    if (exportLayerTable.ContainsKey(subKey))
                    {
                        var subValue = exportLayerTable[subKey];
                        subValue.ColorNumber = _colorNumber;
                        subValue.CutColorNumber = _colorNumber;
                        exportLayerTable[subKey] = subValue;
                    }
                    else
                    {
                        var subValue = new ExportLayerInfo();
                        subValue.ColorNumber = _colorNumber;
                        subValue.CutColorNumber = _colorNumber;
                        exportLayerTable.Add(subKey, subValue);
                    }
                }
            }

            foreach (var exportLayer in exportLayerTable)
            {
                exportLayer.Value.ColorNumber = _colorNumber;
                exportLayer.Value.CutColorNumber = _colorNumber;
                exportLayerTable[exportLayer.Key] = exportLayer.Value;
            }
            
            options.SetExportLayerTable(exportLayerTable);

            using (var t = new Transaction(RevitManager.Document))
            {
                t.Start("Create FreeAxez Export Settings");

                var exportDWGSettings = ExportDWGSettings.Create(RevitManager.Document, _freeAxezDWGExportOptionsNameMonochrome, options);

                t.Commit();
            }

            return options;
        }
    }
}
