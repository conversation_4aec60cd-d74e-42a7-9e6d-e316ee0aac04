﻿using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.TransferProjectContent.ViewModels
{
    public class ConfirmationViewModel : WindowViewModel
    {
        private readonly Action<bool> _callback;

        public ConfirmationViewModel(Action<bool> callback)
        {
            _callback = callback;
            ConfirmCommand = new RelayCommand(OnConfirm);
            CancelCommand = new RelayCommand(OnCancel);
        }

        public ICommand ConfirmCommand { get; }
        public ICommand CancelCommand { get; }

        private void OnConfirm(object p)
        {
            _callback(true);
        }

        private void OnCancel(object p)
        {
            _callback(false);
        }
    }
}
