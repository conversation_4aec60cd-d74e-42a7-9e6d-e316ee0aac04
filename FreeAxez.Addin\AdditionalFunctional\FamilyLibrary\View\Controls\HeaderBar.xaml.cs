using System.Windows.Controls;
using System.Windows;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls;

public partial class HeaderBar : UserControl
{
    public static readonly DependencyProperty PageNameProperty = 
        DependencyProperty.Register("PageName", typeof(string), typeof(HeaderBar), new PropertyMetadata(null));

    public string PageName
    {
        get { return (string)GetValue(PageNameProperty); }
        set { SetValue(PageNameProperty, value); }
    }
    public HeaderBar()
    {
        InitializeComponent();
    }
}