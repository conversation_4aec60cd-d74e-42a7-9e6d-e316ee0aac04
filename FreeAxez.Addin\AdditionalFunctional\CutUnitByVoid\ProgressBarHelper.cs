﻿using FreeAxez.Addin.AdditionalFunctional.CutUnitByVoid.View;
using System.Threading;

namespace FreeAxez.Addin.AdditionalFunctional.CutUnitByVoid
{
    public class ProgressBarHelper
    {
        private Thread _progressBarThread;
        private ProgressBarWindow _progressBarWindow;


        public void Show()
        {
            _progressBarThread = new Thread(() =>
            {
                _progressBarWindow = new ProgressBarWindow();
                _progressBarWindow.Closed += (sender, e) => _progressBarWindow.Dispatcher.InvokeShutdown();
                _progressBarWindow.Show();
                System.Windows.Threading.Dispatcher.Run();
            });

            _progressBarThread.SetApartmentState(ApartmentState.STA);
            _progressBarThread.IsBackground = true;
            _progressBarThread.Start();
        }

        public void Close()
        {
            if (_progressBarWindow != null)
            {
                _progressBarWindow.Dispatcher.Invoke(() => _progressBarWindow.Close());
            }
        }

        /// <returns>Returns true if successfully updated, false otherwise.</returns>
        public bool SetMaxValue(int maxValue)
        {
            try
            {
                if (_progressBarWindow != null)
                {
                    _progressBarWindow.Dispatcher.Invoke(() => _progressBarWindow.SetMaxValue(maxValue));
                }
            }
            catch
            {
                return false;
            }
            return true;
        }

        /// <returns>Returns true if successfully updated, false otherwise.</returns>
        public bool UpdateProgress(int value)
        {
            try
            {
                if (_progressBarWindow != null)
                {
                    _progressBarWindow.Dispatcher.Invoke(() => _progressBarWindow.UpdateProgress(value));
                }
            }
            catch 
            {
                return false;
            }
            return true;
        }

        /// <returns>Returns true if successfully updated, false otherwise.</returns>
        public bool SetStatus(string message)
        {
            try
            {
                if (_progressBarWindow != null)
                {
                    _progressBarWindow.Dispatcher.Invoke(() => _progressBarWindow.SetStatus(message));
                }
            }
            catch
            {
                return false;
            }
            return true;
        }
    }
}
