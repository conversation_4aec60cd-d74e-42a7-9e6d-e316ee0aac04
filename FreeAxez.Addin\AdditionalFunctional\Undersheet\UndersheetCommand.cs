﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.AdditionalFunctional.Undersheet.Utils;
using FreeAxez.Addin.AdditionalFunctional.Undersheet.Views;
using FreeAxez.Addin.Infrastructure.UI;
using System.Collections.Generic;
using System.Linq;
using System;

namespace FreeAxez.Addin.AdditionalFunctional.Undersheet
{
    [TransactionAttribute(TransactionMode.Manual)]
    [RegenerationAttribute(RegenerationOption.Manual)]
    internal class UndersheetCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            if (RevitManager.UIDocument.ActiveView.ViewType != ViewType.FloorPlan)
            {
                InfoDialog.ShowDialog("Warning", "Undersheet placement is only\npossible on the floor plan.");
                return Result.Cancelled;
            }

            var undersheetSymbol = FreeAxezFamilyCollector.GetUndersheetFamilySymbol();
            if (undersheetSymbol == null)
            {
                InfoDialog.ShowDialog("Warning", $"No undersheet family with the symbol name\n" +
                    $"\"{FreeAxezFamilyCollector.UndersheetFamilySymbolName}\" was found in the project.");
                return Result.Cancelled;
            }

            if (!PlacementOptionWindow.ShowDialog(out Line startLine, out FilledRegion placementRegion))
            {
                return Result.Cancelled;
            }

            var levelId = (RevitManager.Document.ActiveView as View).GenLevel.Id;

            List<FamilyInstance> units = null;
            if (placementRegion == null)
            {
                units = FreeAxezFamilyCollector.GetUnits(levelId);
            }
            else
            {
                units = FreeAxezFamilyCollector.GetUnits(levelId, placementRegion);
            }

            if (units.Count == 0)
            {
                InfoDialog.ShowDialog("Warning", "There are no units on the plane or\nthey have not entered the selected region.");
                return Result.Cancelled;
            }

            var undersheet = new UndersheetCreator(startLine, units, undersheetSymbol);
            var createdUndersheetFamilies = undersheet.Create();


            var count = createdUndersheetFamilies.Count;
            var length = createdUndersheetFamilies.Sum(f => f.LookupParameter(UndersheetCreator.LengthParameterName).AsDouble());
            var formatedLength = Math.Round(length * 12, 2); // Convert to inches

            InfoDialog.ShowDialog("Warning", $"Created {count} undersheet instances\n" +
                                             $"with a total length of {formatedLength} inches.");

            return Result.Succeeded;
        }
    }
}
