﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;

namespace FreeAxez.Addin.AdditionalFunctional.Ramp.Utils
{
    public class LineSelectionFilter : ISelectionFilter
    {
        public bool AllowElement(Element elem)
        {
            if (elem is DetailLine || elem is ModelLine)
            {
                return true;
            }

            return false;
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            return false;
        }
    }
}