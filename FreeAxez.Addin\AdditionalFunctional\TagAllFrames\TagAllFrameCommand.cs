﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.TagAllFrames.Models;
using FreeAxez.Addin.AdditionalFunctional.TagAllFrames.Views;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI;

namespace FreeAxez.Addin.AdditionalFunctional.TagAllFrames
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    internal class TagAllFrameCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            if (RevitManager.UIDocument.ActiveView.ViewType == ViewType.ThreeD)
            {
                InfoDialog.ShowDialog("Error", "Unable to place tags in 3D view.\nPlease select another view.");
                return Result.Cancelled;
            }

            var frameTagFamily = new FrameAnnotationFamily();
            if (frameTagFamily.IsFamilyNotExist(out var errorMessage))
            {
                InfoDialog.ShowDialog("Error", errorMessage);
                return Result.Cancelled;
            }

            var frameFamily = new FrameFamily();
            if (frameFamily.IsFamilyNotExist(out errorMessage))
            {
                InfoDialog.ShowDialog("Error", errorMessage);
                return Result.Cancelled;
            }
            else if (frameFamily.IsNoPlacedInstancesOnView(out errorMessage))
            {
                InfoDialog.ShowDialog("Warning", errorMessage);
                return Result.Cancelled;
            }

            var tagAllFramesView = new TagAllFramesView();
            tagAllFramesView.ShowDialog();

            return Result.Succeeded;
        }
    }
}