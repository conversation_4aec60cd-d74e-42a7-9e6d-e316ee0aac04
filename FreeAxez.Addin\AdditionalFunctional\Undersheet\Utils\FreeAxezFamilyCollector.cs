﻿using Autodesk.Revit.DB;
using FreeAxez.Addin.Infrastructure;
using System.Collections.Generic;
using System.Linq;

namespace FreeAxez.Addin.AdditionalFunctional.Undersheet.Utils
{
    public static class FreeAxezFamilyCollector
    {
        private const string FreeAxezFamilyIdentifier = "FreeAxez";
        public const string UndersheetFamilySymbolName = "FreeAxez-Undersheet";


        public static FamilySymbol GetUndersheetFamilySymbol()
        {
            return new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_DetailComponents)
                .WhereElementIsElementType()
                .OfClass(typeof(FamilySymbol))
                .Cast<FamilySymbol>()
                .Where(s => s.Name == UndersheetFamilySymbolName)
                .FirstOrDefault();
        }

        public static List<FamilyInstance> GetUnits(ElementId levelId)
        {
            // TODO: Change filter for including just necessary elements

            return new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .OfClass(typeof(FamilyInstance))
                .Cast<FamilyInstance>()
                .Where(f => f.LevelId == levelId)
                .Where(f => f.Symbol.FamilyName.Contains(FreeAxezFamilyIdentifier))
                .ToList();
        }

        public static List<FamilyInstance> GetUnits(ElementId levelId, FilledRegion placementRegion)
        {
            var placementRegionBox = placementRegion.get_BoundingBox(RevitManager.Document.ActiveView);
            var outline = new Outline(placementRegionBox.Min.Add(new XYZ(0,0,-1)), placementRegionBox.Max.Add(new XYZ(0,0,1)));

            var units = new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_SpecialityEquipment)
                .OfClass(typeof(FamilyInstance))
                .WherePasses(new BoundingBoxIntersectsFilter(outline))
                .Cast<FamilyInstance>()
                .Where(f => f.LevelId == levelId)
                .Where(f => f.Symbol.FamilyName.Contains(FreeAxezFamilyIdentifier))
                .ToList();

            var face = placementRegion
                .get_Geometry(new Options())
                .Cast<Solid>()
                .Where(s => s.SurfaceArea > 0)
                .Select(s => s.Faces)
                .First()
                .get_Item(0) as PlanarFace;

            var validUnits = new List<FamilyInstance>();
            foreach (var u in units)
            {
                var locationPoint = (u.Location as LocationPoint).Point;
                var locationUV = new UV(locationPoint.Subtract(face.Origin).X, locationPoint.Subtract(face.Origin).Y);
                if (face.IsInside(locationUV))
                {
                    validUnits.Add(u);
                    continue;
                }

                var bb = u.get_BoundingBox(RevitManager.Document.ActiveView);
                var min = bb.Min.Subtract(face.Origin);
                var max = bb.Max.Subtract(face.Origin);

                var corner1 = new UV(min.X, min.Y);
                var corner2 = new UV(min.X, max.Y);
                var corner3 = new UV(max.X, max.Y);
                var corner4 = new UV(max.X, min.Y);

                if (face.IsInside(corner1) || face.IsInside(corner2) || face.IsInside(corner3) || face.IsInside(corner4))
                {
                    validUnits.Add(u);
                }
            }

            return validUnits;
        }
    }
}
