﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;

namespace FreeAxez.Addin.AdditionalFunctional.Undersheet.Utils
{
    public class FilledRegionSelectionFilter : ISelectionFilter
    {
        public bool AllowElement(Element elem)
        {
            return elem is FilledRegion;
        }

        public bool AllowReference(Reference reference, XYZ position)
        {
            return false;
        }
    }
}
