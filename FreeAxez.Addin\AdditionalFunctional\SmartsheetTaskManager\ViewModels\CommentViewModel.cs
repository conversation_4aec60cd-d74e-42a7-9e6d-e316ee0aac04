﻿using System;
using System.Windows.Media;

namespace FreeAxez.Addin.AdditionalFunctional.SmartsheetTaskManager.ViewModels;

public class CommentViewModel
{
    public CommentViewModel(string text,
                            string createdBy,
                            DateTime createdAt,
                            Brush nameColor)
    {
        Text = text;
        CreatedBy = createdBy;
        CreatedAt = createdAt;
        NameColor = nameColor;
    }

    public string Text { get; }
    public string CreatedBy { get; }
    public DateTime CreatedAt { get; }
    public Brush NameColor { get; }
}
