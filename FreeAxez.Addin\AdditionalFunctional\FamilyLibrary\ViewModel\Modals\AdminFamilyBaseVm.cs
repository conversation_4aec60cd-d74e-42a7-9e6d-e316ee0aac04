﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using Autodesk.Revit.DB;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Model;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.Services;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.View.Controls;
using FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Controls;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using Microsoft.WindowsAPICodePack.Shell;

namespace FreeAxez.Addin.AdditionalFunctional.FamilyLibrary.ViewModel.Modals
{
    public abstract class AdminFamilyBaseVm : ModalDialogVm
    {
        protected readonly ObservableCollection<LibraryItemDto> FamiliesToUpload = new();
        // Store file errors as (fileName, errorMessage) pairs
        protected readonly List<(string FileName, string ErrorMessage)> FileErrors = new();
        protected readonly List<string> LoadingErrors = new();
        protected ObservableCollection<LibraryCategoryDto> _categories;
        protected bool _isUploading;
        protected LibraryCategoryDto _selectedCategory;
        protected ObservableCollection<LibraryItemDetailsNew> _uploadFileInfo = new();

        protected AdminFamilyBaseVm()
        {
            UploadFileInfo.CollectionChanged += (s, e) =>
            {
                if (!UploadFileInfo.Any()) ClearWarningsAndErrors();
                UpdateHasUploadedFiles();
            };
            IsUploading = false;
            RejectFileCommand = new RelayCommand(RejectChooseFile);
            LoadCategoriesCommand = new AsyncRelayCommand(async () => await LoadCategories());
            LoadCategoriesCommand.Execute(null);
        }

        public ICommand LoadCategoriesCommand { get; protected set; }
        public ICommand RejectFileCommand { get; protected set; }

        protected ObservableCollection<MessageWarning> _warnings { get; set; } = new();
        protected ObservableCollection<MessageError> _errors { get; set; } = new();

        public ObservableCollection<LibraryItemDetailsNew> UploadFileInfo
        {
            get => _uploadFileInfo;
            protected set
            {
                _uploadFileInfo = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasUploadedFiles));
            }
        }

        public ObservableCollection<MessageWarning> Warnings
        {
            get => _warnings;
            protected set
            {
                _warnings = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<MessageError> Errors
        {
            get => _errors;
            protected set
            {
                _errors = value;
                OnPropertyChanged();
            }
        }

        public bool HasUploadedFiles => UploadFileInfo.Any();

        public virtual bool CanApply => HasUploadedFiles && !IsUploading;

        public ObservableCollection<LibraryCategoryDto> Categories
        {
            get => _categories;
            set
            {
                _categories = value;
                OnPropertyChanged();
            }
        }

        public LibraryCategoryDto SelectedCategory
        {
            get => _selectedCategory;
            set
            {
                _selectedCategory = value;
                OnPropertyChanged();
            }
        }

        public bool IsUploading
        {
            get => _isUploading;
            set
            {
                _isUploading = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CanApply));
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public event Action CategoriesLoaded;

        protected async Task LoadCategories()
        {
            var categories = await ApiService.Instance.GetAllCategoriesAsync();
            if (categories != null) Categories = new ObservableCollection<LibraryCategoryDto>(categories);
            CategoriesLoaded?.Invoke();
        }

        protected void RejectChooseFile(object parameter)
        {
            if (parameter is LibraryItemDetailsNewVm fileDetailVm)
            {
                var itemToRemove = FamiliesToUpload.FirstOrDefault(item => item.Name == fileDetailVm.FileName);
                if (itemToRemove != null) FamiliesToUpload.Remove(itemToRemove);

                var detailToRemove = UploadFileInfo.FirstOrDefault(detail => detail.DataContext == fileDetailVm);
                if (detailToRemove != null) UploadFileInfo.Remove(detailToRemove);
            }
        }

        // Get thumbnail from .rfa file
        protected static BitmapSource GetThumbnail(string filePath)
        {
            try
            {
                using (var shellFile = ShellFile.FromFilePath(filePath))
                {
                    var thumbnail = shellFile.Thumbnail;
                    thumbnail.FormatOption = ShellThumbnailFormatOption.ThumbnailOnly;
                    return thumbnail.MediumBitmapSource;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Failed to get thumbnail for {filePath}: {ex.Message}");
                return null;
            }
        }

        // Get parameter value from FamilyManager or return default
        protected string GetParameterValueOrDefault(FamilyManager familyManager, string parameterName)
        {
            foreach (FamilyType familyType in familyManager.Types)
            {
                FamilyParameter parameter = familyManager.get_Parameter(parameterName);
                if (parameter != null)
                {
                    string value = parameter.StorageType switch
                    {
                        StorageType.Integer => familyType.AsInteger(parameter).ToString(),
                        StorageType.Double => familyType.AsDouble(parameter).ToString(),
                        StorageType.String => familyType.AsString(parameter),
                        _ => null
                    };

                    if (value != null) return value;
                }
            }
            return "Unknown";
        }

        // Show detailed error messages for failed files
        protected void ShowIncompatibleFilesMessage()
        {
            var message = "The following files could not be processed:\n" +
                          string.Join("\n", FileErrors.Select(e => $"{e.FileName}: {e.ErrorMessage}"));
            FamilyLibraryCore.ShowMessage("File Processing Errors", message, MessageType.Warning);
        }

        // Show success message for uploaded families
        protected virtual void ShowSuccessMessage()
        {
            FamilyLibraryCore.ShowMessage("Success", "All families were successfully uploaded to Library", MessageType.Success);
        }

        // Show errors during upload
        protected void ShowLoadingErrorsMessage()
        {
            var message = "Errors occurred while uploading some families:\n" +
                          string.Join("\n", LoadingErrors);
            FamilyLibraryCore.ShowMessage("Upload Errors", message, MessageType.Warning);
        }

        private void ClearWarningsAndErrors()
        {
            Warnings.Clear();
            Errors.Clear();
        }

        private void UpdateHasUploadedFiles()
        {
            OnPropertyChanged(nameof(HasUploadedFiles));
            OnPropertyChanged(nameof(CanApply));
        }

        protected abstract void ExecuteChooseFile(object parameter);
        protected abstract void ExecuteApply(object parameter);
    }
}